import React, { useEffect, useState } from 'react';
import { useDispatch, useSelector } from 'react-redux';
import {
  Container,
  Grid,
  Typography,
  Card,
  CardMedia,
  CardContent,
  Button,
  Box,
  TextField,
  InputAdornment,
  Paper,
  Chip,
  Rating,
  IconButton,
  Badge,
  Avatar,
  Divider,
  CircularProgress,
  Alert,
  Tabs,
  Tab,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  Slider
} from '@mui/material';
import {
  Search,
  ShoppingCart,
  Favorite,
  FavoriteBorder,
  Star,
  LocalShipping,
  Security,
  Assignment,
  FilterList,
  Sort,
  CompareArrows,
  Share,
  LocalOffer
} from '@mui/icons-material';
import { styled } from '@mui/material/styles';
import { productsAPI, cartAPI, handleAPIError } from '../services/api';
import { useAuth } from '../context/AuthContext';
import { useNavigate } from 'react-router-dom';

// Flipkart-style styled components
const FlipkartHeader = styled(Paper)(({ theme }) => ({
  backgroundColor: '#2874f0',
  color: 'white',
  padding: theme.spacing(1, 0),
  boxShadow: '0 2px 4px rgba(0,0,0,0.1)',
}));

const CategoryCard = styled(Card)(({ theme }) => ({
  textAlign: 'center',
  padding: theme.spacing(2),
  cursor: 'pointer',
  transition: 'all 0.3s ease',
  '&:hover': {
    transform: 'translateY(-4px)',
    boxShadow: '0 8px 25px rgba(0,0,0,0.15)',
  },
}));

const ProductCard = styled(Card)(({ theme }) => ({
  height: '100%',
  display: 'flex',
  flexDirection: 'column',
  transition: 'all 0.3s ease',
  cursor: 'pointer',
  '&:hover': {
    transform: 'translateY(-4px)',
    boxShadow: '0 8px 25px rgba(0,0,0,0.15)',
  },
}));

const OfferBanner = styled(Box)(({ theme }) => ({
  background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',
  color: 'white',
  padding: theme.spacing(3),
  borderRadius: theme.spacing(1),
  textAlign: 'center',
  margin: theme.spacing(2, 0),
}));

const Home = () => {
  const dispatch = useDispatch();
  const navigate = useNavigate();
  const { user, isAuthenticated, logout } = useAuth();
  const [products, setProducts] = useState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const [searchTerm, setSearchTerm] = useState('');
  const [selectedCategory, setSelectedCategory] = useState('All');
  const [priceRange, setPriceRange] = useState([0, 100]);
  const [sortBy, setSortBy] = useState('featured');
  const [favorites, setFavorites] = useState(new Set());
  const [cartItems, setCartItems] = useState([]);
  const [tabValue, setTabValue] = useState(0);

  // Fetch products from API
  useEffect(() => {
    const fetchProducts = async () => {
      try {
        setLoading(true);
        const response = await productsAPI.getAll();
        setProducts(response.data);
        setError(null);
      } catch (err) {
        setError(handleAPIError(err));
        // Fallback to mock data
        setProducts(getMockProducts());
      } finally {
        setLoading(false);
      }
    };

    fetchProducts();

    // Load cart items
    const cart = cartAPI.getCart();
    setCartItems(cart.items);
  }, []);

  // Mock data fallback
  const getMockProducts = () => [
    {
      _id: '1',
      name: 'Ceylon Cinnamon Sticks',
      price: 15.99,
      image: 'https://images.unsplash.com/photo-1506905925346-21bda4d32df4?w=300&h=300&fit=crop',
      rating: 4.8,
      numReviews: 124,
      category: 'Whole Spices',
      stock: 50,
      origin: 'Sri Lanka',
      weight: 100,
      unit: 'g',
      featured: true,
      description: 'Premium quality Ceylon cinnamon sticks from Sri Lanka'
    },
    {
      _id: '2',
      name: 'Black Peppercorns',
      price: 12.50,
      image: 'https://images.unsplash.com/photo-1596040033229-a9821ebd058d?w=300&h=300&fit=crop',
      rating: 4.9,
      numReviews: 89,
      category: 'Whole Spices',
      stock: 30,
      origin: 'India',
      weight: 50,
      unit: 'g',
      featured: false,
      description: 'Freshly ground black peppercorns with intense flavor'
    },
    {
      _id: '3',
      name: 'Organic Turmeric Powder',
      price: 18.75,
      image: 'https://images.unsplash.com/photo-1615485500704-8e990f9900f7?w=300&h=300&fit=crop',
      rating: 4.7,
      numReviews: 156,
      category: 'Ground Spices',
      stock: 25,
      origin: 'India',
      weight: 200,
      unit: 'g',
      featured: true,
      description: 'Pure organic turmeric powder with anti-inflammatory properties'
    },
    {
      _id: '4',
      name: 'Green Cardamom Pods',
      price: 24.99,
      image: 'https://images.unsplash.com/photo-1596040033229-a9821ebd058d?w=300&h=300&fit=crop',
      rating: 4.6,
      numReviews: 78,
      category: 'Whole Spices',
      stock: 40,
      origin: 'Guatemala',
      weight: 50,
      unit: 'g',
      featured: false,
      description: 'Aromatic green cardamom pods perfect for tea and desserts'
    },
    {
      _id: '5',
      name: 'Star Anise',
      price: 16.99,
      image: 'https://images.unsplash.com/photo-1506905925346-21bda4d32df4?w=300&h=300&fit=crop',
      rating: 4.5,
      numReviews: 45,
      category: 'Whole Spices',
      stock: 35,
      origin: 'China',
      weight: 75,
      unit: 'g',
      featured: false,
      description: 'Whole star anise with sweet licorice flavor'
    },
    {
      _id: '6',
      name: 'Saffron Threads',
      price: 89.99,
      image: 'https://images.unsplash.com/photo-1615485500704-8e990f9900f7?w=300&h=300&fit=crop',
      rating: 4.9,
      numReviews: 234,
      category: 'Herbs',
      stock: 10,
      origin: 'Kashmir',
      weight: 1,
      unit: 'g',
      featured: true,
      description: 'Premium saffron threads - the most expensive spice in the world'
    }
  ];

  const categories = [
    { name: 'All', icon: '🛒', count: products.length },
    { name: 'Whole Spices', icon: '🌿', count: products.filter(p => p.category === 'Whole Spices').length },
    { name: 'Ground Spices', icon: '🥄', count: products.filter(p => p.category === 'Ground Spices').length },
    { name: 'Herbs', icon: '🌱', count: products.filter(p => p.category === 'Herbs').length },
    { name: 'Spice Blends', icon: '🍛', count: products.filter(p => p.category === 'Spice Blends').length },
    { name: 'Seasonings', icon: '🧂', count: products.filter(p => p.category === 'Seasonings').length }
  ];

  const handleAddToCart = (product) => {
    const cart = cartAPI.addToCart(product);
    setCartItems(cart.items);
    dispatch({ type: 'ADD_TO_CART', payload: product });
  };

  const toggleFavorite = (productId) => {
    const newFavorites = new Set(favorites);
    if (newFavorites.has(productId)) {
      newFavorites.delete(productId);
    } else {
      newFavorites.add(productId);
    }
    setFavorites(newFavorites);
  };

  // Filter and sort products
  const filteredProducts = products
    .filter(product => {
      const matchesSearch = product.name.toLowerCase().includes(searchTerm.toLowerCase());
      const matchesCategory = selectedCategory === 'All' || product.category === selectedCategory;
      const matchesPrice = product.price >= priceRange[0] && product.price <= priceRange[1];
      return matchesSearch && matchesCategory && matchesPrice;
    })
    .sort((a, b) => {
      switch (sortBy) {
        case 'price-low': return a.price - b.price;
        case 'price-high': return b.price - a.price;
        case 'rating': return b.rating - a.rating;
        case 'newest': return new Date(b.createdAt) - new Date(a.createdAt);
        default: return b.featured - a.featured;
      }
    });

  if (loading) {
    return (
      <Box sx={{ display: 'flex', justifyContent: 'center', alignItems: 'center', height: '50vh' }}>
        <CircularProgress size={60} />
      </Box>
    );
  }

  return (
    <Box sx={{ backgroundColor: '#f1f3f6', minHeight: '100vh' }}>
      {/* Flipkart-style Header */}
      <FlipkartHeader>
        <Container maxWidth="xl">
          <Grid container alignItems="center" spacing={2}>
            <Grid item xs={12} md={3}>
              <Box sx={{ display: 'flex', alignItems: 'center' }}>
                <Typography variant="h5" sx={{ fontWeight: 'bold', mr: 2 }}>
                  🛒 SpiceMart
                </Typography>
                <Typography variant="caption" sx={{ fontStyle: 'italic' }}>
                  Explore <span style={{ color: '#ffe500' }}>Plus</span>
                </Typography>
              </Box>
            </Grid>

            <Grid item xs={12} md={5}>
              <TextField
                fullWidth
                placeholder="Search for spices, herbs and more..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                InputProps={{
                  startAdornment: (
                    <InputAdornment position="start">
                      <Search sx={{ color: '#2874f0' }} />
                    </InputAdornment>
                  ),
                  sx: { backgroundColor: 'white', borderRadius: 1 }
                }}
                size="small"
              />
            </Grid>

            <Grid item xs={12} md={4}>
              <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'flex-end', gap: 2 }}>
                <Button color="inherit" startIcon={<Assignment />}>
                  Become a Seller
                </Button>

                {isAuthenticated ? (
                  <>
                    <Button
                      color="inherit"
                      onClick={() => navigate(user?.isAdmin ? '/admin' : '/dashboard')}
                    >
                      {user?.isAdmin ? 'Admin' : 'Dashboard'}
                    </Button>
                    <Badge badgeContent={cartItems.length} color="error">
                      <IconButton color="inherit" onClick={() => navigate('/cart')}>
                        <ShoppingCart />
                      </IconButton>
                    </Badge>
                    <IconButton
                      color="inherit"
                      onClick={() => navigate('/dashboard')}
                    >
                      <Avatar sx={{ bgcolor: '#ff6f00', width: 32, height: 32 }}>
                        {user?.name?.charAt(0).toUpperCase()}
                      </Avatar>
                    </IconButton>
                    <Button color="inherit" onClick={logout}>
                      Logout
                    </Button>
                  </>
                ) : (
                  <>
                    <Button color="inherit" onClick={() => navigate('/login')}>
                      Login
                    </Button>
                    <Button
                      color="inherit"
                      onClick={() => navigate('/register')}
                      sx={{
                        backgroundColor: 'rgba(255,255,255,0.1)',
                        '&:hover': { backgroundColor: 'rgba(255,255,255,0.2)' }
                      }}
                    >
                      Sign Up
                    </Button>
                    <Badge badgeContent={cartItems.length} color="error">
                      <IconButton color="inherit" onClick={() => navigate('/cart')}>
                        <ShoppingCart />
                      </IconButton>
                    </Badge>
                  </>
                )}
              </Box>
            </Grid>
          </Grid>
        </Container>
      </FlipkartHeader>

      {/* Offer Banner */}
      <Container maxWidth="xl" sx={{ mt: 2 }}>
        <OfferBanner>
          <Typography variant="h4" sx={{ fontWeight: 'bold', mb: 1 }}>
            🎉 Big Billion Days Sale! 🎉
          </Typography>
          <Typography variant="h6">
            Up to 80% OFF on Premium Spices | Free Delivery | No Cost EMI
          </Typography>
        </OfferBanner>
      </Container>

      {/* Categories Section */}
      <Container maxWidth="xl" sx={{ mt: 3 }}>
        <Paper sx={{ p: 2 }}>
          <Typography variant="h6" sx={{ mb: 2, fontWeight: 'bold' }}>
            Shop by Category
          </Typography>
          <Grid container spacing={2}>
            {categories.map((category) => (
              <Grid item xs={6} sm={4} md={2} key={category.name}>
                <CategoryCard
                  onClick={() => setSelectedCategory(category.name)}
                  sx={{
                    backgroundColor: selectedCategory === category.name ? '#e3f2fd' : 'white',
                    border: selectedCategory === category.name ? '2px solid #2874f0' : '1px solid #e0e0e0'
                  }}
                >
                  <Typography variant="h4" sx={{ mb: 1 }}>
                    {category.icon}
                  </Typography>
                  <Typography variant="body2" sx={{ fontWeight: 'bold' }}>
                    {category.name}
                  </Typography>
                  <Typography variant="caption" color="textSecondary">
                    ({category.count} items)
                  </Typography>
                </CategoryCard>
              </Grid>
            ))}
          </Grid>
        </Paper>
      </Container>

      {/* Filters and Sort */}
      <Container maxWidth="xl" sx={{ mt: 2 }}>
        <Paper sx={{ p: 2 }}>
          <Grid container spacing={2} alignItems="center">
            <Grid item xs={12} md={3}>
              <FormControl fullWidth size="small">
                <InputLabel>Sort By</InputLabel>
                <Select
                  value={sortBy}
                  label="Sort By"
                  onChange={(e) => setSortBy(e.target.value)}
                >
                  <MenuItem value="featured">Featured</MenuItem>
                  <MenuItem value="price-low">Price: Low to High</MenuItem>
                  <MenuItem value="price-high">Price: High to Low</MenuItem>
                  <MenuItem value="rating">Customer Rating</MenuItem>
                  <MenuItem value="newest">Newest First</MenuItem>
                </Select>
              </FormControl>
            </Grid>

            <Grid item xs={12} md={4}>
              <Typography variant="body2" sx={{ mb: 1 }}>
                Price Range: ${priceRange[0]} - ${priceRange[1]}
              </Typography>
              <Slider
                value={priceRange}
                onChange={(e, newValue) => setPriceRange(newValue)}
                valueLabelDisplay="auto"
                min={0}
                max={100}
                sx={{ color: '#2874f0' }}
              />
            </Grid>

            <Grid item xs={12} md={5}>
              <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                <Chip
                  icon={<FilterList />}
                  label="Filters"
                  variant="outlined"
                  sx={{ color: '#2874f0', borderColor: '#2874f0' }}
                />
                <Chip
                  icon={<LocalShipping />}
                  label="Free Delivery"
                  variant="outlined"
                  color="success"
                />
                <Chip
                  icon={<Security />}
                  label="Assured"
                  variant="outlined"
                  color="primary"
                />
              </Box>
            </Grid>
          </Grid>
        </Paper>
      </Container>

      {/* Products Grid */}
      <Container maxWidth="xl" sx={{ mt: 2, pb: 4 }}>
        {error && (
          <Alert severity="warning" sx={{ mb: 2 }}>
            {error} - Showing sample data
          </Alert>
        )}

        <Typography variant="h6" sx={{ mb: 2, fontWeight: 'bold' }}>
          {selectedCategory === 'All' ? 'All Products' : selectedCategory}
          <span style={{ color: '#878787', fontWeight: 'normal' }}>
            ({filteredProducts.length} items)
          </span>
        </Typography>

        <Grid container spacing={2}>
          {filteredProducts.map((product) => (
            <Grid item xs={6} sm={4} md={3} lg={2.4} key={product._id}>
              <ProductCard>
                <Box sx={{ position: 'relative' }}>
                  <CardMedia
                    component="img"
                    height="200"
                    image={product.image}
                    alt={product.name}
                    sx={{ objectFit: 'cover' }}
                  />
                  <IconButton
                    sx={{
                      position: 'absolute',
                      top: 8,
                      right: 8,
                      backgroundColor: 'rgba(255,255,255,0.8)',
                      '&:hover': { backgroundColor: 'rgba(255,255,255,0.9)' }
                    }}
                    onClick={() => toggleFavorite(product._id)}
                  >
                    {favorites.has(product._id) ?
                      <Favorite sx={{ color: '#ff6b6b' }} /> :
                      <FavoriteBorder />
                    }
                  </IconButton>
                  {product.featured && (
                    <Chip
                      label="Bestseller"
                      color="error"
                      size="small"
                      sx={{
                        position: 'absolute',
                        top: 8,
                        left: 8,
                        fontSize: '0.7rem'
                      }}
                    />
                  )}
                </Box>

                <CardContent sx={{ flexGrow: 1, p: 2 }}>
                  <Typography
                    variant="body2"
                    sx={{
                      fontWeight: 'bold',
                      overflow: 'hidden',
                      textOverflow: 'ellipsis',
                      whiteSpace: 'nowrap',
                      mb: 1
                    }}
                  >
                    {product.name}
                  </Typography>

                  <Box sx={{ display: 'flex', alignItems: 'center', mb: 1 }}>
                    <Rating value={product.rating || 4.5} readOnly size="small" />
                    <Typography variant="caption" sx={{ ml: 1, color: '#878787' }}>
                      ({product.numReviews || 0})
                    </Typography>
                  </Box>

                  <Box sx={{ display: 'flex', alignItems: 'center', mb: 1 }}>
                    <Typography variant="h6" sx={{ fontWeight: 'bold', color: '#212121' }}>
                      ₹{(product.price * 75).toFixed(0)}
                    </Typography>
                    <Typography
                      variant="caption"
                      sx={{
                        ml: 1,
                        textDecoration: 'line-through',
                        color: '#878787'
                      }}
                    >
                      ₹{(product.price * 100).toFixed(0)}
                    </Typography>
                    <Typography variant="caption" sx={{ ml: 1, color: '#388e3c', fontWeight: 'bold' }}>
                      25% off
                    </Typography>
                  </Box>

                  <Typography variant="caption" sx={{ color: '#878787', display: 'block', mb: 1 }}>
                    {product.weight}{product.unit} | {product.origin}
                  </Typography>

                  <Box sx={{ display: 'flex', gap: 1 }}>
                    <Button
                      variant="contained"
                      size="small"
                      fullWidth
                      onClick={() => handleAddToCart(product)}
                      sx={{
                        backgroundColor: '#ff9f00',
                        '&:hover': { backgroundColor: '#e68900' },
                        fontSize: '0.75rem',
                        py: 0.5
                      }}
                    >
                      ADD TO CART
                    </Button>
                  </Box>

                  <Box sx={{ display: 'flex', alignItems: 'center', mt: 1, gap: 1 }}>
                    <LocalShipping sx={{ fontSize: 14, color: '#388e3c' }} />
                    <Typography variant="caption" sx={{ color: '#388e3c' }}>
                      Free Delivery
                    </Typography>
                  </Box>
                </CardContent>
              </ProductCard>
            </Grid>
          ))}
        </Grid>

        {filteredProducts.length === 0 && (
          <Box sx={{ textAlign: 'center', py: 8 }}>
            <Typography variant="h6" color="textSecondary">
              No products found matching your criteria
            </Typography>
            <Button
              variant="outlined"
              sx={{ mt: 2 }}
              onClick={() => {
                setSearchTerm('');
                setSelectedCategory('All');
                setPriceRange([0, 100]);
              }}
            >
              Clear Filters
            </Button>
          </Box>
        )}
      </Container>
    </Box>
  );
};

export default Home;