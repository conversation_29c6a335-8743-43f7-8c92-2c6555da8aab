import React from 'react';
import { 
  Drawer, 
  List, 
  ListItem, 
  ListItemIcon, 
  ListItemText, 
  ListItemButton,
  Divider,
  Typography,
  Box 
} from '@mui/material';
import { 
  Home, 
  ShoppingCart, 
  Dashboard, 
  AdminPanelSettings,
  Person,
  Store,
  Analytics,
  Settings,
  Logout
} from '@mui/icons-material';
import { useNavigate, useLocation } from 'react-router-dom';

const Navigation = ({ open, onClose }) => {
  const navigate = useNavigate();
  const location = useLocation();

  const userMenuItems = [
    { text: 'Home', icon: <Home />, path: '/' },
    { text: 'Shopping Cart', icon: <ShoppingCart />, path: '/cart' },
    { text: 'My Dashboard', icon: <Dashboard />, path: '/dashboard' },
  ];

  const adminMenuItems = [
    { text: 'Admin Dashboard', icon: <AdminPanelSettings />, path: '/admin' },
    { text: 'Products', icon: <Store />, path: '/admin/products' },
    { text: 'Analytics', icon: <Analytics />, path: '/admin/analytics' },
    { text: 'Settings', icon: <Settings />, path: '/admin/settings' },
  ];

  const handleNavigation = (path) => {
    navigate(path);
    onClose();
  };

  return (
    <Drawer
      anchor="left"
      open={open}
      onClose={onClose}
      sx={{
        '& .MuiDrawer-paper': {
          width: 280,
          boxSizing: 'border-box',
        },
      }}
    >
      <Box sx={{ p: 2, backgroundColor: '#2c3e50', color: 'white' }}>
        <Typography variant="h6" sx={{ fontWeight: 'bold' }}>
          🌶️ Spice Ecommerce
        </Typography>
        <Typography variant="caption">
          Premium Quality Spices
        </Typography>
      </Box>

      <List>
        <ListItem>
          <Typography variant="subtitle2" color="textSecondary" sx={{ fontWeight: 'bold' }}>
            USER MENU
          </Typography>
        </ListItem>
        {userMenuItems.map((item) => (
          <ListItemButton
            key={item.text}
            onClick={() => handleNavigation(item.path)}
            selected={location.pathname === item.path}
            sx={{
              '&.Mui-selected': {
                backgroundColor: '#e3f2fd',
                '& .MuiListItemIcon-root': {
                  color: '#1976d2',
                },
                '& .MuiListItemText-primary': {
                  color: '#1976d2',
                  fontWeight: 'bold',
                },
              },
            }}
          >
            <ListItemIcon>{item.icon}</ListItemIcon>
            <ListItemText primary={item.text} />
          </ListItemButton>
        ))}

        <Divider sx={{ my: 2 }} />

        <ListItem>
          <Typography variant="subtitle2" color="textSecondary" sx={{ fontWeight: 'bold' }}>
            ADMIN MENU
          </Typography>
        </ListItem>
        {adminMenuItems.map((item) => (
          <ListItemButton
            key={item.text}
            onClick={() => handleNavigation(item.path)}
            selected={location.pathname === item.path}
            sx={{
              '&.Mui-selected': {
                backgroundColor: '#fff3e0',
                '& .MuiListItemIcon-root': {
                  color: '#f57c00',
                },
                '& .MuiListItemText-primary': {
                  color: '#f57c00',
                  fontWeight: 'bold',
                },
              },
            }}
          >
            <ListItemIcon>{item.icon}</ListItemIcon>
            <ListItemText primary={item.text} />
          </ListItemButton>
        ))}

        <Divider sx={{ my: 2 }} />

        <ListItemButton onClick={() => handleNavigation('/profile')}>
          <ListItemIcon><Person /></ListItemIcon>
          <ListItemText primary="Profile" />
        </ListItemButton>

        <ListItemButton onClick={() => console.log('Logout')}>
          <ListItemIcon><Logout /></ListItemIcon>
          <ListItemText primary="Logout" />
        </ListItemButton>
      </List>
    </Drawer>
  );
};

export default Navigation;
