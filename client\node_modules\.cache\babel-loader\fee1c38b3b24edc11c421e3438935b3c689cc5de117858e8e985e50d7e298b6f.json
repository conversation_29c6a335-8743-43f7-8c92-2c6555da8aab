{"ast": null, "code": "import { timeInterval } from \"./interval.js\";\nexport const timeYear = timeInterval(date => {\n  date.setMonth(0, 1);\n  date.setHours(0, 0, 0, 0);\n}, (date, step) => {\n  date.setFullYear(date.getFullYear() + step);\n}, (start, end) => {\n  return end.getFullYear() - start.getFullYear();\n}, date => {\n  return date.getFullYear();\n});\n\n// An optimized implementation for this simple case.\ntimeYear.every = k => {\n  return !isFinite(k = Math.floor(k)) || !(k > 0) ? null : timeInterval(date => {\n    date.setFullYear(Math.floor(date.getFullYear() / k) * k);\n    date.setMonth(0, 1);\n    date.setHours(0, 0, 0, 0);\n  }, (date, step) => {\n    date.setFullYear(date.getFullYear() + step * k);\n  });\n};\nexport const timeYears = timeYear.range;\nexport const utcYear = timeInterval(date => {\n  date.setUTCMonth(0, 1);\n  date.setUTCHours(0, 0, 0, 0);\n}, (date, step) => {\n  date.setUTCFullYear(date.getUTCFullYear() + step);\n}, (start, end) => {\n  return end.getUTCFullYear() - start.getUTCFullYear();\n}, date => {\n  return date.getUTCFullYear();\n});\n\n// An optimized implementation for this simple case.\nutcYear.every = k => {\n  return !isFinite(k = Math.floor(k)) || !(k > 0) ? null : timeInterval(date => {\n    date.setUTCFullYear(Math.floor(date.getUTCFullYear() / k) * k);\n    date.setUTCMonth(0, 1);\n    date.setUTCHours(0, 0, 0, 0);\n  }, (date, step) => {\n    date.setUTCFullYear(date.getUTCFullYear() + step * k);\n  });\n};\nexport const utcYears = utcYear.range;", "map": {"version": 3, "names": ["timeInterval", "timeYear", "date", "setMonth", "setHours", "step", "setFullYear", "getFullYear", "start", "end", "every", "k", "isFinite", "Math", "floor", "timeYears", "range", "utcYear", "setUTCMonth", "setUTCHours", "setUTCFullYear", "getUTCFullYear", "utcYears"], "sources": ["D:/ecommerce/node_modules/d3-time/src/year.js"], "sourcesContent": ["import {timeInterval} from \"./interval.js\";\n\nexport const timeYear = timeInterval((date) => {\n  date.setMonth(0, 1);\n  date.setHours(0, 0, 0, 0);\n}, (date, step) => {\n  date.setFullYear(date.getFullYear() + step);\n}, (start, end) => {\n  return end.getFullYear() - start.getFullYear();\n}, (date) => {\n  return date.getFullYear();\n});\n\n// An optimized implementation for this simple case.\ntimeYear.every = (k) => {\n  return !isFinite(k = Math.floor(k)) || !(k > 0) ? null : timeInterval((date) => {\n    date.setFullYear(Math.floor(date.getFullYear() / k) * k);\n    date.setMonth(0, 1);\n    date.setHours(0, 0, 0, 0);\n  }, (date, step) => {\n    date.setFullYear(date.getFullYear() + step * k);\n  });\n};\n\nexport const timeYears = timeYear.range;\n\nexport const utcYear = timeInterval((date) => {\n  date.setUTCMonth(0, 1);\n  date.setUTCHours(0, 0, 0, 0);\n}, (date, step) => {\n  date.setUTCFullYear(date.getUTCFullYear() + step);\n}, (start, end) => {\n  return end.getUTCFullYear() - start.getUTCFullYear();\n}, (date) => {\n  return date.getUTCFullYear();\n});\n\n// An optimized implementation for this simple case.\nutcYear.every = (k) => {\n  return !isFinite(k = Math.floor(k)) || !(k > 0) ? null : timeInterval((date) => {\n    date.setUTCFullYear(Math.floor(date.getUTCFullYear() / k) * k);\n    date.setUTCMonth(0, 1);\n    date.setUTCHours(0, 0, 0, 0);\n  }, (date, step) => {\n    date.setUTCFullYear(date.getUTCFullYear() + step * k);\n  });\n};\n\nexport const utcYears = utcYear.range;\n"], "mappings": "AAAA,SAAQA,YAAY,QAAO,eAAe;AAE1C,OAAO,MAAMC,QAAQ,GAAGD,YAAY,CAAEE,IAAI,IAAK;EAC7CA,IAAI,CAACC,QAAQ,CAAC,CAAC,EAAE,CAAC,CAAC;EACnBD,IAAI,CAACE,QAAQ,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;AAC3B,CAAC,EAAE,CAACF,IAAI,EAAEG,IAAI,KAAK;EACjBH,IAAI,CAACI,WAAW,CAACJ,IAAI,CAACK,WAAW,CAAC,CAAC,GAAGF,IAAI,CAAC;AAC7C,CAAC,EAAE,CAACG,KAAK,EAAEC,GAAG,KAAK;EACjB,OAAOA,GAAG,CAACF,WAAW,CAAC,CAAC,GAAGC,KAAK,CAACD,WAAW,CAAC,CAAC;AAChD,CAAC,EAAGL,IAAI,IAAK;EACX,OAAOA,IAAI,CAACK,WAAW,CAAC,CAAC;AAC3B,CAAC,CAAC;;AAEF;AACAN,QAAQ,CAACS,KAAK,GAAIC,CAAC,IAAK;EACtB,OAAO,CAACC,QAAQ,CAACD,CAAC,GAAGE,IAAI,CAACC,KAAK,CAACH,CAAC,CAAC,CAAC,IAAI,EAAEA,CAAC,GAAG,CAAC,CAAC,GAAG,IAAI,GAAGX,YAAY,CAAEE,IAAI,IAAK;IAC9EA,IAAI,CAACI,WAAW,CAACO,IAAI,CAACC,KAAK,CAACZ,IAAI,CAACK,WAAW,CAAC,CAAC,GAAGI,CAAC,CAAC,GAAGA,CAAC,CAAC;IACxDT,IAAI,CAACC,QAAQ,CAAC,CAAC,EAAE,CAAC,CAAC;IACnBD,IAAI,CAACE,QAAQ,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;EAC3B,CAAC,EAAE,CAACF,IAAI,EAAEG,IAAI,KAAK;IACjBH,IAAI,CAACI,WAAW,CAACJ,IAAI,CAACK,WAAW,CAAC,CAAC,GAAGF,IAAI,GAAGM,CAAC,CAAC;EACjD,CAAC,CAAC;AACJ,CAAC;AAED,OAAO,MAAMI,SAAS,GAAGd,QAAQ,CAACe,KAAK;AAEvC,OAAO,MAAMC,OAAO,GAAGjB,YAAY,CAAEE,IAAI,IAAK;EAC5CA,IAAI,CAACgB,WAAW,CAAC,CAAC,EAAE,CAAC,CAAC;EACtBhB,IAAI,CAACiB,WAAW,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;AAC9B,CAAC,EAAE,CAACjB,IAAI,EAAEG,IAAI,KAAK;EACjBH,IAAI,CAACkB,cAAc,CAAClB,IAAI,CAACmB,cAAc,CAAC,CAAC,GAAGhB,IAAI,CAAC;AACnD,CAAC,EAAE,CAACG,KAAK,EAAEC,GAAG,KAAK;EACjB,OAAOA,GAAG,CAACY,cAAc,CAAC,CAAC,GAAGb,KAAK,CAACa,cAAc,CAAC,CAAC;AACtD,CAAC,EAAGnB,IAAI,IAAK;EACX,OAAOA,IAAI,CAACmB,cAAc,CAAC,CAAC;AAC9B,CAAC,CAAC;;AAEF;AACAJ,OAAO,CAACP,KAAK,GAAIC,CAAC,IAAK;EACrB,OAAO,CAACC,QAAQ,CAACD,CAAC,GAAGE,IAAI,CAACC,KAAK,CAACH,CAAC,CAAC,CAAC,IAAI,EAAEA,CAAC,GAAG,CAAC,CAAC,GAAG,IAAI,GAAGX,YAAY,CAAEE,IAAI,IAAK;IAC9EA,IAAI,CAACkB,cAAc,CAACP,IAAI,CAACC,KAAK,CAACZ,IAAI,CAACmB,cAAc,CAAC,CAAC,GAAGV,CAAC,CAAC,GAAGA,CAAC,CAAC;IAC9DT,IAAI,CAACgB,WAAW,CAAC,CAAC,EAAE,CAAC,CAAC;IACtBhB,IAAI,CAACiB,WAAW,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;EAC9B,CAAC,EAAE,CAACjB,IAAI,EAAEG,IAAI,KAAK;IACjBH,IAAI,CAACkB,cAAc,CAAClB,IAAI,CAACmB,cAAc,CAAC,CAAC,GAAGhB,IAAI,GAAGM,CAAC,CAAC;EACvD,CAAC,CAAC;AACJ,CAAC;AAED,OAAO,MAAMW,QAAQ,GAAGL,OAAO,CAACD,KAAK", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}