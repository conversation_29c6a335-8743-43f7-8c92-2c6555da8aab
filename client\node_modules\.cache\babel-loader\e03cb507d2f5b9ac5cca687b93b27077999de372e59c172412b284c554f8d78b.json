{"ast": null, "code": "var _jsxFileName = \"D:\\\\ecommerce\\\\client\\\\src\\\\components\\\\admin\\\\AdminDashboard.js\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect } from 'react';\nimport { Box, Grid, Paper, Typography, Card, CardContent, Table, TableBody, TableCell, TableContainer, TableHead, TableRow, Button, Chip, Avatar, LinearProgress, IconButton, Menu, MenuItem, CircularProgress, Alert } from '@mui/material';\nimport { TrendingUp, TrendingDown, People, ShoppingCart, Visibility, AttachMoney, MoreVert, Edit, Delete, Refresh } from '@mui/icons-material';\nimport { analyticsAPI, handleAPIError } from '../../services/api';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst AdminDashboard = () => {\n  _s();\n  var _analytics$stats, _analytics$stats$tota, _analytics$stats2, _analytics$stats3, _analytics$stats3$pag, _analytics$stats4;\n  const [anchorEl, setAnchorEl] = useState(null);\n  const [selectedProduct, setSelectedProduct] = useState(null);\n  const [analytics, setAnalytics] = useState(null);\n  const [loading, setLoading] = useState(true);\n  const [error, setError] = useState(null);\n\n  // Fetch analytics data\n  const fetchAnalytics = async () => {\n    try {\n      setLoading(true);\n      setError(null);\n      const response = await analyticsAPI.getAdminAnalytics();\n      setAnalytics(response.data);\n    } catch (err) {\n      setError(handleAPIError(err));\n      console.error('Failed to fetch analytics:', err);\n      // Fallback to mock data\n      setAnalytics(getMockAnalytics());\n    } finally {\n      setLoading(false);\n    }\n  };\n  useEffect(() => {\n    fetchAnalytics();\n  }, []);\n\n  // Mock data fallback\n  const getMockAnalytics = () => ({\n    stats: {\n      totalVisits: 914001,\n      bounceRate: 46.41,\n      pageViews: 4054876,\n      growthRate: 46.43\n    },\n    totals: {\n      users: 1250,\n      products: 45,\n      orders: 324,\n      revenue: 72000\n    },\n    monthlyData: [{\n      month: 'Jan',\n      users: 324,\n      sales: 45000\n    }, {\n      month: 'Feb',\n      users: 456,\n      sales: 52000\n    }, {\n      month: 'Mar',\n      users: 378,\n      sales: 48000\n    }, {\n      month: 'Apr',\n      users: 520,\n      sales: 61000\n    }, {\n      month: 'May',\n      users: 489,\n      sales: 58000\n    }, {\n      month: 'Jun',\n      users: 612,\n      sales: 72000\n    }],\n    recentOrders: [{\n      id: 'ORD001',\n      customer: 'John Doe',\n      total: 24.99,\n      status: 'Completed',\n      date: '2024-01-15',\n      items: 2\n    }, {\n      id: 'ORD002',\n      customer: 'Jane Smith',\n      total: 18.50,\n      status: 'Processing',\n      date: '2024-01-14',\n      items: 1\n    }, {\n      id: 'ORD003',\n      customer: 'Mike Johnson',\n      total: 32.75,\n      status: 'Shipped',\n      date: '2024-01-13',\n      items: 3\n    }],\n    topProducts: [{\n      id: 1,\n      name: 'Cinnamon Sticks',\n      sales: 245,\n      revenue: 6125\n    }, {\n      id: 2,\n      name: 'Black Pepper',\n      sales: 189,\n      revenue: 3402\n    }, {\n      id: 3,\n      name: 'Turmeric Powder',\n      sales: 156,\n      revenue: 4992\n    }],\n    trafficSources: [{\n      name: 'Organic',\n      value: 44.46,\n      color: '#2196f3'\n    }, {\n      name: 'Referral',\n      value: 5.54,\n      color: '#4caf50'\n    }, {\n      name: 'Other',\n      value: 50,\n      color: '#ff9800'\n    }],\n    browserStats: [{\n      name: 'Google Chrome',\n      percentage: 60,\n      color: '#4285f4'\n    }, {\n      name: 'Mozilla Firefox',\n      percentage: 18,\n      color: '#ff7139'\n    }, {\n      name: 'Internet Explorer',\n      percentage: 12,\n      color: '#00bcf2'\n    }, {\n      name: 'Safari',\n      percentage: 10,\n      color: '#000000'\n    }]\n  });\n  if (loading) {\n    return /*#__PURE__*/_jsxDEV(Box, {\n      sx: {\n        display: 'flex',\n        justifyContent: 'center',\n        alignItems: 'center',\n        height: '100vh'\n      },\n      children: /*#__PURE__*/_jsxDEV(CircularProgress, {\n        size: 60\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 115,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 114,\n      columnNumber: 7\n    }, this);\n  }\n  const stats = [{\n    title: 'Total Visits',\n    value: (analytics === null || analytics === void 0 ? void 0 : (_analytics$stats = analytics.stats) === null || _analytics$stats === void 0 ? void 0 : (_analytics$stats$tota = _analytics$stats.totalVisits) === null || _analytics$stats$tota === void 0 ? void 0 : _analytics$stats$tota.toLocaleString()) || '914,001',\n    icon: People,\n    color: '#f44336',\n    trend: '+12.5%'\n  }, {\n    title: 'Bounce Rate',\n    value: `${(analytics === null || analytics === void 0 ? void 0 : (_analytics$stats2 = analytics.stats) === null || _analytics$stats2 === void 0 ? void 0 : _analytics$stats2.bounceRate) || 46.41}%`,\n    icon: TrendingDown,\n    color: '#ff9800',\n    trend: '-2.3%'\n  }, {\n    title: 'Page Views',\n    value: (analytics === null || analytics === void 0 ? void 0 : (_analytics$stats3 = analytics.stats) === null || _analytics$stats3 === void 0 ? void 0 : (_analytics$stats3$pag = _analytics$stats3.pageViews) === null || _analytics$stats3$pag === void 0 ? void 0 : _analytics$stats3$pag.toLocaleString()) || '4,054,876',\n    icon: Visibility,\n    color: '#4caf50',\n    trend: '+8.7%'\n  }, {\n    title: 'Growth Rate',\n    value: `${(analytics === null || analytics === void 0 ? void 0 : (_analytics$stats4 = analytics.stats) === null || _analytics$stats4 === void 0 ? void 0 : _analytics$stats4.growthRate) || 46.43}%`,\n    icon: TrendingUp,\n    color: '#2196f3',\n    trend: '+15.2%'\n  }];\n  const trafficData = [{\n    name: 'Organic',\n    value: 44.46,\n    color: '#2196f3'\n  }, {\n    name: 'Referral',\n    value: 5.54,\n    color: '#4caf50'\n  }, {\n    name: 'Other',\n    value: 50,\n    color: '#ff9800'\n  }];\n  const browserStats = [{\n    name: 'Google Chrome',\n    percentage: 60,\n    color: '#4285f4'\n  }, {\n    name: 'Mozilla Firefox',\n    percentage: 18,\n    color: '#ff7139'\n  }, {\n    name: 'Internet Explorer',\n    percentage: 12,\n    color: '#00bcf2'\n  }, {\n    name: 'Safari',\n    percentage: 10,\n    color: '#000000'\n  }];\n  const monthlyData = [{\n    month: 'Jan',\n    users: 324,\n    sales: 45000\n  }, {\n    month: 'Feb',\n    users: 456,\n    sales: 52000\n  }, {\n    month: 'Mar',\n    users: 378,\n    sales: 48000\n  }, {\n    month: 'Apr',\n    users: 520,\n    sales: 61000\n  }, {\n    month: 'May',\n    users: 489,\n    sales: 58000\n  }, {\n    month: 'Jun',\n    users: 612,\n    sales: 72000\n  }];\n  const recentOrders = [{\n    id: 'ORD001',\n    customer: 'John Doe',\n    product: 'Cinnamon Sticks',\n    amount: 24.99,\n    status: 'Completed',\n    date: '2024-01-15'\n  }, {\n    id: 'ORD002',\n    customer: 'Jane Smith',\n    product: 'Black Pepper',\n    amount: 18.50,\n    status: 'Processing',\n    date: '2024-01-14'\n  }, {\n    id: 'ORD003',\n    customer: 'Mike Johnson',\n    product: 'Turmeric Powder',\n    amount: 32.75,\n    status: 'Shipped',\n    date: '2024-01-13'\n  }, {\n    id: 'ORD004',\n    customer: 'Sarah Wilson',\n    product: 'Cardamom Pods',\n    amount: 45.20,\n    status: 'Pending',\n    date: '2024-01-12'\n  }];\n  const topProducts = [{\n    id: 1,\n    name: 'Cinnamon Sticks',\n    sales: 245,\n    revenue: 6125,\n    image: '🍯'\n  }, {\n    id: 2,\n    name: 'Black Pepper',\n    sales: 189,\n    revenue: 3402,\n    image: '🌶️'\n  }, {\n    id: 3,\n    name: 'Turmeric Powder',\n    sales: 156,\n    revenue: 4992,\n    image: '🟡'\n  }, {\n    id: 4,\n    name: 'Cardamom Pods',\n    sales: 134,\n    revenue: 6058,\n    image: '🟢'\n  }];\n  const handleMenuClick = (event, product) => {\n    setAnchorEl(event.currentTarget);\n    setSelectedProduct(product);\n  };\n  const handleMenuClose = () => {\n    setAnchorEl(null);\n    setSelectedProduct(null);\n  };\n  const getStatusColor = status => {\n    switch (status) {\n      case 'Completed':\n        return 'success';\n      case 'Processing':\n        return 'warning';\n      case 'Shipped':\n        return 'info';\n      case 'Pending':\n        return 'default';\n      default:\n        return 'default';\n    }\n  };\n  return /*#__PURE__*/_jsxDEV(Box, {\n    sx: {\n      p: 3,\n      backgroundColor: '#f5f5f5',\n      minHeight: '100vh'\n    },\n    children: [/*#__PURE__*/_jsxDEV(Typography, {\n      variant: \"h4\",\n      sx: {\n        mb: 3,\n        fontWeight: 'bold'\n      },\n      children: \"Admin Dashboard\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 209,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Grid, {\n      container: true,\n      spacing: 3,\n      sx: {\n        mb: 4\n      },\n      children: stats.map((stat, index) => /*#__PURE__*/_jsxDEV(Grid, {\n        item: true,\n        xs: 12,\n        sm: 6,\n        md: 3,\n        children: /*#__PURE__*/_jsxDEV(Card, {\n          sx: {\n            background: stat.color,\n            color: 'white',\n            height: '120px'\n          },\n          children: /*#__PURE__*/_jsxDEV(CardContent, {\n            children: [/*#__PURE__*/_jsxDEV(Box, {\n              sx: {\n                display: 'flex',\n                justifyContent: 'space-between',\n                alignItems: 'center'\n              },\n              children: [/*#__PURE__*/_jsxDEV(Box, {\n                children: [/*#__PURE__*/_jsxDEV(Typography, {\n                  variant: \"h4\",\n                  sx: {\n                    fontWeight: 'bold',\n                    mb: 1\n                  },\n                  children: stat.value\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 221,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                  variant: \"body2\",\n                  sx: {\n                    opacity: 0.9\n                  },\n                  children: stat.title\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 224,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 220,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(stat.icon, {\n                sx: {\n                  fontSize: 40,\n                  opacity: 0.8\n                }\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 228,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 219,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(Typography, {\n              variant: \"caption\",\n              sx: {\n                mt: 1,\n                display: 'block'\n              },\n              children: stat.trend\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 230,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 218,\n            columnNumber: 15\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 217,\n          columnNumber: 13\n        }, this)\n      }, index, false, {\n        fileName: _jsxFileName,\n        lineNumber: 216,\n        columnNumber: 11\n      }, this))\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 214,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Grid, {\n      container: true,\n      spacing: 3,\n      children: [/*#__PURE__*/_jsxDEV(Grid, {\n        item: true,\n        xs: 12,\n        md: 8,\n        children: /*#__PURE__*/_jsxDEV(Paper, {\n          sx: {\n            p: 3,\n            height: '400px'\n          },\n          children: [/*#__PURE__*/_jsxDEV(Typography, {\n            variant: \"h6\",\n            sx: {\n              mb: 2\n            },\n            children: \"Monthly Sales & Users\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 243,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Box, {\n            sx: {\n              height: '90%',\n              display: 'flex',\n              flexDirection: 'column',\n              justifyContent: 'center'\n            },\n            children: [/*#__PURE__*/_jsxDEV(Grid, {\n              container: true,\n              spacing: 2,\n              children: monthlyData.map((data, index) => /*#__PURE__*/_jsxDEV(Grid, {\n                item: true,\n                xs: 2,\n                children: /*#__PURE__*/_jsxDEV(Box, {\n                  sx: {\n                    textAlign: 'center'\n                  },\n                  children: [/*#__PURE__*/_jsxDEV(Typography, {\n                    variant: \"caption\",\n                    color: \"textSecondary\",\n                    children: data.month\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 249,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(Box, {\n                    sx: {\n                      mt: 1,\n                      mb: 1\n                    },\n                    children: [/*#__PURE__*/_jsxDEV(Box, {\n                      sx: {\n                        height: `${data.users / 600 * 100}px`,\n                        backgroundColor: '#2196f3',\n                        borderRadius: 1,\n                        mb: 0.5,\n                        minHeight: '20px'\n                      }\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 253,\n                      columnNumber: 25\n                    }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                      variant: \"caption\",\n                      sx: {\n                        fontSize: '0.7rem'\n                      },\n                      children: [data.users, \" users\"]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 262,\n                      columnNumber: 25\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 252,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(Box, {\n                    children: [/*#__PURE__*/_jsxDEV(Box, {\n                      sx: {\n                        height: `${data.sales / 80000 * 100}px`,\n                        backgroundColor: '#4caf50',\n                        borderRadius: 1,\n                        mb: 0.5,\n                        minHeight: '20px'\n                      }\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 267,\n                      columnNumber: 25\n                    }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                      variant: \"caption\",\n                      sx: {\n                        fontSize: '0.7rem'\n                      },\n                      children: [\"$\", data.sales]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 276,\n                      columnNumber: 25\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 266,\n                    columnNumber: 23\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 248,\n                  columnNumber: 21\n                }, this)\n              }, data.month, false, {\n                fileName: _jsxFileName,\n                lineNumber: 247,\n                columnNumber: 19\n              }, this))\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 245,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Box, {\n              sx: {\n                display: 'flex',\n                justifyContent: 'center',\n                mt: 2,\n                gap: 2\n              },\n              children: [/*#__PURE__*/_jsxDEV(Box, {\n                sx: {\n                  display: 'flex',\n                  alignItems: 'center'\n                },\n                children: [/*#__PURE__*/_jsxDEV(Box, {\n                  sx: {\n                    width: 12,\n                    height: 12,\n                    backgroundColor: '#2196f3',\n                    mr: 1\n                  }\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 286,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                  variant: \"caption\",\n                  children: \"Users\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 287,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 285,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(Box, {\n                sx: {\n                  display: 'flex',\n                  alignItems: 'center'\n                },\n                children: [/*#__PURE__*/_jsxDEV(Box, {\n                  sx: {\n                    width: 12,\n                    height: 12,\n                    backgroundColor: '#4caf50',\n                    mr: 1\n                  }\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 290,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                  variant: \"caption\",\n                  children: \"Sales ($)\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 291,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 289,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 284,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 244,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 242,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 241,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Grid, {\n        item: true,\n        xs: 12,\n        md: 4,\n        children: /*#__PURE__*/_jsxDEV(Paper, {\n          sx: {\n            p: 3,\n            height: '400px'\n          },\n          children: [/*#__PURE__*/_jsxDEV(Typography, {\n            variant: \"h6\",\n            sx: {\n              mb: 2\n            },\n            children: \"Customer Satisfaction\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 301,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Box, {\n            sx: {\n              textAlign: 'center',\n              mb: 3\n            },\n            children: [/*#__PURE__*/_jsxDEV(Typography, {\n              variant: \"h2\",\n              sx: {\n                color: '#4caf50',\n                fontWeight: 'bold'\n              },\n              children: \"93.13%\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 303,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Typography, {\n              variant: \"body2\",\n              color: \"textSecondary\",\n              children: \"Previous: 79.82 | Change: +14.29\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 306,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 302,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Typography, {\n            variant: \"subtitle1\",\n            sx: {\n              mb: 2\n            },\n            children: \"Browser Stats\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 311,\n            columnNumber: 13\n          }, this), browserStats.map((browser, index) => /*#__PURE__*/_jsxDEV(Box, {\n            sx: {\n              mb: 2\n            },\n            children: [/*#__PURE__*/_jsxDEV(Box, {\n              sx: {\n                display: 'flex',\n                justifyContent: 'space-between',\n                mb: 1\n              },\n              children: [/*#__PURE__*/_jsxDEV(Typography, {\n                variant: \"body2\",\n                children: browser.name\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 315,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                variant: \"body2\",\n                children: [browser.percentage, \"%\"]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 316,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 314,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(LinearProgress, {\n              variant: \"determinate\",\n              value: browser.percentage,\n              sx: {\n                height: 8,\n                borderRadius: 4,\n                backgroundColor: '#e0e0e0',\n                '& .MuiLinearProgress-bar': {\n                  backgroundColor: browser.color\n                }\n              }\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 318,\n              columnNumber: 17\n            }, this)]\n          }, index, true, {\n            fileName: _jsxFileName,\n            lineNumber: 313,\n            columnNumber: 15\n          }, this))]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 300,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 299,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Grid, {\n        item: true,\n        xs: 12,\n        md: 6,\n        children: /*#__PURE__*/_jsxDEV(Paper, {\n          sx: {\n            p: 3,\n            height: '350px'\n          },\n          children: [/*#__PURE__*/_jsxDEV(Typography, {\n            variant: \"h6\",\n            sx: {\n              mb: 2\n            },\n            children: \"Visit By Traffic Types\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 338,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Box, {\n            sx: {\n              display: 'flex',\n              flexDirection: 'column',\n              height: '80%',\n              justifyContent: 'center'\n            },\n            children: [trafficData.map((item, index) => /*#__PURE__*/_jsxDEV(Box, {\n              sx: {\n                mb: 3\n              },\n              children: [/*#__PURE__*/_jsxDEV(Box, {\n                sx: {\n                  display: 'flex',\n                  justifyContent: 'space-between',\n                  mb: 1\n                },\n                children: [/*#__PURE__*/_jsxDEV(Typography, {\n                  variant: \"body2\",\n                  children: item.name\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 343,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                  variant: \"body2\",\n                  sx: {\n                    fontWeight: 'bold'\n                  },\n                  children: [item.value, \"%\"]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 344,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 342,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(LinearProgress, {\n                variant: \"determinate\",\n                value: item.value,\n                sx: {\n                  height: 12,\n                  borderRadius: 6,\n                  backgroundColor: '#e0e0e0',\n                  '& .MuiLinearProgress-bar': {\n                    backgroundColor: item.color,\n                    borderRadius: 6\n                  }\n                }\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 348,\n                columnNumber: 19\n              }, this)]\n            }, index, true, {\n              fileName: _jsxFileName,\n              lineNumber: 341,\n              columnNumber: 17\n            }, this)), /*#__PURE__*/_jsxDEV(Box, {\n              sx: {\n                mt: 2,\n                textAlign: 'center'\n              },\n              children: /*#__PURE__*/_jsxDEV(Typography, {\n                variant: \"caption\",\n                color: \"textSecondary\",\n                children: \"Total traffic distribution across different sources\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 364,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 363,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 339,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 337,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 336,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Grid, {\n        item: true,\n        xs: 12,\n        md: 6,\n        children: /*#__PURE__*/_jsxDEV(Paper, {\n          sx: {\n            p: 3,\n            height: '350px'\n          },\n          children: [/*#__PURE__*/_jsxDEV(Typography, {\n            variant: \"h6\",\n            sx: {\n              mb: 2\n            },\n            children: \"Top Selling Products\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 375,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(TableContainer, {\n            children: /*#__PURE__*/_jsxDEV(Table, {\n              size: \"small\",\n              children: [/*#__PURE__*/_jsxDEV(TableHead, {\n                children: /*#__PURE__*/_jsxDEV(TableRow, {\n                  children: [/*#__PURE__*/_jsxDEV(TableCell, {\n                    children: \"Product\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 380,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n                    align: \"right\",\n                    children: \"Sales\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 381,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n                    align: \"right\",\n                    children: \"Revenue\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 382,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n                    align: \"right\",\n                    children: \"Actions\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 383,\n                    columnNumber: 21\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 379,\n                  columnNumber: 19\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 378,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(TableBody, {\n                children: topProducts.map(product => /*#__PURE__*/_jsxDEV(TableRow, {\n                  children: [/*#__PURE__*/_jsxDEV(TableCell, {\n                    children: /*#__PURE__*/_jsxDEV(Box, {\n                      sx: {\n                        display: 'flex',\n                        alignItems: 'center'\n                      },\n                      children: [/*#__PURE__*/_jsxDEV(Typography, {\n                        sx: {\n                          mr: 1,\n                          fontSize: '1.2em'\n                        },\n                        children: product.image\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 391,\n                        columnNumber: 27\n                      }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                        variant: \"body2\",\n                        children: product.name\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 392,\n                        columnNumber: 27\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 390,\n                      columnNumber: 25\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 389,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n                    align: \"right\",\n                    children: product.sales\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 395,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n                    align: \"right\",\n                    children: [\"$\", product.revenue]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 396,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n                    align: \"right\",\n                    children: /*#__PURE__*/_jsxDEV(IconButton, {\n                      size: \"small\",\n                      onClick: e => handleMenuClick(e, product),\n                      children: /*#__PURE__*/_jsxDEV(MoreVert, {}, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 399,\n                        columnNumber: 27\n                      }, this)\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 398,\n                      columnNumber: 25\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 397,\n                    columnNumber: 23\n                  }, this)]\n                }, product.id, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 388,\n                  columnNumber: 21\n                }, this))\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 386,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 377,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 376,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 374,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 373,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 239,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Paper, {\n      sx: {\n        p: 3,\n        mt: 3\n      },\n      children: [/*#__PURE__*/_jsxDEV(Typography, {\n        variant: \"h6\",\n        sx: {\n          mb: 2\n        },\n        children: \"Recent Orders\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 413,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(TableContainer, {\n        children: /*#__PURE__*/_jsxDEV(Table, {\n          children: [/*#__PURE__*/_jsxDEV(TableHead, {\n            children: /*#__PURE__*/_jsxDEV(TableRow, {\n              children: [/*#__PURE__*/_jsxDEV(TableCell, {\n                children: \"Order ID\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 418,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n                children: \"Customer\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 419,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n                children: \"Product\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 420,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n                align: \"right\",\n                children: \"Amount\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 421,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n                children: \"Status\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 422,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n                children: \"Date\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 423,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n                align: \"right\",\n                children: \"Actions\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 424,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 417,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 416,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(TableBody, {\n            children: recentOrders.map(order => /*#__PURE__*/_jsxDEV(TableRow, {\n              children: [/*#__PURE__*/_jsxDEV(TableCell, {\n                children: order.id\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 430,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n                children: order.customer\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 431,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n                children: order.product\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 432,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n                align: \"right\",\n                children: [\"$\", order.amount]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 433,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n                children: /*#__PURE__*/_jsxDEV(Chip, {\n                  label: order.status,\n                  color: getStatusColor(order.status),\n                  size: \"small\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 435,\n                  columnNumber: 21\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 434,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n                children: order.date\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 441,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n                align: \"right\",\n                children: /*#__PURE__*/_jsxDEV(Button, {\n                  size: \"small\",\n                  variant: \"outlined\",\n                  children: \"View\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 443,\n                  columnNumber: 21\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 442,\n                columnNumber: 19\n              }, this)]\n            }, order.id, true, {\n              fileName: _jsxFileName,\n              lineNumber: 429,\n              columnNumber: 17\n            }, this))\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 427,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 415,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 414,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 412,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Menu, {\n      anchorEl: anchorEl,\n      open: Boolean(anchorEl),\n      onClose: handleMenuClose,\n      children: [/*#__PURE__*/_jsxDEV(MenuItem, {\n        onClick: handleMenuClose,\n        children: [/*#__PURE__*/_jsxDEV(Edit, {\n          sx: {\n            mr: 1\n          }\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 461,\n          columnNumber: 11\n        }, this), \" Edit\"]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 460,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(MenuItem, {\n        onClick: handleMenuClose,\n        children: [/*#__PURE__*/_jsxDEV(Delete, {\n          sx: {\n            mr: 1\n          }\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 464,\n          columnNumber: 11\n        }, this), \" Delete\"]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 463,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 455,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 208,\n    columnNumber: 5\n  }, this);\n};\n_s(AdminDashboard, \"d4Qz07Eg2M/Y61mDRYW1VuwcSn8=\");\n_c = AdminDashboard;\nexport default AdminDashboard;\nvar _c;\n$RefreshReg$(_c, \"AdminDashboard\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "Box", "Grid", "Paper", "Typography", "Card", "<PERSON><PERSON><PERSON><PERSON>", "Table", "TableBody", "TableCell", "TableContainer", "TableHead", "TableRow", "<PERSON><PERSON>", "Chip", "Avatar", "LinearProgress", "IconButton", "<PERSON><PERSON>", "MenuItem", "CircularProgress", "<PERSON><PERSON>", "TrendingUp", "TrendingDown", "People", "ShoppingCart", "Visibility", "AttachMoney", "<PERSON><PERSON><PERSON>", "Edit", "Delete", "Refresh", "analyticsAPI", "handleAPIError", "jsxDEV", "_jsxDEV", "AdminDashboard", "_s", "_analytics$stats", "_analytics$stats$tota", "_analytics$stats2", "_analytics$stats3", "_analytics$stats3$pag", "_analytics$stats4", "anchorEl", "setAnchorEl", "selectedProduct", "setSelectedProduct", "analytics", "setAnalytics", "loading", "setLoading", "error", "setError", "fetchAnalytics", "response", "getAdminAnalytics", "data", "err", "console", "getMockAnalytics", "stats", "totalVisits", "bounceRate", "pageViews", "growthRate", "totals", "users", "products", "orders", "revenue", "monthlyData", "month", "sales", "recentOrders", "id", "customer", "total", "status", "date", "items", "topProducts", "name", "trafficSources", "value", "color", "browserStats", "percentage", "sx", "display", "justifyContent", "alignItems", "height", "children", "size", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "title", "toLocaleString", "icon", "trend", "trafficData", "product", "amount", "image", "handleMenuClick", "event", "currentTarget", "handleMenuClose", "getStatusColor", "p", "backgroundColor", "minHeight", "variant", "mb", "fontWeight", "container", "spacing", "map", "stat", "index", "item", "xs", "sm", "md", "background", "opacity", "fontSize", "mt", "flexDirection", "textAlign", "borderRadius", "gap", "width", "mr", "browser", "align", "onClick", "e", "order", "label", "open", "Boolean", "onClose", "_c", "$RefreshReg$"], "sources": ["D:/ecommerce/client/src/components/admin/AdminDashboard.js"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\nimport {\n  Box,\n  Grid,\n  Paper,\n  Typography,\n  Card,\n  CardContent,\n  Table,\n  TableBody,\n  TableCell,\n  TableContainer,\n  TableHead,\n  TableRow,\n  Button,\n  Chip,\n  Avatar,\n  LinearProgress,\n  IconButton,\n  Menu,\n  MenuItem,\n  CircularProgress,\n  Alert,\n} from '@mui/material';\nimport {\n  TrendingUp,\n  TrendingDown,\n  People,\n  ShoppingCart,\n  Visibility,\n  AttachMoney,\n  MoreVert,\n  Edit,\n  Delete,\n  Refresh,\n} from '@mui/icons-material';\nimport { analyticsAPI, handleAPIError } from '../../services/api';\n\nconst AdminDashboard = () => {\n  const [anchorEl, setAnchorEl] = useState(null);\n  const [selectedProduct, setSelectedProduct] = useState(null);\n  const [analytics, setAnalytics] = useState(null);\n  const [loading, setLoading] = useState(true);\n  const [error, setError] = useState(null);\n\n  // Fetch analytics data\n  const fetchAnalytics = async () => {\n    try {\n      setLoading(true);\n      setError(null);\n      const response = await analyticsAPI.getAdminAnalytics();\n      setAnalytics(response.data);\n    } catch (err) {\n      setError(handleAPIError(err));\n      console.error('Failed to fetch analytics:', err);\n      // Fallback to mock data\n      setAnalytics(getMockAnalytics());\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  useEffect(() => {\n    fetchAnalytics();\n  }, []);\n\n  // Mock data fallback\n  const getMockAnalytics = () => ({\n    stats: {\n      totalVisits: 914001,\n      bounceRate: 46.41,\n      pageViews: 4054876,\n      growthRate: 46.43\n    },\n    totals: {\n      users: 1250,\n      products: 45,\n      orders: 324,\n      revenue: 72000\n    },\n    monthlyData: [\n      { month: 'Jan', users: 324, sales: 45000 },\n      { month: 'Feb', users: 456, sales: 52000 },\n      { month: 'Mar', users: 378, sales: 48000 },\n      { month: 'Apr', users: 520, sales: 61000 },\n      { month: 'May', users: 489, sales: 58000 },\n      { month: 'Jun', users: 612, sales: 72000 },\n    ],\n    recentOrders: [\n      { id: 'ORD001', customer: 'John Doe', total: 24.99, status: 'Completed', date: '2024-01-15', items: 2 },\n      { id: 'ORD002', customer: 'Jane Smith', total: 18.50, status: 'Processing', date: '2024-01-14', items: 1 },\n      { id: 'ORD003', customer: 'Mike Johnson', total: 32.75, status: 'Shipped', date: '2024-01-13', items: 3 },\n    ],\n    topProducts: [\n      { id: 1, name: 'Cinnamon Sticks', sales: 245, revenue: 6125 },\n      { id: 2, name: 'Black Pepper', sales: 189, revenue: 3402 },\n      { id: 3, name: 'Turmeric Powder', sales: 156, revenue: 4992 },\n    ],\n    trafficSources: [\n      { name: 'Organic', value: 44.46, color: '#2196f3' },\n      { name: 'Referral', value: 5.54, color: '#4caf50' },\n      { name: 'Other', value: 50, color: '#ff9800' }\n    ],\n    browserStats: [\n      { name: 'Google Chrome', percentage: 60, color: '#4285f4' },\n      { name: 'Mozilla Firefox', percentage: 18, color: '#ff7139' },\n      { name: 'Internet Explorer', percentage: 12, color: '#00bcf2' },\n      { name: 'Safari', percentage: 10, color: '#000000' }\n    ]\n  });\n\n  if (loading) {\n    return (\n      <Box sx={{ display: 'flex', justifyContent: 'center', alignItems: 'center', height: '100vh' }}>\n        <CircularProgress size={60} />\n      </Box>\n    );\n  }\n\n  const stats = [\n    {\n      title: 'Total Visits',\n      value: analytics?.stats?.totalVisits?.toLocaleString() || '914,001',\n      icon: People,\n      color: '#f44336',\n      trend: '+12.5%'\n    },\n    {\n      title: 'Bounce Rate',\n      value: `${analytics?.stats?.bounceRate || 46.41}%`,\n      icon: TrendingDown,\n      color: '#ff9800',\n      trend: '-2.3%'\n    },\n    {\n      title: 'Page Views',\n      value: analytics?.stats?.pageViews?.toLocaleString() || '4,054,876',\n      icon: Visibility,\n      color: '#4caf50',\n      trend: '+8.7%'\n    },\n    {\n      title: 'Growth Rate',\n      value: `${analytics?.stats?.growthRate || 46.43}%`,\n      icon: TrendingUp,\n      color: '#2196f3',\n      trend: '+15.2%'\n    },\n  ];\n\n  const trafficData = [\n    { name: 'Organic', value: 44.46, color: '#2196f3' },\n    { name: 'Referral', value: 5.54, color: '#4caf50' },\n    { name: 'Other', value: 50, color: '#ff9800' },\n  ];\n\n  const browserStats = [\n    { name: 'Google Chrome', percentage: 60, color: '#4285f4' },\n    { name: 'Mozilla Firefox', percentage: 18, color: '#ff7139' },\n    { name: 'Internet Explorer', percentage: 12, color: '#00bcf2' },\n    { name: 'Safari', percentage: 10, color: '#000000' },\n  ];\n\n  const monthlyData = [\n    { month: 'Jan', users: 324, sales: 45000 },\n    { month: 'Feb', users: 456, sales: 52000 },\n    { month: 'Mar', users: 378, sales: 48000 },\n    { month: 'Apr', users: 520, sales: 61000 },\n    { month: 'May', users: 489, sales: 58000 },\n    { month: 'Jun', users: 612, sales: 72000 },\n  ];\n\n  const recentOrders = [\n    { id: 'ORD001', customer: 'John Doe', product: 'Cinnamon Sticks', amount: 24.99, status: 'Completed', date: '2024-01-15' },\n    { id: 'ORD002', customer: 'Jane Smith', product: 'Black Pepper', amount: 18.50, status: 'Processing', date: '2024-01-14' },\n    { id: 'ORD003', customer: 'Mike Johnson', product: 'Turmeric Powder', amount: 32.75, status: 'Shipped', date: '2024-01-13' },\n    { id: 'ORD004', customer: 'Sarah Wilson', product: 'Cardamom Pods', amount: 45.20, status: 'Pending', date: '2024-01-12' },\n  ];\n\n  const topProducts = [\n    { id: 1, name: 'Cinnamon Sticks', sales: 245, revenue: 6125, image: '🍯' },\n    { id: 2, name: 'Black Pepper', sales: 189, revenue: 3402, image: '🌶️' },\n    { id: 3, name: 'Turmeric Powder', sales: 156, revenue: 4992, image: '🟡' },\n    { id: 4, name: 'Cardamom Pods', sales: 134, revenue: 6058, image: '🟢' },\n  ];\n\n  const handleMenuClick = (event, product) => {\n    setAnchorEl(event.currentTarget);\n    setSelectedProduct(product);\n  };\n\n  const handleMenuClose = () => {\n    setAnchorEl(null);\n    setSelectedProduct(null);\n  };\n\n  const getStatusColor = (status) => {\n    switch (status) {\n      case 'Completed': return 'success';\n      case 'Processing': return 'warning';\n      case 'Shipped': return 'info';\n      case 'Pending': return 'default';\n      default: return 'default';\n    }\n  };\n\n  return (\n    <Box sx={{ p: 3, backgroundColor: '#f5f5f5', minHeight: '100vh' }}>\n      <Typography variant=\"h4\" sx={{ mb: 3, fontWeight: 'bold' }}>\n        Admin Dashboard\n      </Typography>\n\n      {/* Stats Cards */}\n      <Grid container spacing={3} sx={{ mb: 4 }}>\n        {stats.map((stat, index) => (\n          <Grid item xs={12} sm={6} md={3} key={index}>\n            <Card sx={{ background: stat.color, color: 'white', height: '120px' }}>\n              <CardContent>\n                <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>\n                  <Box>\n                    <Typography variant=\"h4\" sx={{ fontWeight: 'bold', mb: 1 }}>\n                      {stat.value}\n                    </Typography>\n                    <Typography variant=\"body2\" sx={{ opacity: 0.9 }}>\n                      {stat.title}\n                    </Typography>\n                  </Box>\n                  <stat.icon sx={{ fontSize: 40, opacity: 0.8 }} />\n                </Box>\n                <Typography variant=\"caption\" sx={{ mt: 1, display: 'block' }}>\n                  {stat.trend}\n                </Typography>\n              </CardContent>\n            </Card>\n          </Grid>\n        ))}\n      </Grid>\n\n      <Grid container spacing={3}>\n        {/* User Statistics Chart */}\n        <Grid item xs={12} md={8}>\n          <Paper sx={{ p: 3, height: '400px' }}>\n            <Typography variant=\"h6\" sx={{ mb: 2 }}>Monthly Sales & Users</Typography>\n            <Box sx={{ height: '90%', display: 'flex', flexDirection: 'column', justifyContent: 'center' }}>\n              <Grid container spacing={2}>\n                {monthlyData.map((data, index) => (\n                  <Grid item xs={2} key={data.month}>\n                    <Box sx={{ textAlign: 'center' }}>\n                      <Typography variant=\"caption\" color=\"textSecondary\">\n                        {data.month}\n                      </Typography>\n                      <Box sx={{ mt: 1, mb: 1 }}>\n                        <Box\n                          sx={{\n                            height: `${(data.users / 600) * 100}px`,\n                            backgroundColor: '#2196f3',\n                            borderRadius: 1,\n                            mb: 0.5,\n                            minHeight: '20px',\n                          }}\n                        />\n                        <Typography variant=\"caption\" sx={{ fontSize: '0.7rem' }}>\n                          {data.users} users\n                        </Typography>\n                      </Box>\n                      <Box>\n                        <Box\n                          sx={{\n                            height: `${(data.sales / 80000) * 100}px`,\n                            backgroundColor: '#4caf50',\n                            borderRadius: 1,\n                            mb: 0.5,\n                            minHeight: '20px',\n                          }}\n                        />\n                        <Typography variant=\"caption\" sx={{ fontSize: '0.7rem' }}>\n                          ${data.sales}\n                        </Typography>\n                      </Box>\n                    </Box>\n                  </Grid>\n                ))}\n              </Grid>\n              <Box sx={{ display: 'flex', justifyContent: 'center', mt: 2, gap: 2 }}>\n                <Box sx={{ display: 'flex', alignItems: 'center' }}>\n                  <Box sx={{ width: 12, height: 12, backgroundColor: '#2196f3', mr: 1 }} />\n                  <Typography variant=\"caption\">Users</Typography>\n                </Box>\n                <Box sx={{ display: 'flex', alignItems: 'center' }}>\n                  <Box sx={{ width: 12, height: 12, backgroundColor: '#4caf50', mr: 1 }} />\n                  <Typography variant=\"caption\">Sales ($)</Typography>\n                </Box>\n              </Box>\n            </Box>\n          </Paper>\n        </Grid>\n\n        {/* Customer Satisfaction */}\n        <Grid item xs={12} md={4}>\n          <Paper sx={{ p: 3, height: '400px' }}>\n            <Typography variant=\"h6\" sx={{ mb: 2 }}>Customer Satisfaction</Typography>\n            <Box sx={{ textAlign: 'center', mb: 3 }}>\n              <Typography variant=\"h2\" sx={{ color: '#4caf50', fontWeight: 'bold' }}>\n                93.13%\n              </Typography>\n              <Typography variant=\"body2\" color=\"textSecondary\">\n                Previous: 79.82 | Change: +14.29\n              </Typography>\n            </Box>\n\n            <Typography variant=\"subtitle1\" sx={{ mb: 2 }}>Browser Stats</Typography>\n            {browserStats.map((browser, index) => (\n              <Box key={index} sx={{ mb: 2 }}>\n                <Box sx={{ display: 'flex', justifyContent: 'space-between', mb: 1 }}>\n                  <Typography variant=\"body2\">{browser.name}</Typography>\n                  <Typography variant=\"body2\">{browser.percentage}%</Typography>\n                </Box>\n                <LinearProgress\n                  variant=\"determinate\"\n                  value={browser.percentage}\n                  sx={{\n                    height: 8,\n                    borderRadius: 4,\n                    backgroundColor: '#e0e0e0',\n                    '& .MuiLinearProgress-bar': {\n                      backgroundColor: browser.color\n                    }\n                  }}\n                />\n              </Box>\n            ))}\n          </Paper>\n        </Grid>\n\n        {/* Traffic Sources */}\n        <Grid item xs={12} md={6}>\n          <Paper sx={{ p: 3, height: '350px' }}>\n            <Typography variant=\"h6\" sx={{ mb: 2 }}>Visit By Traffic Types</Typography>\n            <Box sx={{ display: 'flex', flexDirection: 'column', height: '80%', justifyContent: 'center' }}>\n              {trafficData.map((item, index) => (\n                <Box key={index} sx={{ mb: 3 }}>\n                  <Box sx={{ display: 'flex', justifyContent: 'space-between', mb: 1 }}>\n                    <Typography variant=\"body2\">{item.name}</Typography>\n                    <Typography variant=\"body2\" sx={{ fontWeight: 'bold' }}>\n                      {item.value}%\n                    </Typography>\n                  </Box>\n                  <LinearProgress\n                    variant=\"determinate\"\n                    value={item.value}\n                    sx={{\n                      height: 12,\n                      borderRadius: 6,\n                      backgroundColor: '#e0e0e0',\n                      '& .MuiLinearProgress-bar': {\n                        backgroundColor: item.color,\n                        borderRadius: 6,\n                      },\n                    }}\n                  />\n                </Box>\n              ))}\n              <Box sx={{ mt: 2, textAlign: 'center' }}>\n                <Typography variant=\"caption\" color=\"textSecondary\">\n                  Total traffic distribution across different sources\n                </Typography>\n              </Box>\n            </Box>\n          </Paper>\n        </Grid>\n\n        {/* Top Products */}\n        <Grid item xs={12} md={6}>\n          <Paper sx={{ p: 3, height: '350px' }}>\n            <Typography variant=\"h6\" sx={{ mb: 2 }}>Top Selling Products</Typography>\n            <TableContainer>\n              <Table size=\"small\">\n                <TableHead>\n                  <TableRow>\n                    <TableCell>Product</TableCell>\n                    <TableCell align=\"right\">Sales</TableCell>\n                    <TableCell align=\"right\">Revenue</TableCell>\n                    <TableCell align=\"right\">Actions</TableCell>\n                  </TableRow>\n                </TableHead>\n                <TableBody>\n                  {topProducts.map((product) => (\n                    <TableRow key={product.id}>\n                      <TableCell>\n                        <Box sx={{ display: 'flex', alignItems: 'center' }}>\n                          <Typography sx={{ mr: 1, fontSize: '1.2em' }}>{product.image}</Typography>\n                          <Typography variant=\"body2\">{product.name}</Typography>\n                        </Box>\n                      </TableCell>\n                      <TableCell align=\"right\">{product.sales}</TableCell>\n                      <TableCell align=\"right\">${product.revenue}</TableCell>\n                      <TableCell align=\"right\">\n                        <IconButton size=\"small\" onClick={(e) => handleMenuClick(e, product)}>\n                          <MoreVert />\n                        </IconButton>\n                      </TableCell>\n                    </TableRow>\n                  ))}\n                </TableBody>\n              </Table>\n            </TableContainer>\n          </Paper>\n        </Grid>\n      </Grid>\n\n      {/* Recent Orders */}\n      <Paper sx={{ p: 3, mt: 3 }}>\n        <Typography variant=\"h6\" sx={{ mb: 2 }}>Recent Orders</Typography>\n        <TableContainer>\n          <Table>\n            <TableHead>\n              <TableRow>\n                <TableCell>Order ID</TableCell>\n                <TableCell>Customer</TableCell>\n                <TableCell>Product</TableCell>\n                <TableCell align=\"right\">Amount</TableCell>\n                <TableCell>Status</TableCell>\n                <TableCell>Date</TableCell>\n                <TableCell align=\"right\">Actions</TableCell>\n              </TableRow>\n            </TableHead>\n            <TableBody>\n              {recentOrders.map((order) => (\n                <TableRow key={order.id}>\n                  <TableCell>{order.id}</TableCell>\n                  <TableCell>{order.customer}</TableCell>\n                  <TableCell>{order.product}</TableCell>\n                  <TableCell align=\"right\">${order.amount}</TableCell>\n                  <TableCell>\n                    <Chip\n                      label={order.status}\n                      color={getStatusColor(order.status)}\n                      size=\"small\"\n                    />\n                  </TableCell>\n                  <TableCell>{order.date}</TableCell>\n                  <TableCell align=\"right\">\n                    <Button size=\"small\" variant=\"outlined\">\n                      View\n                    </Button>\n                  </TableCell>\n                </TableRow>\n              ))}\n            </TableBody>\n          </Table>\n        </TableContainer>\n      </Paper>\n\n      {/* Menu for product actions */}\n      <Menu\n        anchorEl={anchorEl}\n        open={Boolean(anchorEl)}\n        onClose={handleMenuClose}\n      >\n        <MenuItem onClick={handleMenuClose}>\n          <Edit sx={{ mr: 1 }} /> Edit\n        </MenuItem>\n        <MenuItem onClick={handleMenuClose}>\n          <Delete sx={{ mr: 1 }} /> Delete\n        </MenuItem>\n      </Menu>\n    </Box>\n  );\n};\n\nexport default AdminDashboard;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAClD,SACEC,GAAG,EACHC,IAAI,EACJC,KAAK,EACLC,UAAU,EACVC,IAAI,EACJC,WAAW,EACXC,KAAK,EACLC,SAAS,EACTC,SAAS,EACTC,cAAc,EACdC,SAAS,EACTC,QAAQ,EACRC,MAAM,EACNC,IAAI,EACJC,MAAM,EACNC,cAAc,EACdC,UAAU,EACVC,IAAI,EACJC,QAAQ,EACRC,gBAAgB,EAChBC,KAAK,QACA,eAAe;AACtB,SACEC,UAAU,EACVC,YAAY,EACZC,MAAM,EACNC,YAAY,EACZC,UAAU,EACVC,WAAW,EACXC,QAAQ,EACRC,IAAI,EACJC,MAAM,EACNC,OAAO,QACF,qBAAqB;AAC5B,SAASC,YAAY,EAAEC,cAAc,QAAQ,oBAAoB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAElE,MAAMC,cAAc,GAAGA,CAAA,KAAM;EAAAC,EAAA;EAAA,IAAAC,gBAAA,EAAAC,qBAAA,EAAAC,iBAAA,EAAAC,iBAAA,EAAAC,qBAAA,EAAAC,iBAAA;EAC3B,MAAM,CAACC,QAAQ,EAAEC,WAAW,CAAC,GAAG9C,QAAQ,CAAC,IAAI,CAAC;EAC9C,MAAM,CAAC+C,eAAe,EAAEC,kBAAkB,CAAC,GAAGhD,QAAQ,CAAC,IAAI,CAAC;EAC5D,MAAM,CAACiD,SAAS,EAAEC,YAAY,CAAC,GAAGlD,QAAQ,CAAC,IAAI,CAAC;EAChD,MAAM,CAACmD,OAAO,EAAEC,UAAU,CAAC,GAAGpD,QAAQ,CAAC,IAAI,CAAC;EAC5C,MAAM,CAACqD,KAAK,EAAEC,QAAQ,CAAC,GAAGtD,QAAQ,CAAC,IAAI,CAAC;;EAExC;EACA,MAAMuD,cAAc,GAAG,MAAAA,CAAA,KAAY;IACjC,IAAI;MACFH,UAAU,CAAC,IAAI,CAAC;MAChBE,QAAQ,CAAC,IAAI,CAAC;MACd,MAAME,QAAQ,GAAG,MAAMvB,YAAY,CAACwB,iBAAiB,CAAC,CAAC;MACvDP,YAAY,CAACM,QAAQ,CAACE,IAAI,CAAC;IAC7B,CAAC,CAAC,OAAOC,GAAG,EAAE;MACZL,QAAQ,CAACpB,cAAc,CAACyB,GAAG,CAAC,CAAC;MAC7BC,OAAO,CAACP,KAAK,CAAC,4BAA4B,EAAEM,GAAG,CAAC;MAChD;MACAT,YAAY,CAACW,gBAAgB,CAAC,CAAC,CAAC;IAClC,CAAC,SAAS;MACRT,UAAU,CAAC,KAAK,CAAC;IACnB;EACF,CAAC;EAEDnD,SAAS,CAAC,MAAM;IACdsD,cAAc,CAAC,CAAC;EAClB,CAAC,EAAE,EAAE,CAAC;;EAEN;EACA,MAAMM,gBAAgB,GAAGA,CAAA,MAAO;IAC9BC,KAAK,EAAE;MACLC,WAAW,EAAE,MAAM;MACnBC,UAAU,EAAE,KAAK;MACjBC,SAAS,EAAE,OAAO;MAClBC,UAAU,EAAE;IACd,CAAC;IACDC,MAAM,EAAE;MACNC,KAAK,EAAE,IAAI;MACXC,QAAQ,EAAE,EAAE;MACZC,MAAM,EAAE,GAAG;MACXC,OAAO,EAAE;IACX,CAAC;IACDC,WAAW,EAAE,CACX;MAAEC,KAAK,EAAE,KAAK;MAAEL,KAAK,EAAE,GAAG;MAAEM,KAAK,EAAE;IAAM,CAAC,EAC1C;MAAED,KAAK,EAAE,KAAK;MAAEL,KAAK,EAAE,GAAG;MAAEM,KAAK,EAAE;IAAM,CAAC,EAC1C;MAAED,KAAK,EAAE,KAAK;MAAEL,KAAK,EAAE,GAAG;MAAEM,KAAK,EAAE;IAAM,CAAC,EAC1C;MAAED,KAAK,EAAE,KAAK;MAAEL,KAAK,EAAE,GAAG;MAAEM,KAAK,EAAE;IAAM,CAAC,EAC1C;MAAED,KAAK,EAAE,KAAK;MAAEL,KAAK,EAAE,GAAG;MAAEM,KAAK,EAAE;IAAM,CAAC,EAC1C;MAAED,KAAK,EAAE,KAAK;MAAEL,KAAK,EAAE,GAAG;MAAEM,KAAK,EAAE;IAAM,CAAC,CAC3C;IACDC,YAAY,EAAE,CACZ;MAAEC,EAAE,EAAE,QAAQ;MAAEC,QAAQ,EAAE,UAAU;MAAEC,KAAK,EAAE,KAAK;MAAEC,MAAM,EAAE,WAAW;MAAEC,IAAI,EAAE,YAAY;MAAEC,KAAK,EAAE;IAAE,CAAC,EACvG;MAAEL,EAAE,EAAE,QAAQ;MAAEC,QAAQ,EAAE,YAAY;MAAEC,KAAK,EAAE,KAAK;MAAEC,MAAM,EAAE,YAAY;MAAEC,IAAI,EAAE,YAAY;MAAEC,KAAK,EAAE;IAAE,CAAC,EAC1G;MAAEL,EAAE,EAAE,QAAQ;MAAEC,QAAQ,EAAE,cAAc;MAAEC,KAAK,EAAE,KAAK;MAAEC,MAAM,EAAE,SAAS;MAAEC,IAAI,EAAE,YAAY;MAAEC,KAAK,EAAE;IAAE,CAAC,CAC1G;IACDC,WAAW,EAAE,CACX;MAAEN,EAAE,EAAE,CAAC;MAAEO,IAAI,EAAE,iBAAiB;MAAET,KAAK,EAAE,GAAG;MAAEH,OAAO,EAAE;IAAK,CAAC,EAC7D;MAAEK,EAAE,EAAE,CAAC;MAAEO,IAAI,EAAE,cAAc;MAAET,KAAK,EAAE,GAAG;MAAEH,OAAO,EAAE;IAAK,CAAC,EAC1D;MAAEK,EAAE,EAAE,CAAC;MAAEO,IAAI,EAAE,iBAAiB;MAAET,KAAK,EAAE,GAAG;MAAEH,OAAO,EAAE;IAAK,CAAC,CAC9D;IACDa,cAAc,EAAE,CACd;MAAED,IAAI,EAAE,SAAS;MAAEE,KAAK,EAAE,KAAK;MAAEC,KAAK,EAAE;IAAU,CAAC,EACnD;MAAEH,IAAI,EAAE,UAAU;MAAEE,KAAK,EAAE,IAAI;MAAEC,KAAK,EAAE;IAAU,CAAC,EACnD;MAAEH,IAAI,EAAE,OAAO;MAAEE,KAAK,EAAE,EAAE;MAAEC,KAAK,EAAE;IAAU,CAAC,CAC/C;IACDC,YAAY,EAAE,CACZ;MAAEJ,IAAI,EAAE,eAAe;MAAEK,UAAU,EAAE,EAAE;MAAEF,KAAK,EAAE;IAAU,CAAC,EAC3D;MAAEH,IAAI,EAAE,iBAAiB;MAAEK,UAAU,EAAE,EAAE;MAAEF,KAAK,EAAE;IAAU,CAAC,EAC7D;MAAEH,IAAI,EAAE,mBAAmB;MAAEK,UAAU,EAAE,EAAE;MAAEF,KAAK,EAAE;IAAU,CAAC,EAC/D;MAAEH,IAAI,EAAE,QAAQ;MAAEK,UAAU,EAAE,EAAE;MAAEF,KAAK,EAAE;IAAU,CAAC;EAExD,CAAC,CAAC;EAEF,IAAInC,OAAO,EAAE;IACX,oBACEf,OAAA,CAAClC,GAAG;MAACuF,EAAE,EAAE;QAAEC,OAAO,EAAE,MAAM;QAAEC,cAAc,EAAE,QAAQ;QAAEC,UAAU,EAAE,QAAQ;QAAEC,MAAM,EAAE;MAAQ,CAAE;MAAAC,QAAA,eAC5F1D,OAAA,CAACf,gBAAgB;QAAC0E,IAAI,EAAE;MAAG;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAC3B,CAAC;EAEV;EAEA,MAAMrC,KAAK,GAAG,CACZ;IACEsC,KAAK,EAAE,cAAc;IACrBf,KAAK,EAAE,CAAApC,SAAS,aAATA,SAAS,wBAAAV,gBAAA,GAATU,SAAS,CAAEa,KAAK,cAAAvB,gBAAA,wBAAAC,qBAAA,GAAhBD,gBAAA,CAAkBwB,WAAW,cAAAvB,qBAAA,uBAA7BA,qBAAA,CAA+B6D,cAAc,CAAC,CAAC,KAAI,SAAS;IACnEC,IAAI,EAAE7E,MAAM;IACZ6D,KAAK,EAAE,SAAS;IAChBiB,KAAK,EAAE;EACT,CAAC,EACD;IACEH,KAAK,EAAE,aAAa;IACpBf,KAAK,EAAE,GAAG,CAAApC,SAAS,aAATA,SAAS,wBAAAR,iBAAA,GAATQ,SAAS,CAAEa,KAAK,cAAArB,iBAAA,uBAAhBA,iBAAA,CAAkBuB,UAAU,KAAI,KAAK,GAAG;IAClDsC,IAAI,EAAE9E,YAAY;IAClB8D,KAAK,EAAE,SAAS;IAChBiB,KAAK,EAAE;EACT,CAAC,EACD;IACEH,KAAK,EAAE,YAAY;IACnBf,KAAK,EAAE,CAAApC,SAAS,aAATA,SAAS,wBAAAP,iBAAA,GAATO,SAAS,CAAEa,KAAK,cAAApB,iBAAA,wBAAAC,qBAAA,GAAhBD,iBAAA,CAAkBuB,SAAS,cAAAtB,qBAAA,uBAA3BA,qBAAA,CAA6B0D,cAAc,CAAC,CAAC,KAAI,WAAW;IACnEC,IAAI,EAAE3E,UAAU;IAChB2D,KAAK,EAAE,SAAS;IAChBiB,KAAK,EAAE;EACT,CAAC,EACD;IACEH,KAAK,EAAE,aAAa;IACpBf,KAAK,EAAE,GAAG,CAAApC,SAAS,aAATA,SAAS,wBAAAL,iBAAA,GAATK,SAAS,CAAEa,KAAK,cAAAlB,iBAAA,uBAAhBA,iBAAA,CAAkBsB,UAAU,KAAI,KAAK,GAAG;IAClDoC,IAAI,EAAE/E,UAAU;IAChB+D,KAAK,EAAE,SAAS;IAChBiB,KAAK,EAAE;EACT,CAAC,CACF;EAED,MAAMC,WAAW,GAAG,CAClB;IAAErB,IAAI,EAAE,SAAS;IAAEE,KAAK,EAAE,KAAK;IAAEC,KAAK,EAAE;EAAU,CAAC,EACnD;IAAEH,IAAI,EAAE,UAAU;IAAEE,KAAK,EAAE,IAAI;IAAEC,KAAK,EAAE;EAAU,CAAC,EACnD;IAAEH,IAAI,EAAE,OAAO;IAAEE,KAAK,EAAE,EAAE;IAAEC,KAAK,EAAE;EAAU,CAAC,CAC/C;EAED,MAAMC,YAAY,GAAG,CACnB;IAAEJ,IAAI,EAAE,eAAe;IAAEK,UAAU,EAAE,EAAE;IAAEF,KAAK,EAAE;EAAU,CAAC,EAC3D;IAAEH,IAAI,EAAE,iBAAiB;IAAEK,UAAU,EAAE,EAAE;IAAEF,KAAK,EAAE;EAAU,CAAC,EAC7D;IAAEH,IAAI,EAAE,mBAAmB;IAAEK,UAAU,EAAE,EAAE;IAAEF,KAAK,EAAE;EAAU,CAAC,EAC/D;IAAEH,IAAI,EAAE,QAAQ;IAAEK,UAAU,EAAE,EAAE;IAAEF,KAAK,EAAE;EAAU,CAAC,CACrD;EAED,MAAMd,WAAW,GAAG,CAClB;IAAEC,KAAK,EAAE,KAAK;IAAEL,KAAK,EAAE,GAAG;IAAEM,KAAK,EAAE;EAAM,CAAC,EAC1C;IAAED,KAAK,EAAE,KAAK;IAAEL,KAAK,EAAE,GAAG;IAAEM,KAAK,EAAE;EAAM,CAAC,EAC1C;IAAED,KAAK,EAAE,KAAK;IAAEL,KAAK,EAAE,GAAG;IAAEM,KAAK,EAAE;EAAM,CAAC,EAC1C;IAAED,KAAK,EAAE,KAAK;IAAEL,KAAK,EAAE,GAAG;IAAEM,KAAK,EAAE;EAAM,CAAC,EAC1C;IAAED,KAAK,EAAE,KAAK;IAAEL,KAAK,EAAE,GAAG;IAAEM,KAAK,EAAE;EAAM,CAAC,EAC1C;IAAED,KAAK,EAAE,KAAK;IAAEL,KAAK,EAAE,GAAG;IAAEM,KAAK,EAAE;EAAM,CAAC,CAC3C;EAED,MAAMC,YAAY,GAAG,CACnB;IAAEC,EAAE,EAAE,QAAQ;IAAEC,QAAQ,EAAE,UAAU;IAAE4B,OAAO,EAAE,iBAAiB;IAAEC,MAAM,EAAE,KAAK;IAAE3B,MAAM,EAAE,WAAW;IAAEC,IAAI,EAAE;EAAa,CAAC,EAC1H;IAAEJ,EAAE,EAAE,QAAQ;IAAEC,QAAQ,EAAE,YAAY;IAAE4B,OAAO,EAAE,cAAc;IAAEC,MAAM,EAAE,KAAK;IAAE3B,MAAM,EAAE,YAAY;IAAEC,IAAI,EAAE;EAAa,CAAC,EAC1H;IAAEJ,EAAE,EAAE,QAAQ;IAAEC,QAAQ,EAAE,cAAc;IAAE4B,OAAO,EAAE,iBAAiB;IAAEC,MAAM,EAAE,KAAK;IAAE3B,MAAM,EAAE,SAAS;IAAEC,IAAI,EAAE;EAAa,CAAC,EAC5H;IAAEJ,EAAE,EAAE,QAAQ;IAAEC,QAAQ,EAAE,cAAc;IAAE4B,OAAO,EAAE,eAAe;IAAEC,MAAM,EAAE,KAAK;IAAE3B,MAAM,EAAE,SAAS;IAAEC,IAAI,EAAE;EAAa,CAAC,CAC3H;EAED,MAAME,WAAW,GAAG,CAClB;IAAEN,EAAE,EAAE,CAAC;IAAEO,IAAI,EAAE,iBAAiB;IAAET,KAAK,EAAE,GAAG;IAAEH,OAAO,EAAE,IAAI;IAAEoC,KAAK,EAAE;EAAK,CAAC,EAC1E;IAAE/B,EAAE,EAAE,CAAC;IAAEO,IAAI,EAAE,cAAc;IAAET,KAAK,EAAE,GAAG;IAAEH,OAAO,EAAE,IAAI;IAAEoC,KAAK,EAAE;EAAM,CAAC,EACxE;IAAE/B,EAAE,EAAE,CAAC;IAAEO,IAAI,EAAE,iBAAiB;IAAET,KAAK,EAAE,GAAG;IAAEH,OAAO,EAAE,IAAI;IAAEoC,KAAK,EAAE;EAAK,CAAC,EAC1E;IAAE/B,EAAE,EAAE,CAAC;IAAEO,IAAI,EAAE,eAAe;IAAET,KAAK,EAAE,GAAG;IAAEH,OAAO,EAAE,IAAI;IAAEoC,KAAK,EAAE;EAAK,CAAC,CACzE;EAED,MAAMC,eAAe,GAAGA,CAACC,KAAK,EAAEJ,OAAO,KAAK;IAC1C3D,WAAW,CAAC+D,KAAK,CAACC,aAAa,CAAC;IAChC9D,kBAAkB,CAACyD,OAAO,CAAC;EAC7B,CAAC;EAED,MAAMM,eAAe,GAAGA,CAAA,KAAM;IAC5BjE,WAAW,CAAC,IAAI,CAAC;IACjBE,kBAAkB,CAAC,IAAI,CAAC;EAC1B,CAAC;EAED,MAAMgE,cAAc,GAAIjC,MAAM,IAAK;IACjC,QAAQA,MAAM;MACZ,KAAK,WAAW;QAAE,OAAO,SAAS;MAClC,KAAK,YAAY;QAAE,OAAO,SAAS;MACnC,KAAK,SAAS;QAAE,OAAO,MAAM;MAC7B,KAAK,SAAS;QAAE,OAAO,SAAS;MAChC;QAAS,OAAO,SAAS;IAC3B;EACF,CAAC;EAED,oBACE3C,OAAA,CAAClC,GAAG;IAACuF,EAAE,EAAE;MAAEwB,CAAC,EAAE,CAAC;MAAEC,eAAe,EAAE,SAAS;MAAEC,SAAS,EAAE;IAAQ,CAAE;IAAArB,QAAA,gBAChE1D,OAAA,CAAC/B,UAAU;MAAC+G,OAAO,EAAC,IAAI;MAAC3B,EAAE,EAAE;QAAE4B,EAAE,EAAE,CAAC;QAAEC,UAAU,EAAE;MAAO,CAAE;MAAAxB,QAAA,EAAC;IAE5D;MAAAE,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAY,CAAC,eAGb/D,OAAA,CAACjC,IAAI;MAACoH,SAAS;MAACC,OAAO,EAAE,CAAE;MAAC/B,EAAE,EAAE;QAAE4B,EAAE,EAAE;MAAE,CAAE;MAAAvB,QAAA,EACvChC,KAAK,CAAC2D,GAAG,CAAC,CAACC,IAAI,EAAEC,KAAK,kBACrBvF,OAAA,CAACjC,IAAI;QAACyH,IAAI;QAACC,EAAE,EAAE,EAAG;QAACC,EAAE,EAAE,CAAE;QAACC,EAAE,EAAE,CAAE;QAAAjC,QAAA,eAC9B1D,OAAA,CAAC9B,IAAI;UAACmF,EAAE,EAAE;YAAEuC,UAAU,EAAEN,IAAI,CAACpC,KAAK;YAAEA,KAAK,EAAE,OAAO;YAAEO,MAAM,EAAE;UAAQ,CAAE;UAAAC,QAAA,eACpE1D,OAAA,CAAC7B,WAAW;YAAAuF,QAAA,gBACV1D,OAAA,CAAClC,GAAG;cAACuF,EAAE,EAAE;gBAAEC,OAAO,EAAE,MAAM;gBAAEC,cAAc,EAAE,eAAe;gBAAEC,UAAU,EAAE;cAAS,CAAE;cAAAE,QAAA,gBAClF1D,OAAA,CAAClC,GAAG;gBAAA4F,QAAA,gBACF1D,OAAA,CAAC/B,UAAU;kBAAC+G,OAAO,EAAC,IAAI;kBAAC3B,EAAE,EAAE;oBAAE6B,UAAU,EAAE,MAAM;oBAAED,EAAE,EAAE;kBAAE,CAAE;kBAAAvB,QAAA,EACxD4B,IAAI,CAACrC;gBAAK;kBAAAW,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACD,CAAC,eACb/D,OAAA,CAAC/B,UAAU;kBAAC+G,OAAO,EAAC,OAAO;kBAAC3B,EAAE,EAAE;oBAAEwC,OAAO,EAAE;kBAAI,CAAE;kBAAAnC,QAAA,EAC9C4B,IAAI,CAACtB;gBAAK;kBAAAJ,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACD,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACV,CAAC,eACN/D,OAAA,CAACsF,IAAI,CAACpB,IAAI;gBAACb,EAAE,EAAE;kBAAEyC,QAAQ,EAAE,EAAE;kBAAED,OAAO,EAAE;gBAAI;cAAE;gBAAAjC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC9C,CAAC,eACN/D,OAAA,CAAC/B,UAAU;cAAC+G,OAAO,EAAC,SAAS;cAAC3B,EAAE,EAAE;gBAAE0C,EAAE,EAAE,CAAC;gBAAEzC,OAAO,EAAE;cAAQ,CAAE;cAAAI,QAAA,EAC3D4B,IAAI,CAACnB;YAAK;cAAAP,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACD,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACF;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACV;MAAC,GAlB6BwB,KAAK;QAAA3B,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAmBrC,CACP;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACE,CAAC,eAEP/D,OAAA,CAACjC,IAAI;MAACoH,SAAS;MAACC,OAAO,EAAE,CAAE;MAAA1B,QAAA,gBAEzB1D,OAAA,CAACjC,IAAI;QAACyH,IAAI;QAACC,EAAE,EAAE,EAAG;QAACE,EAAE,EAAE,CAAE;QAAAjC,QAAA,eACvB1D,OAAA,CAAChC,KAAK;UAACqF,EAAE,EAAE;YAAEwB,CAAC,EAAE,CAAC;YAAEpB,MAAM,EAAE;UAAQ,CAAE;UAAAC,QAAA,gBACnC1D,OAAA,CAAC/B,UAAU;YAAC+G,OAAO,EAAC,IAAI;YAAC3B,EAAE,EAAE;cAAE4B,EAAE,EAAE;YAAE,CAAE;YAAAvB,QAAA,EAAC;UAAqB;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAY,CAAC,eAC1E/D,OAAA,CAAClC,GAAG;YAACuF,EAAE,EAAE;cAAEI,MAAM,EAAE,KAAK;cAAEH,OAAO,EAAE,MAAM;cAAE0C,aAAa,EAAE,QAAQ;cAAEzC,cAAc,EAAE;YAAS,CAAE;YAAAG,QAAA,gBAC7F1D,OAAA,CAACjC,IAAI;cAACoH,SAAS;cAACC,OAAO,EAAE,CAAE;cAAA1B,QAAA,EACxBtB,WAAW,CAACiD,GAAG,CAAC,CAAC/D,IAAI,EAAEiE,KAAK,kBAC3BvF,OAAA,CAACjC,IAAI;gBAACyH,IAAI;gBAACC,EAAE,EAAE,CAAE;gBAAA/B,QAAA,eACf1D,OAAA,CAAClC,GAAG;kBAACuF,EAAE,EAAE;oBAAE4C,SAAS,EAAE;kBAAS,CAAE;kBAAAvC,QAAA,gBAC/B1D,OAAA,CAAC/B,UAAU;oBAAC+G,OAAO,EAAC,SAAS;oBAAC9B,KAAK,EAAC,eAAe;oBAAAQ,QAAA,EAChDpC,IAAI,CAACe;kBAAK;oBAAAuB,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACD,CAAC,eACb/D,OAAA,CAAClC,GAAG;oBAACuF,EAAE,EAAE;sBAAE0C,EAAE,EAAE,CAAC;sBAAEd,EAAE,EAAE;oBAAE,CAAE;oBAAAvB,QAAA,gBACxB1D,OAAA,CAAClC,GAAG;sBACFuF,EAAE,EAAE;wBACFI,MAAM,EAAE,GAAInC,IAAI,CAACU,KAAK,GAAG,GAAG,GAAI,GAAG,IAAI;wBACvC8C,eAAe,EAAE,SAAS;wBAC1BoB,YAAY,EAAE,CAAC;wBACfjB,EAAE,EAAE,GAAG;wBACPF,SAAS,EAAE;sBACb;oBAAE;sBAAAnB,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACH,CAAC,eACF/D,OAAA,CAAC/B,UAAU;sBAAC+G,OAAO,EAAC,SAAS;sBAAC3B,EAAE,EAAE;wBAAEyC,QAAQ,EAAE;sBAAS,CAAE;sBAAApC,QAAA,GACtDpC,IAAI,CAACU,KAAK,EAAC,QACd;oBAAA;sBAAA4B,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAY,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACV,CAAC,eACN/D,OAAA,CAAClC,GAAG;oBAAA4F,QAAA,gBACF1D,OAAA,CAAClC,GAAG;sBACFuF,EAAE,EAAE;wBACFI,MAAM,EAAE,GAAInC,IAAI,CAACgB,KAAK,GAAG,KAAK,GAAI,GAAG,IAAI;wBACzCwC,eAAe,EAAE,SAAS;wBAC1BoB,YAAY,EAAE,CAAC;wBACfjB,EAAE,EAAE,GAAG;wBACPF,SAAS,EAAE;sBACb;oBAAE;sBAAAnB,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACH,CAAC,eACF/D,OAAA,CAAC/B,UAAU;sBAAC+G,OAAO,EAAC,SAAS;sBAAC3B,EAAE,EAAE;wBAAEyC,QAAQ,EAAE;sBAAS,CAAE;sBAAApC,QAAA,GAAC,GACvD,EAACpC,IAAI,CAACgB,KAAK;oBAAA;sBAAAsB,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACF,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACV,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH;cAAC,GAjCezC,IAAI,CAACe,KAAK;gBAAAuB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAkC3B,CACP;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACE,CAAC,eACP/D,OAAA,CAAClC,GAAG;cAACuF,EAAE,EAAE;gBAAEC,OAAO,EAAE,MAAM;gBAAEC,cAAc,EAAE,QAAQ;gBAAEwC,EAAE,EAAE,CAAC;gBAAEI,GAAG,EAAE;cAAE,CAAE;cAAAzC,QAAA,gBACpE1D,OAAA,CAAClC,GAAG;gBAACuF,EAAE,EAAE;kBAAEC,OAAO,EAAE,MAAM;kBAAEE,UAAU,EAAE;gBAAS,CAAE;gBAAAE,QAAA,gBACjD1D,OAAA,CAAClC,GAAG;kBAACuF,EAAE,EAAE;oBAAE+C,KAAK,EAAE,EAAE;oBAAE3C,MAAM,EAAE,EAAE;oBAAEqB,eAAe,EAAE,SAAS;oBAAEuB,EAAE,EAAE;kBAAE;gBAAE;kBAAAzC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAC,eACzE/D,OAAA,CAAC/B,UAAU;kBAAC+G,OAAO,EAAC,SAAS;kBAAAtB,QAAA,EAAC;gBAAK;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAY,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC7C,CAAC,eACN/D,OAAA,CAAClC,GAAG;gBAACuF,EAAE,EAAE;kBAAEC,OAAO,EAAE,MAAM;kBAAEE,UAAU,EAAE;gBAAS,CAAE;gBAAAE,QAAA,gBACjD1D,OAAA,CAAClC,GAAG;kBAACuF,EAAE,EAAE;oBAAE+C,KAAK,EAAE,EAAE;oBAAE3C,MAAM,EAAE,EAAE;oBAAEqB,eAAe,EAAE,SAAS;oBAAEuB,EAAE,EAAE;kBAAE;gBAAE;kBAAAzC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAC,eACzE/D,OAAA,CAAC/B,UAAU;kBAAC+G,OAAO,EAAC,SAAS;kBAAAtB,QAAA,EAAC;gBAAS;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAY,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACjD,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACD;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACJ,CAAC,eAGP/D,OAAA,CAACjC,IAAI;QAACyH,IAAI;QAACC,EAAE,EAAE,EAAG;QAACE,EAAE,EAAE,CAAE;QAAAjC,QAAA,eACvB1D,OAAA,CAAChC,KAAK;UAACqF,EAAE,EAAE;YAAEwB,CAAC,EAAE,CAAC;YAAEpB,MAAM,EAAE;UAAQ,CAAE;UAAAC,QAAA,gBACnC1D,OAAA,CAAC/B,UAAU;YAAC+G,OAAO,EAAC,IAAI;YAAC3B,EAAE,EAAE;cAAE4B,EAAE,EAAE;YAAE,CAAE;YAAAvB,QAAA,EAAC;UAAqB;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAY,CAAC,eAC1E/D,OAAA,CAAClC,GAAG;YAACuF,EAAE,EAAE;cAAE4C,SAAS,EAAE,QAAQ;cAAEhB,EAAE,EAAE;YAAE,CAAE;YAAAvB,QAAA,gBACtC1D,OAAA,CAAC/B,UAAU;cAAC+G,OAAO,EAAC,IAAI;cAAC3B,EAAE,EAAE;gBAAEH,KAAK,EAAE,SAAS;gBAAEgC,UAAU,EAAE;cAAO,CAAE;cAAAxB,QAAA,EAAC;YAEvE;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAY,CAAC,eACb/D,OAAA,CAAC/B,UAAU;cAAC+G,OAAO,EAAC,OAAO;cAAC9B,KAAK,EAAC,eAAe;cAAAQ,QAAA,EAAC;YAElD;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAY,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACV,CAAC,eAEN/D,OAAA,CAAC/B,UAAU;YAAC+G,OAAO,EAAC,WAAW;YAAC3B,EAAE,EAAE;cAAE4B,EAAE,EAAE;YAAE,CAAE;YAAAvB,QAAA,EAAC;UAAa;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAY,CAAC,EACxEZ,YAAY,CAACkC,GAAG,CAAC,CAACiB,OAAO,EAAEf,KAAK,kBAC/BvF,OAAA,CAAClC,GAAG;YAAauF,EAAE,EAAE;cAAE4B,EAAE,EAAE;YAAE,CAAE;YAAAvB,QAAA,gBAC7B1D,OAAA,CAAClC,GAAG;cAACuF,EAAE,EAAE;gBAAEC,OAAO,EAAE,MAAM;gBAAEC,cAAc,EAAE,eAAe;gBAAE0B,EAAE,EAAE;cAAE,CAAE;cAAAvB,QAAA,gBACnE1D,OAAA,CAAC/B,UAAU;gBAAC+G,OAAO,EAAC,OAAO;gBAAAtB,QAAA,EAAE4C,OAAO,CAACvD;cAAI;gBAAAa,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAa,CAAC,eACvD/D,OAAA,CAAC/B,UAAU;gBAAC+G,OAAO,EAAC,OAAO;gBAAAtB,QAAA,GAAE4C,OAAO,CAAClD,UAAU,EAAC,GAAC;cAAA;gBAAAQ,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAY,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC3D,CAAC,eACN/D,OAAA,CAACnB,cAAc;cACbmG,OAAO,EAAC,aAAa;cACrB/B,KAAK,EAAEqD,OAAO,CAAClD,UAAW;cAC1BC,EAAE,EAAE;gBACFI,MAAM,EAAE,CAAC;gBACTyC,YAAY,EAAE,CAAC;gBACfpB,eAAe,EAAE,SAAS;gBAC1B,0BAA0B,EAAE;kBAC1BA,eAAe,EAAEwB,OAAO,CAACpD;gBAC3B;cACF;YAAE;cAAAU,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC;UAAA,GAhBMwB,KAAK;YAAA3B,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAiBV,CACN,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACG;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACJ,CAAC,eAGP/D,OAAA,CAACjC,IAAI;QAACyH,IAAI;QAACC,EAAE,EAAE,EAAG;QAACE,EAAE,EAAE,CAAE;QAAAjC,QAAA,eACvB1D,OAAA,CAAChC,KAAK;UAACqF,EAAE,EAAE;YAAEwB,CAAC,EAAE,CAAC;YAAEpB,MAAM,EAAE;UAAQ,CAAE;UAAAC,QAAA,gBACnC1D,OAAA,CAAC/B,UAAU;YAAC+G,OAAO,EAAC,IAAI;YAAC3B,EAAE,EAAE;cAAE4B,EAAE,EAAE;YAAE,CAAE;YAAAvB,QAAA,EAAC;UAAsB;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAY,CAAC,eAC3E/D,OAAA,CAAClC,GAAG;YAACuF,EAAE,EAAE;cAAEC,OAAO,EAAE,MAAM;cAAE0C,aAAa,EAAE,QAAQ;cAAEvC,MAAM,EAAE,KAAK;cAAEF,cAAc,EAAE;YAAS,CAAE;YAAAG,QAAA,GAC5FU,WAAW,CAACiB,GAAG,CAAC,CAACG,IAAI,EAAED,KAAK,kBAC3BvF,OAAA,CAAClC,GAAG;cAAauF,EAAE,EAAE;gBAAE4B,EAAE,EAAE;cAAE,CAAE;cAAAvB,QAAA,gBAC7B1D,OAAA,CAAClC,GAAG;gBAACuF,EAAE,EAAE;kBAAEC,OAAO,EAAE,MAAM;kBAAEC,cAAc,EAAE,eAAe;kBAAE0B,EAAE,EAAE;gBAAE,CAAE;gBAAAvB,QAAA,gBACnE1D,OAAA,CAAC/B,UAAU;kBAAC+G,OAAO,EAAC,OAAO;kBAAAtB,QAAA,EAAE8B,IAAI,CAACzC;gBAAI;kBAAAa,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAa,CAAC,eACpD/D,OAAA,CAAC/B,UAAU;kBAAC+G,OAAO,EAAC,OAAO;kBAAC3B,EAAE,EAAE;oBAAE6B,UAAU,EAAE;kBAAO,CAAE;kBAAAxB,QAAA,GACpD8B,IAAI,CAACvC,KAAK,EAAC,GACd;gBAAA;kBAAAW,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAY,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACV,CAAC,eACN/D,OAAA,CAACnB,cAAc;gBACbmG,OAAO,EAAC,aAAa;gBACrB/B,KAAK,EAAEuC,IAAI,CAACvC,KAAM;gBAClBI,EAAE,EAAE;kBACFI,MAAM,EAAE,EAAE;kBACVyC,YAAY,EAAE,CAAC;kBACfpB,eAAe,EAAE,SAAS;kBAC1B,0BAA0B,EAAE;oBAC1BA,eAAe,EAAEU,IAAI,CAACtC,KAAK;oBAC3BgD,YAAY,EAAE;kBAChB;gBACF;cAAE;gBAAAtC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC;YAAA,GAnBMwB,KAAK;cAAA3B,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAoBV,CACN,CAAC,eACF/D,OAAA,CAAClC,GAAG;cAACuF,EAAE,EAAE;gBAAE0C,EAAE,EAAE,CAAC;gBAAEE,SAAS,EAAE;cAAS,CAAE;cAAAvC,QAAA,eACtC1D,OAAA,CAAC/B,UAAU;gBAAC+G,OAAO,EAAC,SAAS;gBAAC9B,KAAK,EAAC,eAAe;gBAAAQ,QAAA,EAAC;cAEpD;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAY;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACV,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACD;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACJ,CAAC,eAGP/D,OAAA,CAACjC,IAAI;QAACyH,IAAI;QAACC,EAAE,EAAE,EAAG;QAACE,EAAE,EAAE,CAAE;QAAAjC,QAAA,eACvB1D,OAAA,CAAChC,KAAK;UAACqF,EAAE,EAAE;YAAEwB,CAAC,EAAE,CAAC;YAAEpB,MAAM,EAAE;UAAQ,CAAE;UAAAC,QAAA,gBACnC1D,OAAA,CAAC/B,UAAU;YAAC+G,OAAO,EAAC,IAAI;YAAC3B,EAAE,EAAE;cAAE4B,EAAE,EAAE;YAAE,CAAE;YAAAvB,QAAA,EAAC;UAAoB;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAY,CAAC,eACzE/D,OAAA,CAACzB,cAAc;YAAAmF,QAAA,eACb1D,OAAA,CAAC5B,KAAK;cAACuF,IAAI,EAAC,OAAO;cAAAD,QAAA,gBACjB1D,OAAA,CAACxB,SAAS;gBAAAkF,QAAA,eACR1D,OAAA,CAACvB,QAAQ;kBAAAiF,QAAA,gBACP1D,OAAA,CAAC1B,SAAS;oBAAAoF,QAAA,EAAC;kBAAO;oBAAAE,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAW,CAAC,eAC9B/D,OAAA,CAAC1B,SAAS;oBAACiI,KAAK,EAAC,OAAO;oBAAA7C,QAAA,EAAC;kBAAK;oBAAAE,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAW,CAAC,eAC1C/D,OAAA,CAAC1B,SAAS;oBAACiI,KAAK,EAAC,OAAO;oBAAA7C,QAAA,EAAC;kBAAO;oBAAAE,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAW,CAAC,eAC5C/D,OAAA,CAAC1B,SAAS;oBAACiI,KAAK,EAAC,OAAO;oBAAA7C,QAAA,EAAC;kBAAO;oBAAAE,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAW,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACpC;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACF,CAAC,eACZ/D,OAAA,CAAC3B,SAAS;gBAAAqF,QAAA,EACPZ,WAAW,CAACuC,GAAG,CAAEhB,OAAO,iBACvBrE,OAAA,CAACvB,QAAQ;kBAAAiF,QAAA,gBACP1D,OAAA,CAAC1B,SAAS;oBAAAoF,QAAA,eACR1D,OAAA,CAAClC,GAAG;sBAACuF,EAAE,EAAE;wBAAEC,OAAO,EAAE,MAAM;wBAAEE,UAAU,EAAE;sBAAS,CAAE;sBAAAE,QAAA,gBACjD1D,OAAA,CAAC/B,UAAU;wBAACoF,EAAE,EAAE;0BAAEgD,EAAE,EAAE,CAAC;0BAAEP,QAAQ,EAAE;wBAAQ,CAAE;wBAAApC,QAAA,EAAEW,OAAO,CAACE;sBAAK;wBAAAX,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAa,CAAC,eAC1E/D,OAAA,CAAC/B,UAAU;wBAAC+G,OAAO,EAAC,OAAO;wBAAAtB,QAAA,EAAEW,OAAO,CAACtB;sBAAI;wBAAAa,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAa,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACpD;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACG,CAAC,eACZ/D,OAAA,CAAC1B,SAAS;oBAACiI,KAAK,EAAC,OAAO;oBAAA7C,QAAA,EAAEW,OAAO,CAAC/B;kBAAK;oBAAAsB,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAY,CAAC,eACpD/D,OAAA,CAAC1B,SAAS;oBAACiI,KAAK,EAAC,OAAO;oBAAA7C,QAAA,GAAC,GAAC,EAACW,OAAO,CAAClC,OAAO;kBAAA;oBAAAyB,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAY,CAAC,eACvD/D,OAAA,CAAC1B,SAAS;oBAACiI,KAAK,EAAC,OAAO;oBAAA7C,QAAA,eACtB1D,OAAA,CAAClB,UAAU;sBAAC6E,IAAI,EAAC,OAAO;sBAAC6C,OAAO,EAAGC,CAAC,IAAKjC,eAAe,CAACiC,CAAC,EAAEpC,OAAO,CAAE;sBAAAX,QAAA,eACnE1D,OAAA,CAACP,QAAQ;wBAAAmE,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAE;oBAAC;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACF;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACJ,CAAC;gBAAA,GAbCM,OAAO,CAAC7B,EAAE;kBAAAoB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAcf,CACX;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACO,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACP;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACM,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACZ;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACJ,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,eAGP/D,OAAA,CAAChC,KAAK;MAACqF,EAAE,EAAE;QAAEwB,CAAC,EAAE,CAAC;QAAEkB,EAAE,EAAE;MAAE,CAAE;MAAArC,QAAA,gBACzB1D,OAAA,CAAC/B,UAAU;QAAC+G,OAAO,EAAC,IAAI;QAAC3B,EAAE,EAAE;UAAE4B,EAAE,EAAE;QAAE,CAAE;QAAAvB,QAAA,EAAC;MAAa;QAAAE,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAY,CAAC,eAClE/D,OAAA,CAACzB,cAAc;QAAAmF,QAAA,eACb1D,OAAA,CAAC5B,KAAK;UAAAsF,QAAA,gBACJ1D,OAAA,CAACxB,SAAS;YAAAkF,QAAA,eACR1D,OAAA,CAACvB,QAAQ;cAAAiF,QAAA,gBACP1D,OAAA,CAAC1B,SAAS;gBAAAoF,QAAA,EAAC;cAAQ;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAW,CAAC,eAC/B/D,OAAA,CAAC1B,SAAS;gBAAAoF,QAAA,EAAC;cAAQ;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAW,CAAC,eAC/B/D,OAAA,CAAC1B,SAAS;gBAAAoF,QAAA,EAAC;cAAO;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAW,CAAC,eAC9B/D,OAAA,CAAC1B,SAAS;gBAACiI,KAAK,EAAC,OAAO;gBAAA7C,QAAA,EAAC;cAAM;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAW,CAAC,eAC3C/D,OAAA,CAAC1B,SAAS;gBAAAoF,QAAA,EAAC;cAAM;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAW,CAAC,eAC7B/D,OAAA,CAAC1B,SAAS;gBAAAoF,QAAA,EAAC;cAAI;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAW,CAAC,eAC3B/D,OAAA,CAAC1B,SAAS;gBAACiI,KAAK,EAAC,OAAO;gBAAA7C,QAAA,EAAC;cAAO;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAW,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACpC;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACF,CAAC,eACZ/D,OAAA,CAAC3B,SAAS;YAAAqF,QAAA,EACPnB,YAAY,CAAC8C,GAAG,CAAEqB,KAAK,iBACtB1G,OAAA,CAACvB,QAAQ;cAAAiF,QAAA,gBACP1D,OAAA,CAAC1B,SAAS;gBAAAoF,QAAA,EAAEgD,KAAK,CAAClE;cAAE;gBAAAoB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAY,CAAC,eACjC/D,OAAA,CAAC1B,SAAS;gBAAAoF,QAAA,EAAEgD,KAAK,CAACjE;cAAQ;gBAAAmB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAY,CAAC,eACvC/D,OAAA,CAAC1B,SAAS;gBAAAoF,QAAA,EAAEgD,KAAK,CAACrC;cAAO;gBAAAT,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAY,CAAC,eACtC/D,OAAA,CAAC1B,SAAS;gBAACiI,KAAK,EAAC,OAAO;gBAAA7C,QAAA,GAAC,GAAC,EAACgD,KAAK,CAACpC,MAAM;cAAA;gBAAAV,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAY,CAAC,eACpD/D,OAAA,CAAC1B,SAAS;gBAAAoF,QAAA,eACR1D,OAAA,CAACrB,IAAI;kBACHgI,KAAK,EAAED,KAAK,CAAC/D,MAAO;kBACpBO,KAAK,EAAE0B,cAAc,CAAC8B,KAAK,CAAC/D,MAAM,CAAE;kBACpCgB,IAAI,EAAC;gBAAO;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACb;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACO,CAAC,eACZ/D,OAAA,CAAC1B,SAAS;gBAAAoF,QAAA,EAAEgD,KAAK,CAAC9D;cAAI;gBAAAgB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAY,CAAC,eACnC/D,OAAA,CAAC1B,SAAS;gBAACiI,KAAK,EAAC,OAAO;gBAAA7C,QAAA,eACtB1D,OAAA,CAACtB,MAAM;kBAACiF,IAAI,EAAC,OAAO;kBAACqB,OAAO,EAAC,UAAU;kBAAAtB,QAAA,EAAC;gBAExC;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACA,CAAC;YAAA,GAjBC2C,KAAK,CAAClE,EAAE;cAAAoB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAkBb,CACX;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACO,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACP;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACM,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACZ,CAAC,eAGR/D,OAAA,CAACjB,IAAI;MACH0B,QAAQ,EAAEA,QAAS;MACnBmG,IAAI,EAAEC,OAAO,CAACpG,QAAQ,CAAE;MACxBqG,OAAO,EAAEnC,eAAgB;MAAAjB,QAAA,gBAEzB1D,OAAA,CAAChB,QAAQ;QAACwH,OAAO,EAAE7B,eAAgB;QAAAjB,QAAA,gBACjC1D,OAAA,CAACN,IAAI;UAAC2D,EAAE,EAAE;YAAEgD,EAAE,EAAE;UAAE;QAAE;UAAAzC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,SACzB;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAU,CAAC,eACX/D,OAAA,CAAChB,QAAQ;QAACwH,OAAO,EAAE7B,eAAgB;QAAAjB,QAAA,gBACjC1D,OAAA,CAACL,MAAM;UAAC0D,EAAE,EAAE;YAAEgD,EAAE,EAAE;UAAE;QAAE;UAAAzC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,WAC3B;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAU,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACP,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACJ,CAAC;AAEV,CAAC;AAAC7D,EAAA,CA9aID,cAAc;AAAA8G,EAAA,GAAd9G,cAAc;AAgbpB,eAAeA,cAAc;AAAC,IAAA8G,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}