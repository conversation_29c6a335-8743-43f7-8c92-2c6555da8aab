{"ast": null, "code": "var _jsxFileName = \"D:\\\\ecommerce\\\\client\\\\src\\\\components\\\\Home.js\",\n  _s = $RefreshSig$();\nimport React, { useEffect, useState } from 'react';\nimport { useDispatch, useSelector } from 'react-redux';\nimport { Container, Grid, Typography, Card, CardMedia, CardContent, Button, Box, TextField, InputAdornment, Paper, Chip, Rating, IconButton, Badge, Avatar, Divider, CircularProgress, Alert, Tabs, Tab, FormControl, InputLabel, Select, MenuItem, Slider } from '@mui/material';\nimport { Search, ShoppingCart, Favorite, FavoriteBorder, Star, LocalShipping, Security, Assignment, FilterList, Sort, CompareArrows, Share, LocalOffer } from '@mui/icons-material';\nimport { styled } from '@mui/material/styles';\nimport { productsAPI, cartAPI, handleAPIError } from '../services/api';\nimport { useAuth } from '../context/AuthContext';\nimport { useNavigate } from 'react-router-dom';\n\n// Flipkart-style styled components\nimport { jsxDEV as _jsxDEV, Fragment as _Fragment } from \"react/jsx-dev-runtime\";\nconst FlipkartHeader = styled(Paper)(({\n  theme\n}) => ({\n  backgroundColor: '#2874f0',\n  color: 'white',\n  padding: theme.spacing(1, 0),\n  boxShadow: '0 2px 4px rgba(0,0,0,0.1)'\n}));\n_c = FlipkartHeader;\nconst CategoryCard = styled(Card)(({\n  theme\n}) => ({\n  textAlign: 'center',\n  padding: theme.spacing(2),\n  cursor: 'pointer',\n  transition: 'all 0.3s ease',\n  '&:hover': {\n    transform: 'translateY(-4px)',\n    boxShadow: '0 8px 25px rgba(0,0,0,0.15)'\n  }\n}));\n_c2 = CategoryCard;\nconst ProductCard = styled(Card)(({\n  theme\n}) => ({\n  height: '100%',\n  display: 'flex',\n  flexDirection: 'column',\n  transition: 'all 0.3s ease',\n  cursor: 'pointer',\n  '&:hover': {\n    transform: 'translateY(-4px)',\n    boxShadow: '0 8px 25px rgba(0,0,0,0.15)'\n  }\n}));\n_c3 = ProductCard;\nconst OfferBanner = styled(Box)(({\n  theme\n}) => ({\n  background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',\n  color: 'white',\n  padding: theme.spacing(3),\n  borderRadius: theme.spacing(1),\n  textAlign: 'center',\n  margin: theme.spacing(2, 0)\n}));\n_c4 = OfferBanner;\nconst Home = () => {\n  _s();\n  var _user$name;\n  const dispatch = useDispatch();\n  const navigate = useNavigate();\n  const {\n    user,\n    isAuthenticated,\n    logout\n  } = useAuth();\n  const [products, setProducts] = useState([]);\n  const [loading, setLoading] = useState(true);\n  const [error, setError] = useState(null);\n  const [searchTerm, setSearchTerm] = useState('');\n  const [selectedCategory, setSelectedCategory] = useState('All');\n  const [priceRange, setPriceRange] = useState([0, 100]);\n  const [sortBy, setSortBy] = useState('featured');\n  const [favorites, setFavorites] = useState(new Set());\n  const [cartItems, setCartItems] = useState([]);\n  const [tabValue, setTabValue] = useState(0);\n\n  // Fetch products from API\n  useEffect(() => {\n    const fetchProducts = async () => {\n      try {\n        setLoading(true);\n        const response = await productsAPI.getAll();\n        setProducts(response.data);\n        setError(null);\n      } catch (err) {\n        setError(handleAPIError(err));\n        // Fallback to mock data\n        setProducts(getMockProducts());\n      } finally {\n        setLoading(false);\n      }\n    };\n    fetchProducts();\n\n    // Load cart items\n    const cart = cartAPI.getCart();\n    setCartItems(cart.items);\n  }, []);\n\n  // Mock data fallback\n  const getMockProducts = () => [{\n    _id: '1',\n    name: 'Ceylon Cinnamon Sticks',\n    price: 15.99,\n    image: 'https://images.unsplash.com/photo-1506905925346-21bda4d32df4?w=300&h=300&fit=crop',\n    rating: 4.8,\n    numReviews: 124,\n    category: 'Whole Spices',\n    stock: 50,\n    origin: 'Sri Lanka',\n    weight: 100,\n    unit: 'g',\n    featured: true,\n    description: 'Premium quality Ceylon cinnamon sticks from Sri Lanka'\n  }, {\n    _id: '2',\n    name: 'Black Peppercorns',\n    price: 12.50,\n    image: 'https://images.unsplash.com/photo-1596040033229-a9821ebd058d?w=300&h=300&fit=crop',\n    rating: 4.9,\n    numReviews: 89,\n    category: 'Whole Spices',\n    stock: 30,\n    origin: 'India',\n    weight: 50,\n    unit: 'g',\n    featured: false,\n    description: 'Freshly ground black peppercorns with intense flavor'\n  }, {\n    _id: '3',\n    name: 'Organic Turmeric Powder',\n    price: 18.75,\n    image: 'https://images.unsplash.com/photo-1615485500704-8e990f9900f7?w=300&h=300&fit=crop',\n    rating: 4.7,\n    numReviews: 156,\n    category: 'Ground Spices',\n    stock: 25,\n    origin: 'India',\n    weight: 200,\n    unit: 'g',\n    featured: true,\n    description: 'Pure organic turmeric powder with anti-inflammatory properties'\n  }, {\n    _id: '4',\n    name: 'Green Cardamom Pods',\n    price: 24.99,\n    image: 'https://images.unsplash.com/photo-1596040033229-a9821ebd058d?w=300&h=300&fit=crop',\n    rating: 4.6,\n    numReviews: 78,\n    category: 'Whole Spices',\n    stock: 40,\n    origin: 'Guatemala',\n    weight: 50,\n    unit: 'g',\n    featured: false,\n    description: 'Aromatic green cardamom pods perfect for tea and desserts'\n  }, {\n    _id: '5',\n    name: 'Star Anise',\n    price: 16.99,\n    image: 'https://images.unsplash.com/photo-1506905925346-21bda4d32df4?w=300&h=300&fit=crop',\n    rating: 4.5,\n    numReviews: 45,\n    category: 'Whole Spices',\n    stock: 35,\n    origin: 'China',\n    weight: 75,\n    unit: 'g',\n    featured: false,\n    description: 'Whole star anise with sweet licorice flavor'\n  }, {\n    _id: '6',\n    name: 'Saffron Threads',\n    price: 89.99,\n    image: 'https://images.unsplash.com/photo-1615485500704-8e990f9900f7?w=300&h=300&fit=crop',\n    rating: 4.9,\n    numReviews: 234,\n    category: 'Herbs',\n    stock: 10,\n    origin: 'Kashmir',\n    weight: 1,\n    unit: 'g',\n    featured: true,\n    description: 'Premium saffron threads - the most expensive spice in the world'\n  }];\n  const categories = [{\n    name: 'All',\n    icon: '🛒',\n    count: products.length\n  }, {\n    name: 'Whole Spices',\n    icon: '🌿',\n    count: products.filter(p => p.category === 'Whole Spices').length\n  }, {\n    name: 'Ground Spices',\n    icon: '🥄',\n    count: products.filter(p => p.category === 'Ground Spices').length\n  }, {\n    name: 'Herbs',\n    icon: '🌱',\n    count: products.filter(p => p.category === 'Herbs').length\n  }, {\n    name: 'Spice Blends',\n    icon: '🍛',\n    count: products.filter(p => p.category === 'Spice Blends').length\n  }, {\n    name: 'Seasonings',\n    icon: '🧂',\n    count: products.filter(p => p.category === 'Seasonings').length\n  }];\n  const handleAddToCart = product => {\n    const cart = cartAPI.addToCart(product);\n    setCartItems(cart.items);\n    dispatch({\n      type: 'ADD_TO_CART',\n      payload: product\n    });\n  };\n  const toggleFavorite = productId => {\n    const newFavorites = new Set(favorites);\n    if (newFavorites.has(productId)) {\n      newFavorites.delete(productId);\n    } else {\n      newFavorites.add(productId);\n    }\n    setFavorites(newFavorites);\n  };\n\n  // Filter and sort products\n  const filteredProducts = products.filter(product => {\n    const matchesSearch = product.name.toLowerCase().includes(searchTerm.toLowerCase());\n    const matchesCategory = selectedCategory === 'All' || product.category === selectedCategory;\n    const matchesPrice = product.price >= priceRange[0] && product.price <= priceRange[1];\n    return matchesSearch && matchesCategory && matchesPrice;\n  }).sort((a, b) => {\n    switch (sortBy) {\n      case 'price-low':\n        return a.price - b.price;\n      case 'price-high':\n        return b.price - a.price;\n      case 'rating':\n        return b.rating - a.rating;\n      case 'newest':\n        return new Date(b.createdAt) - new Date(a.createdAt);\n      default:\n        return b.featured - a.featured;\n    }\n  });\n  if (loading) {\n    return /*#__PURE__*/_jsxDEV(Box, {\n      sx: {\n        display: 'flex',\n        justifyContent: 'center',\n        alignItems: 'center',\n        height: '50vh'\n      },\n      children: /*#__PURE__*/_jsxDEV(CircularProgress, {\n        size: 60\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 270,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 269,\n      columnNumber: 7\n    }, this);\n  }\n  return /*#__PURE__*/_jsxDEV(Box, {\n    sx: {\n      backgroundColor: '#f1f3f6',\n      minHeight: '100vh'\n    },\n    children: [/*#__PURE__*/_jsxDEV(FlipkartHeader, {\n      children: /*#__PURE__*/_jsxDEV(Container, {\n        maxWidth: \"xl\",\n        children: /*#__PURE__*/_jsxDEV(Grid, {\n          container: true,\n          alignItems: \"center\",\n          spacing: 2,\n          children: [/*#__PURE__*/_jsxDEV(Grid, {\n            item: true,\n            xs: 12,\n            md: 3,\n            children: /*#__PURE__*/_jsxDEV(Box, {\n              sx: {\n                display: 'flex',\n                alignItems: 'center'\n              },\n              children: [/*#__PURE__*/_jsxDEV(Typography, {\n                variant: \"h5\",\n                sx: {\n                  fontWeight: 'bold',\n                  mr: 2\n                },\n                children: \"\\uD83D\\uDED2 SpiceMart\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 283,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                variant: \"caption\",\n                sx: {\n                  fontStyle: 'italic'\n                },\n                children: [\"Explore \", /*#__PURE__*/_jsxDEV(\"span\", {\n                  style: {\n                    color: '#ffe500'\n                  },\n                  children: \"Plus\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 287,\n                  columnNumber: 27\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 286,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 282,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 281,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Grid, {\n            item: true,\n            xs: 12,\n            md: 5,\n            children: /*#__PURE__*/_jsxDEV(TextField, {\n              fullWidth: true,\n              placeholder: \"Search for spices, herbs and more...\",\n              value: searchTerm,\n              onChange: e => setSearchTerm(e.target.value),\n              InputProps: {\n                startAdornment: /*#__PURE__*/_jsxDEV(InputAdornment, {\n                  position: \"start\",\n                  children: /*#__PURE__*/_jsxDEV(Search, {\n                    sx: {\n                      color: '#2874f0'\n                    }\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 301,\n                    columnNumber: 23\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 300,\n                  columnNumber: 21\n                }, this),\n                sx: {\n                  backgroundColor: 'white',\n                  borderRadius: 1\n                }\n              },\n              size: \"small\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 293,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 292,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Grid, {\n            item: true,\n            xs: 12,\n            md: 4,\n            children: /*#__PURE__*/_jsxDEV(Box, {\n              sx: {\n                display: 'flex',\n                alignItems: 'center',\n                justifyContent: 'flex-end',\n                gap: 2\n              },\n              children: [/*#__PURE__*/_jsxDEV(Button, {\n                color: \"inherit\",\n                startIcon: /*#__PURE__*/_jsxDEV(Assignment, {}, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 312,\n                  columnNumber: 52\n                }, this),\n                children: \"Become a Seller\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 312,\n                columnNumber: 17\n              }, this), isAuthenticated ? /*#__PURE__*/_jsxDEV(_Fragment, {\n                children: [/*#__PURE__*/_jsxDEV(Button, {\n                  color: \"inherit\",\n                  onClick: () => navigate(user !== null && user !== void 0 && user.isAdmin ? '/admin' : '/dashboard'),\n                  children: user !== null && user !== void 0 && user.isAdmin ? 'Admin' : 'Dashboard'\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 318,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(Badge, {\n                  badgeContent: cartItems.length,\n                  color: \"error\",\n                  children: /*#__PURE__*/_jsxDEV(IconButton, {\n                    color: \"inherit\",\n                    onClick: () => navigate('/cart'),\n                    children: /*#__PURE__*/_jsxDEV(ShoppingCart, {}, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 326,\n                      columnNumber: 25\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 325,\n                    columnNumber: 23\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 324,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(IconButton, {\n                  color: \"inherit\",\n                  onClick: () => navigate('/dashboard'),\n                  children: /*#__PURE__*/_jsxDEV(Avatar, {\n                    sx: {\n                      bgcolor: '#ff6f00',\n                      width: 32,\n                      height: 32\n                    },\n                    children: user === null || user === void 0 ? void 0 : (_user$name = user.name) === null || _user$name === void 0 ? void 0 : _user$name.charAt(0).toUpperCase()\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 333,\n                    columnNumber: 23\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 329,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(Button, {\n                  color: \"inherit\",\n                  onClick: logout,\n                  children: \"Logout\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 337,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true) : /*#__PURE__*/_jsxDEV(_Fragment, {\n                children: [/*#__PURE__*/_jsxDEV(Button, {\n                  color: \"inherit\",\n                  onClick: () => navigate('/login'),\n                  children: \"Login\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 343,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(Button, {\n                  color: \"inherit\",\n                  onClick: () => navigate('/register'),\n                  sx: {\n                    backgroundColor: 'rgba(255,255,255,0.1)',\n                    '&:hover': {\n                      backgroundColor: 'rgba(255,255,255,0.2)'\n                    }\n                  },\n                  children: \"Sign Up\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 346,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(Badge, {\n                  badgeContent: cartItems.length,\n                  color: \"error\",\n                  children: /*#__PURE__*/_jsxDEV(IconButton, {\n                    color: \"inherit\",\n                    onClick: () => navigate('/cart'),\n                    children: /*#__PURE__*/_jsxDEV(ShoppingCart, {}, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 358,\n                      columnNumber: 25\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 357,\n                    columnNumber: 23\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 356,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 311,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 310,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 280,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 279,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 278,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Container, {\n      maxWidth: \"xl\",\n      sx: {\n        mt: 2\n      },\n      children: /*#__PURE__*/_jsxDEV(OfferBanner, {\n        children: [/*#__PURE__*/_jsxDEV(Typography, {\n          variant: \"h4\",\n          sx: {\n            fontWeight: 'bold',\n            mb: 1\n          },\n          children: \"\\uD83C\\uDF89 Big Billion Days Sale! \\uD83C\\uDF89\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 372,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Typography, {\n          variant: \"h6\",\n          children: \"Up to 80% OFF on Premium Spices | Free Delivery | No Cost EMI\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 375,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 371,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 370,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Container, {\n      maxWidth: \"xl\",\n      sx: {\n        mt: 3\n      },\n      children: /*#__PURE__*/_jsxDEV(Paper, {\n        sx: {\n          p: 2\n        },\n        children: [/*#__PURE__*/_jsxDEV(Typography, {\n          variant: \"h6\",\n          sx: {\n            mb: 2,\n            fontWeight: 'bold'\n          },\n          children: \"Shop by Category\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 384,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Grid, {\n          container: true,\n          spacing: 2,\n          children: categories.map(category => /*#__PURE__*/_jsxDEV(Grid, {\n            item: true,\n            xs: 6,\n            sm: 4,\n            md: 2,\n            children: /*#__PURE__*/_jsxDEV(CategoryCard, {\n              onClick: () => setSelectedCategory(category.name),\n              sx: {\n                backgroundColor: selectedCategory === category.name ? '#e3f2fd' : 'white',\n                border: selectedCategory === category.name ? '2px solid #2874f0' : '1px solid #e0e0e0'\n              },\n              children: [/*#__PURE__*/_jsxDEV(Typography, {\n                variant: \"h4\",\n                sx: {\n                  mb: 1\n                },\n                children: category.icon\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 397,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                variant: \"body2\",\n                sx: {\n                  fontWeight: 'bold'\n                },\n                children: category.name\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 400,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                variant: \"caption\",\n                color: \"textSecondary\",\n                children: [\"(\", category.count, \" items)\"]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 403,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 390,\n              columnNumber: 17\n            }, this)\n          }, category.name, false, {\n            fileName: _jsxFileName,\n            lineNumber: 389,\n            columnNumber: 15\n          }, this))\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 387,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 383,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 382,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Container, {\n      maxWidth: \"xl\",\n      sx: {\n        mt: 2\n      },\n      children: /*#__PURE__*/_jsxDEV(Paper, {\n        sx: {\n          p: 2\n        },\n        children: /*#__PURE__*/_jsxDEV(Grid, {\n          container: true,\n          spacing: 2,\n          alignItems: \"center\",\n          children: [/*#__PURE__*/_jsxDEV(Grid, {\n            item: true,\n            xs: 12,\n            md: 3,\n            children: /*#__PURE__*/_jsxDEV(FormControl, {\n              fullWidth: true,\n              size: \"small\",\n              children: [/*#__PURE__*/_jsxDEV(InputLabel, {\n                children: \"Sort By\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 419,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(Select, {\n                value: sortBy,\n                label: \"Sort By\",\n                onChange: e => setSortBy(e.target.value),\n                children: [/*#__PURE__*/_jsxDEV(MenuItem, {\n                  value: \"featured\",\n                  children: \"Featured\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 425,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(MenuItem, {\n                  value: \"price-low\",\n                  children: \"Price: Low to High\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 426,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(MenuItem, {\n                  value: \"price-high\",\n                  children: \"Price: High to Low\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 427,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(MenuItem, {\n                  value: \"rating\",\n                  children: \"Customer Rating\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 428,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(MenuItem, {\n                  value: \"newest\",\n                  children: \"Newest First\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 429,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 420,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 418,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 417,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Grid, {\n            item: true,\n            xs: 12,\n            md: 4,\n            children: [/*#__PURE__*/_jsxDEV(Typography, {\n              variant: \"body2\",\n              sx: {\n                mb: 1\n              },\n              children: [\"Price Range: $\", priceRange[0], \" - $\", priceRange[1]]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 435,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Slider, {\n              value: priceRange,\n              onChange: (e, newValue) => setPriceRange(newValue),\n              valueLabelDisplay: \"auto\",\n              min: 0,\n              max: 100,\n              sx: {\n                color: '#2874f0'\n              }\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 438,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 434,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Grid, {\n            item: true,\n            xs: 12,\n            md: 5,\n            children: /*#__PURE__*/_jsxDEV(Box, {\n              sx: {\n                display: 'flex',\n                alignItems: 'center',\n                gap: 1\n              },\n              children: [/*#__PURE__*/_jsxDEV(Chip, {\n                icon: /*#__PURE__*/_jsxDEV(FilterList, {}, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 451,\n                  columnNumber: 25\n                }, this),\n                label: \"Filters\",\n                variant: \"outlined\",\n                sx: {\n                  color: '#2874f0',\n                  borderColor: '#2874f0'\n                }\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 450,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(Chip, {\n                icon: /*#__PURE__*/_jsxDEV(LocalShipping, {}, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 457,\n                  columnNumber: 25\n                }, this),\n                label: \"Free Delivery\",\n                variant: \"outlined\",\n                color: \"success\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 456,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(Chip, {\n                icon: /*#__PURE__*/_jsxDEV(Security, {}, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 463,\n                  columnNumber: 25\n                }, this),\n                label: \"Assured\",\n                variant: \"outlined\",\n                color: \"primary\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 462,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 449,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 448,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 416,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 415,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 414,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Container, {\n      maxWidth: \"xl\",\n      sx: {\n        mt: 2,\n        pb: 4\n      },\n      children: [error && /*#__PURE__*/_jsxDEV(Alert, {\n        severity: \"warning\",\n        sx: {\n          mb: 2\n        },\n        children: [error, \" - Showing sample data\"]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 477,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(Typography, {\n        variant: \"h6\",\n        sx: {\n          mb: 2,\n          fontWeight: 'bold'\n        },\n        children: [selectedCategory === 'All' ? 'All Products' : selectedCategory, /*#__PURE__*/_jsxDEV(\"span\", {\n          style: {\n            color: '#878787',\n            fontWeight: 'normal'\n          },\n          children: [\"(\", filteredProducts.length, \" items)\"]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 484,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 482,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Grid, {\n        container: true,\n        spacing: 2,\n        children: filteredProducts.map(product => /*#__PURE__*/_jsxDEV(Grid, {\n          item: true,\n          xs: 6,\n          sm: 4,\n          md: 3,\n          lg: 2.4,\n          children: /*#__PURE__*/_jsxDEV(ProductCard, {\n            children: [/*#__PURE__*/_jsxDEV(Box, {\n              sx: {\n                position: 'relative'\n              },\n              children: [/*#__PURE__*/_jsxDEV(CardMedia, {\n                component: \"img\",\n                height: \"200\",\n                image: product.image,\n                alt: product.name,\n                sx: {\n                  objectFit: 'cover'\n                }\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 494,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(IconButton, {\n                sx: {\n                  position: 'absolute',\n                  top: 8,\n                  right: 8,\n                  backgroundColor: 'rgba(255,255,255,0.8)',\n                  '&:hover': {\n                    backgroundColor: 'rgba(255,255,255,0.9)'\n                  }\n                },\n                onClick: () => toggleFavorite(product._id),\n                children: favorites.has(product._id) ? /*#__PURE__*/_jsxDEV(Favorite, {\n                  sx: {\n                    color: '#ff6b6b'\n                  }\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 512,\n                  columnNumber: 23\n                }, this) : /*#__PURE__*/_jsxDEV(FavoriteBorder, {}, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 513,\n                  columnNumber: 23\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 501,\n                columnNumber: 19\n              }, this), product.featured && /*#__PURE__*/_jsxDEV(Chip, {\n                label: \"Bestseller\",\n                color: \"error\",\n                size: \"small\",\n                sx: {\n                  position: 'absolute',\n                  top: 8,\n                  left: 8,\n                  fontSize: '0.7rem'\n                }\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 517,\n                columnNumber: 21\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 493,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(CardContent, {\n              sx: {\n                flexGrow: 1,\n                p: 2\n              },\n              children: [/*#__PURE__*/_jsxDEV(Typography, {\n                variant: \"body2\",\n                sx: {\n                  fontWeight: 'bold',\n                  overflow: 'hidden',\n                  textOverflow: 'ellipsis',\n                  whiteSpace: 'nowrap',\n                  mb: 1\n                },\n                children: product.name\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 532,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(Box, {\n                sx: {\n                  display: 'flex',\n                  alignItems: 'center',\n                  mb: 1\n                },\n                children: [/*#__PURE__*/_jsxDEV(Rating, {\n                  value: product.rating || 4.5,\n                  readOnly: true,\n                  size: \"small\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 546,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                  variant: \"caption\",\n                  sx: {\n                    ml: 1,\n                    color: '#878787'\n                  },\n                  children: [\"(\", product.numReviews || 0, \")\"]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 547,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 545,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(Box, {\n                sx: {\n                  display: 'flex',\n                  alignItems: 'center',\n                  mb: 1\n                },\n                children: [/*#__PURE__*/_jsxDEV(Typography, {\n                  variant: \"h6\",\n                  sx: {\n                    fontWeight: 'bold',\n                    color: '#212121'\n                  },\n                  children: [\"\\u20B9\", (product.price * 75).toFixed(0)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 553,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                  variant: \"caption\",\n                  sx: {\n                    ml: 1,\n                    textDecoration: 'line-through',\n                    color: '#878787'\n                  },\n                  children: [\"\\u20B9\", (product.price * 100).toFixed(0)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 556,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                  variant: \"caption\",\n                  sx: {\n                    ml: 1,\n                    color: '#388e3c',\n                    fontWeight: 'bold'\n                  },\n                  children: \"25% off\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 566,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 552,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                variant: \"caption\",\n                sx: {\n                  color: '#878787',\n                  display: 'block',\n                  mb: 1\n                },\n                children: [product.weight, product.unit, \" | \", product.origin]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 571,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(Box, {\n                sx: {\n                  display: 'flex',\n                  gap: 1\n                },\n                children: /*#__PURE__*/_jsxDEV(Button, {\n                  variant: \"contained\",\n                  size: \"small\",\n                  fullWidth: true,\n                  onClick: () => handleAddToCart(product),\n                  sx: {\n                    backgroundColor: '#ff9f00',\n                    '&:hover': {\n                      backgroundColor: '#e68900'\n                    },\n                    fontSize: '0.75rem',\n                    py: 0.5\n                  },\n                  children: \"ADD TO CART\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 576,\n                  columnNumber: 21\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 575,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(Box, {\n                sx: {\n                  display: 'flex',\n                  alignItems: 'center',\n                  mt: 1,\n                  gap: 1\n                },\n                children: [/*#__PURE__*/_jsxDEV(LocalShipping, {\n                  sx: {\n                    fontSize: 14,\n                    color: '#388e3c'\n                  }\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 593,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                  variant: \"caption\",\n                  sx: {\n                    color: '#388e3c'\n                  },\n                  children: \"Free Delivery\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 594,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 592,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 531,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 492,\n            columnNumber: 15\n          }, this)\n        }, product._id, false, {\n          fileName: _jsxFileName,\n          lineNumber: 491,\n          columnNumber: 13\n        }, this))\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 489,\n        columnNumber: 9\n      }, this), filteredProducts.length === 0 && /*#__PURE__*/_jsxDEV(Box, {\n        sx: {\n          textAlign: 'center',\n          py: 8\n        },\n        children: [/*#__PURE__*/_jsxDEV(Typography, {\n          variant: \"h6\",\n          color: \"textSecondary\",\n          children: \"No products found matching your criteria\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 606,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(Button, {\n          variant: \"outlined\",\n          sx: {\n            mt: 2\n          },\n          onClick: () => {\n            setSearchTerm('');\n            setSelectedCategory('All');\n            setPriceRange([0, 100]);\n          },\n          children: \"Clear Filters\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 609,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 605,\n        columnNumber: 11\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 475,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 276,\n    columnNumber: 5\n  }, this);\n};\n_s(Home, \"pew14eEd9E9LqmWWHvrCW2KGGlE=\", false, function () {\n  return [useDispatch, useNavigate, useAuth];\n});\n_c5 = Home;\nexport default Home;\nvar _c, _c2, _c3, _c4, _c5;\n$RefreshReg$(_c, \"FlipkartHeader\");\n$RefreshReg$(_c2, \"CategoryCard\");\n$RefreshReg$(_c3, \"ProductCard\");\n$RefreshReg$(_c4, \"OfferBanner\");\n$RefreshReg$(_c5, \"Home\");", "map": {"version": 3, "names": ["React", "useEffect", "useState", "useDispatch", "useSelector", "Container", "Grid", "Typography", "Card", "CardMedia", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "Box", "TextField", "InputAdornment", "Paper", "Chip", "Rating", "IconButton", "Badge", "Avatar", "Divider", "CircularProgress", "<PERSON><PERSON>", "Tabs", "Tab", "FormControl", "InputLabel", "Select", "MenuItem", "Slide<PERSON>", "Search", "ShoppingCart", "Favorite", "FavoriteBorder", "Star", "LocalShipping", "Security", "Assignment", "FilterList", "Sort", "CompareArrows", "Share", "LocalOffer", "styled", "productsAPI", "cartAPI", "handleAPIError", "useAuth", "useNavigate", "jsxDEV", "_jsxDEV", "Fragment", "_Fragment", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "theme", "backgroundColor", "color", "padding", "spacing", "boxShadow", "_c", "CategoryCard", "textAlign", "cursor", "transition", "transform", "_c2", "ProductCard", "height", "display", "flexDirection", "_c3", "OfferBanner", "background", "borderRadius", "margin", "_c4", "Home", "_s", "_user$name", "dispatch", "navigate", "user", "isAuthenticated", "logout", "products", "setProducts", "loading", "setLoading", "error", "setError", "searchTerm", "setSearchTerm", "selectedCate<PERSON><PERSON>", "setSelectedCategory", "priceRange", "setPriceRange", "sortBy", "setSortBy", "favorites", "setFavorites", "Set", "cartItems", "setCartItems", "tabValue", "setTabValue", "fetchProducts", "response", "getAll", "data", "err", "getMockProducts", "cart", "getCart", "items", "_id", "name", "price", "image", "rating", "numReviews", "category", "stock", "origin", "weight", "unit", "featured", "description", "categories", "icon", "count", "length", "filter", "p", "handleAddToCart", "product", "addToCart", "type", "payload", "toggleFavorite", "productId", "newFavorites", "has", "delete", "add", "filteredProducts", "matchesSearch", "toLowerCase", "includes", "matchesCategory", "matchesPrice", "sort", "a", "b", "Date", "createdAt", "sx", "justifyContent", "alignItems", "children", "size", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "minHeight", "max<PERSON><PERSON><PERSON>", "container", "item", "xs", "md", "variant", "fontWeight", "mr", "fontStyle", "style", "fullWidth", "placeholder", "value", "onChange", "e", "target", "InputProps", "startAdornment", "position", "gap", "startIcon", "onClick", "isAdmin", "badgeContent", "bgcolor", "width", "char<PERSON>t", "toUpperCase", "mt", "mb", "map", "sm", "border", "label", "newValue", "valueLabelDisplay", "min", "max", "borderColor", "pb", "severity", "lg", "component", "alt", "objectFit", "top", "right", "left", "fontSize", "flexGrow", "overflow", "textOverflow", "whiteSpace", "readOnly", "ml", "toFixed", "textDecoration", "py", "_c5", "$RefreshReg$"], "sources": ["D:/ecommerce/client/src/components/Home.js"], "sourcesContent": ["import React, { useEffect, useState } from 'react';\r\nimport { useDispatch, useSelector } from 'react-redux';\r\nimport {\r\n  Container,\r\n  Grid,\r\n  Typography,\r\n  Card,\r\n  CardMedia,\r\n  CardContent,\r\n  Button,\r\n  Box,\r\n  TextField,\r\n  InputAdornment,\r\n  Paper,\r\n  Chip,\r\n  Rating,\r\n  IconButton,\r\n  Badge,\r\n  Avatar,\r\n  Divider,\r\n  CircularProgress,\r\n  Alert,\r\n  Tabs,\r\n  Tab,\r\n  FormControl,\r\n  InputLabel,\r\n  Select,\r\n  MenuItem,\r\n  Slider\r\n} from '@mui/material';\r\nimport {\r\n  Search,\r\n  ShoppingCart,\r\n  Favorite,\r\n  FavoriteBorder,\r\n  Star,\r\n  LocalShipping,\r\n  Security,\r\n  Assignment,\r\n  FilterList,\r\n  Sort,\r\n  CompareArrows,\r\n  Share,\r\n  LocalOffer\r\n} from '@mui/icons-material';\r\nimport { styled } from '@mui/material/styles';\r\nimport { productsAPI, cartAPI, handleAPIError } from '../services/api';\r\nimport { useAuth } from '../context/AuthContext';\r\nimport { useNavigate } from 'react-router-dom';\r\n\r\n// Flipkart-style styled components\r\nconst FlipkartHeader = styled(Paper)(({ theme }) => ({\r\n  backgroundColor: '#2874f0',\r\n  color: 'white',\r\n  padding: theme.spacing(1, 0),\r\n  boxShadow: '0 2px 4px rgba(0,0,0,0.1)',\r\n}));\r\n\r\nconst CategoryCard = styled(Card)(({ theme }) => ({\r\n  textAlign: 'center',\r\n  padding: theme.spacing(2),\r\n  cursor: 'pointer',\r\n  transition: 'all 0.3s ease',\r\n  '&:hover': {\r\n    transform: 'translateY(-4px)',\r\n    boxShadow: '0 8px 25px rgba(0,0,0,0.15)',\r\n  },\r\n}));\r\n\r\nconst ProductCard = styled(Card)(({ theme }) => ({\r\n  height: '100%',\r\n  display: 'flex',\r\n  flexDirection: 'column',\r\n  transition: 'all 0.3s ease',\r\n  cursor: 'pointer',\r\n  '&:hover': {\r\n    transform: 'translateY(-4px)',\r\n    boxShadow: '0 8px 25px rgba(0,0,0,0.15)',\r\n  },\r\n}));\r\n\r\nconst OfferBanner = styled(Box)(({ theme }) => ({\r\n  background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',\r\n  color: 'white',\r\n  padding: theme.spacing(3),\r\n  borderRadius: theme.spacing(1),\r\n  textAlign: 'center',\r\n  margin: theme.spacing(2, 0),\r\n}));\r\n\r\nconst Home = () => {\r\n  const dispatch = useDispatch();\r\n  const navigate = useNavigate();\r\n  const { user, isAuthenticated, logout } = useAuth();\r\n  const [products, setProducts] = useState([]);\r\n  const [loading, setLoading] = useState(true);\r\n  const [error, setError] = useState(null);\r\n  const [searchTerm, setSearchTerm] = useState('');\r\n  const [selectedCategory, setSelectedCategory] = useState('All');\r\n  const [priceRange, setPriceRange] = useState([0, 100]);\r\n  const [sortBy, setSortBy] = useState('featured');\r\n  const [favorites, setFavorites] = useState(new Set());\r\n  const [cartItems, setCartItems] = useState([]);\r\n  const [tabValue, setTabValue] = useState(0);\r\n\r\n  // Fetch products from API\r\n  useEffect(() => {\r\n    const fetchProducts = async () => {\r\n      try {\r\n        setLoading(true);\r\n        const response = await productsAPI.getAll();\r\n        setProducts(response.data);\r\n        setError(null);\r\n      } catch (err) {\r\n        setError(handleAPIError(err));\r\n        // Fallback to mock data\r\n        setProducts(getMockProducts());\r\n      } finally {\r\n        setLoading(false);\r\n      }\r\n    };\r\n\r\n    fetchProducts();\r\n\r\n    // Load cart items\r\n    const cart = cartAPI.getCart();\r\n    setCartItems(cart.items);\r\n  }, []);\r\n\r\n  // Mock data fallback\r\n  const getMockProducts = () => [\r\n    {\r\n      _id: '1',\r\n      name: 'Ceylon Cinnamon Sticks',\r\n      price: 15.99,\r\n      image: 'https://images.unsplash.com/photo-1506905925346-21bda4d32df4?w=300&h=300&fit=crop',\r\n      rating: 4.8,\r\n      numReviews: 124,\r\n      category: 'Whole Spices',\r\n      stock: 50,\r\n      origin: 'Sri Lanka',\r\n      weight: 100,\r\n      unit: 'g',\r\n      featured: true,\r\n      description: 'Premium quality Ceylon cinnamon sticks from Sri Lanka'\r\n    },\r\n    {\r\n      _id: '2',\r\n      name: 'Black Peppercorns',\r\n      price: 12.50,\r\n      image: 'https://images.unsplash.com/photo-1596040033229-a9821ebd058d?w=300&h=300&fit=crop',\r\n      rating: 4.9,\r\n      numReviews: 89,\r\n      category: 'Whole Spices',\r\n      stock: 30,\r\n      origin: 'India',\r\n      weight: 50,\r\n      unit: 'g',\r\n      featured: false,\r\n      description: 'Freshly ground black peppercorns with intense flavor'\r\n    },\r\n    {\r\n      _id: '3',\r\n      name: 'Organic Turmeric Powder',\r\n      price: 18.75,\r\n      image: 'https://images.unsplash.com/photo-1615485500704-8e990f9900f7?w=300&h=300&fit=crop',\r\n      rating: 4.7,\r\n      numReviews: 156,\r\n      category: 'Ground Spices',\r\n      stock: 25,\r\n      origin: 'India',\r\n      weight: 200,\r\n      unit: 'g',\r\n      featured: true,\r\n      description: 'Pure organic turmeric powder with anti-inflammatory properties'\r\n    },\r\n    {\r\n      _id: '4',\r\n      name: 'Green Cardamom Pods',\r\n      price: 24.99,\r\n      image: 'https://images.unsplash.com/photo-1596040033229-a9821ebd058d?w=300&h=300&fit=crop',\r\n      rating: 4.6,\r\n      numReviews: 78,\r\n      category: 'Whole Spices',\r\n      stock: 40,\r\n      origin: 'Guatemala',\r\n      weight: 50,\r\n      unit: 'g',\r\n      featured: false,\r\n      description: 'Aromatic green cardamom pods perfect for tea and desserts'\r\n    },\r\n    {\r\n      _id: '5',\r\n      name: 'Star Anise',\r\n      price: 16.99,\r\n      image: 'https://images.unsplash.com/photo-1506905925346-21bda4d32df4?w=300&h=300&fit=crop',\r\n      rating: 4.5,\r\n      numReviews: 45,\r\n      category: 'Whole Spices',\r\n      stock: 35,\r\n      origin: 'China',\r\n      weight: 75,\r\n      unit: 'g',\r\n      featured: false,\r\n      description: 'Whole star anise with sweet licorice flavor'\r\n    },\r\n    {\r\n      _id: '6',\r\n      name: 'Saffron Threads',\r\n      price: 89.99,\r\n      image: 'https://images.unsplash.com/photo-1615485500704-8e990f9900f7?w=300&h=300&fit=crop',\r\n      rating: 4.9,\r\n      numReviews: 234,\r\n      category: 'Herbs',\r\n      stock: 10,\r\n      origin: 'Kashmir',\r\n      weight: 1,\r\n      unit: 'g',\r\n      featured: true,\r\n      description: 'Premium saffron threads - the most expensive spice in the world'\r\n    }\r\n  ];\r\n\r\n  const categories = [\r\n    { name: 'All', icon: '🛒', count: products.length },\r\n    { name: 'Whole Spices', icon: '🌿', count: products.filter(p => p.category === 'Whole Spices').length },\r\n    { name: 'Ground Spices', icon: '🥄', count: products.filter(p => p.category === 'Ground Spices').length },\r\n    { name: 'Herbs', icon: '🌱', count: products.filter(p => p.category === 'Herbs').length },\r\n    { name: 'Spice Blends', icon: '🍛', count: products.filter(p => p.category === 'Spice Blends').length },\r\n    { name: 'Seasonings', icon: '🧂', count: products.filter(p => p.category === 'Seasonings').length }\r\n  ];\r\n\r\n  const handleAddToCart = (product) => {\r\n    const cart = cartAPI.addToCart(product);\r\n    setCartItems(cart.items);\r\n    dispatch({ type: 'ADD_TO_CART', payload: product });\r\n  };\r\n\r\n  const toggleFavorite = (productId) => {\r\n    const newFavorites = new Set(favorites);\r\n    if (newFavorites.has(productId)) {\r\n      newFavorites.delete(productId);\r\n    } else {\r\n      newFavorites.add(productId);\r\n    }\r\n    setFavorites(newFavorites);\r\n  };\r\n\r\n  // Filter and sort products\r\n  const filteredProducts = products\r\n    .filter(product => {\r\n      const matchesSearch = product.name.toLowerCase().includes(searchTerm.toLowerCase());\r\n      const matchesCategory = selectedCategory === 'All' || product.category === selectedCategory;\r\n      const matchesPrice = product.price >= priceRange[0] && product.price <= priceRange[1];\r\n      return matchesSearch && matchesCategory && matchesPrice;\r\n    })\r\n    .sort((a, b) => {\r\n      switch (sortBy) {\r\n        case 'price-low': return a.price - b.price;\r\n        case 'price-high': return b.price - a.price;\r\n        case 'rating': return b.rating - a.rating;\r\n        case 'newest': return new Date(b.createdAt) - new Date(a.createdAt);\r\n        default: return b.featured - a.featured;\r\n      }\r\n    });\r\n\r\n  if (loading) {\r\n    return (\r\n      <Box sx={{ display: 'flex', justifyContent: 'center', alignItems: 'center', height: '50vh' }}>\r\n        <CircularProgress size={60} />\r\n      </Box>\r\n    );\r\n  }\r\n\r\n  return (\r\n    <Box sx={{ backgroundColor: '#f1f3f6', minHeight: '100vh' }}>\r\n      {/* Flipkart-style Header */}\r\n      <FlipkartHeader>\r\n        <Container maxWidth=\"xl\">\r\n          <Grid container alignItems=\"center\" spacing={2}>\r\n            <Grid item xs={12} md={3}>\r\n              <Box sx={{ display: 'flex', alignItems: 'center' }}>\r\n                <Typography variant=\"h5\" sx={{ fontWeight: 'bold', mr: 2 }}>\r\n                  🛒 SpiceMart\r\n                </Typography>\r\n                <Typography variant=\"caption\" sx={{ fontStyle: 'italic' }}>\r\n                  Explore <span style={{ color: '#ffe500' }}>Plus</span>\r\n                </Typography>\r\n              </Box>\r\n            </Grid>\r\n\r\n            <Grid item xs={12} md={5}>\r\n              <TextField\r\n                fullWidth\r\n                placeholder=\"Search for spices, herbs and more...\"\r\n                value={searchTerm}\r\n                onChange={(e) => setSearchTerm(e.target.value)}\r\n                InputProps={{\r\n                  startAdornment: (\r\n                    <InputAdornment position=\"start\">\r\n                      <Search sx={{ color: '#2874f0' }} />\r\n                    </InputAdornment>\r\n                  ),\r\n                  sx: { backgroundColor: 'white', borderRadius: 1 }\r\n                }}\r\n                size=\"small\"\r\n              />\r\n            </Grid>\r\n\r\n            <Grid item xs={12} md={4}>\r\n              <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'flex-end', gap: 2 }}>\r\n                <Button color=\"inherit\" startIcon={<Assignment />}>\r\n                  Become a Seller\r\n                </Button>\r\n\r\n                {isAuthenticated ? (\r\n                  <>\r\n                    <Button\r\n                      color=\"inherit\"\r\n                      onClick={() => navigate(user?.isAdmin ? '/admin' : '/dashboard')}\r\n                    >\r\n                      {user?.isAdmin ? 'Admin' : 'Dashboard'}\r\n                    </Button>\r\n                    <Badge badgeContent={cartItems.length} color=\"error\">\r\n                      <IconButton color=\"inherit\" onClick={() => navigate('/cart')}>\r\n                        <ShoppingCart />\r\n                      </IconButton>\r\n                    </Badge>\r\n                    <IconButton\r\n                      color=\"inherit\"\r\n                      onClick={() => navigate('/dashboard')}\r\n                    >\r\n                      <Avatar sx={{ bgcolor: '#ff6f00', width: 32, height: 32 }}>\r\n                        {user?.name?.charAt(0).toUpperCase()}\r\n                      </Avatar>\r\n                    </IconButton>\r\n                    <Button color=\"inherit\" onClick={logout}>\r\n                      Logout\r\n                    </Button>\r\n                  </>\r\n                ) : (\r\n                  <>\r\n                    <Button color=\"inherit\" onClick={() => navigate('/login')}>\r\n                      Login\r\n                    </Button>\r\n                    <Button\r\n                      color=\"inherit\"\r\n                      onClick={() => navigate('/register')}\r\n                      sx={{\r\n                        backgroundColor: 'rgba(255,255,255,0.1)',\r\n                        '&:hover': { backgroundColor: 'rgba(255,255,255,0.2)' }\r\n                      }}\r\n                    >\r\n                      Sign Up\r\n                    </Button>\r\n                    <Badge badgeContent={cartItems.length} color=\"error\">\r\n                      <IconButton color=\"inherit\" onClick={() => navigate('/cart')}>\r\n                        <ShoppingCart />\r\n                      </IconButton>\r\n                    </Badge>\r\n                  </>\r\n                )}\r\n              </Box>\r\n            </Grid>\r\n          </Grid>\r\n        </Container>\r\n      </FlipkartHeader>\r\n\r\n      {/* Offer Banner */}\r\n      <Container maxWidth=\"xl\" sx={{ mt: 2 }}>\r\n        <OfferBanner>\r\n          <Typography variant=\"h4\" sx={{ fontWeight: 'bold', mb: 1 }}>\r\n            🎉 Big Billion Days Sale! 🎉\r\n          </Typography>\r\n          <Typography variant=\"h6\">\r\n            Up to 80% OFF on Premium Spices | Free Delivery | No Cost EMI\r\n          </Typography>\r\n        </OfferBanner>\r\n      </Container>\r\n\r\n      {/* Categories Section */}\r\n      <Container maxWidth=\"xl\" sx={{ mt: 3 }}>\r\n        <Paper sx={{ p: 2 }}>\r\n          <Typography variant=\"h6\" sx={{ mb: 2, fontWeight: 'bold' }}>\r\n            Shop by Category\r\n          </Typography>\r\n          <Grid container spacing={2}>\r\n            {categories.map((category) => (\r\n              <Grid item xs={6} sm={4} md={2} key={category.name}>\r\n                <CategoryCard\r\n                  onClick={() => setSelectedCategory(category.name)}\r\n                  sx={{\r\n                    backgroundColor: selectedCategory === category.name ? '#e3f2fd' : 'white',\r\n                    border: selectedCategory === category.name ? '2px solid #2874f0' : '1px solid #e0e0e0'\r\n                  }}\r\n                >\r\n                  <Typography variant=\"h4\" sx={{ mb: 1 }}>\r\n                    {category.icon}\r\n                  </Typography>\r\n                  <Typography variant=\"body2\" sx={{ fontWeight: 'bold' }}>\r\n                    {category.name}\r\n                  </Typography>\r\n                  <Typography variant=\"caption\" color=\"textSecondary\">\r\n                    ({category.count} items)\r\n                  </Typography>\r\n                </CategoryCard>\r\n              </Grid>\r\n            ))}\r\n          </Grid>\r\n        </Paper>\r\n      </Container>\r\n\r\n      {/* Filters and Sort */}\r\n      <Container maxWidth=\"xl\" sx={{ mt: 2 }}>\r\n        <Paper sx={{ p: 2 }}>\r\n          <Grid container spacing={2} alignItems=\"center\">\r\n            <Grid item xs={12} md={3}>\r\n              <FormControl fullWidth size=\"small\">\r\n                <InputLabel>Sort By</InputLabel>\r\n                <Select\r\n                  value={sortBy}\r\n                  label=\"Sort By\"\r\n                  onChange={(e) => setSortBy(e.target.value)}\r\n                >\r\n                  <MenuItem value=\"featured\">Featured</MenuItem>\r\n                  <MenuItem value=\"price-low\">Price: Low to High</MenuItem>\r\n                  <MenuItem value=\"price-high\">Price: High to Low</MenuItem>\r\n                  <MenuItem value=\"rating\">Customer Rating</MenuItem>\r\n                  <MenuItem value=\"newest\">Newest First</MenuItem>\r\n                </Select>\r\n              </FormControl>\r\n            </Grid>\r\n\r\n            <Grid item xs={12} md={4}>\r\n              <Typography variant=\"body2\" sx={{ mb: 1 }}>\r\n                Price Range: ${priceRange[0]} - ${priceRange[1]}\r\n              </Typography>\r\n              <Slider\r\n                value={priceRange}\r\n                onChange={(e, newValue) => setPriceRange(newValue)}\r\n                valueLabelDisplay=\"auto\"\r\n                min={0}\r\n                max={100}\r\n                sx={{ color: '#2874f0' }}\r\n              />\r\n            </Grid>\r\n\r\n            <Grid item xs={12} md={5}>\r\n              <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>\r\n                <Chip\r\n                  icon={<FilterList />}\r\n                  label=\"Filters\"\r\n                  variant=\"outlined\"\r\n                  sx={{ color: '#2874f0', borderColor: '#2874f0' }}\r\n                />\r\n                <Chip\r\n                  icon={<LocalShipping />}\r\n                  label=\"Free Delivery\"\r\n                  variant=\"outlined\"\r\n                  color=\"success\"\r\n                />\r\n                <Chip\r\n                  icon={<Security />}\r\n                  label=\"Assured\"\r\n                  variant=\"outlined\"\r\n                  color=\"primary\"\r\n                />\r\n              </Box>\r\n            </Grid>\r\n          </Grid>\r\n        </Paper>\r\n      </Container>\r\n\r\n      {/* Products Grid */}\r\n      <Container maxWidth=\"xl\" sx={{ mt: 2, pb: 4 }}>\r\n        {error && (\r\n          <Alert severity=\"warning\" sx={{ mb: 2 }}>\r\n            {error} - Showing sample data\r\n          </Alert>\r\n        )}\r\n\r\n        <Typography variant=\"h6\" sx={{ mb: 2, fontWeight: 'bold' }}>\r\n          {selectedCategory === 'All' ? 'All Products' : selectedCategory}\r\n          <span style={{ color: '#878787', fontWeight: 'normal' }}>\r\n            ({filteredProducts.length} items)\r\n          </span>\r\n        </Typography>\r\n\r\n        <Grid container spacing={2}>\r\n          {filteredProducts.map((product) => (\r\n            <Grid item xs={6} sm={4} md={3} lg={2.4} key={product._id}>\r\n              <ProductCard>\r\n                <Box sx={{ position: 'relative' }}>\r\n                  <CardMedia\r\n                    component=\"img\"\r\n                    height=\"200\"\r\n                    image={product.image}\r\n                    alt={product.name}\r\n                    sx={{ objectFit: 'cover' }}\r\n                  />\r\n                  <IconButton\r\n                    sx={{\r\n                      position: 'absolute',\r\n                      top: 8,\r\n                      right: 8,\r\n                      backgroundColor: 'rgba(255,255,255,0.8)',\r\n                      '&:hover': { backgroundColor: 'rgba(255,255,255,0.9)' }\r\n                    }}\r\n                    onClick={() => toggleFavorite(product._id)}\r\n                  >\r\n                    {favorites.has(product._id) ?\r\n                      <Favorite sx={{ color: '#ff6b6b' }} /> :\r\n                      <FavoriteBorder />\r\n                    }\r\n                  </IconButton>\r\n                  {product.featured && (\r\n                    <Chip\r\n                      label=\"Bestseller\"\r\n                      color=\"error\"\r\n                      size=\"small\"\r\n                      sx={{\r\n                        position: 'absolute',\r\n                        top: 8,\r\n                        left: 8,\r\n                        fontSize: '0.7rem'\r\n                      }}\r\n                    />\r\n                  )}\r\n                </Box>\r\n\r\n                <CardContent sx={{ flexGrow: 1, p: 2 }}>\r\n                  <Typography\r\n                    variant=\"body2\"\r\n                    sx={{\r\n                      fontWeight: 'bold',\r\n                      overflow: 'hidden',\r\n                      textOverflow: 'ellipsis',\r\n                      whiteSpace: 'nowrap',\r\n                      mb: 1\r\n                    }}\r\n                  >\r\n                    {product.name}\r\n                  </Typography>\r\n\r\n                  <Box sx={{ display: 'flex', alignItems: 'center', mb: 1 }}>\r\n                    <Rating value={product.rating || 4.5} readOnly size=\"small\" />\r\n                    <Typography variant=\"caption\" sx={{ ml: 1, color: '#878787' }}>\r\n                      ({product.numReviews || 0})\r\n                    </Typography>\r\n                  </Box>\r\n\r\n                  <Box sx={{ display: 'flex', alignItems: 'center', mb: 1 }}>\r\n                    <Typography variant=\"h6\" sx={{ fontWeight: 'bold', color: '#212121' }}>\r\n                      ₹{(product.price * 75).toFixed(0)}\r\n                    </Typography>\r\n                    <Typography\r\n                      variant=\"caption\"\r\n                      sx={{\r\n                        ml: 1,\r\n                        textDecoration: 'line-through',\r\n                        color: '#878787'\r\n                      }}\r\n                    >\r\n                      ₹{(product.price * 100).toFixed(0)}\r\n                    </Typography>\r\n                    <Typography variant=\"caption\" sx={{ ml: 1, color: '#388e3c', fontWeight: 'bold' }}>\r\n                      25% off\r\n                    </Typography>\r\n                  </Box>\r\n\r\n                  <Typography variant=\"caption\" sx={{ color: '#878787', display: 'block', mb: 1 }}>\r\n                    {product.weight}{product.unit} | {product.origin}\r\n                  </Typography>\r\n\r\n                  <Box sx={{ display: 'flex', gap: 1 }}>\r\n                    <Button\r\n                      variant=\"contained\"\r\n                      size=\"small\"\r\n                      fullWidth\r\n                      onClick={() => handleAddToCart(product)}\r\n                      sx={{\r\n                        backgroundColor: '#ff9f00',\r\n                        '&:hover': { backgroundColor: '#e68900' },\r\n                        fontSize: '0.75rem',\r\n                        py: 0.5\r\n                      }}\r\n                    >\r\n                      ADD TO CART\r\n                    </Button>\r\n                  </Box>\r\n\r\n                  <Box sx={{ display: 'flex', alignItems: 'center', mt: 1, gap: 1 }}>\r\n                    <LocalShipping sx={{ fontSize: 14, color: '#388e3c' }} />\r\n                    <Typography variant=\"caption\" sx={{ color: '#388e3c' }}>\r\n                      Free Delivery\r\n                    </Typography>\r\n                  </Box>\r\n                </CardContent>\r\n              </ProductCard>\r\n            </Grid>\r\n          ))}\r\n        </Grid>\r\n\r\n        {filteredProducts.length === 0 && (\r\n          <Box sx={{ textAlign: 'center', py: 8 }}>\r\n            <Typography variant=\"h6\" color=\"textSecondary\">\r\n              No products found matching your criteria\r\n            </Typography>\r\n            <Button\r\n              variant=\"outlined\"\r\n              sx={{ mt: 2 }}\r\n              onClick={() => {\r\n                setSearchTerm('');\r\n                setSelectedCategory('All');\r\n                setPriceRange([0, 100]);\r\n              }}\r\n            >\r\n              Clear Filters\r\n            </Button>\r\n          </Box>\r\n        )}\r\n      </Container>\r\n    </Box>\r\n  );\r\n};\r\n\r\nexport default Home;"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,SAAS,EAAEC,QAAQ,QAAQ,OAAO;AAClD,SAASC,WAAW,EAAEC,WAAW,QAAQ,aAAa;AACtD,SACEC,SAAS,EACTC,IAAI,EACJC,UAAU,EACVC,IAAI,EACJC,SAAS,EACTC,WAAW,EACXC,MAAM,EACNC,GAAG,EACHC,SAAS,EACTC,cAAc,EACdC,KAAK,EACLC,IAAI,EACJC,MAAM,EACNC,UAAU,EACVC,KAAK,EACLC,MAAM,EACNC,OAAO,EACPC,gBAAgB,EAChBC,KAAK,EACLC,IAAI,EACJC,GAAG,EACHC,WAAW,EACXC,UAAU,EACVC,MAAM,EACNC,QAAQ,EACRC,MAAM,QACD,eAAe;AACtB,SACEC,MAAM,EACNC,YAAY,EACZC,QAAQ,EACRC,cAAc,EACdC,IAAI,EACJC,aAAa,EACbC,QAAQ,EACRC,UAAU,EACVC,UAAU,EACVC,IAAI,EACJC,aAAa,EACbC,KAAK,EACLC,UAAU,QACL,qBAAqB;AAC5B,SAASC,MAAM,QAAQ,sBAAsB;AAC7C,SAASC,WAAW,EAAEC,OAAO,EAAEC,cAAc,QAAQ,iBAAiB;AACtE,SAASC,OAAO,QAAQ,wBAAwB;AAChD,SAASC,WAAW,QAAQ,kBAAkB;;AAE9C;AAAA,SAAAC,MAAA,IAAAC,OAAA,EAAAC,QAAA,IAAAC,SAAA;AACA,MAAMC,cAAc,GAAGV,MAAM,CAAC7B,KAAK,CAAC,CAAC,CAAC;EAAEwC;AAAM,CAAC,MAAM;EACnDC,eAAe,EAAE,SAAS;EAC1BC,KAAK,EAAE,OAAO;EACdC,OAAO,EAAEH,KAAK,CAACI,OAAO,CAAC,CAAC,EAAE,CAAC,CAAC;EAC5BC,SAAS,EAAE;AACb,CAAC,CAAC,CAAC;AAACC,EAAA,GALEP,cAAc;AAOpB,MAAMQ,YAAY,GAAGlB,MAAM,CAACpC,IAAI,CAAC,CAAC,CAAC;EAAE+C;AAAM,CAAC,MAAM;EAChDQ,SAAS,EAAE,QAAQ;EACnBL,OAAO,EAAEH,KAAK,CAACI,OAAO,CAAC,CAAC,CAAC;EACzBK,MAAM,EAAE,SAAS;EACjBC,UAAU,EAAE,eAAe;EAC3B,SAAS,EAAE;IACTC,SAAS,EAAE,kBAAkB;IAC7BN,SAAS,EAAE;EACb;AACF,CAAC,CAAC,CAAC;AAACO,GAAA,GATEL,YAAY;AAWlB,MAAMM,WAAW,GAAGxB,MAAM,CAACpC,IAAI,CAAC,CAAC,CAAC;EAAE+C;AAAM,CAAC,MAAM;EAC/Cc,MAAM,EAAE,MAAM;EACdC,OAAO,EAAE,MAAM;EACfC,aAAa,EAAE,QAAQ;EACvBN,UAAU,EAAE,eAAe;EAC3BD,MAAM,EAAE,SAAS;EACjB,SAAS,EAAE;IACTE,SAAS,EAAE,kBAAkB;IAC7BN,SAAS,EAAE;EACb;AACF,CAAC,CAAC,CAAC;AAACY,GAAA,GAVEJ,WAAW;AAYjB,MAAMK,WAAW,GAAG7B,MAAM,CAAChC,GAAG,CAAC,CAAC,CAAC;EAAE2C;AAAM,CAAC,MAAM;EAC9CmB,UAAU,EAAE,mDAAmD;EAC/DjB,KAAK,EAAE,OAAO;EACdC,OAAO,EAAEH,KAAK,CAACI,OAAO,CAAC,CAAC,CAAC;EACzBgB,YAAY,EAAEpB,KAAK,CAACI,OAAO,CAAC,CAAC,CAAC;EAC9BI,SAAS,EAAE,QAAQ;EACnBa,MAAM,EAAErB,KAAK,CAACI,OAAO,CAAC,CAAC,EAAE,CAAC;AAC5B,CAAC,CAAC,CAAC;AAACkB,GAAA,GAPEJ,WAAW;AASjB,MAAMK,IAAI,GAAGA,CAAA,KAAM;EAAAC,EAAA;EAAA,IAAAC,UAAA;EACjB,MAAMC,QAAQ,GAAG9E,WAAW,CAAC,CAAC;EAC9B,MAAM+E,QAAQ,GAAGjC,WAAW,CAAC,CAAC;EAC9B,MAAM;IAAEkC,IAAI;IAAEC,eAAe;IAAEC;EAAO,CAAC,GAAGrC,OAAO,CAAC,CAAC;EACnD,MAAM,CAACsC,QAAQ,EAAEC,WAAW,CAAC,GAAGrF,QAAQ,CAAC,EAAE,CAAC;EAC5C,MAAM,CAACsF,OAAO,EAAEC,UAAU,CAAC,GAAGvF,QAAQ,CAAC,IAAI,CAAC;EAC5C,MAAM,CAACwF,KAAK,EAAEC,QAAQ,CAAC,GAAGzF,QAAQ,CAAC,IAAI,CAAC;EACxC,MAAM,CAAC0F,UAAU,EAAEC,aAAa,CAAC,GAAG3F,QAAQ,CAAC,EAAE,CAAC;EAChD,MAAM,CAAC4F,gBAAgB,EAAEC,mBAAmB,CAAC,GAAG7F,QAAQ,CAAC,KAAK,CAAC;EAC/D,MAAM,CAAC8F,UAAU,EAAEC,aAAa,CAAC,GAAG/F,QAAQ,CAAC,CAAC,CAAC,EAAE,GAAG,CAAC,CAAC;EACtD,MAAM,CAACgG,MAAM,EAAEC,SAAS,CAAC,GAAGjG,QAAQ,CAAC,UAAU,CAAC;EAChD,MAAM,CAACkG,SAAS,EAAEC,YAAY,CAAC,GAAGnG,QAAQ,CAAC,IAAIoG,GAAG,CAAC,CAAC,CAAC;EACrD,MAAM,CAACC,SAAS,EAAEC,YAAY,CAAC,GAAGtG,QAAQ,CAAC,EAAE,CAAC;EAC9C,MAAM,CAACuG,QAAQ,EAAEC,WAAW,CAAC,GAAGxG,QAAQ,CAAC,CAAC,CAAC;;EAE3C;EACAD,SAAS,CAAC,MAAM;IACd,MAAM0G,aAAa,GAAG,MAAAA,CAAA,KAAY;MAChC,IAAI;QACFlB,UAAU,CAAC,IAAI,CAAC;QAChB,MAAMmB,QAAQ,GAAG,MAAM/D,WAAW,CAACgE,MAAM,CAAC,CAAC;QAC3CtB,WAAW,CAACqB,QAAQ,CAACE,IAAI,CAAC;QAC1BnB,QAAQ,CAAC,IAAI,CAAC;MAChB,CAAC,CAAC,OAAOoB,GAAG,EAAE;QACZpB,QAAQ,CAAC5C,cAAc,CAACgE,GAAG,CAAC,CAAC;QAC7B;QACAxB,WAAW,CAACyB,eAAe,CAAC,CAAC,CAAC;MAChC,CAAC,SAAS;QACRvB,UAAU,CAAC,KAAK,CAAC;MACnB;IACF,CAAC;IAEDkB,aAAa,CAAC,CAAC;;IAEf;IACA,MAAMM,IAAI,GAAGnE,OAAO,CAACoE,OAAO,CAAC,CAAC;IAC9BV,YAAY,CAACS,IAAI,CAACE,KAAK,CAAC;EAC1B,CAAC,EAAE,EAAE,CAAC;;EAEN;EACA,MAAMH,eAAe,GAAGA,CAAA,KAAM,CAC5B;IACEI,GAAG,EAAE,GAAG;IACRC,IAAI,EAAE,wBAAwB;IAC9BC,KAAK,EAAE,KAAK;IACZC,KAAK,EAAE,mFAAmF;IAC1FC,MAAM,EAAE,GAAG;IACXC,UAAU,EAAE,GAAG;IACfC,QAAQ,EAAE,cAAc;IACxBC,KAAK,EAAE,EAAE;IACTC,MAAM,EAAE,WAAW;IACnBC,MAAM,EAAE,GAAG;IACXC,IAAI,EAAE,GAAG;IACTC,QAAQ,EAAE,IAAI;IACdC,WAAW,EAAE;EACf,CAAC,EACD;IACEZ,GAAG,EAAE,GAAG;IACRC,IAAI,EAAE,mBAAmB;IACzBC,KAAK,EAAE,KAAK;IACZC,KAAK,EAAE,mFAAmF;IAC1FC,MAAM,EAAE,GAAG;IACXC,UAAU,EAAE,EAAE;IACdC,QAAQ,EAAE,cAAc;IACxBC,KAAK,EAAE,EAAE;IACTC,MAAM,EAAE,OAAO;IACfC,MAAM,EAAE,EAAE;IACVC,IAAI,EAAE,GAAG;IACTC,QAAQ,EAAE,KAAK;IACfC,WAAW,EAAE;EACf,CAAC,EACD;IACEZ,GAAG,EAAE,GAAG;IACRC,IAAI,EAAE,yBAAyB;IAC/BC,KAAK,EAAE,KAAK;IACZC,KAAK,EAAE,mFAAmF;IAC1FC,MAAM,EAAE,GAAG;IACXC,UAAU,EAAE,GAAG;IACfC,QAAQ,EAAE,eAAe;IACzBC,KAAK,EAAE,EAAE;IACTC,MAAM,EAAE,OAAO;IACfC,MAAM,EAAE,GAAG;IACXC,IAAI,EAAE,GAAG;IACTC,QAAQ,EAAE,IAAI;IACdC,WAAW,EAAE;EACf,CAAC,EACD;IACEZ,GAAG,EAAE,GAAG;IACRC,IAAI,EAAE,qBAAqB;IAC3BC,KAAK,EAAE,KAAK;IACZC,KAAK,EAAE,mFAAmF;IAC1FC,MAAM,EAAE,GAAG;IACXC,UAAU,EAAE,EAAE;IACdC,QAAQ,EAAE,cAAc;IACxBC,KAAK,EAAE,EAAE;IACTC,MAAM,EAAE,WAAW;IACnBC,MAAM,EAAE,EAAE;IACVC,IAAI,EAAE,GAAG;IACTC,QAAQ,EAAE,KAAK;IACfC,WAAW,EAAE;EACf,CAAC,EACD;IACEZ,GAAG,EAAE,GAAG;IACRC,IAAI,EAAE,YAAY;IAClBC,KAAK,EAAE,KAAK;IACZC,KAAK,EAAE,mFAAmF;IAC1FC,MAAM,EAAE,GAAG;IACXC,UAAU,EAAE,EAAE;IACdC,QAAQ,EAAE,cAAc;IACxBC,KAAK,EAAE,EAAE;IACTC,MAAM,EAAE,OAAO;IACfC,MAAM,EAAE,EAAE;IACVC,IAAI,EAAE,GAAG;IACTC,QAAQ,EAAE,KAAK;IACfC,WAAW,EAAE;EACf,CAAC,EACD;IACEZ,GAAG,EAAE,GAAG;IACRC,IAAI,EAAE,iBAAiB;IACvBC,KAAK,EAAE,KAAK;IACZC,KAAK,EAAE,mFAAmF;IAC1FC,MAAM,EAAE,GAAG;IACXC,UAAU,EAAE,GAAG;IACfC,QAAQ,EAAE,OAAO;IACjBC,KAAK,EAAE,EAAE;IACTC,MAAM,EAAE,SAAS;IACjBC,MAAM,EAAE,CAAC;IACTC,IAAI,EAAE,GAAG;IACTC,QAAQ,EAAE,IAAI;IACdC,WAAW,EAAE;EACf,CAAC,CACF;EAED,MAAMC,UAAU,GAAG,CACjB;IAAEZ,IAAI,EAAE,KAAK;IAAEa,IAAI,EAAE,IAAI;IAAEC,KAAK,EAAE7C,QAAQ,CAAC8C;EAAO,CAAC,EACnD;IAAEf,IAAI,EAAE,cAAc;IAAEa,IAAI,EAAE,IAAI;IAAEC,KAAK,EAAE7C,QAAQ,CAAC+C,MAAM,CAACC,CAAC,IAAIA,CAAC,CAACZ,QAAQ,KAAK,cAAc,CAAC,CAACU;EAAO,CAAC,EACvG;IAAEf,IAAI,EAAE,eAAe;IAAEa,IAAI,EAAE,IAAI;IAAEC,KAAK,EAAE7C,QAAQ,CAAC+C,MAAM,CAACC,CAAC,IAAIA,CAAC,CAACZ,QAAQ,KAAK,eAAe,CAAC,CAACU;EAAO,CAAC,EACzG;IAAEf,IAAI,EAAE,OAAO;IAAEa,IAAI,EAAE,IAAI;IAAEC,KAAK,EAAE7C,QAAQ,CAAC+C,MAAM,CAACC,CAAC,IAAIA,CAAC,CAACZ,QAAQ,KAAK,OAAO,CAAC,CAACU;EAAO,CAAC,EACzF;IAAEf,IAAI,EAAE,cAAc;IAAEa,IAAI,EAAE,IAAI;IAAEC,KAAK,EAAE7C,QAAQ,CAAC+C,MAAM,CAACC,CAAC,IAAIA,CAAC,CAACZ,QAAQ,KAAK,cAAc,CAAC,CAACU;EAAO,CAAC,EACvG;IAAEf,IAAI,EAAE,YAAY;IAAEa,IAAI,EAAE,IAAI;IAAEC,KAAK,EAAE7C,QAAQ,CAAC+C,MAAM,CAACC,CAAC,IAAIA,CAAC,CAACZ,QAAQ,KAAK,YAAY,CAAC,CAACU;EAAO,CAAC,CACpG;EAED,MAAMG,eAAe,GAAIC,OAAO,IAAK;IACnC,MAAMvB,IAAI,GAAGnE,OAAO,CAAC2F,SAAS,CAACD,OAAO,CAAC;IACvChC,YAAY,CAACS,IAAI,CAACE,KAAK,CAAC;IACxBlC,QAAQ,CAAC;MAAEyD,IAAI,EAAE,aAAa;MAAEC,OAAO,EAAEH;IAAQ,CAAC,CAAC;EACrD,CAAC;EAED,MAAMI,cAAc,GAAIC,SAAS,IAAK;IACpC,MAAMC,YAAY,GAAG,IAAIxC,GAAG,CAACF,SAAS,CAAC;IACvC,IAAI0C,YAAY,CAACC,GAAG,CAACF,SAAS,CAAC,EAAE;MAC/BC,YAAY,CAACE,MAAM,CAACH,SAAS,CAAC;IAChC,CAAC,MAAM;MACLC,YAAY,CAACG,GAAG,CAACJ,SAAS,CAAC;IAC7B;IACAxC,YAAY,CAACyC,YAAY,CAAC;EAC5B,CAAC;;EAED;EACA,MAAMI,gBAAgB,GAAG5D,QAAQ,CAC9B+C,MAAM,CAACG,OAAO,IAAI;IACjB,MAAMW,aAAa,GAAGX,OAAO,CAACnB,IAAI,CAAC+B,WAAW,CAAC,CAAC,CAACC,QAAQ,CAACzD,UAAU,CAACwD,WAAW,CAAC,CAAC,CAAC;IACnF,MAAME,eAAe,GAAGxD,gBAAgB,KAAK,KAAK,IAAI0C,OAAO,CAACd,QAAQ,KAAK5B,gBAAgB;IAC3F,MAAMyD,YAAY,GAAGf,OAAO,CAAClB,KAAK,IAAItB,UAAU,CAAC,CAAC,CAAC,IAAIwC,OAAO,CAAClB,KAAK,IAAItB,UAAU,CAAC,CAAC,CAAC;IACrF,OAAOmD,aAAa,IAAIG,eAAe,IAAIC,YAAY;EACzD,CAAC,CAAC,CACDC,IAAI,CAAC,CAACC,CAAC,EAAEC,CAAC,KAAK;IACd,QAAQxD,MAAM;MACZ,KAAK,WAAW;QAAE,OAAOuD,CAAC,CAACnC,KAAK,GAAGoC,CAAC,CAACpC,KAAK;MAC1C,KAAK,YAAY;QAAE,OAAOoC,CAAC,CAACpC,KAAK,GAAGmC,CAAC,CAACnC,KAAK;MAC3C,KAAK,QAAQ;QAAE,OAAOoC,CAAC,CAAClC,MAAM,GAAGiC,CAAC,CAACjC,MAAM;MACzC,KAAK,QAAQ;QAAE,OAAO,IAAImC,IAAI,CAACD,CAAC,CAACE,SAAS,CAAC,GAAG,IAAID,IAAI,CAACF,CAAC,CAACG,SAAS,CAAC;MACnE;QAAS,OAAOF,CAAC,CAAC3B,QAAQ,GAAG0B,CAAC,CAAC1B,QAAQ;IACzC;EACF,CAAC,CAAC;EAEJ,IAAIvC,OAAO,EAAE;IACX,oBACErC,OAAA,CAACvC,GAAG;MAACiJ,EAAE,EAAE;QAAEvF,OAAO,EAAE,MAAM;QAAEwF,cAAc,EAAE,QAAQ;QAAEC,UAAU,EAAE,QAAQ;QAAE1F,MAAM,EAAE;MAAO,CAAE;MAAA2F,QAAA,eAC3F7G,OAAA,CAAC7B,gBAAgB;QAAC2I,IAAI,EAAE;MAAG;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAC3B,CAAC;EAEV;EAEA,oBACElH,OAAA,CAACvC,GAAG;IAACiJ,EAAE,EAAE;MAAErG,eAAe,EAAE,SAAS;MAAE8G,SAAS,EAAE;IAAQ,CAAE;IAAAN,QAAA,gBAE1D7G,OAAA,CAACG,cAAc;MAAA0G,QAAA,eACb7G,OAAA,CAAC9C,SAAS;QAACkK,QAAQ,EAAC,IAAI;QAAAP,QAAA,eACtB7G,OAAA,CAAC7C,IAAI;UAACkK,SAAS;UAACT,UAAU,EAAC,QAAQ;UAACpG,OAAO,EAAE,CAAE;UAAAqG,QAAA,gBAC7C7G,OAAA,CAAC7C,IAAI;YAACmK,IAAI;YAACC,EAAE,EAAE,EAAG;YAACC,EAAE,EAAE,CAAE;YAAAX,QAAA,eACvB7G,OAAA,CAACvC,GAAG;cAACiJ,EAAE,EAAE;gBAAEvF,OAAO,EAAE,MAAM;gBAAEyF,UAAU,EAAE;cAAS,CAAE;cAAAC,QAAA,gBACjD7G,OAAA,CAAC5C,UAAU;gBAACqK,OAAO,EAAC,IAAI;gBAACf,EAAE,EAAE;kBAAEgB,UAAU,EAAE,MAAM;kBAAEC,EAAE,EAAE;gBAAE,CAAE;gBAAAd,QAAA,EAAC;cAE5D;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAY,CAAC,eACblH,OAAA,CAAC5C,UAAU;gBAACqK,OAAO,EAAC,SAAS;gBAACf,EAAE,EAAE;kBAAEkB,SAAS,EAAE;gBAAS,CAAE;gBAAAf,QAAA,GAAC,UACjD,eAAA7G,OAAA;kBAAM6H,KAAK,EAAE;oBAAEvH,KAAK,EAAE;kBAAU,CAAE;kBAAAuG,QAAA,EAAC;gBAAI;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC5C,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACV;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACF,CAAC,eAEPlH,OAAA,CAAC7C,IAAI;YAACmK,IAAI;YAACC,EAAE,EAAE,EAAG;YAACC,EAAE,EAAE,CAAE;YAAAX,QAAA,eACvB7G,OAAA,CAACtC,SAAS;cACRoK,SAAS;cACTC,WAAW,EAAC,sCAAsC;cAClDC,KAAK,EAAEvF,UAAW;cAClBwF,QAAQ,EAAGC,CAAC,IAAKxF,aAAa,CAACwF,CAAC,CAACC,MAAM,CAACH,KAAK,CAAE;cAC/CI,UAAU,EAAE;gBACVC,cAAc,eACZrI,OAAA,CAACrC,cAAc;kBAAC2K,QAAQ,EAAC,OAAO;kBAAAzB,QAAA,eAC9B7G,OAAA,CAACpB,MAAM;oBAAC8H,EAAE,EAAE;sBAAEpG,KAAK,EAAE;oBAAU;kBAAE;oBAAAyG,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACtB,CACjB;gBACDR,EAAE,EAAE;kBAAErG,eAAe,EAAE,OAAO;kBAAEmB,YAAY,EAAE;gBAAE;cAClD,CAAE;cACFsF,IAAI,EAAC;YAAO;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACb;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACE,CAAC,eAEPlH,OAAA,CAAC7C,IAAI;YAACmK,IAAI;YAACC,EAAE,EAAE,EAAG;YAACC,EAAE,EAAE,CAAE;YAAAX,QAAA,eACvB7G,OAAA,CAACvC,GAAG;cAACiJ,EAAE,EAAE;gBAAEvF,OAAO,EAAE,MAAM;gBAAEyF,UAAU,EAAE,QAAQ;gBAAED,cAAc,EAAE,UAAU;gBAAE4B,GAAG,EAAE;cAAE,CAAE;cAAA1B,QAAA,gBACrF7G,OAAA,CAACxC,MAAM;gBAAC8C,KAAK,EAAC,SAAS;gBAACkI,SAAS,eAAExI,OAAA,CAACb,UAAU;kBAAA4H,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAE;gBAAAL,QAAA,EAAC;cAEnD;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC,EAERjF,eAAe,gBACdjC,OAAA,CAAAE,SAAA;gBAAA2G,QAAA,gBACE7G,OAAA,CAACxC,MAAM;kBACL8C,KAAK,EAAC,SAAS;kBACfmI,OAAO,EAAEA,CAAA,KAAM1G,QAAQ,CAACC,IAAI,aAAJA,IAAI,eAAJA,IAAI,CAAE0G,OAAO,GAAG,QAAQ,GAAG,YAAY,CAAE;kBAAA7B,QAAA,EAEhE7E,IAAI,aAAJA,IAAI,eAAJA,IAAI,CAAE0G,OAAO,GAAG,OAAO,GAAG;gBAAW;kBAAA3B,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAChC,CAAC,eACTlH,OAAA,CAAChC,KAAK;kBAAC2K,YAAY,EAAEvF,SAAS,CAAC6B,MAAO;kBAAC3E,KAAK,EAAC,OAAO;kBAAAuG,QAAA,eAClD7G,OAAA,CAACjC,UAAU;oBAACuC,KAAK,EAAC,SAAS;oBAACmI,OAAO,EAAEA,CAAA,KAAM1G,QAAQ,CAAC,OAAO,CAAE;oBAAA8E,QAAA,eAC3D7G,OAAA,CAACnB,YAAY;sBAAAkI,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAE;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACN;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACR,CAAC,eACRlH,OAAA,CAACjC,UAAU;kBACTuC,KAAK,EAAC,SAAS;kBACfmI,OAAO,EAAEA,CAAA,KAAM1G,QAAQ,CAAC,YAAY,CAAE;kBAAA8E,QAAA,eAEtC7G,OAAA,CAAC/B,MAAM;oBAACyI,EAAE,EAAE;sBAAEkC,OAAO,EAAE,SAAS;sBAAEC,KAAK,EAAE,EAAE;sBAAE3H,MAAM,EAAE;oBAAG,CAAE;oBAAA2F,QAAA,EACvD7E,IAAI,aAAJA,IAAI,wBAAAH,UAAA,GAAJG,IAAI,CAAEkC,IAAI,cAAArC,UAAA,uBAAVA,UAAA,CAAYiH,MAAM,CAAC,CAAC,CAAC,CAACC,WAAW,CAAC;kBAAC;oBAAAhC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAC9B;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACC,CAAC,eACblH,OAAA,CAACxC,MAAM;kBAAC8C,KAAK,EAAC,SAAS;kBAACmI,OAAO,EAAEvG,MAAO;kBAAA2E,QAAA,EAAC;gBAEzC;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC;cAAA,eACT,CAAC,gBAEHlH,OAAA,CAAAE,SAAA;gBAAA2G,QAAA,gBACE7G,OAAA,CAACxC,MAAM;kBAAC8C,KAAK,EAAC,SAAS;kBAACmI,OAAO,EAAEA,CAAA,KAAM1G,QAAQ,CAAC,QAAQ,CAAE;kBAAA8E,QAAA,EAAC;gBAE3D;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,eACTlH,OAAA,CAACxC,MAAM;kBACL8C,KAAK,EAAC,SAAS;kBACfmI,OAAO,EAAEA,CAAA,KAAM1G,QAAQ,CAAC,WAAW,CAAE;kBACrC2E,EAAE,EAAE;oBACFrG,eAAe,EAAE,uBAAuB;oBACxC,SAAS,EAAE;sBAAEA,eAAe,EAAE;oBAAwB;kBACxD,CAAE;kBAAAwG,QAAA,EACH;gBAED;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,eACTlH,OAAA,CAAChC,KAAK;kBAAC2K,YAAY,EAAEvF,SAAS,CAAC6B,MAAO;kBAAC3E,KAAK,EAAC,OAAO;kBAAAuG,QAAA,eAClD7G,OAAA,CAACjC,UAAU;oBAACuC,KAAK,EAAC,SAAS;oBAACmI,OAAO,EAAEA,CAAA,KAAM1G,QAAQ,CAAC,OAAO,CAAE;oBAAA8E,QAAA,eAC3D7G,OAAA,CAACnB,YAAY;sBAAAkI,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAE;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACN;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACR,CAAC;cAAA,eACR,CACH;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACE;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACF,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACE;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACE,CAAC,eAGjBlH,OAAA,CAAC9C,SAAS;MAACkK,QAAQ,EAAC,IAAI;MAACV,EAAE,EAAE;QAAEsC,EAAE,EAAE;MAAE,CAAE;MAAAnC,QAAA,eACrC7G,OAAA,CAACsB,WAAW;QAAAuF,QAAA,gBACV7G,OAAA,CAAC5C,UAAU;UAACqK,OAAO,EAAC,IAAI;UAACf,EAAE,EAAE;YAAEgB,UAAU,EAAE,MAAM;YAAEuB,EAAE,EAAE;UAAE,CAAE;UAAApC,QAAA,EAAC;QAE5D;UAAAE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAY,CAAC,eACblH,OAAA,CAAC5C,UAAU;UAACqK,OAAO,EAAC,IAAI;UAAAZ,QAAA,EAAC;QAEzB;UAAAE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAY,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACF;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACL,CAAC,eAGZlH,OAAA,CAAC9C,SAAS;MAACkK,QAAQ,EAAC,IAAI;MAACV,EAAE,EAAE;QAAEsC,EAAE,EAAE;MAAE,CAAE;MAAAnC,QAAA,eACrC7G,OAAA,CAACpC,KAAK;QAAC8I,EAAE,EAAE;UAAEvB,CAAC,EAAE;QAAE,CAAE;QAAA0B,QAAA,gBAClB7G,OAAA,CAAC5C,UAAU;UAACqK,OAAO,EAAC,IAAI;UAACf,EAAE,EAAE;YAAEuC,EAAE,EAAE,CAAC;YAAEvB,UAAU,EAAE;UAAO,CAAE;UAAAb,QAAA,EAAC;QAE5D;UAAAE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAY,CAAC,eACblH,OAAA,CAAC7C,IAAI;UAACkK,SAAS;UAAC7G,OAAO,EAAE,CAAE;UAAAqG,QAAA,EACxB/B,UAAU,CAACoE,GAAG,CAAE3E,QAAQ,iBACvBvE,OAAA,CAAC7C,IAAI;YAACmK,IAAI;YAACC,EAAE,EAAE,CAAE;YAAC4B,EAAE,EAAE,CAAE;YAAC3B,EAAE,EAAE,CAAE;YAAAX,QAAA,eAC7B7G,OAAA,CAACW,YAAY;cACX8H,OAAO,EAAEA,CAAA,KAAM7F,mBAAmB,CAAC2B,QAAQ,CAACL,IAAI,CAAE;cAClDwC,EAAE,EAAE;gBACFrG,eAAe,EAAEsC,gBAAgB,KAAK4B,QAAQ,CAACL,IAAI,GAAG,SAAS,GAAG,OAAO;gBACzEkF,MAAM,EAAEzG,gBAAgB,KAAK4B,QAAQ,CAACL,IAAI,GAAG,mBAAmB,GAAG;cACrE,CAAE;cAAA2C,QAAA,gBAEF7G,OAAA,CAAC5C,UAAU;gBAACqK,OAAO,EAAC,IAAI;gBAACf,EAAE,EAAE;kBAAEuC,EAAE,EAAE;gBAAE,CAAE;gBAAApC,QAAA,EACpCtC,QAAQ,CAACQ;cAAI;gBAAAgC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACJ,CAAC,eACblH,OAAA,CAAC5C,UAAU;gBAACqK,OAAO,EAAC,OAAO;gBAACf,EAAE,EAAE;kBAAEgB,UAAU,EAAE;gBAAO,CAAE;gBAAAb,QAAA,EACpDtC,QAAQ,CAACL;cAAI;gBAAA6C,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACJ,CAAC,eACblH,OAAA,CAAC5C,UAAU;gBAACqK,OAAO,EAAC,SAAS;gBAACnH,KAAK,EAAC,eAAe;gBAAAuG,QAAA,GAAC,GACjD,EAACtC,QAAQ,CAACS,KAAK,EAAC,SACnB;cAAA;gBAAA+B,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAY,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACD;UAAC,GAjBoB3C,QAAQ,CAACL,IAAI;YAAA6C,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAkB5C,CACP;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACE,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACF;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACC,CAAC,eAGZlH,OAAA,CAAC9C,SAAS;MAACkK,QAAQ,EAAC,IAAI;MAACV,EAAE,EAAE;QAAEsC,EAAE,EAAE;MAAE,CAAE;MAAAnC,QAAA,eACrC7G,OAAA,CAACpC,KAAK;QAAC8I,EAAE,EAAE;UAAEvB,CAAC,EAAE;QAAE,CAAE;QAAA0B,QAAA,eAClB7G,OAAA,CAAC7C,IAAI;UAACkK,SAAS;UAAC7G,OAAO,EAAE,CAAE;UAACoG,UAAU,EAAC,QAAQ;UAAAC,QAAA,gBAC7C7G,OAAA,CAAC7C,IAAI;YAACmK,IAAI;YAACC,EAAE,EAAE,EAAG;YAACC,EAAE,EAAE,CAAE;YAAAX,QAAA,eACvB7G,OAAA,CAACzB,WAAW;cAACuJ,SAAS;cAAChB,IAAI,EAAC,OAAO;cAAAD,QAAA,gBACjC7G,OAAA,CAACxB,UAAU;gBAAAqI,QAAA,EAAC;cAAO;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAY,CAAC,eAChClH,OAAA,CAACvB,MAAM;gBACLuJ,KAAK,EAAEjF,MAAO;gBACdsG,KAAK,EAAC,SAAS;gBACfpB,QAAQ,EAAGC,CAAC,IAAKlF,SAAS,CAACkF,CAAC,CAACC,MAAM,CAACH,KAAK,CAAE;gBAAAnB,QAAA,gBAE3C7G,OAAA,CAACtB,QAAQ;kBAACsJ,KAAK,EAAC,UAAU;kBAAAnB,QAAA,EAAC;gBAAQ;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAU,CAAC,eAC9ClH,OAAA,CAACtB,QAAQ;kBAACsJ,KAAK,EAAC,WAAW;kBAAAnB,QAAA,EAAC;gBAAkB;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAU,CAAC,eACzDlH,OAAA,CAACtB,QAAQ;kBAACsJ,KAAK,EAAC,YAAY;kBAAAnB,QAAA,EAAC;gBAAkB;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAU,CAAC,eAC1DlH,OAAA,CAACtB,QAAQ;kBAACsJ,KAAK,EAAC,QAAQ;kBAAAnB,QAAA,EAAC;gBAAe;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAU,CAAC,eACnDlH,OAAA,CAACtB,QAAQ;kBAACsJ,KAAK,EAAC,QAAQ;kBAAAnB,QAAA,EAAC;gBAAY;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAU,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC1C,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACE;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACV,CAAC,eAEPlH,OAAA,CAAC7C,IAAI;YAACmK,IAAI;YAACC,EAAE,EAAE,EAAG;YAACC,EAAE,EAAE,CAAE;YAAAX,QAAA,gBACvB7G,OAAA,CAAC5C,UAAU;cAACqK,OAAO,EAAC,OAAO;cAACf,EAAE,EAAE;gBAAEuC,EAAE,EAAE;cAAE,CAAE;cAAApC,QAAA,GAAC,gBAC3B,EAAChE,UAAU,CAAC,CAAC,CAAC,EAAC,MAAI,EAACA,UAAU,CAAC,CAAC,CAAC;YAAA;cAAAkE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACrC,CAAC,eACblH,OAAA,CAACrB,MAAM;cACLqJ,KAAK,EAAEnF,UAAW;cAClBoF,QAAQ,EAAEA,CAACC,CAAC,EAAEoB,QAAQ,KAAKxG,aAAa,CAACwG,QAAQ,CAAE;cACnDC,iBAAiB,EAAC,MAAM;cACxBC,GAAG,EAAE,CAAE;cACPC,GAAG,EAAE,GAAI;cACT/C,EAAE,EAAE;gBAAEpG,KAAK,EAAE;cAAU;YAAE;cAAAyG,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC1B,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACE,CAAC,eAEPlH,OAAA,CAAC7C,IAAI;YAACmK,IAAI;YAACC,EAAE,EAAE,EAAG;YAACC,EAAE,EAAE,CAAE;YAAAX,QAAA,eACvB7G,OAAA,CAACvC,GAAG;cAACiJ,EAAE,EAAE;gBAAEvF,OAAO,EAAE,MAAM;gBAAEyF,UAAU,EAAE,QAAQ;gBAAE2B,GAAG,EAAE;cAAE,CAAE;cAAA1B,QAAA,gBACzD7G,OAAA,CAACnC,IAAI;gBACHkH,IAAI,eAAE/E,OAAA,CAACZ,UAAU;kBAAA2H,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAE;gBACrBmC,KAAK,EAAC,SAAS;gBACf5B,OAAO,EAAC,UAAU;gBAClBf,EAAE,EAAE;kBAAEpG,KAAK,EAAE,SAAS;kBAAEoJ,WAAW,EAAE;gBAAU;cAAE;gBAAA3C,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAClD,CAAC,eACFlH,OAAA,CAACnC,IAAI;gBACHkH,IAAI,eAAE/E,OAAA,CAACf,aAAa;kBAAA8H,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAE;gBACxBmC,KAAK,EAAC,eAAe;gBACrB5B,OAAO,EAAC,UAAU;gBAClBnH,KAAK,EAAC;cAAS;gBAAAyG,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAChB,CAAC,eACFlH,OAAA,CAACnC,IAAI;gBACHkH,IAAI,eAAE/E,OAAA,CAACd,QAAQ;kBAAA6H,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAE;gBACnBmC,KAAK,EAAC,SAAS;gBACf5B,OAAO,EAAC,UAAU;gBAClBnH,KAAK,EAAC;cAAS;gBAAAyG,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAChB,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACC;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACF,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACF;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACC,CAAC,eAGZlH,OAAA,CAAC9C,SAAS;MAACkK,QAAQ,EAAC,IAAI;MAACV,EAAE,EAAE;QAAEsC,EAAE,EAAE,CAAC;QAAEW,EAAE,EAAE;MAAE,CAAE;MAAA9C,QAAA,GAC3CtE,KAAK,iBACJvC,OAAA,CAAC5B,KAAK;QAACwL,QAAQ,EAAC,SAAS;QAAClD,EAAE,EAAE;UAAEuC,EAAE,EAAE;QAAE,CAAE;QAAApC,QAAA,GACrCtE,KAAK,EAAC,wBACT;MAAA;QAAAwE,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAO,CACR,eAEDlH,OAAA,CAAC5C,UAAU;QAACqK,OAAO,EAAC,IAAI;QAACf,EAAE,EAAE;UAAEuC,EAAE,EAAE,CAAC;UAAEvB,UAAU,EAAE;QAAO,CAAE;QAAAb,QAAA,GACxDlE,gBAAgB,KAAK,KAAK,GAAG,cAAc,GAAGA,gBAAgB,eAC/D3C,OAAA;UAAM6H,KAAK,EAAE;YAAEvH,KAAK,EAAE,SAAS;YAAEoH,UAAU,EAAE;UAAS,CAAE;UAAAb,QAAA,GAAC,GACtD,EAACd,gBAAgB,CAACd,MAAM,EAAC,SAC5B;QAAA;UAAA8B,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACG,CAAC,eAEblH,OAAA,CAAC7C,IAAI;QAACkK,SAAS;QAAC7G,OAAO,EAAE,CAAE;QAAAqG,QAAA,EACxBd,gBAAgB,CAACmD,GAAG,CAAE7D,OAAO,iBAC5BrF,OAAA,CAAC7C,IAAI;UAACmK,IAAI;UAACC,EAAE,EAAE,CAAE;UAAC4B,EAAE,EAAE,CAAE;UAAC3B,EAAE,EAAE,CAAE;UAACqC,EAAE,EAAE,GAAI;UAAAhD,QAAA,eACtC7G,OAAA,CAACiB,WAAW;YAAA4F,QAAA,gBACV7G,OAAA,CAACvC,GAAG;cAACiJ,EAAE,EAAE;gBAAE4B,QAAQ,EAAE;cAAW,CAAE;cAAAzB,QAAA,gBAChC7G,OAAA,CAAC1C,SAAS;gBACRwM,SAAS,EAAC,KAAK;gBACf5I,MAAM,EAAC,KAAK;gBACZkD,KAAK,EAAEiB,OAAO,CAACjB,KAAM;gBACrB2F,GAAG,EAAE1E,OAAO,CAACnB,IAAK;gBAClBwC,EAAE,EAAE;kBAAEsD,SAAS,EAAE;gBAAQ;cAAE;gBAAAjD,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC5B,CAAC,eACFlH,OAAA,CAACjC,UAAU;gBACT2I,EAAE,EAAE;kBACF4B,QAAQ,EAAE,UAAU;kBACpB2B,GAAG,EAAE,CAAC;kBACNC,KAAK,EAAE,CAAC;kBACR7J,eAAe,EAAE,uBAAuB;kBACxC,SAAS,EAAE;oBAAEA,eAAe,EAAE;kBAAwB;gBACxD,CAAE;gBACFoI,OAAO,EAAEA,CAAA,KAAMhD,cAAc,CAACJ,OAAO,CAACpB,GAAG,CAAE;gBAAA4C,QAAA,EAE1C5D,SAAS,CAAC2C,GAAG,CAACP,OAAO,CAACpB,GAAG,CAAC,gBACzBjE,OAAA,CAAClB,QAAQ;kBAAC4H,EAAE,EAAE;oBAAEpG,KAAK,EAAE;kBAAU;gBAAE;kBAAAyG,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAC,gBACtClH,OAAA,CAACjB,cAAc;kBAAAgI,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAEV,CAAC,EACZ7B,OAAO,CAACT,QAAQ,iBACf5E,OAAA,CAACnC,IAAI;gBACHwL,KAAK,EAAC,YAAY;gBAClB/I,KAAK,EAAC,OAAO;gBACbwG,IAAI,EAAC,OAAO;gBACZJ,EAAE,EAAE;kBACF4B,QAAQ,EAAE,UAAU;kBACpB2B,GAAG,EAAE,CAAC;kBACNE,IAAI,EAAE,CAAC;kBACPC,QAAQ,EAAE;gBACZ;cAAE;gBAAArD,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CACF;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACE,CAAC,eAENlH,OAAA,CAACzC,WAAW;cAACmJ,EAAE,EAAE;gBAAE2D,QAAQ,EAAE,CAAC;gBAAElF,CAAC,EAAE;cAAE,CAAE;cAAA0B,QAAA,gBACrC7G,OAAA,CAAC5C,UAAU;gBACTqK,OAAO,EAAC,OAAO;gBACff,EAAE,EAAE;kBACFgB,UAAU,EAAE,MAAM;kBAClB4C,QAAQ,EAAE,QAAQ;kBAClBC,YAAY,EAAE,UAAU;kBACxBC,UAAU,EAAE,QAAQ;kBACpBvB,EAAE,EAAE;gBACN,CAAE;gBAAApC,QAAA,EAEDxB,OAAO,CAACnB;cAAI;gBAAA6C,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC,eAEblH,OAAA,CAACvC,GAAG;gBAACiJ,EAAE,EAAE;kBAAEvF,OAAO,EAAE,MAAM;kBAAEyF,UAAU,EAAE,QAAQ;kBAAEqC,EAAE,EAAE;gBAAE,CAAE;gBAAApC,QAAA,gBACxD7G,OAAA,CAAClC,MAAM;kBAACkK,KAAK,EAAE3C,OAAO,CAAChB,MAAM,IAAI,GAAI;kBAACoG,QAAQ;kBAAC3D,IAAI,EAAC;gBAAO;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAC,eAC9DlH,OAAA,CAAC5C,UAAU;kBAACqK,OAAO,EAAC,SAAS;kBAACf,EAAE,EAAE;oBAAEgE,EAAE,EAAE,CAAC;oBAAEpK,KAAK,EAAE;kBAAU,CAAE;kBAAAuG,QAAA,GAAC,GAC5D,EAACxB,OAAO,CAACf,UAAU,IAAI,CAAC,EAAC,GAC5B;gBAAA;kBAAAyC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAY,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACV,CAAC,eAENlH,OAAA,CAACvC,GAAG;gBAACiJ,EAAE,EAAE;kBAAEvF,OAAO,EAAE,MAAM;kBAAEyF,UAAU,EAAE,QAAQ;kBAAEqC,EAAE,EAAE;gBAAE,CAAE;gBAAApC,QAAA,gBACxD7G,OAAA,CAAC5C,UAAU;kBAACqK,OAAO,EAAC,IAAI;kBAACf,EAAE,EAAE;oBAAEgB,UAAU,EAAE,MAAM;oBAAEpH,KAAK,EAAE;kBAAU,CAAE;kBAAAuG,QAAA,GAAC,QACpE,EAAC,CAACxB,OAAO,CAAClB,KAAK,GAAG,EAAE,EAAEwG,OAAO,CAAC,CAAC,CAAC;gBAAA;kBAAA5D,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACvB,CAAC,eACblH,OAAA,CAAC5C,UAAU;kBACTqK,OAAO,EAAC,SAAS;kBACjBf,EAAE,EAAE;oBACFgE,EAAE,EAAE,CAAC;oBACLE,cAAc,EAAE,cAAc;oBAC9BtK,KAAK,EAAE;kBACT,CAAE;kBAAAuG,QAAA,GACH,QACE,EAAC,CAACxB,OAAO,CAAClB,KAAK,GAAG,GAAG,EAAEwG,OAAO,CAAC,CAAC,CAAC;gBAAA;kBAAA5D,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACxB,CAAC,eACblH,OAAA,CAAC5C,UAAU;kBAACqK,OAAO,EAAC,SAAS;kBAACf,EAAE,EAAE;oBAAEgE,EAAE,EAAE,CAAC;oBAAEpK,KAAK,EAAE,SAAS;oBAAEoH,UAAU,EAAE;kBAAO,CAAE;kBAAAb,QAAA,EAAC;gBAEnF;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAY,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACV,CAAC,eAENlH,OAAA,CAAC5C,UAAU;gBAACqK,OAAO,EAAC,SAAS;gBAACf,EAAE,EAAE;kBAAEpG,KAAK,EAAE,SAAS;kBAAEa,OAAO,EAAE,OAAO;kBAAE8H,EAAE,EAAE;gBAAE,CAAE;gBAAApC,QAAA,GAC7ExB,OAAO,CAACX,MAAM,EAAEW,OAAO,CAACV,IAAI,EAAC,KAAG,EAACU,OAAO,CAACZ,MAAM;cAAA;gBAAAsC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACtC,CAAC,eAEblH,OAAA,CAACvC,GAAG;gBAACiJ,EAAE,EAAE;kBAAEvF,OAAO,EAAE,MAAM;kBAAEoH,GAAG,EAAE;gBAAE,CAAE;gBAAA1B,QAAA,eACnC7G,OAAA,CAACxC,MAAM;kBACLiK,OAAO,EAAC,WAAW;kBACnBX,IAAI,EAAC,OAAO;kBACZgB,SAAS;kBACTW,OAAO,EAAEA,CAAA,KAAMrD,eAAe,CAACC,OAAO,CAAE;kBACxCqB,EAAE,EAAE;oBACFrG,eAAe,EAAE,SAAS;oBAC1B,SAAS,EAAE;sBAAEA,eAAe,EAAE;oBAAU,CAAC;oBACzC+J,QAAQ,EAAE,SAAS;oBACnBS,EAAE,EAAE;kBACN,CAAE;kBAAAhE,QAAA,EACH;gBAED;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACN,CAAC,eAENlH,OAAA,CAACvC,GAAG;gBAACiJ,EAAE,EAAE;kBAAEvF,OAAO,EAAE,MAAM;kBAAEyF,UAAU,EAAE,QAAQ;kBAAEoC,EAAE,EAAE,CAAC;kBAAET,GAAG,EAAE;gBAAE,CAAE;gBAAA1B,QAAA,gBAChE7G,OAAA,CAACf,aAAa;kBAACyH,EAAE,EAAE;oBAAE0D,QAAQ,EAAE,EAAE;oBAAE9J,KAAK,EAAE;kBAAU;gBAAE;kBAAAyG,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAC,eACzDlH,OAAA,CAAC5C,UAAU;kBAACqK,OAAO,EAAC,SAAS;kBAACf,EAAE,EAAE;oBAAEpG,KAAK,EAAE;kBAAU,CAAE;kBAAAuG,QAAA,EAAC;gBAExD;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAY,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACV,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACK,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH;QAAC,GA5G8B7B,OAAO,CAACpB,GAAG;UAAA8C,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OA6GnD,CACP;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACE,CAAC,EAENnB,gBAAgB,CAACd,MAAM,KAAK,CAAC,iBAC5BjF,OAAA,CAACvC,GAAG;QAACiJ,EAAE,EAAE;UAAE9F,SAAS,EAAE,QAAQ;UAAEiK,EAAE,EAAE;QAAE,CAAE;QAAAhE,QAAA,gBACtC7G,OAAA,CAAC5C,UAAU;UAACqK,OAAO,EAAC,IAAI;UAACnH,KAAK,EAAC,eAAe;UAAAuG,QAAA,EAAC;QAE/C;UAAAE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAY,CAAC,eACblH,OAAA,CAACxC,MAAM;UACLiK,OAAO,EAAC,UAAU;UAClBf,EAAE,EAAE;YAAEsC,EAAE,EAAE;UAAE,CAAE;UACdP,OAAO,EAAEA,CAAA,KAAM;YACb/F,aAAa,CAAC,EAAE,CAAC;YACjBE,mBAAmB,CAAC,KAAK,CAAC;YAC1BE,aAAa,CAAC,CAAC,CAAC,EAAE,GAAG,CAAC,CAAC;UACzB,CAAE;UAAA+D,QAAA,EACH;QAED;UAAAE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACN,CACN;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACQ,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACT,CAAC;AAEV,CAAC;AAACtF,EAAA,CAthBID,IAAI;EAAA,QACS3E,WAAW,EACX8C,WAAW,EACcD,OAAO;AAAA;AAAAiL,GAAA,GAH7CnJ,IAAI;AAwhBV,eAAeA,IAAI;AAAC,IAAAjB,EAAA,EAAAM,GAAA,EAAAK,GAAA,EAAAK,GAAA,EAAAoJ,GAAA;AAAAC,YAAA,CAAArK,EAAA;AAAAqK,YAAA,CAAA/J,GAAA;AAAA+J,YAAA,CAAA1J,GAAA;AAAA0J,YAAA,CAAArJ,GAAA;AAAAqJ,YAAA,CAAAD,GAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}