{"ast": null, "code": "var _jsxFileName = \"D:\\\\ecommerce\\\\client\\\\src\\\\components\\\\auth\\\\ProtectedRoute.js\",\n  _s = $RefreshSig$();\nimport React from 'react';\nimport { Navigate, useLocation } from 'react-router-dom';\nimport { Box, CircularProgress, Typography } from '@mui/material';\nimport { useAuth } from '../../context/AuthContext';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst ProtectedRoute = ({\n  children,\n  adminOnly = false\n}) => {\n  _s();\n  const {\n    user,\n    isAuthenticated,\n    loading\n  } = useAuth();\n  const location = useLocation();\n\n  // Show loading spinner while checking authentication\n  if (loading) {\n    return /*#__PURE__*/_jsxDEV(Box, {\n      sx: {\n        display: 'flex',\n        flexDirection: 'column',\n        alignItems: 'center',\n        justifyContent: 'center',\n        height: '100vh',\n        backgroundColor: '#f1f3f6'\n      },\n      children: [/*#__PURE__*/_jsxDEV(CircularProgress, {\n        size: 60,\n        sx: {\n          color: '#2874f0',\n          mb: 2\n        }\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 23,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Typography, {\n        variant: \"h6\",\n        color: \"textSecondary\",\n        children: \"Loading...\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 24,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 13,\n      columnNumber: 7\n    }, this);\n  }\n\n  // Redirect to login if not authenticated\n  if (!isAuthenticated) {\n    return /*#__PURE__*/_jsxDEV(Navigate, {\n      to: \"/login\",\n      state: {\n        from: location\n      },\n      replace: true\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 33,\n      columnNumber: 12\n    }, this);\n  }\n\n  // Redirect to home if admin route but user is not admin\n  if (adminOnly && !(user !== null && user !== void 0 && user.isAdmin)) {\n    return /*#__PURE__*/_jsxDEV(Navigate, {\n      to: \"/\",\n      replace: true\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 38,\n      columnNumber: 12\n    }, this);\n  }\n  return children;\n};\n_s(ProtectedRoute, \"pvp8inAvQHEb0BVUe3eLqKvyMMQ=\", false, function () {\n  return [useAuth, useLocation];\n});\n_c = ProtectedRoute;\nexport default ProtectedRoute;\nvar _c;\n$RefreshReg$(_c, \"ProtectedRoute\");", "map": {"version": 3, "names": ["React", "Navigate", "useLocation", "Box", "CircularProgress", "Typography", "useAuth", "jsxDEV", "_jsxDEV", "ProtectedRoute", "children", "adminOnly", "_s", "user", "isAuthenticated", "loading", "location", "sx", "display", "flexDirection", "alignItems", "justifyContent", "height", "backgroundColor", "size", "color", "mb", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "variant", "to", "state", "from", "replace", "isAdmin", "_c", "$RefreshReg$"], "sources": ["D:/ecommerce/client/src/components/auth/ProtectedRoute.js"], "sourcesContent": ["import React from 'react';\nimport { Navigate, useLocation } from 'react-router-dom';\nimport { Box, CircularProgress, Typography } from '@mui/material';\nimport { useAuth } from '../../context/AuthContext';\n\nconst ProtectedRoute = ({ children, adminOnly = false }) => {\n  const { user, isAuthenticated, loading } = useAuth();\n  const location = useLocation();\n\n  // Show loading spinner while checking authentication\n  if (loading) {\n    return (\n      <Box\n        sx={{\n          display: 'flex',\n          flexDirection: 'column',\n          alignItems: 'center',\n          justifyContent: 'center',\n          height: '100vh',\n          backgroundColor: '#f1f3f6',\n        }}\n      >\n        <CircularProgress size={60} sx={{ color: '#2874f0', mb: 2 }} />\n        <Typography variant=\"h6\" color=\"textSecondary\">\n          Loading...\n        </Typography>\n      </Box>\n    );\n  }\n\n  // Redirect to login if not authenticated\n  if (!isAuthenticated) {\n    return <Navigate to=\"/login\" state={{ from: location }} replace />;\n  }\n\n  // Redirect to home if admin route but user is not admin\n  if (adminOnly && !user?.isAdmin) {\n    return <Navigate to=\"/\" replace />;\n  }\n\n  return children;\n};\n\nexport default ProtectedRoute;\n"], "mappings": ";;AAAA,OAAOA,KAAK,MAAM,OAAO;AACzB,SAASC,QAAQ,EAAEC,WAAW,QAAQ,kBAAkB;AACxD,SAASC,GAAG,EAAEC,gBAAgB,EAAEC,UAAU,QAAQ,eAAe;AACjE,SAASC,OAAO,QAAQ,2BAA2B;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAEpD,MAAMC,cAAc,GAAGA,CAAC;EAAEC,QAAQ;EAAEC,SAAS,GAAG;AAAM,CAAC,KAAK;EAAAC,EAAA;EAC1D,MAAM;IAAEC,IAAI;IAAEC,eAAe;IAAEC;EAAQ,CAAC,GAAGT,OAAO,CAAC,CAAC;EACpD,MAAMU,QAAQ,GAAGd,WAAW,CAAC,CAAC;;EAE9B;EACA,IAAIa,OAAO,EAAE;IACX,oBACEP,OAAA,CAACL,GAAG;MACFc,EAAE,EAAE;QACFC,OAAO,EAAE,MAAM;QACfC,aAAa,EAAE,QAAQ;QACvBC,UAAU,EAAE,QAAQ;QACpBC,cAAc,EAAE,QAAQ;QACxBC,MAAM,EAAE,OAAO;QACfC,eAAe,EAAE;MACnB,CAAE;MAAAb,QAAA,gBAEFF,OAAA,CAACJ,gBAAgB;QAACoB,IAAI,EAAE,EAAG;QAACP,EAAE,EAAE;UAAEQ,KAAK,EAAE,SAAS;UAAEC,EAAE,EAAE;QAAE;MAAE;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC,eAC/DtB,OAAA,CAACH,UAAU;QAAC0B,OAAO,EAAC,IAAI;QAACN,KAAK,EAAC,eAAe;QAAAf,QAAA,EAAC;MAE/C;QAAAiB,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAY,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACV,CAAC;EAEV;;EAEA;EACA,IAAI,CAAChB,eAAe,EAAE;IACpB,oBAAON,OAAA,CAACP,QAAQ;MAAC+B,EAAE,EAAC,QAAQ;MAACC,KAAK,EAAE;QAAEC,IAAI,EAAElB;MAAS,CAAE;MAACmB,OAAO;IAAA;MAAAR,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC;EACpE;;EAEA;EACA,IAAInB,SAAS,IAAI,EAACE,IAAI,aAAJA,IAAI,eAAJA,IAAI,CAAEuB,OAAO,GAAE;IAC/B,oBAAO5B,OAAA,CAACP,QAAQ;MAAC+B,EAAE,EAAC,GAAG;MAACG,OAAO;IAAA;MAAAR,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC;EACpC;EAEA,OAAOpB,QAAQ;AACjB,CAAC;AAACE,EAAA,CApCIH,cAAc;EAAA,QACyBH,OAAO,EACjCJ,WAAW;AAAA;AAAAmC,EAAA,GAFxB5B,cAAc;AAsCpB,eAAeA,cAAc;AAAC,IAAA4B,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}