{"ast": null, "code": "var _jsxFileName = \"D:\\\\ecommerce\\\\client\\\\src\\\\context\\\\AuthContext.js\",\n  _s = $RefreshSig$(),\n  _s2 = $RefreshSig$();\nimport React, { createContext, useContext, useReducer, useEffect } from 'react';\nimport { authAPI } from '../services/api';\n\n// Initial state\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst initialState = {\n  user: null,\n  isAuthenticated: false,\n  loading: true,\n  error: null\n};\n\n// Action types\nconst AUTH_ACTIONS = {\n  LOGIN_START: 'LOGIN_START',\n  LOGIN_SUCCESS: 'LOGIN_SUCCESS',\n  LOGIN_FAILURE: 'LOGIN_FAILURE',\n  LOGOUT: 'LOGOUT',\n  CLEAR_ERROR: 'CLEAR_ERROR',\n  SET_LOADING: 'SET_LOADING'\n};\n\n// Reducer\nconst authReducer = (state, action) => {\n  switch (action.type) {\n    case AUTH_ACTIONS.LOGIN_START:\n      return {\n        ...state,\n        loading: true,\n        error: null\n      };\n    case AUTH_ACTIONS.LOGIN_SUCCESS:\n      return {\n        ...state,\n        user: action.payload,\n        isAuthenticated: true,\n        loading: false,\n        error: null\n      };\n    case AUTH_ACTIONS.LOGIN_FAILURE:\n      return {\n        ...state,\n        user: null,\n        isAuthenticated: false,\n        loading: false,\n        error: action.payload\n      };\n    case AUTH_ACTIONS.LOGOUT:\n      return {\n        ...state,\n        user: null,\n        isAuthenticated: false,\n        loading: false,\n        error: null\n      };\n    case AUTH_ACTIONS.CLEAR_ERROR:\n      return {\n        ...state,\n        error: null\n      };\n    case AUTH_ACTIONS.SET_LOADING:\n      return {\n        ...state,\n        loading: action.payload\n      };\n    default:\n      return state;\n  }\n};\n\n// Create context\nconst AuthContext = /*#__PURE__*/createContext();\n\n// Provider component\nexport const AuthProvider = ({\n  children\n}) => {\n  _s();\n  const [state, dispatch] = useReducer(authReducer, initialState);\n\n  // Check if user is logged in on app start\n  useEffect(() => {\n    const checkAuth = () => {\n      try {\n        const user = localStorage.getItem('user');\n        const token = authAPI.getToken();\n        if (user && token && authAPI.isAuthenticated()) {\n          dispatch({\n            type: AUTH_ACTIONS.LOGIN_SUCCESS,\n            payload: JSON.parse(user)\n          });\n        } else {\n          // Clear invalid data\n          localStorage.removeItem('user');\n          authAPI.removeToken();\n          dispatch({\n            type: AUTH_ACTIONS.SET_LOADING,\n            payload: false\n          });\n        }\n      } catch (error) {\n        console.error('Auth check error:', error);\n        localStorage.removeItem('user');\n        authAPI.removeToken();\n        dispatch({\n          type: AUTH_ACTIONS.SET_LOADING,\n          payload: false\n        });\n      }\n    };\n    checkAuth();\n  }, []);\n\n  // Login function\n  const login = userData => {\n    dispatch({\n      type: AUTH_ACTIONS.LOGIN_SUCCESS,\n      payload: userData\n    });\n  };\n\n  // Logout function\n  const logout = () => {\n    localStorage.removeItem('user');\n    authAPI.removeToken();\n    dispatch({\n      type: AUTH_ACTIONS.LOGOUT\n    });\n  };\n\n  // Clear error function\n  const clearError = () => {\n    dispatch({\n      type: AUTH_ACTIONS.CLEAR_ERROR\n    });\n  };\n\n  // Set loading function\n  const setLoading = loading => {\n    dispatch({\n      type: AUTH_ACTIONS.SET_LOADING,\n      payload: loading\n    });\n  };\n  const value = {\n    ...state,\n    login,\n    logout,\n    clearError,\n    setLoading\n  };\n  return /*#__PURE__*/_jsxDEV(AuthContext.Provider, {\n    value: value,\n    children: children\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 140,\n    columnNumber: 5\n  }, this);\n};\n\n// Custom hook to use auth context\n_s(AuthProvider, \"bgCdjuTOmPdSBRwTap80EFd9Y3U=\");\n_c = AuthProvider;\nexport const useAuth = () => {\n  _s2();\n  const context = useContext(AuthContext);\n  if (!context) {\n    throw new Error('useAuth must be used within an AuthProvider');\n  }\n  return context;\n};\n_s2(useAuth, \"b9L3QQ+jgeyIrH0NfHrJ8nn7VMU=\");\nexport default AuthContext;\nvar _c;\n$RefreshReg$(_c, \"AuthProvider\");", "map": {"version": 3, "names": ["React", "createContext", "useContext", "useReducer", "useEffect", "authAPI", "jsxDEV", "_jsxDEV", "initialState", "user", "isAuthenticated", "loading", "error", "AUTH_ACTIONS", "LOGIN_START", "LOGIN_SUCCESS", "LOGIN_FAILURE", "LOGOUT", "CLEAR_ERROR", "SET_LOADING", "authReducer", "state", "action", "type", "payload", "AuthContext", "<PERSON>th<PERSON><PERSON><PERSON>", "children", "_s", "dispatch", "checkAuth", "localStorage", "getItem", "token", "getToken", "JSON", "parse", "removeItem", "removeToken", "console", "login", "userData", "logout", "clearError", "setLoading", "value", "Provider", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "_c", "useAuth", "_s2", "context", "Error", "$RefreshReg$"], "sources": ["D:/ecommerce/client/src/context/AuthContext.js"], "sourcesContent": ["import React, { createContext, useContext, useReducer, useEffect } from 'react';\nimport { authAPI } from '../services/api';\n\n// Initial state\nconst initialState = {\n  user: null,\n  isAuthenticated: false,\n  loading: true,\n  error: null,\n};\n\n// Action types\nconst AUTH_ACTIONS = {\n  LOGIN_START: 'LOGIN_START',\n  LOGIN_SUCCESS: 'LOGIN_SUCCESS',\n  LOGIN_FAILURE: 'LOGIN_FAILURE',\n  LOGOUT: 'LOGOUT',\n  CLEAR_ERROR: 'CLEAR_ERROR',\n  SET_LOADING: 'SET_LOADING',\n};\n\n// Reducer\nconst authReducer = (state, action) => {\n  switch (action.type) {\n    case AUTH_ACTIONS.LOGIN_START:\n      return {\n        ...state,\n        loading: true,\n        error: null,\n      };\n    case AUTH_ACTIONS.LOGIN_SUCCESS:\n      return {\n        ...state,\n        user: action.payload,\n        isAuthenticated: true,\n        loading: false,\n        error: null,\n      };\n    case AUTH_ACTIONS.LOGIN_FAILURE:\n      return {\n        ...state,\n        user: null,\n        isAuthenticated: false,\n        loading: false,\n        error: action.payload,\n      };\n    case AUTH_ACTIONS.LOGOUT:\n      return {\n        ...state,\n        user: null,\n        isAuthenticated: false,\n        loading: false,\n        error: null,\n      };\n    case AUTH_ACTIONS.CLEAR_ERROR:\n      return {\n        ...state,\n        error: null,\n      };\n    case AUTH_ACTIONS.SET_LOADING:\n      return {\n        ...state,\n        loading: action.payload,\n      };\n    default:\n      return state;\n  }\n};\n\n// Create context\nconst AuthContext = createContext();\n\n// Provider component\nexport const AuthProvider = ({ children }) => {\n  const [state, dispatch] = useReducer(authReducer, initialState);\n\n  // Check if user is logged in on app start\n  useEffect(() => {\n    const checkAuth = () => {\n      try {\n        const user = localStorage.getItem('user');\n        const token = authAPI.getToken();\n\n        if (user && token && authAPI.isAuthenticated()) {\n          dispatch({\n            type: AUTH_ACTIONS.LOGIN_SUCCESS,\n            payload: JSON.parse(user),\n          });\n        } else {\n          // Clear invalid data\n          localStorage.removeItem('user');\n          authAPI.removeToken();\n          dispatch({ type: AUTH_ACTIONS.SET_LOADING, payload: false });\n        }\n      } catch (error) {\n        console.error('Auth check error:', error);\n        localStorage.removeItem('user');\n        authAPI.removeToken();\n        dispatch({ type: AUTH_ACTIONS.SET_LOADING, payload: false });\n      }\n    };\n\n    checkAuth();\n  }, []);\n\n  // Login function\n  const login = (userData) => {\n    dispatch({\n      type: AUTH_ACTIONS.LOGIN_SUCCESS,\n      payload: userData,\n    });\n  };\n\n  // Logout function\n  const logout = () => {\n    localStorage.removeItem('user');\n    authAPI.removeToken();\n    dispatch({ type: AUTH_ACTIONS.LOGOUT });\n  };\n\n  // Clear error function\n  const clearError = () => {\n    dispatch({ type: AUTH_ACTIONS.CLEAR_ERROR });\n  };\n\n  // Set loading function\n  const setLoading = (loading) => {\n    dispatch({ type: AUTH_ACTIONS.SET_LOADING, payload: loading });\n  };\n\n  const value = {\n    ...state,\n    login,\n    logout,\n    clearError,\n    setLoading,\n  };\n\n  return (\n    <AuthContext.Provider value={value}>\n      {children}\n    </AuthContext.Provider>\n  );\n};\n\n// Custom hook to use auth context\nexport const useAuth = () => {\n  const context = useContext(AuthContext);\n  if (!context) {\n    throw new Error('useAuth must be used within an AuthProvider');\n  }\n  return context;\n};\n\nexport default AuthContext;\n"], "mappings": ";;;AAAA,OAAOA,KAAK,IAAIC,aAAa,EAAEC,UAAU,EAAEC,UAAU,EAAEC,SAAS,QAAQ,OAAO;AAC/E,SAASC,OAAO,QAAQ,iBAAiB;;AAEzC;AAAA,SAAAC,MAAA,IAAAC,OAAA;AACA,MAAMC,YAAY,GAAG;EACnBC,IAAI,EAAE,IAAI;EACVC,eAAe,EAAE,KAAK;EACtBC,OAAO,EAAE,IAAI;EACbC,KAAK,EAAE;AACT,CAAC;;AAED;AACA,MAAMC,YAAY,GAAG;EACnBC,WAAW,EAAE,aAAa;EAC1BC,aAAa,EAAE,eAAe;EAC9BC,aAAa,EAAE,eAAe;EAC9BC,MAAM,EAAE,QAAQ;EAChBC,WAAW,EAAE,aAAa;EAC1BC,WAAW,EAAE;AACf,CAAC;;AAED;AACA,MAAMC,WAAW,GAAGA,CAACC,KAAK,EAAEC,MAAM,KAAK;EACrC,QAAQA,MAAM,CAACC,IAAI;IACjB,KAAKV,YAAY,CAACC,WAAW;MAC3B,OAAO;QACL,GAAGO,KAAK;QACRV,OAAO,EAAE,IAAI;QACbC,KAAK,EAAE;MACT,CAAC;IACH,KAAKC,YAAY,CAACE,aAAa;MAC7B,OAAO;QACL,GAAGM,KAAK;QACRZ,IAAI,EAAEa,MAAM,CAACE,OAAO;QACpBd,eAAe,EAAE,IAAI;QACrBC,OAAO,EAAE,KAAK;QACdC,KAAK,EAAE;MACT,CAAC;IACH,KAAKC,YAAY,CAACG,aAAa;MAC7B,OAAO;QACL,GAAGK,KAAK;QACRZ,IAAI,EAAE,IAAI;QACVC,eAAe,EAAE,KAAK;QACtBC,OAAO,EAAE,KAAK;QACdC,KAAK,EAAEU,MAAM,CAACE;MAChB,CAAC;IACH,KAAKX,YAAY,CAACI,MAAM;MACtB,OAAO;QACL,GAAGI,KAAK;QACRZ,IAAI,EAAE,IAAI;QACVC,eAAe,EAAE,KAAK;QACtBC,OAAO,EAAE,KAAK;QACdC,KAAK,EAAE;MACT,CAAC;IACH,KAAKC,YAAY,CAACK,WAAW;MAC3B,OAAO;QACL,GAAGG,KAAK;QACRT,KAAK,EAAE;MACT,CAAC;IACH,KAAKC,YAAY,CAACM,WAAW;MAC3B,OAAO;QACL,GAAGE,KAAK;QACRV,OAAO,EAAEW,MAAM,CAACE;MAClB,CAAC;IACH;MACE,OAAOH,KAAK;EAChB;AACF,CAAC;;AAED;AACA,MAAMI,WAAW,gBAAGxB,aAAa,CAAC,CAAC;;AAEnC;AACA,OAAO,MAAMyB,YAAY,GAAGA,CAAC;EAAEC;AAAS,CAAC,KAAK;EAAAC,EAAA;EAC5C,MAAM,CAACP,KAAK,EAAEQ,QAAQ,CAAC,GAAG1B,UAAU,CAACiB,WAAW,EAAEZ,YAAY,CAAC;;EAE/D;EACAJ,SAAS,CAAC,MAAM;IACd,MAAM0B,SAAS,GAAGA,CAAA,KAAM;MACtB,IAAI;QACF,MAAMrB,IAAI,GAAGsB,YAAY,CAACC,OAAO,CAAC,MAAM,CAAC;QACzC,MAAMC,KAAK,GAAG5B,OAAO,CAAC6B,QAAQ,CAAC,CAAC;QAEhC,IAAIzB,IAAI,IAAIwB,KAAK,IAAI5B,OAAO,CAACK,eAAe,CAAC,CAAC,EAAE;UAC9CmB,QAAQ,CAAC;YACPN,IAAI,EAAEV,YAAY,CAACE,aAAa;YAChCS,OAAO,EAAEW,IAAI,CAACC,KAAK,CAAC3B,IAAI;UAC1B,CAAC,CAAC;QACJ,CAAC,MAAM;UACL;UACAsB,YAAY,CAACM,UAAU,CAAC,MAAM,CAAC;UAC/BhC,OAAO,CAACiC,WAAW,CAAC,CAAC;UACrBT,QAAQ,CAAC;YAAEN,IAAI,EAAEV,YAAY,CAACM,WAAW;YAAEK,OAAO,EAAE;UAAM,CAAC,CAAC;QAC9D;MACF,CAAC,CAAC,OAAOZ,KAAK,EAAE;QACd2B,OAAO,CAAC3B,KAAK,CAAC,mBAAmB,EAAEA,KAAK,CAAC;QACzCmB,YAAY,CAACM,UAAU,CAAC,MAAM,CAAC;QAC/BhC,OAAO,CAACiC,WAAW,CAAC,CAAC;QACrBT,QAAQ,CAAC;UAAEN,IAAI,EAAEV,YAAY,CAACM,WAAW;UAAEK,OAAO,EAAE;QAAM,CAAC,CAAC;MAC9D;IACF,CAAC;IAEDM,SAAS,CAAC,CAAC;EACb,CAAC,EAAE,EAAE,CAAC;;EAEN;EACA,MAAMU,KAAK,GAAIC,QAAQ,IAAK;IAC1BZ,QAAQ,CAAC;MACPN,IAAI,EAAEV,YAAY,CAACE,aAAa;MAChCS,OAAO,EAAEiB;IACX,CAAC,CAAC;EACJ,CAAC;;EAED;EACA,MAAMC,MAAM,GAAGA,CAAA,KAAM;IACnBX,YAAY,CAACM,UAAU,CAAC,MAAM,CAAC;IAC/BhC,OAAO,CAACiC,WAAW,CAAC,CAAC;IACrBT,QAAQ,CAAC;MAAEN,IAAI,EAAEV,YAAY,CAACI;IAAO,CAAC,CAAC;EACzC,CAAC;;EAED;EACA,MAAM0B,UAAU,GAAGA,CAAA,KAAM;IACvBd,QAAQ,CAAC;MAAEN,IAAI,EAAEV,YAAY,CAACK;IAAY,CAAC,CAAC;EAC9C,CAAC;;EAED;EACA,MAAM0B,UAAU,GAAIjC,OAAO,IAAK;IAC9BkB,QAAQ,CAAC;MAAEN,IAAI,EAAEV,YAAY,CAACM,WAAW;MAAEK,OAAO,EAAEb;IAAQ,CAAC,CAAC;EAChE,CAAC;EAED,MAAMkC,KAAK,GAAG;IACZ,GAAGxB,KAAK;IACRmB,KAAK;IACLE,MAAM;IACNC,UAAU;IACVC;EACF,CAAC;EAED,oBACErC,OAAA,CAACkB,WAAW,CAACqB,QAAQ;IAACD,KAAK,EAAEA,KAAM;IAAAlB,QAAA,EAChCA;EAAQ;IAAAoB,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACW,CAAC;AAE3B,CAAC;;AAED;AAAAtB,EAAA,CAxEaF,YAAY;AAAAyB,EAAA,GAAZzB,YAAY;AAyEzB,OAAO,MAAM0B,OAAO,GAAGA,CAAA,KAAM;EAAAC,GAAA;EAC3B,MAAMC,OAAO,GAAGpD,UAAU,CAACuB,WAAW,CAAC;EACvC,IAAI,CAAC6B,OAAO,EAAE;IACZ,MAAM,IAAIC,KAAK,CAAC,6CAA6C,CAAC;EAChE;EACA,OAAOD,OAAO;AAChB,CAAC;AAACD,GAAA,CANWD,OAAO;AAQpB,eAAe3B,WAAW;AAAC,IAAA0B,EAAA;AAAAK,YAAA,CAAAL,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}