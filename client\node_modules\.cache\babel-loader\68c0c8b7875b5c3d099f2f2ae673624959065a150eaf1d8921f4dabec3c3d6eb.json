{"ast": null, "code": "import none from \"./none.js\";\nexport default function (series, order) {\n  if (!((n = series.length) > 0)) return;\n  for (var i, n, j = 0, m = series[0].length, y; j < m; ++j) {\n    for (y = i = 0; i < n; ++i) y += series[i][j][1] || 0;\n    if (y) for (i = 0; i < n; ++i) series[i][j][1] /= y;\n  }\n  none(series, order);\n}", "map": {"version": 3, "names": ["none", "series", "order", "n", "length", "i", "j", "m", "y"], "sources": ["D:/ecommerce/node_modules/d3-shape/src/offset/expand.js"], "sourcesContent": ["import none from \"./none.js\";\n\nexport default function(series, order) {\n  if (!((n = series.length) > 0)) return;\n  for (var i, n, j = 0, m = series[0].length, y; j < m; ++j) {\n    for (y = i = 0; i < n; ++i) y += series[i][j][1] || 0;\n    if (y) for (i = 0; i < n; ++i) series[i][j][1] /= y;\n  }\n  none(series, order);\n}\n"], "mappings": "AAAA,OAAOA,IAAI,MAAM,WAAW;AAE5B,eAAe,UAASC,MAAM,EAAEC,KAAK,EAAE;EACrC,IAAI,EAAE,CAACC,CAAC,GAAGF,MAAM,CAACG,MAAM,IAAI,CAAC,CAAC,EAAE;EAChC,KAAK,IAAIC,CAAC,EAAEF,CAAC,EAAEG,CAAC,GAAG,CAAC,EAAEC,CAAC,GAAGN,MAAM,CAAC,CAAC,CAAC,CAACG,MAAM,EAAEI,CAAC,EAAEF,CAAC,GAAGC,CAAC,EAAE,EAAED,CAAC,EAAE;IACzD,KAAKE,CAAC,GAAGH,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGF,CAAC,EAAE,EAAEE,CAAC,EAAEG,CAAC,IAAIP,MAAM,CAACI,CAAC,CAAC,CAACC,CAAC,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC;IACrD,IAAIE,CAAC,EAAE,KAAKH,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGF,CAAC,EAAE,EAAEE,CAAC,EAAEJ,MAAM,CAACI,CAAC,CAAC,CAACC,CAAC,CAAC,CAAC,CAAC,CAAC,IAAIE,CAAC;EACrD;EACAR,IAAI,CAACC,MAAM,EAAEC,KAAK,CAAC;AACrB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}