import React, { useState } from 'react';
import {
  Box,
  Paper,
  TextField,
  Button,
  Typography,
  Container,
  Alert,
  CircularProgress,
  Link,
  Divider,
  InputAdornment,
  IconButton,
} from '@mui/material';
import {
  Visibility,
  VisibilityOff,
  Email,
  Lock,
  Google,
  Facebook,
} from '@mui/icons-material';
import { styled } from '@mui/material/styles';
import { useNavigate } from 'react-router-dom';
import { usersAPI, authAPI, handleAPIError } from '../../services/api';

// Flipkart-style styled components
const FlipkartHeader = styled(Paper)(({ theme }) => ({
  backgroundColor: '#2874f0',
  color: 'white',
  padding: theme.spacing(2),
  textAlign: 'center',
  marginBottom: theme.spacing(3),
}));

const LoginContainer = styled(Container)(({ theme }) => ({
  minHeight: '100vh',
  display: 'flex',
  alignItems: 'center',
  backgroundColor: '#f1f3f6',
  padding: theme.spacing(2),
}));

const LoginCard = styled(Paper)(({ theme }) => ({
  padding: theme.spacing(4),
  maxWidth: 400,
  width: '100%',
  margin: '0 auto',
  boxShadow: '0 4px 12px rgba(0,0,0,0.15)',
}));

const Login = () => {
  const navigate = useNavigate();
  const [formData, setFormData] = useState({
    email: '',
    password: '',
  });
  const [showPassword, setShowPassword] = useState(false);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState('');
  const [success, setSuccess] = useState('');

  const { email, password } = formData;

  const handleChange = (e) => {
    setFormData({
      ...formData,
      [e.target.name]: e.target.value,
    });
    setError('');
  };

  const handleSubmit = async (e) => {
    e.preventDefault();
    
    if (!email || !password) {
      setError('Please fill in all fields');
      return;
    }

    try {
      setLoading(true);
      setError('');
      
      const response = await usersAPI.login({ email, password });
      
      // Store token and user data
      authAPI.setToken(response.data.token);
      localStorage.setItem('user', JSON.stringify(response.data));
      
      setSuccess('Login successful! Redirecting...');
      
      // Redirect based on user role
      setTimeout(() => {
        if (response.data.isAdmin) {
          navigate('/admin');
        } else {
          navigate('/dashboard');
        }
      }, 1500);
      
    } catch (err) {
      setError(handleAPIError(err));
    } finally {
      setLoading(false);
    }
  };

  const handleGuestLogin = () => {
    // Demo guest login
    const guestUser = {
      _id: 'guest123',
      name: 'Guest User',
      email: '<EMAIL>',
      isAdmin: false,
      token: 'guest-token'
    };
    
    localStorage.setItem('user', JSON.stringify(guestUser));
    navigate('/');
  };

  return (
    <Box sx={{ backgroundColor: '#f1f3f6', minHeight: '100vh' }}>
      {/* Header */}
      <FlipkartHeader elevation={0}>
        <Typography variant="h4" sx={{ fontWeight: 'bold' }}>
          🛒 SpiceMart
        </Typography>
        <Typography variant="subtitle1">
          Login to access your account
        </Typography>
      </FlipkartHeader>

      <LoginContainer maxWidth="sm">
        <LoginCard>
          <Box sx={{ textAlign: 'center', mb: 3 }}>
            <Typography variant="h5" sx={{ fontWeight: 'bold', mb: 1 }}>
              Welcome Back!
            </Typography>
            <Typography variant="body2" color="textSecondary">
              Sign in to continue shopping
            </Typography>
          </Box>

          {error && (
            <Alert severity="error" sx={{ mb: 2 }}>
              {error}
            </Alert>
          )}

          {success && (
            <Alert severity="success" sx={{ mb: 2 }}>
              {success}
            </Alert>
          )}

          <form onSubmit={handleSubmit}>
            <TextField
              fullWidth
              label="Email Address"
              name="email"
              type="email"
              value={email}
              onChange={handleChange}
              margin="normal"
              required
              InputProps={{
                startAdornment: (
                  <InputAdornment position="start">
                    <Email color="action" />
                  </InputAdornment>
                ),
              }}
            />

            <TextField
              fullWidth
              label="Password"
              name="password"
              type={showPassword ? 'text' : 'password'}
              value={password}
              onChange={handleChange}
              margin="normal"
              required
              InputProps={{
                startAdornment: (
                  <InputAdornment position="start">
                    <Lock color="action" />
                  </InputAdornment>
                ),
                endAdornment: (
                  <InputAdornment position="end">
                    <IconButton
                      onClick={() => setShowPassword(!showPassword)}
                      edge="end"
                    >
                      {showPassword ? <VisibilityOff /> : <Visibility />}
                    </IconButton>
                  </InputAdornment>
                ),
              }}
            />

            <Button
              type="submit"
              fullWidth
              variant="contained"
              size="large"
              disabled={loading}
              sx={{
                mt: 3,
                mb: 2,
                backgroundColor: '#fb641b',
                '&:hover': { backgroundColor: '#e55a16' },
                py: 1.5,
                fontSize: '1.1rem',
                fontWeight: 'bold',
              }}
            >
              {loading ? <CircularProgress size={24} color="inherit" /> : 'LOGIN'}
            </Button>

            <Box sx={{ textAlign: 'center', mb: 2 }}>
              <Link href="#" variant="body2" sx={{ color: '#2874f0' }}>
                Forgot Password?
              </Link>
            </Box>

            <Divider sx={{ my: 2 }}>
              <Typography variant="caption" color="textSecondary">
                OR
              </Typography>
            </Divider>

            <Button
              fullWidth
              variant="outlined"
              onClick={handleGuestLogin}
              sx={{
                mb: 2,
                borderColor: '#2874f0',
                color: '#2874f0',
                '&:hover': {
                  borderColor: '#1e5bb8',
                  backgroundColor: '#e3f2fd',
                },
              }}
            >
              Continue as Guest
            </Button>

            <Box sx={{ display: 'flex', gap: 1, mb: 3 }}>
              <Button
                fullWidth
                variant="outlined"
                startIcon={<Google />}
                sx={{ borderColor: '#db4437', color: '#db4437' }}
              >
                Google
              </Button>
              <Button
                fullWidth
                variant="outlined"
                startIcon={<Facebook />}
                sx={{ borderColor: '#4267b2', color: '#4267b2' }}
              >
                Facebook
              </Button>
            </Box>

            <Box sx={{ textAlign: 'center' }}>
              <Typography variant="body2" color="textSecondary">
                New to SpiceMart?{' '}
                <Link
                  component="button"
                  variant="body2"
                  onClick={() => navigate('/register')}
                  sx={{ color: '#2874f0', textDecoration: 'none' }}
                >
                  Create an account
                </Link>
              </Typography>
            </Box>
          </form>

          {/* Demo Credentials */}
          <Box sx={{ mt: 3, p: 2, backgroundColor: '#f8f9fa', borderRadius: 1 }}>
            <Typography variant="caption" sx={{ fontWeight: 'bold', display: 'block', mb: 1 }}>
              Demo Credentials:
            </Typography>
            <Typography variant="caption" sx={{ display: 'block' }}>
              User: <EMAIL> / 123456
            </Typography>
            <Typography variant="caption" sx={{ display: 'block' }}>
              Admin: <EMAIL> / 123456
            </Typography>
          </Box>
        </LoginCard>
      </LoginContainer>
    </Box>
  );
};

export default Login;
