# Changelog

All notable changes to this project will be documented in this file.

The format is based on [Keep a Changelog](https://keepachangelog.com/en/1.0.0/)
and this project adheres to [Semantic Versioning](https://semver.org/spec/v2.0.0.html).

## [v1.2.6](https://github.com/es-shims/Array.prototype.findLastIndex/compare/v1.2.5...v1.2.6) - 2025-03-14

### Commits

- [Dev Deps] update `@es-shims/api`, `@ljharb/eslint-config`, `auto-changelog`, `es-value-fixtures`, `for-each`, `globalthis`, `has-strict-mode`, `object-inspect`, `tape` [`2012a88`](https://github.com/es-shims/Array.prototype.findLastIndex/commit/2012a880c41f74183bcfa55df7c800e48ca192e0)
- [Deps] update `call-bind`, `es-abstract`, `es-object-atoms`, `es-shim-unscopables` [`1c9fd19`](https://github.com/es-shims/Array.prototype.findLastIndex/commit/1c9fd195dd01ff8546ef20ae11b5748b390e12de)
- [Refactor] use `call-bound` directly [`78722d3`](https://github.com/es-shims/Array.prototype.findLastIndex/commit/78722d3b00af5523e4071d87d9d2746d61b705f3)
- [Tests] replace `aud` with `npm audit` [`cb689c1`](https://github.com/es-shims/Array.prototype.findLastIndex/commit/cb689c1e11297a9202cd8f1c01299eb2aabde879)
- [Dev Deps] add missing peer dep [`d3c92c4`](https://github.com/es-shims/Array.prototype.findLastIndex/commit/d3c92c4070999ee5d5716334e683a1acee54633b)

## [v1.2.5](https://github.com/es-shims/Array.prototype.findLastIndex/compare/v1.2.4...v1.2.5) - 2024-03-19

### Commits

- [meta] remove useless ESM [`4ed36ab`](https://github.com/es-shims/Array.prototype.findLastIndex/commit/4ed36ab7bde25f3b265a2769163409a939d9a4de)
- [Deps] update `call-bind`, `es-abstract` [`a6e3816`](https://github.com/es-shims/Array.prototype.findLastIndex/commit/a6e38162c958d3a54c73094a84fd27e9e48992d3)
- [Refactor] use `es-object-atoms` where possible [`3fbee9b`](https://github.com/es-shims/Array.prototype.findLastIndex/commit/3fbee9b07d97a472b59204360620a491ff1f3cdd)
- [Dev Deps] update `hasown`, `tape` [`dc6356c`](https://github.com/es-shims/Array.prototype.findLastIndex/commit/dc6356c69902912aa1eaee0f5677c6c64fb8ee11)

## [v1.2.4](https://github.com/es-shims/Array.prototype.findLastIndex/compare/v1.2.3...v1.2.4) - 2024-02-05

### Commits

- [Deps] update `call-bind`, `define-properties`, `es-abstract`, `es-shim-unscopables`, `get-intrinsic` [`b689455`](https://github.com/es-shims/Array.prototype.findLastIndex/commit/b689455f6194d84d929bd678755a175aa63b1b37)
- [Refactor] use `es-errors`, so things that only need those do not need `get-intrinsic` [`ca636e6`](https://github.com/es-shims/Array.prototype.findLastIndex/commit/ca636e60abae73755c613117f9a8eccb49a32f48)
- [Tests] use `functions-have-names` [`b91692c`](https://github.com/es-shims/Array.prototype.findLastIndex/commit/b91692c2a695c0065805fcb4f6b6f081d27852c8)
- [Dev Deps] update `aud`, `npmignore`, `tape` [`4f87dff`](https://github.com/es-shims/Array.prototype.findLastIndex/commit/4f87dff212fd5f0b05bdd412e669b4536e8b06f9)
- [Dev Deps] use `hasown` instead of `has` [`57ce292`](https://github.com/es-shims/Array.prototype.findLastIndex/commit/57ce2920dc645c3dec27b3aa8bcbfff9b0bfd135)
- [Dev Deps] update `object-inspect`, `tape` [`fa74bab`](https://github.com/es-shims/Array.prototype.findLastIndex/commit/fa74babe7cf90ec07b8c12c3ab10e89b7093e0b4)

## [v1.2.3](https://github.com/es-shims/Array.prototype.findLastIndex/compare/v1.2.2...v1.2.3) - 2023-08-29

### Commits

- [Deps] update `define-properties`, `es-abstract`, `get-intrinsic` [`27e8133`](https://github.com/es-shims/Array.prototype.findLastIndex/commit/27e8133ccdccab46896da19cd26e3147691a77eb)
- [Tests] add passing test262 test [`678edb3`](https://github.com/es-shims/Array.prototype.findLastIndex/commit/678edb3bc91b10298b4e72a5e471b1aa41f915dc)
- [Dev Deps] update `@es-shims/api`, `@ljharb/eslint-config`, `aud`, `object-inspect`, `tape` [`47abe8b`](https://github.com/es-shims/Array.prototype.findLastIndex/commit/47abe8b2cc14ee1b9f4b785a9aa26947ba20305a)
- [Tests] use `globalthis` [`0cf9142`](https://github.com/es-shims/Array.prototype.findLastIndex/commit/0cf914237811822d9d5268a057c0c9a108b8e5c0)

## [v1.2.2](https://github.com/es-shims/Array.prototype.findLastIndex/compare/v1.2.1...v1.2.2) - 2022-11-02

### Commits

- [meta] use `npmignore` to autogenerate an npmignore file [`f910dc6`](https://github.com/es-shims/Array.prototype.findLastIndex/commit/f910dc6e5999e6941892232810ae9b3910d925ec)
- [Deps] update `es-abstract`, `get-intrinsic` [`6e44207`](https://github.com/es-shims/Array.prototype.findLastIndex/commit/6e4420705d7435b8b9bb978e4858814ba0216fa5)
- [actions] update rebase action to use reusable workflow [`aac0471`](https://github.com/es-shims/Array.prototype.findLastIndex/commit/aac0471a0b2f0f44765ac6055211b58f26fa6636)
- [Deps] update `define-properties`, `es-abstract`, `get-intrinsic` [`c1d1d70`](https://github.com/es-shims/Array.prototype.findLastIndex/commit/c1d1d7083bfa8ab32a9e5560e4b2aa16ce9d0a6d)
- [Dev Deps] update `es-value-fixtures`, `object-inspect`, `tape` [`64c4c0f`](https://github.com/es-shims/Array.prototype.findLastIndex/commit/64c4c0f6dda4793c5d9db5110ec82c6004bcc4ef)
- [Dev Deps] update `aud`, `tape` [`603fbac`](https://github.com/es-shims/Array.prototype.findLastIndex/commit/603fbacf4d034298ff40ab6886f6af7031651acd)
- [Tests] add indication of whether it was shimmed [`7d711f6`](https://github.com/es-shims/Array.prototype.findLastIndex/commit/7d711f6e68c9c5e4e0556946d026c159f7d82f22)
- [Tests] make nullish receiver tests more robust [`335cf53`](https://github.com/es-shims/Array.prototype.findLastIndex/commit/335cf5381ec0bc7ce60de8824b736cb3b40918ae)

## [v1.2.1](https://github.com/es-shims/Array.prototype.findLastIndex/compare/v1.2.0...v1.2.1) - 2022-04-11

### Commits

- [Refactor] use `es-shim-unscopables` [`abe84ee`](https://github.com/es-shims/Array.prototype.findLastIndex/commit/abe84ee8794ce9c84180bc01cf499a7bb2494b3d)

## [v1.2.0](https://github.com/es-shims/Array.prototype.findLastIndex/compare/v1.1.1...v1.2.0) - 2022-04-11

### Commits

- [actions] reuse common workflows [`670cc4c`](https://github.com/es-shims/Array.prototype.findLastIndex/commit/670cc4c0b6f23b7f01d365a9afd2dd88ee2e4967)
- [Dev Deps] update `eslint`, `@ljharb/eslint-config`, `@es-shims/api`, `object-inspect`, `safe-publish-latest`, `tape` [`11bc460`](https://github.com/es-shims/Array.prototype.findLastIndex/commit/11bc460ab842d2a08c43b67aab182e0697d65308)
- [Dev Deps] update `eslint`, `@ljharb/eslint-config`, `aud`, `auto-changelog`, `object-inspect`, `tape` [`b076ad6`](https://github.com/es-shims/Array.prototype.findLastIndex/commit/b076ad6ed9246f3139efa72d0ddbf2e0d861f982)
- [actions] update codecov uploader [`0d62d86`](https://github.com/es-shims/Array.prototype.findLastIndex/commit/0d62d8602c2d3ecefdd0965c25cafb2a864c0903)
- [New] `shim`/`auto`: add `findLast` to `Symbol.unscopables` [`b09e4a6`](https://github.com/es-shims/Array.prototype.findLastIndex/commit/b09e4a6a194366b45945c6d65cdcc64ca786e570)
- [Deps] update `es-abstract` [`024128c`](https://github.com/es-shims/Array.prototype.findLastIndex/commit/024128c1abc5b58863859ea8b9cf4c1cb1df548b)
- [Deps] update `es-abstract` [`2dcab99`](https://github.com/es-shims/Array.prototype.findLastIndex/commit/2dcab99e3ed43546cd6610b12d489d95d8e94d90)

## [v1.1.1](https://github.com/es-shims/Array.prototype.findLastIndex/compare/v1.1.0...v1.1.1) - 2021-10-01

### Commits

- [Refactor] remove unnecessary ESM files [`35161c7`](https://github.com/es-shims/Array.prototype.findLastIndex/commit/35161c76758835dd3fbecdf0b5fbf41a3ce9f218)
- [Tests] add new tests from test262 [`e26fa25`](https://github.com/es-shims/Array.prototype.findLastIndex/commit/e26fa256601f60b1feb5918e4fbcc76d0d773d13)
- [Deps] update `es-abstract` [`5ff80b1`](https://github.com/es-shims/Array.prototype.findLastIndex/commit/5ff80b116f2e083ae72605275af0efec367f3a89)
- [readme] fix URLs [`b962fe2`](https://github.com/es-shims/Array.prototype.findLastIndex/commit/b962fe225fac8a450624d861b5d6e444526bcf32)
- [Dev Deps] update `@ljharb/eslint-config`, `@es-shims/api` [`5aa11dd`](https://github.com/es-shims/Array.prototype.findLastIndex/commit/5aa11dd23ee2bcdc9ca7d2d6007e7f44a9356743)

## [v1.1.0](https://github.com/es-shims/Array.prototype.findLastIndex/compare/v1.0.0...v1.1.0) - 2021-08-07

### Commits

- [New] add ESM entry points [`ac5ada3`](https://github.com/es-shims/Array.prototype.findLastIndex/commit/ac5ada3d359ff267e6933dd817f4106e6de2c157)
- [Fix] ES3 engines: ensure nullish receiver throws [`4c6592a`](https://github.com/es-shims/Array.prototype.findLastIndex/commit/4c6592a7962351e82bb1b06c79de496bf4bcd08d)
- [Dev Deps] update `eslint`, `@tape` [`6895166`](https://github.com/es-shims/Array.prototype.findLastIndex/commit/6895166742a738a80d19ac4eaa6f12c97a5c7814)
- [Fix] add missing entry points to `exports` [`ac2f138`](https://github.com/es-shims/Array.prototype.findLastIndex/commit/ac2f1384dcd9a9f736ce1c3c5dc7ac87e16c4f90)
- [Deps] update `es-abstract` [`c379fb7`](https://github.com/es-shims/Array.prototype.findLastIndex/commit/c379fb7681ea3bb078891bf394756b1086bc43e7)

## v1.0.0 - 2021-07-13

### Commits

- Implementation & Tests [`cf5cae1`](https://github.com/es-shims/Array.prototype.findLastIndex/commit/cf5cae1d78c0c74588a5baade5156950e17f4558)
- Initial commit [`2778f1c`](https://github.com/es-shims/Array.prototype.findLastIndex/commit/2778f1cc708cc79d3bdbc20d5b84d210b9670b8a)
- npm init [`873482d`](https://github.com/es-shims/Array.prototype.findLastIndex/commit/873482d650b6800e9484865c3b0d6d33664bcf9e)
- Only apps should have lockfiles [`b7c73ee`](https://github.com/es-shims/Array.prototype.findLastIndex/commit/b7c73ee858f01b0b1ff70800e09c0bbc2632e1a5)
