{"ast": null, "code": "import { Children, cloneElement, isValidElement } from 'react';\n/**\n * Given `this.props.children`, return an object mapping key to child.\n *\n * @param {*} children `this.props.children`\n * @return {object} Mapping of key to child\n */\n\nexport function getChildMapping(children, mapFn) {\n  var mapper = function mapper(child) {\n    return mapFn && isValidElement(child) ? mapFn(child) : child;\n  };\n  var result = Object.create(null);\n  if (children) Children.map(children, function (c) {\n    return c;\n  }).forEach(function (child) {\n    // run the map function here instead so that the key is the computed one\n    result[child.key] = mapper(child);\n  });\n  return result;\n}\n/**\n * When you're adding or removing children some may be added or removed in the\n * same render pass. We want to show *both* since we want to simultaneously\n * animate elements in and out. This function takes a previous set of keys\n * and a new set of keys and merges them with its best guess of the correct\n * ordering. In the future we may expose some of the utilities in\n * ReactMultiChild to make this easy, but for now React itself does not\n * directly have this concept of the union of prevChildren and nextChildren\n * so we implement it here.\n *\n * @param {object} prev prev children as returned from\n * `ReactTransitionChildMapping.getChildMapping()`.\n * @param {object} next next children as returned from\n * `ReactTransitionChildMapping.getChildMapping()`.\n * @return {object} a key set that contains all keys in `prev` and all keys\n * in `next` in a reasonable order.\n */\n\nexport function mergeChildMappings(prev, next) {\n  prev = prev || {};\n  next = next || {};\n  function getValueForKey(key) {\n    return key in next ? next[key] : prev[key];\n  } // For each key of `next`, the list of keys to insert before that key in\n  // the combined list\n\n  var nextKeysPending = Object.create(null);\n  var pendingKeys = [];\n  for (var prevKey in prev) {\n    if (prevKey in next) {\n      if (pendingKeys.length) {\n        nextKeysPending[prevKey] = pendingKeys;\n        pendingKeys = [];\n      }\n    } else {\n      pendingKeys.push(prevKey);\n    }\n  }\n  var i;\n  var childMapping = {};\n  for (var nextKey in next) {\n    if (nextKeysPending[nextKey]) {\n      for (i = 0; i < nextKeysPending[nextKey].length; i++) {\n        var pendingNextKey = nextKeysPending[nextKey][i];\n        childMapping[nextKeysPending[nextKey][i]] = getValueForKey(pendingNextKey);\n      }\n    }\n    childMapping[nextKey] = getValueForKey(nextKey);\n  } // Finally, add the keys which didn't appear before any key in `next`\n\n  for (i = 0; i < pendingKeys.length; i++) {\n    childMapping[pendingKeys[i]] = getValueForKey(pendingKeys[i]);\n  }\n  return childMapping;\n}\nfunction getProp(child, prop, props) {\n  return props[prop] != null ? props[prop] : child.props[prop];\n}\nexport function getInitialChildMapping(props, onExited) {\n  return getChildMapping(props.children, function (child) {\n    return cloneElement(child, {\n      onExited: onExited.bind(null, child),\n      in: true,\n      appear: getProp(child, 'appear', props),\n      enter: getProp(child, 'enter', props),\n      exit: getProp(child, 'exit', props)\n    });\n  });\n}\nexport function getNextChildMapping(nextProps, prevChildMapping, onExited) {\n  var nextChildMapping = getChildMapping(nextProps.children);\n  var children = mergeChildMappings(prevChildMapping, nextChildMapping);\n  Object.keys(children).forEach(function (key) {\n    var child = children[key];\n    if (!isValidElement(child)) return;\n    var hasPrev = key in prevChildMapping;\n    var hasNext = key in nextChildMapping;\n    var prevChild = prevChildMapping[key];\n    var isLeaving = isValidElement(prevChild) && !prevChild.props.in; // item is new (entering)\n\n    if (hasNext && (!hasPrev || isLeaving)) {\n      // console.log('entering', key)\n      children[key] = cloneElement(child, {\n        onExited: onExited.bind(null, child),\n        in: true,\n        exit: getProp(child, 'exit', nextProps),\n        enter: getProp(child, 'enter', nextProps)\n      });\n    } else if (!hasNext && hasPrev && !isLeaving) {\n      // item is old (exiting)\n      // console.log('leaving', key)\n      children[key] = cloneElement(child, {\n        in: false\n      });\n    } else if (hasNext && hasPrev && isValidElement(prevChild)) {\n      // item hasn't changed transition states\n      // copy over the last transition props;\n      // console.log('unchanged', key)\n      children[key] = cloneElement(child, {\n        onExited: onExited.bind(null, child),\n        in: prevChild.props.in,\n        exit: getProp(child, 'exit', nextProps),\n        enter: getProp(child, 'enter', nextProps)\n      });\n    }\n  });\n  return children;\n}", "map": {"version": 3, "names": ["Children", "cloneElement", "isValidElement", "get<PERSON>hildMapping", "children", "mapFn", "mapper", "child", "result", "Object", "create", "map", "c", "for<PERSON>ach", "key", "mergeChildMappings", "prev", "next", "getValueForKey", "nextKeysPending", "<PERSON><PERSON><PERSON><PERSON>", "prev<PERSON><PERSON>", "length", "push", "i", "childMapping", "<PERSON><PERSON><PERSON>", "pendingNextKey", "getProp", "prop", "props", "getInitialChildMapping", "onExited", "bind", "in", "appear", "enter", "exit", "getNextChildMapping", "nextProps", "prevChildMapping", "next<PERSON><PERSON>dMapping", "keys", "has<PERSON>rev", "hasNext", "prev<PERSON><PERSON><PERSON>", "isLeaving"], "sources": ["D:/ecommerce/client/node_modules/react-transition-group/esm/utils/ChildMapping.js"], "sourcesContent": ["import { Children, cloneElement, isValidElement } from 'react';\n/**\n * Given `this.props.children`, return an object mapping key to child.\n *\n * @param {*} children `this.props.children`\n * @return {object} Mapping of key to child\n */\n\nexport function getChildMapping(children, mapFn) {\n  var mapper = function mapper(child) {\n    return mapFn && isValidElement(child) ? mapFn(child) : child;\n  };\n\n  var result = Object.create(null);\n  if (children) Children.map(children, function (c) {\n    return c;\n  }).forEach(function (child) {\n    // run the map function here instead so that the key is the computed one\n    result[child.key] = mapper(child);\n  });\n  return result;\n}\n/**\n * When you're adding or removing children some may be added or removed in the\n * same render pass. We want to show *both* since we want to simultaneously\n * animate elements in and out. This function takes a previous set of keys\n * and a new set of keys and merges them with its best guess of the correct\n * ordering. In the future we may expose some of the utilities in\n * ReactMultiChild to make this easy, but for now React itself does not\n * directly have this concept of the union of prevChildren and nextChildren\n * so we implement it here.\n *\n * @param {object} prev prev children as returned from\n * `ReactTransitionChildMapping.getChildMapping()`.\n * @param {object} next next children as returned from\n * `ReactTransitionChildMapping.getChildMapping()`.\n * @return {object} a key set that contains all keys in `prev` and all keys\n * in `next` in a reasonable order.\n */\n\nexport function mergeChildMappings(prev, next) {\n  prev = prev || {};\n  next = next || {};\n\n  function getValueForKey(key) {\n    return key in next ? next[key] : prev[key];\n  } // For each key of `next`, the list of keys to insert before that key in\n  // the combined list\n\n\n  var nextKeysPending = Object.create(null);\n  var pendingKeys = [];\n\n  for (var prevKey in prev) {\n    if (prevKey in next) {\n      if (pendingKeys.length) {\n        nextKeysPending[prevKey] = pendingKeys;\n        pendingKeys = [];\n      }\n    } else {\n      pendingKeys.push(prevKey);\n    }\n  }\n\n  var i;\n  var childMapping = {};\n\n  for (var nextKey in next) {\n    if (nextKeysPending[nextKey]) {\n      for (i = 0; i < nextKeysPending[nextKey].length; i++) {\n        var pendingNextKey = nextKeysPending[nextKey][i];\n        childMapping[nextKeysPending[nextKey][i]] = getValueForKey(pendingNextKey);\n      }\n    }\n\n    childMapping[nextKey] = getValueForKey(nextKey);\n  } // Finally, add the keys which didn't appear before any key in `next`\n\n\n  for (i = 0; i < pendingKeys.length; i++) {\n    childMapping[pendingKeys[i]] = getValueForKey(pendingKeys[i]);\n  }\n\n  return childMapping;\n}\n\nfunction getProp(child, prop, props) {\n  return props[prop] != null ? props[prop] : child.props[prop];\n}\n\nexport function getInitialChildMapping(props, onExited) {\n  return getChildMapping(props.children, function (child) {\n    return cloneElement(child, {\n      onExited: onExited.bind(null, child),\n      in: true,\n      appear: getProp(child, 'appear', props),\n      enter: getProp(child, 'enter', props),\n      exit: getProp(child, 'exit', props)\n    });\n  });\n}\nexport function getNextChildMapping(nextProps, prevChildMapping, onExited) {\n  var nextChildMapping = getChildMapping(nextProps.children);\n  var children = mergeChildMappings(prevChildMapping, nextChildMapping);\n  Object.keys(children).forEach(function (key) {\n    var child = children[key];\n    if (!isValidElement(child)) return;\n    var hasPrev = (key in prevChildMapping);\n    var hasNext = (key in nextChildMapping);\n    var prevChild = prevChildMapping[key];\n    var isLeaving = isValidElement(prevChild) && !prevChild.props.in; // item is new (entering)\n\n    if (hasNext && (!hasPrev || isLeaving)) {\n      // console.log('entering', key)\n      children[key] = cloneElement(child, {\n        onExited: onExited.bind(null, child),\n        in: true,\n        exit: getProp(child, 'exit', nextProps),\n        enter: getProp(child, 'enter', nextProps)\n      });\n    } else if (!hasNext && hasPrev && !isLeaving) {\n      // item is old (exiting)\n      // console.log('leaving', key)\n      children[key] = cloneElement(child, {\n        in: false\n      });\n    } else if (hasNext && hasPrev && isValidElement(prevChild)) {\n      // item hasn't changed transition states\n      // copy over the last transition props;\n      // console.log('unchanged', key)\n      children[key] = cloneElement(child, {\n        onExited: onExited.bind(null, child),\n        in: prevChild.props.in,\n        exit: getProp(child, 'exit', nextProps),\n        enter: getProp(child, 'enter', nextProps)\n      });\n    }\n  });\n  return children;\n}"], "mappings": "AAAA,SAASA,QAAQ,EAAEC,YAAY,EAAEC,cAAc,QAAQ,OAAO;AAC9D;AACA;AACA;AACA;AACA;AACA;;AAEA,OAAO,SAASC,eAAeA,CAACC,QAAQ,EAAEC,KAAK,EAAE;EAC/C,IAAIC,MAAM,GAAG,SAASA,MAAMA,CAACC,KAAK,EAAE;IAClC,OAAOF,KAAK,IAAIH,cAAc,CAACK,KAAK,CAAC,GAAGF,KAAK,CAACE,KAAK,CAAC,GAAGA,KAAK;EAC9D,CAAC;EAED,IAAIC,MAAM,GAAGC,MAAM,CAACC,MAAM,CAAC,IAAI,CAAC;EAChC,IAAIN,QAAQ,EAAEJ,QAAQ,CAACW,GAAG,CAACP,QAAQ,EAAE,UAAUQ,CAAC,EAAE;IAChD,OAAOA,CAAC;EACV,CAAC,CAAC,CAACC,OAAO,CAAC,UAAUN,KAAK,EAAE;IAC1B;IACAC,MAAM,CAACD,KAAK,CAACO,GAAG,CAAC,GAAGR,MAAM,CAACC,KAAK,CAAC;EACnC,CAAC,CAAC;EACF,OAAOC,MAAM;AACf;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA,OAAO,SAASO,kBAAkBA,CAACC,IAAI,EAAEC,IAAI,EAAE;EAC7CD,IAAI,GAAGA,IAAI,IAAI,CAAC,CAAC;EACjBC,IAAI,GAAGA,IAAI,IAAI,CAAC,CAAC;EAEjB,SAASC,cAAcA,CAACJ,GAAG,EAAE;IAC3B,OAAOA,GAAG,IAAIG,IAAI,GAAGA,IAAI,CAACH,GAAG,CAAC,GAAGE,IAAI,CAACF,GAAG,CAAC;EAC5C,CAAC,CAAC;EACF;;EAGA,IAAIK,eAAe,GAAGV,MAAM,CAACC,MAAM,CAAC,IAAI,CAAC;EACzC,IAAIU,WAAW,GAAG,EAAE;EAEpB,KAAK,IAAIC,OAAO,IAAIL,IAAI,EAAE;IACxB,IAAIK,OAAO,IAAIJ,IAAI,EAAE;MACnB,IAAIG,WAAW,CAACE,MAAM,EAAE;QACtBH,eAAe,CAACE,OAAO,CAAC,GAAGD,WAAW;QACtCA,WAAW,GAAG,EAAE;MAClB;IACF,CAAC,MAAM;MACLA,WAAW,CAACG,IAAI,CAACF,OAAO,CAAC;IAC3B;EACF;EAEA,IAAIG,CAAC;EACL,IAAIC,YAAY,GAAG,CAAC,CAAC;EAErB,KAAK,IAAIC,OAAO,IAAIT,IAAI,EAAE;IACxB,IAAIE,eAAe,CAACO,OAAO,CAAC,EAAE;MAC5B,KAAKF,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGL,eAAe,CAACO,OAAO,CAAC,CAACJ,MAAM,EAAEE,CAAC,EAAE,EAAE;QACpD,IAAIG,cAAc,GAAGR,eAAe,CAACO,OAAO,CAAC,CAACF,CAAC,CAAC;QAChDC,YAAY,CAACN,eAAe,CAACO,OAAO,CAAC,CAACF,CAAC,CAAC,CAAC,GAAGN,cAAc,CAACS,cAAc,CAAC;MAC5E;IACF;IAEAF,YAAY,CAACC,OAAO,CAAC,GAAGR,cAAc,CAACQ,OAAO,CAAC;EACjD,CAAC,CAAC;;EAGF,KAAKF,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGJ,WAAW,CAACE,MAAM,EAAEE,CAAC,EAAE,EAAE;IACvCC,YAAY,CAACL,WAAW,CAACI,CAAC,CAAC,CAAC,GAAGN,cAAc,CAACE,WAAW,CAACI,CAAC,CAAC,CAAC;EAC/D;EAEA,OAAOC,YAAY;AACrB;AAEA,SAASG,OAAOA,CAACrB,KAAK,EAAEsB,IAAI,EAAEC,KAAK,EAAE;EACnC,OAAOA,KAAK,CAACD,IAAI,CAAC,IAAI,IAAI,GAAGC,KAAK,CAACD,IAAI,CAAC,GAAGtB,KAAK,CAACuB,KAAK,CAACD,IAAI,CAAC;AAC9D;AAEA,OAAO,SAASE,sBAAsBA,CAACD,KAAK,EAAEE,QAAQ,EAAE;EACtD,OAAO7B,eAAe,CAAC2B,KAAK,CAAC1B,QAAQ,EAAE,UAAUG,KAAK,EAAE;IACtD,OAAON,YAAY,CAACM,KAAK,EAAE;MACzByB,QAAQ,EAAEA,QAAQ,CAACC,IAAI,CAAC,IAAI,EAAE1B,KAAK,CAAC;MACpC2B,EAAE,EAAE,IAAI;MACRC,MAAM,EAAEP,OAAO,CAACrB,KAAK,EAAE,QAAQ,EAAEuB,KAAK,CAAC;MACvCM,KAAK,EAAER,OAAO,CAACrB,KAAK,EAAE,OAAO,EAAEuB,KAAK,CAAC;MACrCO,IAAI,EAAET,OAAO,CAACrB,KAAK,EAAE,MAAM,EAAEuB,KAAK;IACpC,CAAC,CAAC;EACJ,CAAC,CAAC;AACJ;AACA,OAAO,SAASQ,mBAAmBA,CAACC,SAAS,EAAEC,gBAAgB,EAAER,QAAQ,EAAE;EACzE,IAAIS,gBAAgB,GAAGtC,eAAe,CAACoC,SAAS,CAACnC,QAAQ,CAAC;EAC1D,IAAIA,QAAQ,GAAGW,kBAAkB,CAACyB,gBAAgB,EAAEC,gBAAgB,CAAC;EACrEhC,MAAM,CAACiC,IAAI,CAACtC,QAAQ,CAAC,CAACS,OAAO,CAAC,UAAUC,GAAG,EAAE;IAC3C,IAAIP,KAAK,GAAGH,QAAQ,CAACU,GAAG,CAAC;IACzB,IAAI,CAACZ,cAAc,CAACK,KAAK,CAAC,EAAE;IAC5B,IAAIoC,OAAO,GAAI7B,GAAG,IAAI0B,gBAAiB;IACvC,IAAII,OAAO,GAAI9B,GAAG,IAAI2B,gBAAiB;IACvC,IAAII,SAAS,GAAGL,gBAAgB,CAAC1B,GAAG,CAAC;IACrC,IAAIgC,SAAS,GAAG5C,cAAc,CAAC2C,SAAS,CAAC,IAAI,CAACA,SAAS,CAACf,KAAK,CAACI,EAAE,CAAC,CAAC;;IAElE,IAAIU,OAAO,KAAK,CAACD,OAAO,IAAIG,SAAS,CAAC,EAAE;MACtC;MACA1C,QAAQ,CAACU,GAAG,CAAC,GAAGb,YAAY,CAACM,KAAK,EAAE;QAClCyB,QAAQ,EAAEA,QAAQ,CAACC,IAAI,CAAC,IAAI,EAAE1B,KAAK,CAAC;QACpC2B,EAAE,EAAE,IAAI;QACRG,IAAI,EAAET,OAAO,CAACrB,KAAK,EAAE,MAAM,EAAEgC,SAAS,CAAC;QACvCH,KAAK,EAAER,OAAO,CAACrB,KAAK,EAAE,OAAO,EAAEgC,SAAS;MAC1C,CAAC,CAAC;IACJ,CAAC,MAAM,IAAI,CAACK,OAAO,IAAID,OAAO,IAAI,CAACG,SAAS,EAAE;MAC5C;MACA;MACA1C,QAAQ,CAACU,GAAG,CAAC,GAAGb,YAAY,CAACM,KAAK,EAAE;QAClC2B,EAAE,EAAE;MACN,CAAC,CAAC;IACJ,CAAC,MAAM,IAAIU,OAAO,IAAID,OAAO,IAAIzC,cAAc,CAAC2C,SAAS,CAAC,EAAE;MAC1D;MACA;MACA;MACAzC,QAAQ,CAACU,GAAG,CAAC,GAAGb,YAAY,CAACM,KAAK,EAAE;QAClCyB,QAAQ,EAAEA,QAAQ,CAACC,IAAI,CAAC,IAAI,EAAE1B,KAAK,CAAC;QACpC2B,EAAE,EAAEW,SAAS,CAACf,KAAK,CAACI,EAAE;QACtBG,IAAI,EAAET,OAAO,CAACrB,KAAK,EAAE,MAAM,EAAEgC,SAAS,CAAC;QACvCH,KAAK,EAAER,OAAO,CAACrB,KAAK,EAAE,OAAO,EAAEgC,SAAS;MAC1C,CAAC,CAAC;IACJ;EACF,CAAC,CAAC;EACF,OAAOnC,QAAQ;AACjB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}