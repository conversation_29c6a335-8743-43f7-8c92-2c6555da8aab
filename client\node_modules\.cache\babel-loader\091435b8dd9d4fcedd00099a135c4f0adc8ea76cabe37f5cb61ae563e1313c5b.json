{"ast": null, "code": "var castPath = require('./_castPath'),\n  isArguments = require('./isArguments'),\n  isArray = require('./isArray'),\n  isIndex = require('./_isIndex'),\n  isLength = require('./isLength'),\n  toKey = require('./_toKey');\n\n/**\n * Checks if `path` exists on `object`.\n *\n * @private\n * @param {Object} object The object to query.\n * @param {Array|string} path The path to check.\n * @param {Function} hasFunc The function to check properties.\n * @returns {boolean} Returns `true` if `path` exists, else `false`.\n */\nfunction hasPath(object, path, hasFunc) {\n  path = castPath(path, object);\n  var index = -1,\n    length = path.length,\n    result = false;\n  while (++index < length) {\n    var key = toKey(path[index]);\n    if (!(result = object != null && hasFunc(object, key))) {\n      break;\n    }\n    object = object[key];\n  }\n  if (result || ++index != length) {\n    return result;\n  }\n  length = object == null ? 0 : object.length;\n  return !!length && isLength(length) && isIndex(key, length) && (isArray(object) || isArguments(object));\n}\nmodule.exports = hasPath;", "map": {"version": 3, "names": ["<PERSON><PERSON><PERSON>", "require", "isArguments", "isArray", "isIndex", "<PERSON><PERSON><PERSON><PERSON>", "to<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "object", "path", "hasFunc", "index", "length", "result", "key", "module", "exports"], "sources": ["D:/ecommerce/node_modules/lodash/_hasPath.js"], "sourcesContent": ["var castPath = require('./_castPath'),\n    isArguments = require('./isArguments'),\n    isArray = require('./isArray'),\n    isIndex = require('./_isIndex'),\n    isLength = require('./isLength'),\n    toKey = require('./_toKey');\n\n/**\n * Checks if `path` exists on `object`.\n *\n * @private\n * @param {Object} object The object to query.\n * @param {Array|string} path The path to check.\n * @param {Function} hasFunc The function to check properties.\n * @returns {boolean} Returns `true` if `path` exists, else `false`.\n */\nfunction hasPath(object, path, hasFunc) {\n  path = castPath(path, object);\n\n  var index = -1,\n      length = path.length,\n      result = false;\n\n  while (++index < length) {\n    var key = toKey(path[index]);\n    if (!(result = object != null && hasFunc(object, key))) {\n      break;\n    }\n    object = object[key];\n  }\n  if (result || ++index != length) {\n    return result;\n  }\n  length = object == null ? 0 : object.length;\n  return !!length && isLength(length) && isIndex(key, length) &&\n    (isArray(object) || isArguments(object));\n}\n\nmodule.exports = hasPath;\n"], "mappings": "AAAA,IAAIA,QAAQ,GAAGC,OAAO,CAAC,aAAa,CAAC;EACjCC,WAAW,GAAGD,OAAO,CAAC,eAAe,CAAC;EACtCE,OAAO,GAAGF,OAAO,CAAC,WAAW,CAAC;EAC9BG,OAAO,GAAGH,OAAO,CAAC,YAAY,CAAC;EAC/BI,QAAQ,GAAGJ,OAAO,CAAC,YAAY,CAAC;EAChCK,KAAK,GAAGL,OAAO,CAAC,UAAU,CAAC;;AAE/B;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASM,OAAOA,CAACC,MAAM,EAAEC,IAAI,EAAEC,OAAO,EAAE;EACtCD,IAAI,GAAGT,QAAQ,CAACS,IAAI,EAAED,MAAM,CAAC;EAE7B,IAAIG,KAAK,GAAG,CAAC,CAAC;IACVC,MAAM,GAAGH,IAAI,CAACG,MAAM;IACpBC,MAAM,GAAG,KAAK;EAElB,OAAO,EAAEF,KAAK,GAAGC,MAAM,EAAE;IACvB,IAAIE,GAAG,GAAGR,KAAK,CAACG,IAAI,CAACE,KAAK,CAAC,CAAC;IAC5B,IAAI,EAAEE,MAAM,GAAGL,MAAM,IAAI,IAAI,IAAIE,OAAO,CAACF,MAAM,EAAEM,GAAG,CAAC,CAAC,EAAE;MACtD;IACF;IACAN,MAAM,GAAGA,MAAM,CAACM,GAAG,CAAC;EACtB;EACA,IAAID,MAAM,IAAI,EAAEF,KAAK,IAAIC,MAAM,EAAE;IAC/B,OAAOC,MAAM;EACf;EACAD,MAAM,GAAGJ,MAAM,IAAI,IAAI,GAAG,CAAC,GAAGA,MAAM,CAACI,MAAM;EAC3C,OAAO,CAAC,CAACA,MAAM,IAAIP,QAAQ,CAACO,MAAM,CAAC,IAAIR,OAAO,CAACU,GAAG,EAAEF,MAAM,CAAC,KACxDT,OAAO,CAACK,MAAM,CAAC,IAAIN,WAAW,CAACM,MAAM,CAAC,CAAC;AAC5C;AAEAO,MAAM,CAACC,OAAO,GAAGT,OAAO", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}