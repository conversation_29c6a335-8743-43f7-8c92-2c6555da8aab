{"ast": null, "code": "var baseHasIn = require('./_baseHasIn'),\n  hasPath = require('./_hasPath');\n\n/**\n * Checks if `path` is a direct or inherited property of `object`.\n *\n * @static\n * @memberOf _\n * @since 4.0.0\n * @category Object\n * @param {Object} object The object to query.\n * @param {Array|string} path The path to check.\n * @returns {boolean} Returns `true` if `path` exists, else `false`.\n * @example\n *\n * var object = _.create({ 'a': _.create({ 'b': 2 }) });\n *\n * _.hasIn(object, 'a');\n * // => true\n *\n * _.hasIn(object, 'a.b');\n * // => true\n *\n * _.hasIn(object, ['a', 'b']);\n * // => true\n *\n * _.hasIn(object, 'b');\n * // => false\n */\nfunction hasIn(object, path) {\n  return object != null && hasPath(object, path, baseHasIn);\n}\nmodule.exports = hasIn;", "map": {"version": 3, "names": ["baseHasIn", "require", "<PERSON><PERSON><PERSON>", "hasIn", "object", "path", "module", "exports"], "sources": ["D:/ecommerce/node_modules/lodash/hasIn.js"], "sourcesContent": ["var baseHasIn = require('./_baseHasIn'),\n    hasPath = require('./_hasPath');\n\n/**\n * Checks if `path` is a direct or inherited property of `object`.\n *\n * @static\n * @memberOf _\n * @since 4.0.0\n * @category Object\n * @param {Object} object The object to query.\n * @param {Array|string} path The path to check.\n * @returns {boolean} Returns `true` if `path` exists, else `false`.\n * @example\n *\n * var object = _.create({ 'a': _.create({ 'b': 2 }) });\n *\n * _.hasIn(object, 'a');\n * // => true\n *\n * _.hasIn(object, 'a.b');\n * // => true\n *\n * _.hasIn(object, ['a', 'b']);\n * // => true\n *\n * _.hasIn(object, 'b');\n * // => false\n */\nfunction hasIn(object, path) {\n  return object != null && hasPath(object, path, baseHasIn);\n}\n\nmodule.exports = hasIn;\n"], "mappings": "AAAA,IAAIA,SAAS,GAAGC,OAAO,CAAC,cAAc,CAAC;EACnCC,OAAO,GAAGD,OAAO,CAAC,YAAY,CAAC;;AAEnC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASE,KAAKA,CAACC,MAAM,EAAEC,IAAI,EAAE;EAC3B,OAAOD,MAAM,IAAI,IAAI,IAAIF,OAAO,CAACE,MAAM,EAAEC,IAAI,EAAEL,SAAS,CAAC;AAC3D;AAEAM,MAAM,CAACC,OAAO,GAAGJ,KAAK", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}