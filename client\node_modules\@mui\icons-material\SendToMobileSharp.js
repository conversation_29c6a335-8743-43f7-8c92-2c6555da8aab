"use strict";
"use client";

var _interopRequireDefault = require("@babel/runtime/helpers/interopRequireDefault");
Object.defineProperty(exports, "__esModule", {
  value: true
});
exports.default = void 0;
var _createSvgIcon = _interopRequireDefault(require("./utils/createSvgIcon"));
var _jsxRuntime = require("react/jsx-runtime");
var _default = exports.default = (0, _createSvgIcon.default)([/*#__PURE__*/(0, _jsxRuntime.jsx)("path", {
  d: "M17 18H7V6h10v1h2V1H5v22h14v-6h-2z"
}, "0"), /*#__PURE__*/(0, _jsxRuntime.jsx)("path", {
  d: "m22 12-4-4v3h-5v2h5v3z"
}, "1")], 'SendToMobileSharp');