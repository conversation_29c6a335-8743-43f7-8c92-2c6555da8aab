{"ast": null, "code": "/**\n * @fileOverview Pie Chart\n */\nimport { generateCategoricalChart } from './generateCategoricalChart';\nimport { PolarAngleAxis } from '../polar/PolarAngleAxis';\nimport { PolarRadiusAxis } from '../polar/PolarRadiusAxis';\nimport { formatAxisMap } from '../util/PolarUtils';\nimport { Pie } from '../polar/Pie';\nexport var PieChart = generateCategoricalChart({\n  chartName: 'PieChart',\n  GraphicalChild: Pie,\n  validateTooltipEventTypes: ['item'],\n  defaultTooltipEventType: 'item',\n  legendContent: 'children',\n  axisComponents: [{\n    axisType: 'angleAxis',\n    AxisComp: PolarAngleAxis\n  }, {\n    axisType: 'radiusAxis',\n    AxisComp: PolarRadiusAxis\n  }],\n  formatAxisMap: formatAxisMap,\n  defaultProps: {\n    layout: 'centric',\n    startAngle: 0,\n    endAngle: 360,\n    cx: '50%',\n    cy: '50%',\n    innerRadius: 0,\n    outerRadius: '80%'\n  }\n});", "map": {"version": 3, "names": ["generateCategoricalChart", "PolarAngleAxis", "PolarRadiusAxis", "formatAxisMap", "Pie", "<PERSON><PERSON><PERSON>", "chartName", "GraphicalChild", "validateTooltipEventTypes", "defaultTooltipEventType", "<PERSON><PERSON><PERSON><PERSON>", "axisComponents", "axisType", "AxisComp", "defaultProps", "layout", "startAngle", "endAngle", "cx", "cy", "innerRadius", "outerRadius"], "sources": ["D:/ecommerce/node_modules/recharts/es6/chart/PieChart.js"], "sourcesContent": ["/**\n * @fileOverview Pie Chart\n */\nimport { generateCategoricalChart } from './generateCategoricalChart';\nimport { PolarAngleAxis } from '../polar/PolarAngleAxis';\nimport { PolarRadiusAxis } from '../polar/PolarRadiusAxis';\nimport { formatAxisMap } from '../util/PolarUtils';\nimport { Pie } from '../polar/Pie';\nexport var PieChart = generateCategoricalChart({\n  chartName: 'PieChart',\n  GraphicalChild: Pie,\n  validateTooltipEventTypes: ['item'],\n  defaultTooltipEventType: 'item',\n  legendContent: 'children',\n  axisComponents: [{\n    axisType: 'angleAxis',\n    AxisComp: PolarAngleAxis\n  }, {\n    axisType: 'radiusAxis',\n    AxisComp: PolarRadiusAxis\n  }],\n  formatAxisMap: formatAxisMap,\n  defaultProps: {\n    layout: 'centric',\n    startAngle: 0,\n    endAngle: 360,\n    cx: '50%',\n    cy: '50%',\n    innerRadius: 0,\n    outerRadius: '80%'\n  }\n});"], "mappings": "AAAA;AACA;AACA;AACA,SAASA,wBAAwB,QAAQ,4BAA4B;AACrE,SAASC,cAAc,QAAQ,yBAAyB;AACxD,SAASC,eAAe,QAAQ,0BAA0B;AAC1D,SAASC,aAAa,QAAQ,oBAAoB;AAClD,SAASC,GAAG,QAAQ,cAAc;AAClC,OAAO,IAAIC,QAAQ,GAAGL,wBAAwB,CAAC;EAC7CM,SAAS,EAAE,UAAU;EACrBC,cAAc,EAAEH,GAAG;EACnBI,yBAAyB,EAAE,CAAC,MAAM,CAAC;EACnCC,uBAAuB,EAAE,MAAM;EAC/BC,aAAa,EAAE,UAAU;EACzBC,cAAc,EAAE,CAAC;IACfC,QAAQ,EAAE,WAAW;IACrBC,QAAQ,EAAEZ;EACZ,CAAC,EAAE;IACDW,QAAQ,EAAE,YAAY;IACtBC,QAAQ,EAAEX;EACZ,CAAC,CAAC;EACFC,aAAa,EAAEA,aAAa;EAC5BW,YAAY,EAAE;IACZC,MAAM,EAAE,SAAS;IACjBC,UAAU,EAAE,CAAC;IACbC,QAAQ,EAAE,GAAG;IACbC,EAAE,EAAE,KAAK;IACTC,EAAE,EAAE,KAAK;IACTC,WAAW,EAAE,CAAC;IACdC,WAAW,EAAE;EACf;AACF,CAAC,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}