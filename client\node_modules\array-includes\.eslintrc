{
	"root": true,

	"extends": "@ljharb",

	"rules": {
		"id-length": [2, { "min": 1, "max": 26 }],
		"new-cap": [2, {
			"capIsNewExceptions": [
				"GetIntrinsic",
				"ToObject",
				"ToIntegerOrInfinity",
				"ToLength",
				"SameValueZero",
				"RequireObjectCoercible",
			],
		}],
	},

	"overrides": [
		{
			"files": "test/**",
			"rules": {
				"max-lines-per-function": 0,
				"no-invalid-this": 1,
			},
		},
	],
}
