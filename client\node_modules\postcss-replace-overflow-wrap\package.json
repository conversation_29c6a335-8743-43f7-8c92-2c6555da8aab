{"name": "postcss-replace-overflow-wrap", "version": "4.0.0", "description": "PostCSS plugin to replace overflow-wrap with word-wrap or optionally retain both declarations.", "keywords": ["postcss", "css", "postcss-plugin", "overflow-wrap", "word-wrap"], "author": "<PERSON> <<EMAIL>>", "license": "MIT", "repository": "MattDiMu/postcss-replace-overflow-wrap", "bugs": {"url": "https://github.com/MattDiMu/postcss-replace-overflow-wrap/issues"}, "homepage": "https://github.com/MattDiMu/postcss-replace-overflow-wrap", "files": ["index.js"], "dependencies": {}, "devDependencies": {"ava": "^0.25.0", "eslint": "^5.3.0", "eslint-config-logux": "^24.0.0", "eslint-config-postcss": "^3.0.3", "eslint-config-standard": "^11.0.0", "eslint-plugin-es5": "^1.3.1", "eslint-plugin-import": "^2.13.0", "eslint-plugin-jest": "^21.20.1", "eslint-plugin-node": "^7.0.1", "eslint-plugin-promise": "^3.8.0", "eslint-plugin-security": "^1.4.0", "eslint-plugin-standard": "^3.1.0", "postcss": "^8.0.3"}, "peerDependencies": {"postcss": "^8.0.3"}, "scripts": {"test": "ava && eslint *.js"}, "eslintConfig": {"extends": "eslint-config-postcss/es5", "rules": {"max-len": 0, "es5/no-modules": false, "indent": ["warn", 4], "es5/no-arrow-functions": 0}}}