import React, { useState, useEffect } from 'react';
import {
  Container,
  Typography,
  Paper,
  Grid,
  TextField,
  Button,
  Box,
  Stepper,
  Step,
  StepLabel,
  Card,
  CardContent,
  Divider,
  Alert,
  CircularProgress,
  FormControl,
  FormLabel,
  RadioGroup,
  FormControlLabel,
  Radio,
  List,
  ListItem,
  ListItemText,
  ListItemAvatar,
  Avatar,
  Breadcrumbs,
  Link,
} from '@mui/material';
import {
  Home as HomeIcon,
  NavigateNext,
  CreditCard,
  AccountBalance,
  Payment,
  LocalShipping,
  CheckCircle,
} from '@mui/icons-material';
import { styled } from '@mui/material/styles';
import { useAuth } from '../context/AuthContext';
import { useNavigate } from 'react-router-dom';
import { cartAPI, handleAPIError } from '../services/api';

// Flipkart-style styled components
const FlipkartHeader = styled(Paper)(({ theme }) => ({
  backgroundColor: '#2874f0',
  color: 'white',
  padding: theme.spacing(2),
  textAlign: 'center',
  marginBottom: theme.spacing(3),
}));

const Checkout = () => {
  const { user } = useAuth();
  const navigate = useNavigate();
  const [activeStep, setActiveStep] = useState(0);
  const [cartItems, setCartItems] = useState([]);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState('');
  const [success, setSuccess] = useState('');
  
  const [shippingAddress, setShippingAddress] = useState({
    firstName: user?.name?.split(' ')[0] || '',
    lastName: user?.name?.split(' ')[1] || '',
    address: '',
    city: '',
    zipCode: '',
    phone: '',
  });
  
  const [paymentMethod, setPaymentMethod] = useState('card');

  const steps = ['Shipping Address', 'Payment Method', 'Review Order'];

  useEffect(() => {
    const cart = cartAPI.getCart();
    setCartItems(cart.items);
  }, []);

  const calculateTotal = () => {
    return cartItems.reduce((sum, item) => sum + (item.price * item.quantity), 0);
  };

  const total = calculateTotal();
  const deliveryCharge = total > 500 ? 0 : 40;
  const finalTotal = total + deliveryCharge;

  const handleNext = () => {
    if (activeStep === 0 && !validateShipping()) {
      return;
    }
    setActiveStep((prevStep) => prevStep + 1);
  };

  const handleBack = () => {
    setActiveStep((prevStep) => prevStep - 1);
  };

  const validateShipping = () => {
    const { firstName, lastName, address, city, zipCode, phone } = shippingAddress;
    if (!firstName || !lastName || !address || !city || !zipCode || !phone) {
      setError('Please fill in all shipping address fields');
      return false;
    }
    setError('');
    return true;
  };

  const handlePlaceOrder = async () => {
    try {
      setLoading(true);
      setError('');

      // Simulate API call
      await new Promise(resolve => setTimeout(resolve, 2000));
      
      // Clear cart
      cartAPI.clearCart();
      
      setSuccess('Order placed successfully! Redirecting...');
      
      setTimeout(() => {
        navigate('/dashboard');
      }, 2000);
      
    } catch (err) {
      setError(handleAPIError(err));
    } finally {
      setLoading(false);
    }
  };

  const renderStepContent = (step) => {
    switch (step) {
      case 0:
        return (
          <Paper sx={{ p: 3 }}>
            <Typography variant="h6" gutterBottom>
              Shipping Address
            </Typography>
            <Grid container spacing={2}>
              <Grid item xs={12} sm={6}>
                <TextField
                  fullWidth
                  label="First Name"
                  value={shippingAddress.firstName}
                  onChange={(e) => setShippingAddress({
                    ...shippingAddress,
                    firstName: e.target.value
                  })}
                  required
                />
              </Grid>
              <Grid item xs={12} sm={6}>
                <TextField
                  fullWidth
                  label="Last Name"
                  value={shippingAddress.lastName}
                  onChange={(e) => setShippingAddress({
                    ...shippingAddress,
                    lastName: e.target.value
                  })}
                  required
                />
              </Grid>
              <Grid item xs={12}>
                <TextField
                  fullWidth
                  label="Address"
                  value={shippingAddress.address}
                  onChange={(e) => setShippingAddress({
                    ...shippingAddress,
                    address: e.target.value
                  })}
                  required
                />
              </Grid>
              <Grid item xs={12} sm={6}>
                <TextField
                  fullWidth
                  label="City"
                  value={shippingAddress.city}
                  onChange={(e) => setShippingAddress({
                    ...shippingAddress,
                    city: e.target.value
                  })}
                  required
                />
              </Grid>
              <Grid item xs={12} sm={6}>
                <TextField
                  fullWidth
                  label="ZIP Code"
                  value={shippingAddress.zipCode}
                  onChange={(e) => setShippingAddress({
                    ...shippingAddress,
                    zipCode: e.target.value
                  })}
                  required
                />
              </Grid>
              <Grid item xs={12}>
                <TextField
                  fullWidth
                  label="Phone Number"
                  value={shippingAddress.phone}
                  onChange={(e) => setShippingAddress({
                    ...shippingAddress,
                    phone: e.target.value
                  })}
                  required
                />
              </Grid>
            </Grid>
          </Paper>
        );
      
      case 1:
        return (
          <Paper sx={{ p: 3 }}>
            <Typography variant="h6" gutterBottom>
              Payment Method
            </Typography>
            <FormControl component="fieldset">
              <RadioGroup
                value={paymentMethod}
                onChange={(e) => setPaymentMethod(e.target.value)}
              >
                <FormControlLabel
                  value="card"
                  control={<Radio />}
                  label={
                    <Box sx={{ display: 'flex', alignItems: 'center' }}>
                      <CreditCard sx={{ mr: 1 }} />
                      Credit/Debit Card
                    </Box>
                  }
                />
                <FormControlLabel
                  value="upi"
                  control={<Radio />}
                  label={
                    <Box sx={{ display: 'flex', alignItems: 'center' }}>
                      <Payment sx={{ mr: 1 }} />
                      UPI
                    </Box>
                  }
                />
                <FormControlLabel
                  value="netbanking"
                  control={<Radio />}
                  label={
                    <Box sx={{ display: 'flex', alignItems: 'center' }}>
                      <AccountBalance sx={{ mr: 1 }} />
                      Net Banking
                    </Box>
                  }
                />
                <FormControlLabel
                  value="cod"
                  control={<Radio />}
                  label={
                    <Box sx={{ display: 'flex', alignItems: 'center' }}>
                      <LocalShipping sx={{ mr: 1 }} />
                      Cash on Delivery
                    </Box>
                  }
                />
              </RadioGroup>
            </FormControl>
          </Paper>
        );
      
      case 2:
        return (
          <Paper sx={{ p: 3 }}>
            <Typography variant="h6" gutterBottom>
              Review Your Order
            </Typography>
            
            <Typography variant="subtitle1" sx={{ mt: 2, mb: 1, fontWeight: 'bold' }}>
              Shipping Address:
            </Typography>
            <Typography variant="body2">
              {shippingAddress.firstName} {shippingAddress.lastName}<br />
              {shippingAddress.address}<br />
              {shippingAddress.city}, {shippingAddress.zipCode}<br />
              Phone: {shippingAddress.phone}
            </Typography>
            
            <Typography variant="subtitle1" sx={{ mt: 2, mb: 1, fontWeight: 'bold' }}>
              Payment Method:
            </Typography>
            <Typography variant="body2" sx={{ textTransform: 'capitalize' }}>
              {paymentMethod.replace(/([A-Z])/g, ' $1')}
            </Typography>
            
            <Typography variant="subtitle1" sx={{ mt: 2, mb: 1, fontWeight: 'bold' }}>
              Order Items:
            </Typography>
            <List>
              {cartItems.map((item) => (
                <ListItem key={item.id}>
                  <ListItemAvatar>
                    <Avatar src={item.image} variant="square" />
                  </ListItemAvatar>
                  <ListItemText
                    primary={item.name}
                    secondary={`Quantity: ${item.quantity} × ₹${(item.price * 75).toFixed(0)}`}
                  />
                </ListItem>
              ))}
            </List>
          </Paper>
        );
      
      default:
        return null;
    }
  };

  return (
    <Box sx={{ backgroundColor: '#f1f3f6', minHeight: '100vh' }}>
      {/* Header */}
      <FlipkartHeader elevation={0}>
        <Typography variant="h5" sx={{ fontWeight: 'bold' }}>
          🛒 SpiceMart - Checkout
        </Typography>
      </FlipkartHeader>

      {/* Breadcrumbs */}
      <Container maxWidth="lg" sx={{ mb: 2 }}>
        <Breadcrumbs separator={<NavigateNext fontSize="small" />}>
          <Link color="inherit" href="/" sx={{ display: 'flex', alignItems: 'center' }}>
            <HomeIcon sx={{ mr: 0.5 }} fontSize="inherit" />
            Home
          </Link>
          <Link color="inherit" href="/cart">
            Cart
          </Link>
          <Typography color="text.primary">Checkout</Typography>
        </Breadcrumbs>
      </Container>

      <Container maxWidth="lg" sx={{ pb: 4 }}>
        {error && (
          <Alert severity="error" sx={{ mb: 2 }}>
            {error}
          </Alert>
        )}

        {success && (
          <Alert severity="success" sx={{ mb: 2 }}>
            {success}
          </Alert>
        )}

        <Stepper activeStep={activeStep} sx={{ mb: 4 }}>
          {steps.map((label) => (
            <Step key={label}>
              <StepLabel>{label}</StepLabel>
            </Step>
          ))}
        </Stepper>

        <Grid container spacing={4}>
          <Grid item xs={12} md={8}>
            {renderStepContent(activeStep)}
            
            <Box sx={{ display: 'flex', justifyContent: 'space-between', mt: 3 }}>
              <Button
                disabled={activeStep === 0}
                onClick={handleBack}
                variant="outlined"
              >
                Back
              </Button>
              
              {activeStep === steps.length - 1 ? (
                <Button
                  variant="contained"
                  onClick={handlePlaceOrder}
                  disabled={loading}
                  sx={{
                    backgroundColor: '#fb641b',
                    '&:hover': { backgroundColor: '#e55a16' },
                  }}
                >
                  {loading ? <CircularProgress size={24} /> : 'Place Order'}
                </Button>
              ) : (
                <Button
                  variant="contained"
                  onClick={handleNext}
                  sx={{
                    backgroundColor: '#2874f0',
                    '&:hover': { backgroundColor: '#1e5bb8' },
                  }}
                >
                  Next
                </Button>
              )}
            </Box>
          </Grid>
          
          <Grid item xs={12} md={4}>
            <Card sx={{ position: 'sticky', top: 20 }}>
              <CardContent>
                <Typography variant="h6" gutterBottom>
                  Order Summary
                </Typography>
                <Divider sx={{ my: 2 }} />
                
                <Box sx={{ display: 'flex', justifyContent: 'space-between', mb: 1 }}>
                  <Typography>Items ({cartItems.length})</Typography>
                  <Typography>₹{(total * 75).toFixed(0)}</Typography>
                </Box>
                
                <Box sx={{ display: 'flex', justifyContent: 'space-between', mb: 1 }}>
                  <Typography>Delivery</Typography>
                  <Typography sx={{ color: deliveryCharge === 0 ? '#388e3c' : 'inherit' }}>
                    {deliveryCharge === 0 ? 'FREE' : `₹${deliveryCharge}`}
                  </Typography>
                </Box>
                
                <Divider sx={{ my: 2 }} />
                
                <Box sx={{ display: 'flex', justifyContent: 'space-between', mb: 2 }}>
                  <Typography variant="h6" sx={{ fontWeight: 'bold' }}>
                    Total Amount
                  </Typography>
                  <Typography variant="h6" sx={{ fontWeight: 'bold' }}>
                    ₹{(finalTotal * 75).toFixed(0)}
                  </Typography>
                </Box>
                
                <Box sx={{ backgroundColor: '#f8f9fa', p: 2, borderRadius: 1 }}>
                  <Typography variant="caption" sx={{ display: 'flex', alignItems: 'center' }}>
                    <CheckCircle sx={{ fontSize: 16, mr: 1, color: '#388e3c' }} />
                    Safe and Secure Payments. Easy returns. 100% Authentic products.
                  </Typography>
                </Box>
              </CardContent>
            </Card>
          </Grid>
        </Grid>
      </Container>
    </Box>
  );
};

export default Checkout;
