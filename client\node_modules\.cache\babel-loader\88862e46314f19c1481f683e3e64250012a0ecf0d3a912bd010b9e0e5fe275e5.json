{"ast": null, "code": "/**\n * @license React\n * react-is.development.js\n *\n * Copyright (c) Meta Platforms, Inc. and affiliates.\n *\n * This source code is licensed under the MIT license found in the\n * LICENSE file in the root directory of this source tree.\n */\n\n\"use strict\";\n\n\"production\" !== process.env.NODE_ENV && function () {\n  function typeOf(object) {\n    if (\"object\" === typeof object && null !== object) {\n      var $$typeof = object.$$typeof;\n      switch ($$typeof) {\n        case REACT_ELEMENT_TYPE:\n          switch (object = object.type, object) {\n            case REACT_FRAGMENT_TYPE:\n            case REACT_PROFILER_TYPE:\n            case REACT_STRICT_MODE_TYPE:\n            case REACT_SUSPENSE_TYPE:\n            case REACT_SUSPENSE_LIST_TYPE:\n            case REACT_VIEW_TRANSITION_TYPE:\n              return object;\n            default:\n              switch (object = object && object.$$typeof, object) {\n                case REACT_CONTEXT_TYPE:\n                case REACT_FORWARD_REF_TYPE:\n                case REACT_LAZY_TYPE:\n                case REACT_MEMO_TYPE:\n                  return object;\n                case REACT_CONSUMER_TYPE:\n                  return object;\n                default:\n                  return $$typeof;\n              }\n          }\n        case REACT_PORTAL_TYPE:\n          return $$typeof;\n      }\n    }\n  }\n  var REACT_ELEMENT_TYPE = Symbol.for(\"react.transitional.element\"),\n    REACT_PORTAL_TYPE = Symbol.for(\"react.portal\"),\n    REACT_FRAGMENT_TYPE = Symbol.for(\"react.fragment\"),\n    REACT_STRICT_MODE_TYPE = Symbol.for(\"react.strict_mode\"),\n    REACT_PROFILER_TYPE = Symbol.for(\"react.profiler\");\n  Symbol.for(\"react.provider\");\n  var REACT_CONSUMER_TYPE = Symbol.for(\"react.consumer\"),\n    REACT_CONTEXT_TYPE = Symbol.for(\"react.context\"),\n    REACT_FORWARD_REF_TYPE = Symbol.for(\"react.forward_ref\"),\n    REACT_SUSPENSE_TYPE = Symbol.for(\"react.suspense\"),\n    REACT_SUSPENSE_LIST_TYPE = Symbol.for(\"react.suspense_list\"),\n    REACT_MEMO_TYPE = Symbol.for(\"react.memo\"),\n    REACT_LAZY_TYPE = Symbol.for(\"react.lazy\"),\n    REACT_VIEW_TRANSITION_TYPE = Symbol.for(\"react.view_transition\"),\n    REACT_CLIENT_REFERENCE = Symbol.for(\"react.client.reference\");\n  exports.ContextConsumer = REACT_CONSUMER_TYPE;\n  exports.ContextProvider = REACT_CONTEXT_TYPE;\n  exports.Element = REACT_ELEMENT_TYPE;\n  exports.ForwardRef = REACT_FORWARD_REF_TYPE;\n  exports.Fragment = REACT_FRAGMENT_TYPE;\n  exports.Lazy = REACT_LAZY_TYPE;\n  exports.Memo = REACT_MEMO_TYPE;\n  exports.Portal = REACT_PORTAL_TYPE;\n  exports.Profiler = REACT_PROFILER_TYPE;\n  exports.StrictMode = REACT_STRICT_MODE_TYPE;\n  exports.Suspense = REACT_SUSPENSE_TYPE;\n  exports.SuspenseList = REACT_SUSPENSE_LIST_TYPE;\n  exports.isContextConsumer = function (object) {\n    return typeOf(object) === REACT_CONSUMER_TYPE;\n  };\n  exports.isContextProvider = function (object) {\n    return typeOf(object) === REACT_CONTEXT_TYPE;\n  };\n  exports.isElement = function (object) {\n    return \"object\" === typeof object && null !== object && object.$$typeof === REACT_ELEMENT_TYPE;\n  };\n  exports.isForwardRef = function (object) {\n    return typeOf(object) === REACT_FORWARD_REF_TYPE;\n  };\n  exports.isFragment = function (object) {\n    return typeOf(object) === REACT_FRAGMENT_TYPE;\n  };\n  exports.isLazy = function (object) {\n    return typeOf(object) === REACT_LAZY_TYPE;\n  };\n  exports.isMemo = function (object) {\n    return typeOf(object) === REACT_MEMO_TYPE;\n  };\n  exports.isPortal = function (object) {\n    return typeOf(object) === REACT_PORTAL_TYPE;\n  };\n  exports.isProfiler = function (object) {\n    return typeOf(object) === REACT_PROFILER_TYPE;\n  };\n  exports.isStrictMode = function (object) {\n    return typeOf(object) === REACT_STRICT_MODE_TYPE;\n  };\n  exports.isSuspense = function (object) {\n    return typeOf(object) === REACT_SUSPENSE_TYPE;\n  };\n  exports.isSuspenseList = function (object) {\n    return typeOf(object) === REACT_SUSPENSE_LIST_TYPE;\n  };\n  exports.isValidElementType = function (type) {\n    return \"string\" === typeof type || \"function\" === typeof type || type === REACT_FRAGMENT_TYPE || type === REACT_PROFILER_TYPE || type === REACT_STRICT_MODE_TYPE || type === REACT_SUSPENSE_TYPE || type === REACT_SUSPENSE_LIST_TYPE || \"object\" === typeof type && null !== type && (type.$$typeof === REACT_LAZY_TYPE || type.$$typeof === REACT_MEMO_TYPE || type.$$typeof === REACT_CONTEXT_TYPE || type.$$typeof === REACT_CONSUMER_TYPE || type.$$typeof === REACT_FORWARD_REF_TYPE || type.$$typeof === REACT_CLIENT_REFERENCE || void 0 !== type.getModuleId) ? !0 : !1;\n  };\n  exports.typeOf = typeOf;\n}();", "map": {"version": 3, "names": ["process", "env", "NODE_ENV", "typeOf", "object", "$$typeof", "REACT_ELEMENT_TYPE", "type", "REACT_FRAGMENT_TYPE", "REACT_PROFILER_TYPE", "REACT_STRICT_MODE_TYPE", "REACT_SUSPENSE_TYPE", "REACT_SUSPENSE_LIST_TYPE", "REACT_VIEW_TRANSITION_TYPE", "REACT_CONTEXT_TYPE", "REACT_FORWARD_REF_TYPE", "REACT_LAZY_TYPE", "REACT_MEMO_TYPE", "REACT_CONSUMER_TYPE", "REACT_PORTAL_TYPE", "Symbol", "for", "REACT_CLIENT_REFERENCE", "exports", "ContextConsumer", "ContextProvider", "Element", "ForwardRef", "Fragment", "Lazy", "Memo", "Portal", "Profiler", "StrictMode", "Suspense", "SuspenseList", "isContextConsumer", "isContextProvider", "isElement", "isForwardRef", "isFragment", "isLazy", "isMemo", "isPortal", "isProfiler", "isStrictMode", "isSuspense", "isSuspenseList", "isValidElementType", "getModuleId"], "sources": ["D:/ecommerce/client/node_modules/react-is/cjs/react-is.development.js"], "sourcesContent": ["/**\n * @license React\n * react-is.development.js\n *\n * Copyright (c) Meta Platforms, Inc. and affiliates.\n *\n * This source code is licensed under the MIT license found in the\n * LICENSE file in the root directory of this source tree.\n */\n\n\"use strict\";\n\"production\" !== process.env.NODE_ENV &&\n  (function () {\n    function typeOf(object) {\n      if (\"object\" === typeof object && null !== object) {\n        var $$typeof = object.$$typeof;\n        switch ($$typeof) {\n          case REACT_ELEMENT_TYPE:\n            switch (((object = object.type), object)) {\n              case REACT_FRAGMENT_TYPE:\n              case REACT_PROFILER_TYPE:\n              case REACT_STRICT_MODE_TYPE:\n              case REACT_SUSPENSE_TYPE:\n              case REACT_SUSPENSE_LIST_TYPE:\n              case REACT_VIEW_TRANSITION_TYPE:\n                return object;\n              default:\n                switch (((object = object && object.$$typeof), object)) {\n                  case REACT_CONTEXT_TYPE:\n                  case REACT_FORWARD_REF_TYPE:\n                  case REACT_LAZY_TYPE:\n                  case REACT_MEMO_TYPE:\n                    return object;\n                  case REACT_CONSUMER_TYPE:\n                    return object;\n                  default:\n                    return $$typeof;\n                }\n            }\n          case REACT_PORTAL_TYPE:\n            return $$typeof;\n        }\n      }\n    }\n    var REACT_ELEMENT_TYPE = Symbol.for(\"react.transitional.element\"),\n      REACT_PORTAL_TYPE = Symbol.for(\"react.portal\"),\n      REACT_FRAGMENT_TYPE = Symbol.for(\"react.fragment\"),\n      REACT_STRICT_MODE_TYPE = Symbol.for(\"react.strict_mode\"),\n      REACT_PROFILER_TYPE = Symbol.for(\"react.profiler\");\n    Symbol.for(\"react.provider\");\n    var REACT_CONSUMER_TYPE = Symbol.for(\"react.consumer\"),\n      REACT_CONTEXT_TYPE = Symbol.for(\"react.context\"),\n      REACT_FORWARD_REF_TYPE = Symbol.for(\"react.forward_ref\"),\n      REACT_SUSPENSE_TYPE = Symbol.for(\"react.suspense\"),\n      REACT_SUSPENSE_LIST_TYPE = Symbol.for(\"react.suspense_list\"),\n      REACT_MEMO_TYPE = Symbol.for(\"react.memo\"),\n      REACT_LAZY_TYPE = Symbol.for(\"react.lazy\"),\n      REACT_VIEW_TRANSITION_TYPE = Symbol.for(\"react.view_transition\"),\n      REACT_CLIENT_REFERENCE = Symbol.for(\"react.client.reference\");\n    exports.ContextConsumer = REACT_CONSUMER_TYPE;\n    exports.ContextProvider = REACT_CONTEXT_TYPE;\n    exports.Element = REACT_ELEMENT_TYPE;\n    exports.ForwardRef = REACT_FORWARD_REF_TYPE;\n    exports.Fragment = REACT_FRAGMENT_TYPE;\n    exports.Lazy = REACT_LAZY_TYPE;\n    exports.Memo = REACT_MEMO_TYPE;\n    exports.Portal = REACT_PORTAL_TYPE;\n    exports.Profiler = REACT_PROFILER_TYPE;\n    exports.StrictMode = REACT_STRICT_MODE_TYPE;\n    exports.Suspense = REACT_SUSPENSE_TYPE;\n    exports.SuspenseList = REACT_SUSPENSE_LIST_TYPE;\n    exports.isContextConsumer = function (object) {\n      return typeOf(object) === REACT_CONSUMER_TYPE;\n    };\n    exports.isContextProvider = function (object) {\n      return typeOf(object) === REACT_CONTEXT_TYPE;\n    };\n    exports.isElement = function (object) {\n      return (\n        \"object\" === typeof object &&\n        null !== object &&\n        object.$$typeof === REACT_ELEMENT_TYPE\n      );\n    };\n    exports.isForwardRef = function (object) {\n      return typeOf(object) === REACT_FORWARD_REF_TYPE;\n    };\n    exports.isFragment = function (object) {\n      return typeOf(object) === REACT_FRAGMENT_TYPE;\n    };\n    exports.isLazy = function (object) {\n      return typeOf(object) === REACT_LAZY_TYPE;\n    };\n    exports.isMemo = function (object) {\n      return typeOf(object) === REACT_MEMO_TYPE;\n    };\n    exports.isPortal = function (object) {\n      return typeOf(object) === REACT_PORTAL_TYPE;\n    };\n    exports.isProfiler = function (object) {\n      return typeOf(object) === REACT_PROFILER_TYPE;\n    };\n    exports.isStrictMode = function (object) {\n      return typeOf(object) === REACT_STRICT_MODE_TYPE;\n    };\n    exports.isSuspense = function (object) {\n      return typeOf(object) === REACT_SUSPENSE_TYPE;\n    };\n    exports.isSuspenseList = function (object) {\n      return typeOf(object) === REACT_SUSPENSE_LIST_TYPE;\n    };\n    exports.isValidElementType = function (type) {\n      return \"string\" === typeof type ||\n        \"function\" === typeof type ||\n        type === REACT_FRAGMENT_TYPE ||\n        type === REACT_PROFILER_TYPE ||\n        type === REACT_STRICT_MODE_TYPE ||\n        type === REACT_SUSPENSE_TYPE ||\n        type === REACT_SUSPENSE_LIST_TYPE ||\n        (\"object\" === typeof type &&\n          null !== type &&\n          (type.$$typeof === REACT_LAZY_TYPE ||\n            type.$$typeof === REACT_MEMO_TYPE ||\n            type.$$typeof === REACT_CONTEXT_TYPE ||\n            type.$$typeof === REACT_CONSUMER_TYPE ||\n            type.$$typeof === REACT_FORWARD_REF_TYPE ||\n            type.$$typeof === REACT_CLIENT_REFERENCE ||\n            void 0 !== type.getModuleId))\n        ? !0\n        : !1;\n    };\n    exports.typeOf = typeOf;\n  })();\n"], "mappings": "AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA,YAAY;;AACZ,YAAY,KAAKA,OAAO,CAACC,GAAG,CAACC,QAAQ,IAClC,YAAY;EACX,SAASC,MAAMA,CAACC,MAAM,EAAE;IACtB,IAAI,QAAQ,KAAK,OAAOA,MAAM,IAAI,IAAI,KAAKA,MAAM,EAAE;MACjD,IAAIC,QAAQ,GAAGD,MAAM,CAACC,QAAQ;MAC9B,QAAQA,QAAQ;QACd,KAAKC,kBAAkB;UACrB,QAAUF,MAAM,GAAGA,MAAM,CAACG,IAAI,EAAGH,MAAM;YACrC,KAAKI,mBAAmB;YACxB,KAAKC,mBAAmB;YACxB,KAAKC,sBAAsB;YAC3B,KAAKC,mBAAmB;YACxB,KAAKC,wBAAwB;YAC7B,KAAKC,0BAA0B;cAC7B,OAAOT,MAAM;YACf;cACE,QAAUA,MAAM,GAAGA,MAAM,IAAIA,MAAM,CAACC,QAAQ,EAAGD,MAAM;gBACnD,KAAKU,kBAAkB;gBACvB,KAAKC,sBAAsB;gBAC3B,KAAKC,eAAe;gBACpB,KAAKC,eAAe;kBAClB,OAAOb,MAAM;gBACf,KAAKc,mBAAmB;kBACtB,OAAOd,MAAM;gBACf;kBACE,OAAOC,QAAQ;cACnB;UACJ;QACF,KAAKc,iBAAiB;UACpB,OAAOd,QAAQ;MACnB;IACF;EACF;EACA,IAAIC,kBAAkB,GAAGc,MAAM,CAACC,GAAG,CAAC,4BAA4B,CAAC;IAC/DF,iBAAiB,GAAGC,MAAM,CAACC,GAAG,CAAC,cAAc,CAAC;IAC9Cb,mBAAmB,GAAGY,MAAM,CAACC,GAAG,CAAC,gBAAgB,CAAC;IAClDX,sBAAsB,GAAGU,MAAM,CAACC,GAAG,CAAC,mBAAmB,CAAC;IACxDZ,mBAAmB,GAAGW,MAAM,CAACC,GAAG,CAAC,gBAAgB,CAAC;EACpDD,MAAM,CAACC,GAAG,CAAC,gBAAgB,CAAC;EAC5B,IAAIH,mBAAmB,GAAGE,MAAM,CAACC,GAAG,CAAC,gBAAgB,CAAC;IACpDP,kBAAkB,GAAGM,MAAM,CAACC,GAAG,CAAC,eAAe,CAAC;IAChDN,sBAAsB,GAAGK,MAAM,CAACC,GAAG,CAAC,mBAAmB,CAAC;IACxDV,mBAAmB,GAAGS,MAAM,CAACC,GAAG,CAAC,gBAAgB,CAAC;IAClDT,wBAAwB,GAAGQ,MAAM,CAACC,GAAG,CAAC,qBAAqB,CAAC;IAC5DJ,eAAe,GAAGG,MAAM,CAACC,GAAG,CAAC,YAAY,CAAC;IAC1CL,eAAe,GAAGI,MAAM,CAACC,GAAG,CAAC,YAAY,CAAC;IAC1CR,0BAA0B,GAAGO,MAAM,CAACC,GAAG,CAAC,uBAAuB,CAAC;IAChEC,sBAAsB,GAAGF,MAAM,CAACC,GAAG,CAAC,wBAAwB,CAAC;EAC/DE,OAAO,CAACC,eAAe,GAAGN,mBAAmB;EAC7CK,OAAO,CAACE,eAAe,GAAGX,kBAAkB;EAC5CS,OAAO,CAACG,OAAO,GAAGpB,kBAAkB;EACpCiB,OAAO,CAACI,UAAU,GAAGZ,sBAAsB;EAC3CQ,OAAO,CAACK,QAAQ,GAAGpB,mBAAmB;EACtCe,OAAO,CAACM,IAAI,GAAGb,eAAe;EAC9BO,OAAO,CAACO,IAAI,GAAGb,eAAe;EAC9BM,OAAO,CAACQ,MAAM,GAAGZ,iBAAiB;EAClCI,OAAO,CAACS,QAAQ,GAAGvB,mBAAmB;EACtCc,OAAO,CAACU,UAAU,GAAGvB,sBAAsB;EAC3Ca,OAAO,CAACW,QAAQ,GAAGvB,mBAAmB;EACtCY,OAAO,CAACY,YAAY,GAAGvB,wBAAwB;EAC/CW,OAAO,CAACa,iBAAiB,GAAG,UAAUhC,MAAM,EAAE;IAC5C,OAAOD,MAAM,CAACC,MAAM,CAAC,KAAKc,mBAAmB;EAC/C,CAAC;EACDK,OAAO,CAACc,iBAAiB,GAAG,UAAUjC,MAAM,EAAE;IAC5C,OAAOD,MAAM,CAACC,MAAM,CAAC,KAAKU,kBAAkB;EAC9C,CAAC;EACDS,OAAO,CAACe,SAAS,GAAG,UAAUlC,MAAM,EAAE;IACpC,OACE,QAAQ,KAAK,OAAOA,MAAM,IAC1B,IAAI,KAAKA,MAAM,IACfA,MAAM,CAACC,QAAQ,KAAKC,kBAAkB;EAE1C,CAAC;EACDiB,OAAO,CAACgB,YAAY,GAAG,UAAUnC,MAAM,EAAE;IACvC,OAAOD,MAAM,CAACC,MAAM,CAAC,KAAKW,sBAAsB;EAClD,CAAC;EACDQ,OAAO,CAACiB,UAAU,GAAG,UAAUpC,MAAM,EAAE;IACrC,OAAOD,MAAM,CAACC,MAAM,CAAC,KAAKI,mBAAmB;EAC/C,CAAC;EACDe,OAAO,CAACkB,MAAM,GAAG,UAAUrC,MAAM,EAAE;IACjC,OAAOD,MAAM,CAACC,MAAM,CAAC,KAAKY,eAAe;EAC3C,CAAC;EACDO,OAAO,CAACmB,MAAM,GAAG,UAAUtC,MAAM,EAAE;IACjC,OAAOD,MAAM,CAACC,MAAM,CAAC,KAAKa,eAAe;EAC3C,CAAC;EACDM,OAAO,CAACoB,QAAQ,GAAG,UAAUvC,MAAM,EAAE;IACnC,OAAOD,MAAM,CAACC,MAAM,CAAC,KAAKe,iBAAiB;EAC7C,CAAC;EACDI,OAAO,CAACqB,UAAU,GAAG,UAAUxC,MAAM,EAAE;IACrC,OAAOD,MAAM,CAACC,MAAM,CAAC,KAAKK,mBAAmB;EAC/C,CAAC;EACDc,OAAO,CAACsB,YAAY,GAAG,UAAUzC,MAAM,EAAE;IACvC,OAAOD,MAAM,CAACC,MAAM,CAAC,KAAKM,sBAAsB;EAClD,CAAC;EACDa,OAAO,CAACuB,UAAU,GAAG,UAAU1C,MAAM,EAAE;IACrC,OAAOD,MAAM,CAACC,MAAM,CAAC,KAAKO,mBAAmB;EAC/C,CAAC;EACDY,OAAO,CAACwB,cAAc,GAAG,UAAU3C,MAAM,EAAE;IACzC,OAAOD,MAAM,CAACC,MAAM,CAAC,KAAKQ,wBAAwB;EACpD,CAAC;EACDW,OAAO,CAACyB,kBAAkB,GAAG,UAAUzC,IAAI,EAAE;IAC3C,OAAO,QAAQ,KAAK,OAAOA,IAAI,IAC7B,UAAU,KAAK,OAAOA,IAAI,IAC1BA,IAAI,KAAKC,mBAAmB,IAC5BD,IAAI,KAAKE,mBAAmB,IAC5BF,IAAI,KAAKG,sBAAsB,IAC/BH,IAAI,KAAKI,mBAAmB,IAC5BJ,IAAI,KAAKK,wBAAwB,IAChC,QAAQ,KAAK,OAAOL,IAAI,IACvB,IAAI,KAAKA,IAAI,KACZA,IAAI,CAACF,QAAQ,KAAKW,eAAe,IAChCT,IAAI,CAACF,QAAQ,KAAKY,eAAe,IACjCV,IAAI,CAACF,QAAQ,KAAKS,kBAAkB,IACpCP,IAAI,CAACF,QAAQ,KAAKa,mBAAmB,IACrCX,IAAI,CAACF,QAAQ,KAAKU,sBAAsB,IACxCR,IAAI,CAACF,QAAQ,KAAKiB,sBAAsB,IACxC,KAAK,CAAC,KAAKf,IAAI,CAAC0C,WAAW,CAAE,GAC/B,CAAC,CAAC,GACF,CAAC,CAAC;EACR,CAAC;EACD1B,OAAO,CAACpB,MAAM,GAAGA,MAAM;AACzB,CAAC,CAAE,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}