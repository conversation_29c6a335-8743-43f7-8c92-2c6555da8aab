{"ast": null, "code": "var isObject = require('./isObject'),\n  isPrototype = require('./_isPrototype'),\n  nativeKeysIn = require('./_nativeKeysIn');\n\n/** Used for built-in method references. */\nvar objectProto = Object.prototype;\n\n/** Used to check objects for own properties. */\nvar hasOwnProperty = objectProto.hasOwnProperty;\n\n/**\n * The base implementation of `_.keysIn` which doesn't treat sparse arrays as dense.\n *\n * @private\n * @param {Object} object The object to query.\n * @returns {Array} Returns the array of property names.\n */\nfunction baseKeysIn(object) {\n  if (!isObject(object)) {\n    return nativeKeysIn(object);\n  }\n  var isProto = isPrototype(object),\n    result = [];\n  for (var key in object) {\n    if (!(key == 'constructor' && (isProto || !hasOwnProperty.call(object, key)))) {\n      result.push(key);\n    }\n  }\n  return result;\n}\nmodule.exports = baseKeysIn;", "map": {"version": 3, "names": ["isObject", "require", "isPrototype", "nativeKeysIn", "objectProto", "Object", "prototype", "hasOwnProperty", "baseKeysIn", "object", "isProto", "result", "key", "call", "push", "module", "exports"], "sources": ["D:/ecommerce/node_modules/lodash/_baseKeysIn.js"], "sourcesContent": ["var isObject = require('./isObject'),\n    isPrototype = require('./_isPrototype'),\n    nativeKeysIn = require('./_nativeKeysIn');\n\n/** Used for built-in method references. */\nvar objectProto = Object.prototype;\n\n/** Used to check objects for own properties. */\nvar hasOwnProperty = objectProto.hasOwnProperty;\n\n/**\n * The base implementation of `_.keysIn` which doesn't treat sparse arrays as dense.\n *\n * @private\n * @param {Object} object The object to query.\n * @returns {Array} Returns the array of property names.\n */\nfunction baseKeysIn(object) {\n  if (!isObject(object)) {\n    return nativeKeysIn(object);\n  }\n  var isProto = isPrototype(object),\n      result = [];\n\n  for (var key in object) {\n    if (!(key == 'constructor' && (isProto || !hasOwnProperty.call(object, key)))) {\n      result.push(key);\n    }\n  }\n  return result;\n}\n\nmodule.exports = baseKeysIn;\n"], "mappings": "AAAA,IAAIA,QAAQ,GAAGC,OAAO,CAAC,YAAY,CAAC;EAChCC,WAAW,GAAGD,OAAO,CAAC,gBAAgB,CAAC;EACvCE,YAAY,GAAGF,OAAO,CAAC,iBAAiB,CAAC;;AAE7C;AACA,IAAIG,WAAW,GAAGC,MAAM,CAACC,SAAS;;AAElC;AACA,IAAIC,cAAc,GAAGH,WAAW,CAACG,cAAc;;AAE/C;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASC,UAAUA,CAACC,MAAM,EAAE;EAC1B,IAAI,CAACT,QAAQ,CAACS,MAAM,CAAC,EAAE;IACrB,OAAON,YAAY,CAACM,MAAM,CAAC;EAC7B;EACA,IAAIC,OAAO,GAAGR,WAAW,CAACO,MAAM,CAAC;IAC7BE,MAAM,GAAG,EAAE;EAEf,KAAK,IAAIC,GAAG,IAAIH,MAAM,EAAE;IACtB,IAAI,EAAEG,GAAG,IAAI,aAAa,KAAKF,OAAO,IAAI,CAACH,cAAc,CAACM,IAAI,CAACJ,MAAM,EAAEG,GAAG,CAAC,CAAC,CAAC,EAAE;MAC7ED,MAAM,CAACG,IAAI,CAACF,GAAG,CAAC;IAClB;EACF;EACA,OAAOD,MAAM;AACf;AAEAI,MAAM,CAACC,OAAO,GAAGR,UAAU", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}