{"ast": null, "code": "import { isNumber } from './DataUtils';\n/**\n * Takes a domain and user props to determine whether he provided the domain via props or if we need to calculate it.\n * @param   {AxisDomain}  domain              The potential domain from props\n * @param   {Boolean}     allowDataOverflow   from props\n * @param   {String}      axisType            from props\n * @returns {Boolean}                         `true` if domain is specified by user\n */\nexport function isDomainSpecifiedByUser(domain, allowDataOverflow, axisType) {\n  if (axisType === 'number' && allowDataOverflow === true && Array.isArray(domain)) {\n    var domainStart = domain === null || domain === void 0 ? void 0 : domain[0];\n    var domainEnd = domain === null || domain === void 0 ? void 0 : domain[1];\n\n    /*\n     * The `isNumber` check is needed because the user could also provide strings like \"dataMin\" via the domain props.\n     * In such case, we have to compute the domain from the data.\n     */\n    if (!!domainStart && !!domainEnd && isNumber(domainStart) && isNumber(domainEnd)) {\n      return true;\n    }\n  }\n  return false;\n}", "map": {"version": 3, "names": ["isNumber", "isDomainSpecifiedByUser", "domain", "allowDataOverflow", "axisType", "Array", "isArray", "domainStart", "domainEnd"], "sources": ["D:/ecommerce/node_modules/recharts/es6/util/isDomainSpecifiedByUser.js"], "sourcesContent": ["import { isNumber } from './DataUtils';\n/**\n * Takes a domain and user props to determine whether he provided the domain via props or if we need to calculate it.\n * @param   {AxisDomain}  domain              The potential domain from props\n * @param   {Boolean}     allowDataOverflow   from props\n * @param   {String}      axisType            from props\n * @returns {Boolean}                         `true` if domain is specified by user\n */\nexport function isDomainSpecifiedByUser(domain, allowDataOverflow, axisType) {\n  if (axisType === 'number' && allowDataOverflow === true && Array.isArray(domain)) {\n    var domainStart = domain === null || domain === void 0 ? void 0 : domain[0];\n    var domainEnd = domain === null || domain === void 0 ? void 0 : domain[1];\n\n    /*\n     * The `isNumber` check is needed because the user could also provide strings like \"dataMin\" via the domain props.\n     * In such case, we have to compute the domain from the data.\n     */\n    if (!!domainStart && !!domainEnd && isNumber(domainStart) && isNumber(domainEnd)) {\n      return true;\n    }\n  }\n  return false;\n}"], "mappings": "AAAA,SAASA,QAAQ,QAAQ,aAAa;AACtC;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO,SAASC,uBAAuBA,CAACC,MAAM,EAAEC,iBAAiB,EAAEC,QAAQ,EAAE;EAC3E,IAAIA,QAAQ,KAAK,QAAQ,IAAID,iBAAiB,KAAK,IAAI,IAAIE,KAAK,CAACC,OAAO,CAACJ,MAAM,CAAC,EAAE;IAChF,IAAIK,WAAW,GAAGL,MAAM,KAAK,IAAI,IAAIA,MAAM,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,MAAM,CAAC,CAAC,CAAC;IAC3E,IAAIM,SAAS,GAAGN,MAAM,KAAK,IAAI,IAAIA,MAAM,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,MAAM,CAAC,CAAC,CAAC;;IAEzE;AACJ;AACA;AACA;IACI,IAAI,CAAC,CAACK,WAAW,IAAI,CAAC,CAACC,SAAS,IAAIR,QAAQ,CAACO,WAAW,CAAC,IAAIP,QAAQ,CAACQ,SAAS,CAAC,EAAE;MAChF,OAAO,IAAI;IACb;EACF;EACA,OAAO,KAAK;AACd", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}