{"ast": null, "code": "var _jsxFileName = \"D:\\\\ecommerce\\\\client\\\\src\\\\components\\\\auth\\\\Login.js\",\n  _s = $RefreshSig$();\nimport React, { useState } from 'react';\nimport { Box, Paper, TextField, Button, Typography, Container, Alert, CircularProgress, Link, Divider, InputAdornment, IconButton } from '@mui/material';\nimport { Visibility, VisibilityOff, Email, Lock, Google, Facebook } from '@mui/icons-material';\nimport { styled } from '@mui/material/styles';\nimport { useNavigate } from 'react-router-dom';\nimport { usersAPI, authAPI, handleAPIError } from '../../services/api';\n\n// Flipkart-style styled components\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst FlipkartHeader = styled(Paper)(({\n  theme\n}) => ({\n  backgroundColor: '#2874f0',\n  color: 'white',\n  padding: theme.spacing(2),\n  textAlign: 'center',\n  marginBottom: theme.spacing(3)\n}));\n_c = FlipkartHeader;\nconst LoginContainer = styled(Container)(({\n  theme\n}) => ({\n  minHeight: '100vh',\n  display: 'flex',\n  alignItems: 'center',\n  backgroundColor: '#f1f3f6',\n  padding: theme.spacing(2)\n}));\n_c2 = LoginContainer;\nconst LoginCard = styled(Paper)(({\n  theme\n}) => ({\n  padding: theme.spacing(4),\n  maxWidth: 400,\n  width: '100%',\n  margin: '0 auto',\n  boxShadow: '0 4px 12px rgba(0,0,0,0.15)'\n}));\n_c3 = LoginCard;\nconst Login = () => {\n  _s();\n  const navigate = useNavigate();\n  const [formData, setFormData] = useState({\n    email: '',\n    password: ''\n  });\n  const [showPassword, setShowPassword] = useState(false);\n  const [loading, setLoading] = useState(false);\n  const [error, setError] = useState('');\n  const [success, setSuccess] = useState('');\n  const {\n    email,\n    password\n  } = formData;\n  const handleChange = e => {\n    setFormData({\n      ...formData,\n      [e.target.name]: e.target.value\n    });\n    setError('');\n  };\n  const handleSubmit = async e => {\n    e.preventDefault();\n    if (!email || !password) {\n      setError('Please fill in all fields');\n      return;\n    }\n    try {\n      setLoading(true);\n      setError('');\n      const response = await usersAPI.login({\n        email,\n        password\n      });\n\n      // Store token and user data\n      authAPI.setToken(response.data.token);\n      localStorage.setItem('user', JSON.stringify(response.data));\n      setSuccess('Login successful! Redirecting...');\n\n      // Redirect based on user role\n      setTimeout(() => {\n        if (response.data.isAdmin) {\n          navigate('/admin');\n        } else {\n          navigate('/dashboard');\n        }\n      }, 1500);\n    } catch (err) {\n      setError(handleAPIError(err));\n    } finally {\n      setLoading(false);\n    }\n  };\n  const handleGuestLogin = () => {\n    // Demo guest login\n    const guestUser = {\n      _id: 'guest123',\n      name: 'Guest User',\n      email: '<EMAIL>',\n      isAdmin: false,\n      token: 'guest-token'\n    };\n    localStorage.setItem('user', JSON.stringify(guestUser));\n    navigate('/');\n  };\n  return /*#__PURE__*/_jsxDEV(Box, {\n    sx: {\n      backgroundColor: '#f1f3f6',\n      minHeight: '100vh'\n    },\n    children: [/*#__PURE__*/_jsxDEV(FlipkartHeader, {\n      elevation: 0,\n      children: [/*#__PURE__*/_jsxDEV(Typography, {\n        variant: \"h4\",\n        sx: {\n          fontWeight: 'bold'\n        },\n        children: \"\\uD83D\\uDED2 SpiceMart\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 128,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Typography, {\n        variant: \"subtitle1\",\n        children: \"Login to access your account\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 131,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 127,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(LoginContainer, {\n      maxWidth: \"sm\",\n      children: /*#__PURE__*/_jsxDEV(LoginCard, {\n        children: [/*#__PURE__*/_jsxDEV(Box, {\n          sx: {\n            textAlign: 'center',\n            mb: 3\n          },\n          children: [/*#__PURE__*/_jsxDEV(Typography, {\n            variant: \"h5\",\n            sx: {\n              fontWeight: 'bold',\n              mb: 1\n            },\n            children: \"Welcome Back!\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 139,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Typography, {\n            variant: \"body2\",\n            color: \"textSecondary\",\n            children: \"Sign in to continue shopping\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 142,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 138,\n          columnNumber: 11\n        }, this), error && /*#__PURE__*/_jsxDEV(Alert, {\n          severity: \"error\",\n          sx: {\n            mb: 2\n          },\n          children: error\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 148,\n          columnNumber: 13\n        }, this), success && /*#__PURE__*/_jsxDEV(Alert, {\n          severity: \"success\",\n          sx: {\n            mb: 2\n          },\n          children: success\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 154,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"form\", {\n          onSubmit: handleSubmit,\n          children: [/*#__PURE__*/_jsxDEV(TextField, {\n            fullWidth: true,\n            label: \"Email Address\",\n            name: \"email\",\n            type: \"email\",\n            value: email,\n            onChange: handleChange,\n            margin: \"normal\",\n            required: true,\n            InputProps: {\n              startAdornment: /*#__PURE__*/_jsxDEV(InputAdornment, {\n                position: \"start\",\n                children: /*#__PURE__*/_jsxDEV(Email, {\n                  color: \"action\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 172,\n                  columnNumber: 21\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 171,\n                columnNumber: 19\n              }, this)\n            }\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 160,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(TextField, {\n            fullWidth: true,\n            label: \"Password\",\n            name: \"password\",\n            type: showPassword ? 'text' : 'password',\n            value: password,\n            onChange: handleChange,\n            margin: \"normal\",\n            required: true,\n            InputProps: {\n              startAdornment: /*#__PURE__*/_jsxDEV(InputAdornment, {\n                position: \"start\",\n                children: /*#__PURE__*/_jsxDEV(Lock, {\n                  color: \"action\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 190,\n                  columnNumber: 21\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 189,\n                columnNumber: 19\n              }, this),\n              endAdornment: /*#__PURE__*/_jsxDEV(InputAdornment, {\n                position: \"end\",\n                children: /*#__PURE__*/_jsxDEV(IconButton, {\n                  onClick: () => setShowPassword(!showPassword),\n                  edge: \"end\",\n                  children: showPassword ? /*#__PURE__*/_jsxDEV(VisibilityOff, {}, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 199,\n                    columnNumber: 39\n                  }, this) : /*#__PURE__*/_jsxDEV(Visibility, {}, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 199,\n                    columnNumber: 59\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 195,\n                  columnNumber: 21\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 194,\n                columnNumber: 19\n              }, this)\n            }\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 178,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Button, {\n            type: \"submit\",\n            fullWidth: true,\n            variant: \"contained\",\n            size: \"large\",\n            disabled: loading,\n            sx: {\n              mt: 3,\n              mb: 2,\n              backgroundColor: '#fb641b',\n              '&:hover': {\n                backgroundColor: '#e55a16'\n              },\n              py: 1.5,\n              fontSize: '1.1rem',\n              fontWeight: 'bold'\n            },\n            children: loading ? /*#__PURE__*/_jsxDEV(CircularProgress, {\n              size: 24,\n              color: \"inherit\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 222,\n              columnNumber: 26\n            }, this) : 'LOGIN'\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 206,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Box, {\n            sx: {\n              textAlign: 'center',\n              mb: 2\n            },\n            children: /*#__PURE__*/_jsxDEV(Link, {\n              href: \"#\",\n              variant: \"body2\",\n              sx: {\n                color: '#2874f0'\n              },\n              children: \"Forgot Password?\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 226,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 225,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Divider, {\n            sx: {\n              my: 2\n            },\n            children: /*#__PURE__*/_jsxDEV(Typography, {\n              variant: \"caption\",\n              color: \"textSecondary\",\n              children: \"OR\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 232,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 231,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Button, {\n            fullWidth: true,\n            variant: \"outlined\",\n            onClick: handleGuestLogin,\n            sx: {\n              mb: 2,\n              borderColor: '#2874f0',\n              color: '#2874f0',\n              '&:hover': {\n                borderColor: '#1e5bb8',\n                backgroundColor: '#e3f2fd'\n              }\n            },\n            children: \"Continue as Guest\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 237,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Box, {\n            sx: {\n              display: 'flex',\n              gap: 1,\n              mb: 3\n            },\n            children: [/*#__PURE__*/_jsxDEV(Button, {\n              fullWidth: true,\n              variant: \"outlined\",\n              startIcon: /*#__PURE__*/_jsxDEV(Google, {}, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 258,\n                columnNumber: 28\n              }, this),\n              sx: {\n                borderColor: '#db4437',\n                color: '#db4437'\n              },\n              children: \"Google\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 255,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Button, {\n              fullWidth: true,\n              variant: \"outlined\",\n              startIcon: /*#__PURE__*/_jsxDEV(Facebook, {}, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 266,\n                columnNumber: 28\n              }, this),\n              sx: {\n                borderColor: '#4267b2',\n                color: '#4267b2'\n              },\n              children: \"Facebook\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 263,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 254,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Box, {\n            sx: {\n              textAlign: 'center'\n            },\n            children: /*#__PURE__*/_jsxDEV(Typography, {\n              variant: \"body2\",\n              color: \"textSecondary\",\n              children: [\"New to SpiceMart?\", ' ', /*#__PURE__*/_jsxDEV(Link, {\n                component: \"button\",\n                variant: \"body2\",\n                onClick: () => navigate('/register'),\n                sx: {\n                  color: '#2874f0',\n                  textDecoration: 'none'\n                },\n                children: \"Create an account\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 276,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 274,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 273,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 159,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Box, {\n          sx: {\n            mt: 3,\n            p: 2,\n            backgroundColor: '#f8f9fa',\n            borderRadius: 1\n          },\n          children: [/*#__PURE__*/_jsxDEV(Typography, {\n            variant: \"caption\",\n            sx: {\n              fontWeight: 'bold',\n              display: 'block',\n              mb: 1\n            },\n            children: \"Demo Credentials:\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 290,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Typography, {\n            variant: \"caption\",\n            sx: {\n              display: 'block'\n            },\n            children: \"User: <EMAIL> / 123456\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 293,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Typography, {\n            variant: \"caption\",\n            sx: {\n              display: 'block'\n            },\n            children: \"Admin: <EMAIL> / 123456\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 296,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 289,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 137,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 136,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 125,\n    columnNumber: 5\n  }, this);\n};\n_s(Login, \"McTp4NFPxEsF91lnbEM+/aLaJmc=\", false, function () {\n  return [useNavigate];\n});\n_c4 = Login;\nexport default Login;\nvar _c, _c2, _c3, _c4;\n$RefreshReg$(_c, \"FlipkartHeader\");\n$RefreshReg$(_c2, \"LoginContainer\");\n$RefreshReg$(_c3, \"LoginCard\");\n$RefreshReg$(_c4, \"Login\");", "map": {"version": 3, "names": ["React", "useState", "Box", "Paper", "TextField", "<PERSON><PERSON>", "Typography", "Container", "<PERSON><PERSON>", "CircularProgress", "Link", "Divider", "InputAdornment", "IconButton", "Visibility", "VisibilityOff", "Email", "Lock", "Google", "Facebook", "styled", "useNavigate", "usersAPI", "authAPI", "handleAPIError", "jsxDEV", "_jsxDEV", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "theme", "backgroundColor", "color", "padding", "spacing", "textAlign", "marginBottom", "_c", "LoginContainer", "minHeight", "display", "alignItems", "_c2", "LoginCard", "max<PERSON><PERSON><PERSON>", "width", "margin", "boxShadow", "_c3", "<PERSON><PERSON>", "_s", "navigate", "formData", "setFormData", "email", "password", "showPassword", "setShowPassword", "loading", "setLoading", "error", "setError", "success", "setSuccess", "handleChange", "e", "target", "name", "value", "handleSubmit", "preventDefault", "response", "login", "setToken", "data", "token", "localStorage", "setItem", "JSON", "stringify", "setTimeout", "isAdmin", "err", "handleGuestLogin", "guestUser", "_id", "sx", "children", "elevation", "variant", "fontWeight", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "mb", "severity", "onSubmit", "fullWidth", "label", "type", "onChange", "required", "InputProps", "startAdornment", "position", "endAdornment", "onClick", "edge", "size", "disabled", "mt", "py", "fontSize", "href", "my", "borderColor", "gap", "startIcon", "component", "textDecoration", "p", "borderRadius", "_c4", "$RefreshReg$"], "sources": ["D:/ecommerce/client/src/components/auth/Login.js"], "sourcesContent": ["import React, { useState } from 'react';\nimport {\n  Box,\n  Paper,\n  TextField,\n  Button,\n  Typography,\n  Container,\n  Alert,\n  CircularProgress,\n  Link,\n  Divider,\n  InputAdornment,\n  IconButton,\n} from '@mui/material';\nimport {\n  Visibility,\n  VisibilityOff,\n  Email,\n  Lock,\n  Google,\n  Facebook,\n} from '@mui/icons-material';\nimport { styled } from '@mui/material/styles';\nimport { useNavigate } from 'react-router-dom';\nimport { usersAPI, authAPI, handleAPIError } from '../../services/api';\n\n// Flipkart-style styled components\nconst FlipkartHeader = styled(Paper)(({ theme }) => ({\n  backgroundColor: '#2874f0',\n  color: 'white',\n  padding: theme.spacing(2),\n  textAlign: 'center',\n  marginBottom: theme.spacing(3),\n}));\n\nconst LoginContainer = styled(Container)(({ theme }) => ({\n  minHeight: '100vh',\n  display: 'flex',\n  alignItems: 'center',\n  backgroundColor: '#f1f3f6',\n  padding: theme.spacing(2),\n}));\n\nconst LoginCard = styled(Paper)(({ theme }) => ({\n  padding: theme.spacing(4),\n  maxWidth: 400,\n  width: '100%',\n  margin: '0 auto',\n  boxShadow: '0 4px 12px rgba(0,0,0,0.15)',\n}));\n\nconst Login = () => {\n  const navigate = useNavigate();\n  const [formData, setFormData] = useState({\n    email: '',\n    password: '',\n  });\n  const [showPassword, setShowPassword] = useState(false);\n  const [loading, setLoading] = useState(false);\n  const [error, setError] = useState('');\n  const [success, setSuccess] = useState('');\n\n  const { email, password } = formData;\n\n  const handleChange = (e) => {\n    setFormData({\n      ...formData,\n      [e.target.name]: e.target.value,\n    });\n    setError('');\n  };\n\n  const handleSubmit = async (e) => {\n    e.preventDefault();\n    \n    if (!email || !password) {\n      setError('Please fill in all fields');\n      return;\n    }\n\n    try {\n      setLoading(true);\n      setError('');\n      \n      const response = await usersAPI.login({ email, password });\n      \n      // Store token and user data\n      authAPI.setToken(response.data.token);\n      localStorage.setItem('user', JSON.stringify(response.data));\n      \n      setSuccess('Login successful! Redirecting...');\n      \n      // Redirect based on user role\n      setTimeout(() => {\n        if (response.data.isAdmin) {\n          navigate('/admin');\n        } else {\n          navigate('/dashboard');\n        }\n      }, 1500);\n      \n    } catch (err) {\n      setError(handleAPIError(err));\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  const handleGuestLogin = () => {\n    // Demo guest login\n    const guestUser = {\n      _id: 'guest123',\n      name: 'Guest User',\n      email: '<EMAIL>',\n      isAdmin: false,\n      token: 'guest-token'\n    };\n    \n    localStorage.setItem('user', JSON.stringify(guestUser));\n    navigate('/');\n  };\n\n  return (\n    <Box sx={{ backgroundColor: '#f1f3f6', minHeight: '100vh' }}>\n      {/* Header */}\n      <FlipkartHeader elevation={0}>\n        <Typography variant=\"h4\" sx={{ fontWeight: 'bold' }}>\n          🛒 SpiceMart\n        </Typography>\n        <Typography variant=\"subtitle1\">\n          Login to access your account\n        </Typography>\n      </FlipkartHeader>\n\n      <LoginContainer maxWidth=\"sm\">\n        <LoginCard>\n          <Box sx={{ textAlign: 'center', mb: 3 }}>\n            <Typography variant=\"h5\" sx={{ fontWeight: 'bold', mb: 1 }}>\n              Welcome Back!\n            </Typography>\n            <Typography variant=\"body2\" color=\"textSecondary\">\n              Sign in to continue shopping\n            </Typography>\n          </Box>\n\n          {error && (\n            <Alert severity=\"error\" sx={{ mb: 2 }}>\n              {error}\n            </Alert>\n          )}\n\n          {success && (\n            <Alert severity=\"success\" sx={{ mb: 2 }}>\n              {success}\n            </Alert>\n          )}\n\n          <form onSubmit={handleSubmit}>\n            <TextField\n              fullWidth\n              label=\"Email Address\"\n              name=\"email\"\n              type=\"email\"\n              value={email}\n              onChange={handleChange}\n              margin=\"normal\"\n              required\n              InputProps={{\n                startAdornment: (\n                  <InputAdornment position=\"start\">\n                    <Email color=\"action\" />\n                  </InputAdornment>\n                ),\n              }}\n            />\n\n            <TextField\n              fullWidth\n              label=\"Password\"\n              name=\"password\"\n              type={showPassword ? 'text' : 'password'}\n              value={password}\n              onChange={handleChange}\n              margin=\"normal\"\n              required\n              InputProps={{\n                startAdornment: (\n                  <InputAdornment position=\"start\">\n                    <Lock color=\"action\" />\n                  </InputAdornment>\n                ),\n                endAdornment: (\n                  <InputAdornment position=\"end\">\n                    <IconButton\n                      onClick={() => setShowPassword(!showPassword)}\n                      edge=\"end\"\n                    >\n                      {showPassword ? <VisibilityOff /> : <Visibility />}\n                    </IconButton>\n                  </InputAdornment>\n                ),\n              }}\n            />\n\n            <Button\n              type=\"submit\"\n              fullWidth\n              variant=\"contained\"\n              size=\"large\"\n              disabled={loading}\n              sx={{\n                mt: 3,\n                mb: 2,\n                backgroundColor: '#fb641b',\n                '&:hover': { backgroundColor: '#e55a16' },\n                py: 1.5,\n                fontSize: '1.1rem',\n                fontWeight: 'bold',\n              }}\n            >\n              {loading ? <CircularProgress size={24} color=\"inherit\" /> : 'LOGIN'}\n            </Button>\n\n            <Box sx={{ textAlign: 'center', mb: 2 }}>\n              <Link href=\"#\" variant=\"body2\" sx={{ color: '#2874f0' }}>\n                Forgot Password?\n              </Link>\n            </Box>\n\n            <Divider sx={{ my: 2 }}>\n              <Typography variant=\"caption\" color=\"textSecondary\">\n                OR\n              </Typography>\n            </Divider>\n\n            <Button\n              fullWidth\n              variant=\"outlined\"\n              onClick={handleGuestLogin}\n              sx={{\n                mb: 2,\n                borderColor: '#2874f0',\n                color: '#2874f0',\n                '&:hover': {\n                  borderColor: '#1e5bb8',\n                  backgroundColor: '#e3f2fd',\n                },\n              }}\n            >\n              Continue as Guest\n            </Button>\n\n            <Box sx={{ display: 'flex', gap: 1, mb: 3 }}>\n              <Button\n                fullWidth\n                variant=\"outlined\"\n                startIcon={<Google />}\n                sx={{ borderColor: '#db4437', color: '#db4437' }}\n              >\n                Google\n              </Button>\n              <Button\n                fullWidth\n                variant=\"outlined\"\n                startIcon={<Facebook />}\n                sx={{ borderColor: '#4267b2', color: '#4267b2' }}\n              >\n                Facebook\n              </Button>\n            </Box>\n\n            <Box sx={{ textAlign: 'center' }}>\n              <Typography variant=\"body2\" color=\"textSecondary\">\n                New to SpiceMart?{' '}\n                <Link\n                  component=\"button\"\n                  variant=\"body2\"\n                  onClick={() => navigate('/register')}\n                  sx={{ color: '#2874f0', textDecoration: 'none' }}\n                >\n                  Create an account\n                </Link>\n              </Typography>\n            </Box>\n          </form>\n\n          {/* Demo Credentials */}\n          <Box sx={{ mt: 3, p: 2, backgroundColor: '#f8f9fa', borderRadius: 1 }}>\n            <Typography variant=\"caption\" sx={{ fontWeight: 'bold', display: 'block', mb: 1 }}>\n              Demo Credentials:\n            </Typography>\n            <Typography variant=\"caption\" sx={{ display: 'block' }}>\n              User: <EMAIL> / 123456\n            </Typography>\n            <Typography variant=\"caption\" sx={{ display: 'block' }}>\n              Admin: <EMAIL> / 123456\n            </Typography>\n          </Box>\n        </LoginCard>\n      </LoginContainer>\n    </Box>\n  );\n};\n\nexport default Login;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,QAAQ,OAAO;AACvC,SACEC,GAAG,EACHC,KAAK,EACLC,SAAS,EACTC,MAAM,EACNC,UAAU,EACVC,SAAS,EACTC,KAAK,EACLC,gBAAgB,EAChBC,IAAI,EACJC,OAAO,EACPC,cAAc,EACdC,UAAU,QACL,eAAe;AACtB,SACEC,UAAU,EACVC,aAAa,EACbC,KAAK,EACLC,IAAI,EACJC,MAAM,EACNC,QAAQ,QACH,qBAAqB;AAC5B,SAASC,MAAM,QAAQ,sBAAsB;AAC7C,SAASC,WAAW,QAAQ,kBAAkB;AAC9C,SAASC,QAAQ,EAAEC,OAAO,EAAEC,cAAc,QAAQ,oBAAoB;;AAEtE;AAAA,SAAAC,MAAA,IAAAC,OAAA;AACA,MAAMC,cAAc,GAAGP,MAAM,CAACjB,KAAK,CAAC,CAAC,CAAC;EAAEyB;AAAM,CAAC,MAAM;EACnDC,eAAe,EAAE,SAAS;EAC1BC,KAAK,EAAE,OAAO;EACdC,OAAO,EAAEH,KAAK,CAACI,OAAO,CAAC,CAAC,CAAC;EACzBC,SAAS,EAAE,QAAQ;EACnBC,YAAY,EAAEN,KAAK,CAACI,OAAO,CAAC,CAAC;AAC/B,CAAC,CAAC,CAAC;AAACG,EAAA,GANER,cAAc;AAQpB,MAAMS,cAAc,GAAGhB,MAAM,CAACb,SAAS,CAAC,CAAC,CAAC;EAAEqB;AAAM,CAAC,MAAM;EACvDS,SAAS,EAAE,OAAO;EAClBC,OAAO,EAAE,MAAM;EACfC,UAAU,EAAE,QAAQ;EACpBV,eAAe,EAAE,SAAS;EAC1BE,OAAO,EAAEH,KAAK,CAACI,OAAO,CAAC,CAAC;AAC1B,CAAC,CAAC,CAAC;AAACQ,GAAA,GANEJ,cAAc;AAQpB,MAAMK,SAAS,GAAGrB,MAAM,CAACjB,KAAK,CAAC,CAAC,CAAC;EAAEyB;AAAM,CAAC,MAAM;EAC9CG,OAAO,EAAEH,KAAK,CAACI,OAAO,CAAC,CAAC,CAAC;EACzBU,QAAQ,EAAE,GAAG;EACbC,KAAK,EAAE,MAAM;EACbC,MAAM,EAAE,QAAQ;EAChBC,SAAS,EAAE;AACb,CAAC,CAAC,CAAC;AAACC,GAAA,GANEL,SAAS;AAQf,MAAMM,KAAK,GAAGA,CAAA,KAAM;EAAAC,EAAA;EAClB,MAAMC,QAAQ,GAAG5B,WAAW,CAAC,CAAC;EAC9B,MAAM,CAAC6B,QAAQ,EAAEC,WAAW,CAAC,GAAGlD,QAAQ,CAAC;IACvCmD,KAAK,EAAE,EAAE;IACTC,QAAQ,EAAE;EACZ,CAAC,CAAC;EACF,MAAM,CAACC,YAAY,EAAEC,eAAe,CAAC,GAAGtD,QAAQ,CAAC,KAAK,CAAC;EACvD,MAAM,CAACuD,OAAO,EAAEC,UAAU,CAAC,GAAGxD,QAAQ,CAAC,KAAK,CAAC;EAC7C,MAAM,CAACyD,KAAK,EAAEC,QAAQ,CAAC,GAAG1D,QAAQ,CAAC,EAAE,CAAC;EACtC,MAAM,CAAC2D,OAAO,EAAEC,UAAU,CAAC,GAAG5D,QAAQ,CAAC,EAAE,CAAC;EAE1C,MAAM;IAAEmD,KAAK;IAAEC;EAAS,CAAC,GAAGH,QAAQ;EAEpC,MAAMY,YAAY,GAAIC,CAAC,IAAK;IAC1BZ,WAAW,CAAC;MACV,GAAGD,QAAQ;MACX,CAACa,CAAC,CAACC,MAAM,CAACC,IAAI,GAAGF,CAAC,CAACC,MAAM,CAACE;IAC5B,CAAC,CAAC;IACFP,QAAQ,CAAC,EAAE,CAAC;EACd,CAAC;EAED,MAAMQ,YAAY,GAAG,MAAOJ,CAAC,IAAK;IAChCA,CAAC,CAACK,cAAc,CAAC,CAAC;IAElB,IAAI,CAAChB,KAAK,IAAI,CAACC,QAAQ,EAAE;MACvBM,QAAQ,CAAC,2BAA2B,CAAC;MACrC;IACF;IAEA,IAAI;MACFF,UAAU,CAAC,IAAI,CAAC;MAChBE,QAAQ,CAAC,EAAE,CAAC;MAEZ,MAAMU,QAAQ,GAAG,MAAM/C,QAAQ,CAACgD,KAAK,CAAC;QAAElB,KAAK;QAAEC;MAAS,CAAC,CAAC;;MAE1D;MACA9B,OAAO,CAACgD,QAAQ,CAACF,QAAQ,CAACG,IAAI,CAACC,KAAK,CAAC;MACrCC,YAAY,CAACC,OAAO,CAAC,MAAM,EAAEC,IAAI,CAACC,SAAS,CAACR,QAAQ,CAACG,IAAI,CAAC,CAAC;MAE3DX,UAAU,CAAC,kCAAkC,CAAC;;MAE9C;MACAiB,UAAU,CAAC,MAAM;QACf,IAAIT,QAAQ,CAACG,IAAI,CAACO,OAAO,EAAE;UACzB9B,QAAQ,CAAC,QAAQ,CAAC;QACpB,CAAC,MAAM;UACLA,QAAQ,CAAC,YAAY,CAAC;QACxB;MACF,CAAC,EAAE,IAAI,CAAC;IAEV,CAAC,CAAC,OAAO+B,GAAG,EAAE;MACZrB,QAAQ,CAACnC,cAAc,CAACwD,GAAG,CAAC,CAAC;IAC/B,CAAC,SAAS;MACRvB,UAAU,CAAC,KAAK,CAAC;IACnB;EACF,CAAC;EAED,MAAMwB,gBAAgB,GAAGA,CAAA,KAAM;IAC7B;IACA,MAAMC,SAAS,GAAG;MAChBC,GAAG,EAAE,UAAU;MACflB,IAAI,EAAE,YAAY;MAClBb,KAAK,EAAE,mBAAmB;MAC1B2B,OAAO,EAAE,KAAK;MACdN,KAAK,EAAE;IACT,CAAC;IAEDC,YAAY,CAACC,OAAO,CAAC,MAAM,EAAEC,IAAI,CAACC,SAAS,CAACK,SAAS,CAAC,CAAC;IACvDjC,QAAQ,CAAC,GAAG,CAAC;EACf,CAAC;EAED,oBACEvB,OAAA,CAACxB,GAAG;IAACkF,EAAE,EAAE;MAAEvD,eAAe,EAAE,SAAS;MAAEQ,SAAS,EAAE;IAAQ,CAAE;IAAAgD,QAAA,gBAE1D3D,OAAA,CAACC,cAAc;MAAC2D,SAAS,EAAE,CAAE;MAAAD,QAAA,gBAC3B3D,OAAA,CAACpB,UAAU;QAACiF,OAAO,EAAC,IAAI;QAACH,EAAE,EAAE;UAAEI,UAAU,EAAE;QAAO,CAAE;QAAAH,QAAA,EAAC;MAErD;QAAAI,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAY,CAAC,eACblE,OAAA,CAACpB,UAAU;QAACiF,OAAO,EAAC,WAAW;QAAAF,QAAA,EAAC;MAEhC;QAAAI,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAY,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACC,CAAC,eAEjBlE,OAAA,CAACU,cAAc;MAACM,QAAQ,EAAC,IAAI;MAAA2C,QAAA,eAC3B3D,OAAA,CAACe,SAAS;QAAA4C,QAAA,gBACR3D,OAAA,CAACxB,GAAG;UAACkF,EAAE,EAAE;YAAEnD,SAAS,EAAE,QAAQ;YAAE4D,EAAE,EAAE;UAAE,CAAE;UAAAR,QAAA,gBACtC3D,OAAA,CAACpB,UAAU;YAACiF,OAAO,EAAC,IAAI;YAACH,EAAE,EAAE;cAAEI,UAAU,EAAE,MAAM;cAAEK,EAAE,EAAE;YAAE,CAAE;YAAAR,QAAA,EAAC;UAE5D;YAAAI,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAY,CAAC,eACblE,OAAA,CAACpB,UAAU;YAACiF,OAAO,EAAC,OAAO;YAACzD,KAAK,EAAC,eAAe;YAAAuD,QAAA,EAAC;UAElD;YAAAI,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAY,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACV,CAAC,EAELlC,KAAK,iBACJhC,OAAA,CAAClB,KAAK;UAACsF,QAAQ,EAAC,OAAO;UAACV,EAAE,EAAE;YAAES,EAAE,EAAE;UAAE,CAAE;UAAAR,QAAA,EACnC3B;QAAK;UAAA+B,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACD,CACR,EAEAhC,OAAO,iBACNlC,OAAA,CAAClB,KAAK;UAACsF,QAAQ,EAAC,SAAS;UAACV,EAAE,EAAE;YAAES,EAAE,EAAE;UAAE,CAAE;UAAAR,QAAA,EACrCzB;QAAO;UAAA6B,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CACR,eAEDlE,OAAA;UAAMqE,QAAQ,EAAE5B,YAAa;UAAAkB,QAAA,gBAC3B3D,OAAA,CAACtB,SAAS;YACR4F,SAAS;YACTC,KAAK,EAAC,eAAe;YACrBhC,IAAI,EAAC,OAAO;YACZiC,IAAI,EAAC,OAAO;YACZhC,KAAK,EAAEd,KAAM;YACb+C,QAAQ,EAAErC,YAAa;YACvBlB,MAAM,EAAC,QAAQ;YACfwD,QAAQ;YACRC,UAAU,EAAE;cACVC,cAAc,eACZ5E,OAAA,CAACd,cAAc;gBAAC2F,QAAQ,EAAC,OAAO;gBAAAlB,QAAA,eAC9B3D,OAAA,CAACV,KAAK;kBAACc,KAAK,EAAC;gBAAQ;kBAAA2D,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACV;YAEpB;UAAE;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,eAEFlE,OAAA,CAACtB,SAAS;YACR4F,SAAS;YACTC,KAAK,EAAC,UAAU;YAChBhC,IAAI,EAAC,UAAU;YACfiC,IAAI,EAAE5C,YAAY,GAAG,MAAM,GAAG,UAAW;YACzCY,KAAK,EAAEb,QAAS;YAChB8C,QAAQ,EAAErC,YAAa;YACvBlB,MAAM,EAAC,QAAQ;YACfwD,QAAQ;YACRC,UAAU,EAAE;cACVC,cAAc,eACZ5E,OAAA,CAACd,cAAc;gBAAC2F,QAAQ,EAAC,OAAO;gBAAAlB,QAAA,eAC9B3D,OAAA,CAACT,IAAI;kBAACa,KAAK,EAAC;gBAAQ;kBAAA2D,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACT,CACjB;cACDY,YAAY,eACV9E,OAAA,CAACd,cAAc;gBAAC2F,QAAQ,EAAC,KAAK;gBAAAlB,QAAA,eAC5B3D,OAAA,CAACb,UAAU;kBACT4F,OAAO,EAAEA,CAAA,KAAMlD,eAAe,CAAC,CAACD,YAAY,CAAE;kBAC9CoD,IAAI,EAAC,KAAK;kBAAArB,QAAA,EAET/B,YAAY,gBAAG5B,OAAA,CAACX,aAAa;oBAAA0E,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE,CAAC,gBAAGlE,OAAA,CAACZ,UAAU;oBAAA2E,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACxC;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACC;YAEpB;UAAE;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,eAEFlE,OAAA,CAACrB,MAAM;YACL6F,IAAI,EAAC,QAAQ;YACbF,SAAS;YACTT,OAAO,EAAC,WAAW;YACnBoB,IAAI,EAAC,OAAO;YACZC,QAAQ,EAAEpD,OAAQ;YAClB4B,EAAE,EAAE;cACFyB,EAAE,EAAE,CAAC;cACLhB,EAAE,EAAE,CAAC;cACLhE,eAAe,EAAE,SAAS;cAC1B,SAAS,EAAE;gBAAEA,eAAe,EAAE;cAAU,CAAC;cACzCiF,EAAE,EAAE,GAAG;cACPC,QAAQ,EAAE,QAAQ;cAClBvB,UAAU,EAAE;YACd,CAAE;YAAAH,QAAA,EAED7B,OAAO,gBAAG9B,OAAA,CAACjB,gBAAgB;cAACkG,IAAI,EAAE,EAAG;cAAC7E,KAAK,EAAC;YAAS;cAAA2D,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,GAAG;UAAO;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC7D,CAAC,eAETlE,OAAA,CAACxB,GAAG;YAACkF,EAAE,EAAE;cAAEnD,SAAS,EAAE,QAAQ;cAAE4D,EAAE,EAAE;YAAE,CAAE;YAAAR,QAAA,eACtC3D,OAAA,CAAChB,IAAI;cAACsG,IAAI,EAAC,GAAG;cAACzB,OAAO,EAAC,OAAO;cAACH,EAAE,EAAE;gBAAEtD,KAAK,EAAE;cAAU,CAAE;cAAAuD,QAAA,EAAC;YAEzD;cAAAI,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACJ,CAAC,eAENlE,OAAA,CAACf,OAAO;YAACyE,EAAE,EAAE;cAAE6B,EAAE,EAAE;YAAE,CAAE;YAAA5B,QAAA,eACrB3D,OAAA,CAACpB,UAAU;cAACiF,OAAO,EAAC,SAAS;cAACzD,KAAK,EAAC,eAAe;cAAAuD,QAAA,EAAC;YAEpD;cAAAI,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAY;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACN,CAAC,eAEVlE,OAAA,CAACrB,MAAM;YACL2F,SAAS;YACTT,OAAO,EAAC,UAAU;YAClBkB,OAAO,EAAExB,gBAAiB;YAC1BG,EAAE,EAAE;cACFS,EAAE,EAAE,CAAC;cACLqB,WAAW,EAAE,SAAS;cACtBpF,KAAK,EAAE,SAAS;cAChB,SAAS,EAAE;gBACToF,WAAW,EAAE,SAAS;gBACtBrF,eAAe,EAAE;cACnB;YACF,CAAE;YAAAwD,QAAA,EACH;UAED;YAAAI,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC,eAETlE,OAAA,CAACxB,GAAG;YAACkF,EAAE,EAAE;cAAE9C,OAAO,EAAE,MAAM;cAAE6E,GAAG,EAAE,CAAC;cAAEtB,EAAE,EAAE;YAAE,CAAE;YAAAR,QAAA,gBAC1C3D,OAAA,CAACrB,MAAM;cACL2F,SAAS;cACTT,OAAO,EAAC,UAAU;cAClB6B,SAAS,eAAE1F,OAAA,CAACR,MAAM;gBAAAuE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAE;cACtBR,EAAE,EAAE;gBAAE8B,WAAW,EAAE,SAAS;gBAAEpF,KAAK,EAAE;cAAU,CAAE;cAAAuD,QAAA,EAClD;YAED;cAAAI,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC,eACTlE,OAAA,CAACrB,MAAM;cACL2F,SAAS;cACTT,OAAO,EAAC,UAAU;cAClB6B,SAAS,eAAE1F,OAAA,CAACP,QAAQ;gBAAAsE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAE;cACxBR,EAAE,EAAE;gBAAE8B,WAAW,EAAE,SAAS;gBAAEpF,KAAK,EAAE;cAAU,CAAE;cAAAuD,QAAA,EAClD;YAED;cAAAI,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACN,CAAC,eAENlE,OAAA,CAACxB,GAAG;YAACkF,EAAE,EAAE;cAAEnD,SAAS,EAAE;YAAS,CAAE;YAAAoD,QAAA,eAC/B3D,OAAA,CAACpB,UAAU;cAACiF,OAAO,EAAC,OAAO;cAACzD,KAAK,EAAC,eAAe;cAAAuD,QAAA,GAAC,mBAC/B,EAAC,GAAG,eACrB3D,OAAA,CAAChB,IAAI;gBACH2G,SAAS,EAAC,QAAQ;gBAClB9B,OAAO,EAAC,OAAO;gBACfkB,OAAO,EAAEA,CAAA,KAAMxD,QAAQ,CAAC,WAAW,CAAE;gBACrCmC,EAAE,EAAE;kBAAEtD,KAAK,EAAE,SAAS;kBAAEwF,cAAc,EAAE;gBAAO,CAAE;gBAAAjC,QAAA,EAClD;cAED;gBAAAI,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACG;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACV,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACF,CAAC,eAGPlE,OAAA,CAACxB,GAAG;UAACkF,EAAE,EAAE;YAAEyB,EAAE,EAAE,CAAC;YAAEU,CAAC,EAAE,CAAC;YAAE1F,eAAe,EAAE,SAAS;YAAE2F,YAAY,EAAE;UAAE,CAAE;UAAAnC,QAAA,gBACpE3D,OAAA,CAACpB,UAAU;YAACiF,OAAO,EAAC,SAAS;YAACH,EAAE,EAAE;cAAEI,UAAU,EAAE,MAAM;cAAElD,OAAO,EAAE,OAAO;cAAEuD,EAAE,EAAE;YAAE,CAAE;YAAAR,QAAA,EAAC;UAEnF;YAAAI,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAY,CAAC,eACblE,OAAA,CAACpB,UAAU;YAACiF,OAAO,EAAC,SAAS;YAACH,EAAE,EAAE;cAAE9C,OAAO,EAAE;YAAQ,CAAE;YAAA+C,QAAA,EAAC;UAExD;YAAAI,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAY,CAAC,eACblE,OAAA,CAACpB,UAAU;YAACiF,OAAO,EAAC,SAAS;YAACH,EAAE,EAAE;cAAE9C,OAAO,EAAE;YAAQ,CAAE;YAAA+C,QAAA,EAAC;UAExD;YAAAI,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAY,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACV,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACG;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACE,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACd,CAAC;AAEV,CAAC;AAAC5C,EAAA,CA3PID,KAAK;EAAA,QACQ1B,WAAW;AAAA;AAAAoG,GAAA,GADxB1E,KAAK;AA6PX,eAAeA,KAAK;AAAC,IAAAZ,EAAA,EAAAK,GAAA,EAAAM,GAAA,EAAA2E,GAAA;AAAAC,YAAA,CAAAvF,EAAA;AAAAuF,YAAA,CAAAlF,GAAA;AAAAkF,YAAA,CAAA5E,GAAA;AAAA4E,YAAA,CAAAD,GAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}