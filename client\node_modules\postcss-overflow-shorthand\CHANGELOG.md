# Changes to PostCSS Overflow Shorthand

### 3.0.4 (July 8, 2022)

- Fix case insensitive matching.

### 3.0.3 (February 5, 2022)

- Improved `es module` and `commonjs` compatibility

### 3.0.2 (January 2, 2022)

- Removed Sourcemaps from package tarball.
- Moved CLI to CLI Package. See [announcement](https://github.com/csstools/postcss-plugins/discussions/121).

### 3.0.1 (December 13, 2021)

- Updated: documentation

### 3.0.0 (September 17, 2021)

- Updated: Support for PostCS 8+ (major).
- Updated: Support for Node 12+ (major).

### 2.0.0 (September 17, 2018)

- Updated: Support for PostCSS v7+
- Updated: Support for Node v6+

### 1.0.1 (May 8, 2018)

- Fixed: Single `overflow` values previously being parsed

### 1.0.0 (April 30, 2018)

- Initial version
