{"ast": null, "code": "var trimmedEndIndex = require('./_trimmedEndIndex');\n\n/** Used to match leading whitespace. */\nvar reTrimStart = /^\\s+/;\n\n/**\n * The base implementation of `_.trim`.\n *\n * @private\n * @param {string} string The string to trim.\n * @returns {string} Returns the trimmed string.\n */\nfunction baseTrim(string) {\n  return string ? string.slice(0, trimmedEndIndex(string) + 1).replace(reTrimStart, '') : string;\n}\nmodule.exports = baseTrim;", "map": {"version": 3, "names": ["trimmedEndIndex", "require", "reTrimStart", "baseTrim", "string", "slice", "replace", "module", "exports"], "sources": ["D:/ecommerce/node_modules/lodash/_baseTrim.js"], "sourcesContent": ["var trimmedEndIndex = require('./_trimmedEndIndex');\n\n/** Used to match leading whitespace. */\nvar reTrimStart = /^\\s+/;\n\n/**\n * The base implementation of `_.trim`.\n *\n * @private\n * @param {string} string The string to trim.\n * @returns {string} Returns the trimmed string.\n */\nfunction baseTrim(string) {\n  return string\n    ? string.slice(0, trimmedEndIndex(string) + 1).replace(reTrimStart, '')\n    : string;\n}\n\nmodule.exports = baseTrim;\n"], "mappings": "AAAA,IAAIA,eAAe,GAAGC,OAAO,CAAC,oBAAoB,CAAC;;AAEnD;AACA,IAAIC,WAAW,GAAG,MAAM;;AAExB;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASC,QAAQA,CAACC,MAAM,EAAE;EACxB,OAAOA,MAAM,GACTA,MAAM,CAACC,KAAK,CAAC,CAAC,EAAEL,eAAe,CAACI,MAAM,CAAC,GAAG,CAAC,CAAC,CAACE,OAAO,CAACJ,WAAW,EAAE,EAAE,CAAC,GACrEE,MAAM;AACZ;AAEAG,MAAM,CAACC,OAAO,GAAGL,QAAQ", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}