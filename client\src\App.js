import React from 'react';
import { BrowserRouter as Router, Routes, Route, Link } from 'react-router-dom';
import { Provider } from 'react-redux';
import { ThemeProvider, createTheme } from '@mui/material/styles';
import CssBaseline from '@mui/material/CssBaseline';
import { AppBar, Toolbar, Typography, Button, Container, Box, IconButton } from '@mui/material';
import { Menu as MenuIcon } from '@mui/icons-material';
import { configureStore, createSlice } from '@reduxjs/toolkit';
import Home from './components/Home';
import Cart from './components/Cart';
import Checkout from './components/Checkout';
import Dashboard from './components/admin/Dashboard';
import AdminDashboard from './components/admin/AdminDashboard';
import UserDashboard from './components/user/UserDashboard';
import Navigation from './components/Navigation';
import './App.css';

// Create a theme
const theme = createTheme({
  palette: {
    primary: {
      main: '#2c3e50',
    },
    secondary: {
      main: '#e74c3c',
    },
  },
});

// Simple Redux store for demo purposes

const cartSlice = createSlice({
  name: 'cart',
  initialState: {
    items: [],
    total: 0,
  },
  reducers: {
    addToCart: (state, action) => {
      state.items.push(action.payload);
      state.total += action.payload.price;
    },
    removeFromCart: (state, action) => {
      const index = state.items.findIndex(item => item.id === action.payload.id);
      if (index !== -1) {
        state.total -= state.items[index].price;
        state.items.splice(index, 1);
      }
    },
  },
});

const store = configureStore({
  reducer: {
    cart: cartSlice.reducer,
  },
});

function App() {
  const [drawerOpen, setDrawerOpen] = React.useState(false);

  const toggleDrawer = () => {
    setDrawerOpen(!drawerOpen);
  };

  return (
    <Provider store={store}>
      <ThemeProvider theme={theme}>
        <CssBaseline />
        <Router>
          <div className="App">
            <AppBar position="static">
              <Toolbar>
                <IconButton
                  edge="start"
                  color="inherit"
                  aria-label="menu"
                  onClick={toggleDrawer}
                  sx={{ mr: 2 }}
                >
                  <MenuIcon />
                </IconButton>
                <Typography variant="h6" component="div" sx={{ flexGrow: 1 }}>
                  Spice Ecommerce
                </Typography>
                <Button color="inherit" component={Link} to="/">
                  Home
                </Button>
                <Button color="inherit" component={Link} to="/cart">
                  Cart
                </Button>
                <Button color="inherit" component={Link} to="/dashboard">
                  Dashboard
                </Button>
                <Button color="inherit" component={Link} to="/admin">
                  Admin
                </Button>
              </Toolbar>
            </AppBar>

            <Navigation open={drawerOpen} onClose={() => setDrawerOpen(false)} />

            <Box sx={{ mt: 0, mb: 4 }}>
              <Routes>
                <Route path="/" element={
                  <Container maxWidth="lg" sx={{ mt: 4, mb: 4 }}>
                    <Home />
                  </Container>
                } />
                <Route path="/cart" element={
                  <Container maxWidth="lg" sx={{ mt: 4, mb: 4 }}>
                    <Cart />
                  </Container>
                } />
                <Route path="/checkout" element={
                  <Container maxWidth="lg" sx={{ mt: 4, mb: 4 }}>
                    <Checkout />
                  </Container>
                } />
                <Route path="/dashboard" element={<UserDashboard />} />
                <Route path="/admin" element={<AdminDashboard />} />
                <Route path="/admin/old" element={
                  <Container maxWidth="lg" sx={{ mt: 4, mb: 4 }}>
                    <Dashboard />
                  </Container>
                } />
              </Routes>
            </Box>

            <footer style={{
              backgroundColor: '#2c3e50',
              color: 'white',
              textAlign: 'center',
              padding: '2rem 0',
              marginTop: '4rem'
            }}>
              <Typography variant="body2">
                © 2024 Spice Ecommerce. All rights reserved.
              </Typography>
            </footer>
          </div>
        </Router>
      </ThemeProvider>
    </Provider>
  );
}

export default App;
