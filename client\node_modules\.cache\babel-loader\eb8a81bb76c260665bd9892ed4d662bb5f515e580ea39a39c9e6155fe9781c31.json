{"ast": null, "code": "var castPath = require('./_castPath'),\n  toKey = require('./_toKey');\n\n/**\n * The base implementation of `_.get` without support for default values.\n *\n * @private\n * @param {Object} object The object to query.\n * @param {Array|string} path The path of the property to get.\n * @returns {*} Returns the resolved value.\n */\nfunction baseGet(object, path) {\n  path = castPath(path, object);\n  var index = 0,\n    length = path.length;\n  while (object != null && index < length) {\n    object = object[toKey(path[index++])];\n  }\n  return index && index == length ? object : undefined;\n}\nmodule.exports = baseGet;", "map": {"version": 3, "names": ["<PERSON><PERSON><PERSON>", "require", "to<PERSON><PERSON>", "baseGet", "object", "path", "index", "length", "undefined", "module", "exports"], "sources": ["D:/ecommerce/node_modules/lodash/_baseGet.js"], "sourcesContent": ["var castPath = require('./_castPath'),\n    toKey = require('./_toKey');\n\n/**\n * The base implementation of `_.get` without support for default values.\n *\n * @private\n * @param {Object} object The object to query.\n * @param {Array|string} path The path of the property to get.\n * @returns {*} Returns the resolved value.\n */\nfunction baseGet(object, path) {\n  path = castPath(path, object);\n\n  var index = 0,\n      length = path.length;\n\n  while (object != null && index < length) {\n    object = object[toKey(path[index++])];\n  }\n  return (index && index == length) ? object : undefined;\n}\n\nmodule.exports = baseGet;\n"], "mappings": "AAAA,IAAIA,QAAQ,GAAGC,OAAO,CAAC,aAAa,CAAC;EACjCC,KAAK,GAAGD,OAAO,CAAC,UAAU,CAAC;;AAE/B;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASE,OAAOA,CAACC,MAAM,EAAEC,IAAI,EAAE;EAC7BA,IAAI,GAAGL,QAAQ,CAACK,IAAI,EAAED,MAAM,CAAC;EAE7B,IAAIE,KAAK,GAAG,CAAC;IACTC,MAAM,GAAGF,IAAI,CAACE,MAAM;EAExB,OAAOH,MAAM,IAAI,IAAI,IAAIE,KAAK,GAAGC,MAAM,EAAE;IACvCH,MAAM,GAAGA,MAAM,CAACF,KAAK,CAACG,IAAI,CAACC,KAAK,EAAE,CAAC,CAAC,CAAC;EACvC;EACA,OAAQA,KAAK,IAAIA,KAAK,IAAIC,MAAM,GAAIH,MAAM,GAAGI,SAAS;AACxD;AAEAC,MAAM,CAACC,OAAO,GAAGP,OAAO", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}