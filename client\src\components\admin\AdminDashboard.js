import React, { useState, useEffect } from 'react';
import {
  Box,
  Grid,
  Paper,
  Typography,
  Card,
  CardContent,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Button,
  Chip,
  Avatar,
  LinearProgress,
  IconButton,
  Menu,
  MenuItem,
  CircularProgress,
  Alert,
} from '@mui/material';
import {
  TrendingUp,
  TrendingDown,
  People,
  ShoppingCart,
  Visibility,
  AttachMoney,
  MoreVert,
  Edit,
  Delete,
  Refresh,
} from '@mui/icons-material';
import { analyticsAPI, handleAPIError } from '../../services/api';

const AdminDashboard = () => {
  const [anchorEl, setAnchorEl] = useState(null);
  const [selectedProduct, setSelectedProduct] = useState(null);
  const [analytics, setAnalytics] = useState(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);

  // Fetch analytics data
  const fetchAnalytics = async () => {
    try {
      setLoading(true);
      setError(null);
      const response = await analyticsAPI.getAdminAnalytics();
      setAnalytics(response.data);
    } catch (err) {
      setError(handleAPIError(err));
      console.error('Failed to fetch analytics:', err);
      // Fallback to mock data
      setAnalytics(getMockAnalytics());
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchAnalytics();
  }, []);

  // Mock data fallback
  const getMockAnalytics = () => ({
    stats: {
      totalVisits: 914001,
      bounceRate: 46.41,
      pageViews: 4054876,
      growthRate: 46.43
    },
    totals: {
      users: 1250,
      products: 45,
      orders: 324,
      revenue: 72000
    },
    monthlyData: [
      { month: 'Jan', users: 324, sales: 45000 },
      { month: 'Feb', users: 456, sales: 52000 },
      { month: 'Mar', users: 378, sales: 48000 },
      { month: 'Apr', users: 520, sales: 61000 },
      { month: 'May', users: 489, sales: 58000 },
      { month: 'Jun', users: 612, sales: 72000 },
    ],
    recentOrders: [
      { id: 'ORD001', customer: 'John Doe', total: 24.99, status: 'Completed', date: '2024-01-15', items: 2 },
      { id: 'ORD002', customer: 'Jane Smith', total: 18.50, status: 'Processing', date: '2024-01-14', items: 1 },
      { id: 'ORD003', customer: 'Mike Johnson', total: 32.75, status: 'Shipped', date: '2024-01-13', items: 3 },
    ],
    topProducts: [
      { id: 1, name: 'Cinnamon Sticks', sales: 245, revenue: 6125 },
      { id: 2, name: 'Black Pepper', sales: 189, revenue: 3402 },
      { id: 3, name: 'Turmeric Powder', sales: 156, revenue: 4992 },
    ],
    trafficSources: [
      { name: 'Organic', value: 44.46, color: '#2196f3' },
      { name: 'Referral', value: 5.54, color: '#4caf50' },
      { name: 'Other', value: 50, color: '#ff9800' }
    ],
    browserStats: [
      { name: 'Google Chrome', percentage: 60, color: '#4285f4' },
      { name: 'Mozilla Firefox', percentage: 18, color: '#ff7139' },
      { name: 'Internet Explorer', percentage: 12, color: '#00bcf2' },
      { name: 'Safari', percentage: 10, color: '#000000' }
    ]
  });

  if (loading) {
    return (
      <Box sx={{ display: 'flex', justifyContent: 'center', alignItems: 'center', height: '100vh' }}>
        <CircularProgress size={60} />
      </Box>
    );
  }

  const stats = [
    {
      title: 'Total Visits',
      value: analytics?.stats?.totalVisits?.toLocaleString() || '914,001',
      icon: People,
      color: '#f44336',
      trend: '+12.5%'
    },
    {
      title: 'Bounce Rate',
      value: `${analytics?.stats?.bounceRate || 46.41}%`,
      icon: TrendingDown,
      color: '#ff9800',
      trend: '-2.3%'
    },
    {
      title: 'Page Views',
      value: analytics?.stats?.pageViews?.toLocaleString() || '4,054,876',
      icon: Visibility,
      color: '#4caf50',
      trend: '+8.7%'
    },
    {
      title: 'Growth Rate',
      value: `${analytics?.stats?.growthRate || 46.43}%`,
      icon: TrendingUp,
      color: '#2196f3',
      trend: '+15.2%'
    },
  ];

  const trafficData = analytics?.trafficSources || [
    { name: 'Organic', value: 44.46, color: '#2196f3' },
    { name: 'Referral', value: 5.54, color: '#4caf50' },
    { name: 'Other', value: 50, color: '#ff9800' },
  ];

  const browserStats = analytics?.browserStats || [
    { name: 'Google Chrome', percentage: 60, color: '#4285f4' },
    { name: 'Mozilla Firefox', percentage: 18, color: '#ff7139' },
    { name: 'Internet Explorer', percentage: 12, color: '#00bcf2' },
    { name: 'Safari', percentage: 10, color: '#000000' },
  ];

  const monthlyData = analytics?.monthlyData || [
    { month: 'Jan', users: 324, sales: 45000 },
    { month: 'Feb', users: 456, sales: 52000 },
    { month: 'Mar', users: 378, sales: 48000 },
    { month: 'Apr', users: 520, sales: 61000 },
    { month: 'May', users: 489, sales: 58000 },
    { month: 'Jun', users: 612, sales: 72000 },
  ];

  const recentOrders = analytics?.recentOrders || [
    { id: 'ORD001', customer: 'John Doe', total: 24.99, status: 'Completed', date: '2024-01-15', items: 2 },
    { id: 'ORD002', customer: 'Jane Smith', total: 18.50, status: 'Processing', date: '2024-01-14', items: 1 },
    { id: 'ORD003', customer: 'Mike Johnson', total: 32.75, status: 'Shipped', date: '2024-01-13', items: 3 },
  ];

  const topProducts = analytics?.topProducts || [
    { id: 1, name: 'Cinnamon Sticks', sales: 245, revenue: 6125, image: '🍯' },
    { id: 2, name: 'Black Pepper', sales: 189, revenue: 3402, image: '🌶️' },
    { id: 3, name: 'Turmeric Powder', sales: 156, revenue: 4992, image: '🟡' },
  ];

  const handleMenuClick = (event, product) => {
    setAnchorEl(event.currentTarget);
    setSelectedProduct(product);
  };

  const handleMenuClose = () => {
    setAnchorEl(null);
    setSelectedProduct(null);
  };

  const getStatusColor = (status) => {
    switch (status) {
      case 'Completed': return 'success';
      case 'Processing': return 'warning';
      case 'Shipped': return 'info';
      case 'Pending': return 'default';
      default: return 'default';
    }
  };

  return (
    <Box sx={{ p: 3, backgroundColor: '#f5f5f5', minHeight: '100vh' }}>
      <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 3 }}>
        <Typography variant="h4" sx={{ fontWeight: 'bold' }}>
          Admin Dashboard
        </Typography>
        <Button
          variant="outlined"
          startIcon={<Refresh />}
          onClick={fetchAnalytics}
          disabled={loading}
        >
          Refresh Data
        </Button>
      </Box>

      {error && (
        <Alert severity="warning" sx={{ mb: 3 }}>
          {error} - Showing fallback data
        </Alert>
      )}

      {/* Stats Cards */}
      <Grid container spacing={3} sx={{ mb: 4 }}>
        {stats.map((stat, index) => (
          <Grid item xs={12} sm={6} md={3} key={index}>
            <Card sx={{ background: stat.color, color: 'white', height: '120px' }}>
              <CardContent>
                <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
                  <Box>
                    <Typography variant="h4" sx={{ fontWeight: 'bold', mb: 1 }}>
                      {stat.value}
                    </Typography>
                    <Typography variant="body2" sx={{ opacity: 0.9 }}>
                      {stat.title}
                    </Typography>
                  </Box>
                  <stat.icon sx={{ fontSize: 40, opacity: 0.8 }} />
                </Box>
                <Typography variant="caption" sx={{ mt: 1, display: 'block' }}>
                  {stat.trend}
                </Typography>
              </CardContent>
            </Card>
          </Grid>
        ))}
      </Grid>

      <Grid container spacing={3}>
        {/* User Statistics Chart */}
        <Grid item xs={12} md={8}>
          <Paper sx={{ p: 3, height: '400px' }}>
            <Typography variant="h6" sx={{ mb: 2 }}>Monthly Sales & Users</Typography>
            <Box sx={{ height: '90%', display: 'flex', flexDirection: 'column', justifyContent: 'center' }}>
              <Grid container spacing={2}>
                {monthlyData.map((data, index) => (
                  <Grid item xs={2} key={data.month}>
                    <Box sx={{ textAlign: 'center' }}>
                      <Typography variant="caption" color="textSecondary">
                        {data.month}
                      </Typography>
                      <Box sx={{ mt: 1, mb: 1 }}>
                        <Box
                          sx={{
                            height: `${(data.users / 600) * 100}px`,
                            backgroundColor: '#2196f3',
                            borderRadius: 1,
                            mb: 0.5,
                            minHeight: '20px',
                          }}
                        />
                        <Typography variant="caption" sx={{ fontSize: '0.7rem' }}>
                          {data.users} users
                        </Typography>
                      </Box>
                      <Box>
                        <Box
                          sx={{
                            height: `${(data.sales / 80000) * 100}px`,
                            backgroundColor: '#4caf50',
                            borderRadius: 1,
                            mb: 0.5,
                            minHeight: '20px',
                          }}
                        />
                        <Typography variant="caption" sx={{ fontSize: '0.7rem' }}>
                          ${data.sales}
                        </Typography>
                      </Box>
                    </Box>
                  </Grid>
                ))}
              </Grid>
              <Box sx={{ display: 'flex', justifyContent: 'center', mt: 2, gap: 2 }}>
                <Box sx={{ display: 'flex', alignItems: 'center' }}>
                  <Box sx={{ width: 12, height: 12, backgroundColor: '#2196f3', mr: 1 }} />
                  <Typography variant="caption">Users</Typography>
                </Box>
                <Box sx={{ display: 'flex', alignItems: 'center' }}>
                  <Box sx={{ width: 12, height: 12, backgroundColor: '#4caf50', mr: 1 }} />
                  <Typography variant="caption">Sales ($)</Typography>
                </Box>
              </Box>
            </Box>
          </Paper>
        </Grid>

        {/* Customer Satisfaction */}
        <Grid item xs={12} md={4}>
          <Paper sx={{ p: 3, height: '400px' }}>
            <Typography variant="h6" sx={{ mb: 2 }}>Customer Satisfaction</Typography>
            <Box sx={{ textAlign: 'center', mb: 3 }}>
              <Typography variant="h2" sx={{ color: '#4caf50', fontWeight: 'bold' }}>
                93.13%
              </Typography>
              <Typography variant="body2" color="textSecondary">
                Previous: 79.82 | Change: +14.29
              </Typography>
            </Box>

            <Typography variant="subtitle1" sx={{ mb: 2 }}>Browser Stats</Typography>
            {browserStats.map((browser, index) => (
              <Box key={index} sx={{ mb: 2 }}>
                <Box sx={{ display: 'flex', justifyContent: 'space-between', mb: 1 }}>
                  <Typography variant="body2">{browser.name}</Typography>
                  <Typography variant="body2">{browser.percentage}%</Typography>
                </Box>
                <LinearProgress
                  variant="determinate"
                  value={browser.percentage}
                  sx={{
                    height: 8,
                    borderRadius: 4,
                    backgroundColor: '#e0e0e0',
                    '& .MuiLinearProgress-bar': {
                      backgroundColor: browser.color
                    }
                  }}
                />
              </Box>
            ))}
          </Paper>
        </Grid>

        {/* Traffic Sources */}
        <Grid item xs={12} md={6}>
          <Paper sx={{ p: 3, height: '350px' }}>
            <Typography variant="h6" sx={{ mb: 2 }}>Visit By Traffic Types</Typography>
            <Box sx={{ display: 'flex', flexDirection: 'column', height: '80%', justifyContent: 'center' }}>
              {trafficData.map((item, index) => (
                <Box key={index} sx={{ mb: 3 }}>
                  <Box sx={{ display: 'flex', justifyContent: 'space-between', mb: 1 }}>
                    <Typography variant="body2">{item.name}</Typography>
                    <Typography variant="body2" sx={{ fontWeight: 'bold' }}>
                      {item.value}%
                    </Typography>
                  </Box>
                  <LinearProgress
                    variant="determinate"
                    value={item.value}
                    sx={{
                      height: 12,
                      borderRadius: 6,
                      backgroundColor: '#e0e0e0',
                      '& .MuiLinearProgress-bar': {
                        backgroundColor: item.color,
                        borderRadius: 6,
                      },
                    }}
                  />
                </Box>
              ))}
              <Box sx={{ mt: 2, textAlign: 'center' }}>
                <Typography variant="caption" color="textSecondary">
                  Total traffic distribution across different sources
                </Typography>
              </Box>
            </Box>
          </Paper>
        </Grid>

        {/* Top Products */}
        <Grid item xs={12} md={6}>
          <Paper sx={{ p: 3, height: '350px' }}>
            <Typography variant="h6" sx={{ mb: 2 }}>Top Selling Products</Typography>
            <TableContainer>
              <Table size="small">
                <TableHead>
                  <TableRow>
                    <TableCell>Product</TableCell>
                    <TableCell align="right">Sales</TableCell>
                    <TableCell align="right">Revenue</TableCell>
                    <TableCell align="right">Actions</TableCell>
                  </TableRow>
                </TableHead>
                <TableBody>
                  {topProducts.map((product) => (
                    <TableRow key={product.id}>
                      <TableCell>
                        <Box sx={{ display: 'flex', alignItems: 'center' }}>
                          <Typography sx={{ mr: 1, fontSize: '1.2em' }}>{product.image}</Typography>
                          <Typography variant="body2">{product.name}</Typography>
                        </Box>
                      </TableCell>
                      <TableCell align="right">{product.sales}</TableCell>
                      <TableCell align="right">${product.revenue}</TableCell>
                      <TableCell align="right">
                        <IconButton size="small" onClick={(e) => handleMenuClick(e, product)}>
                          <MoreVert />
                        </IconButton>
                      </TableCell>
                    </TableRow>
                  ))}
                </TableBody>
              </Table>
            </TableContainer>
          </Paper>
        </Grid>
      </Grid>

      {/* Recent Orders */}
      <Paper sx={{ p: 3, mt: 3 }}>
        <Typography variant="h6" sx={{ mb: 2 }}>Recent Orders</Typography>
        <TableContainer>
          <Table>
            <TableHead>
              <TableRow>
                <TableCell>Order ID</TableCell>
                <TableCell>Customer</TableCell>
                <TableCell>Items</TableCell>
                <TableCell align="right">Total</TableCell>
                <TableCell>Status</TableCell>
                <TableCell>Date</TableCell>
                <TableCell align="right">Actions</TableCell>
              </TableRow>
            </TableHead>
            <TableBody>
              {recentOrders.map((order) => (
                <TableRow key={order.id}>
                  <TableCell>{order.id}</TableCell>
                  <TableCell>{order.customer}</TableCell>
                  <TableCell>{order.items} items</TableCell>
                  <TableCell align="right">${order.total}</TableCell>
                  <TableCell>
                    <Chip
                      label={order.status}
                      color={getStatusColor(order.status)}
                      size="small"
                    />
                  </TableCell>
                  <TableCell>{order.date}</TableCell>
                  <TableCell align="right">
                    <Button size="small" variant="outlined">
                      View
                    </Button>
                  </TableCell>
                </TableRow>
              ))}
            </TableBody>
          </Table>
        </TableContainer>
      </Paper>

      {/* Menu for product actions */}
      <Menu
        anchorEl={anchorEl}
        open={Boolean(anchorEl)}
        onClose={handleMenuClose}
      >
        <MenuItem onClick={handleMenuClose}>
          <Edit sx={{ mr: 1 }} /> Edit
        </MenuItem>
        <MenuItem onClick={handleMenuClose}>
          <Delete sx={{ mr: 1 }} /> Delete
        </MenuItem>
      </Menu>
    </Box>
  );
};

export default AdminDashboard;
