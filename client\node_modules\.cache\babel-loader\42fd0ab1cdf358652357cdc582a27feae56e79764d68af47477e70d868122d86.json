{"ast": null, "code": "/**\n * @fileOverview Scatter Chart\n */\nimport { generateCategoricalChart } from './generateCategoricalChart';\nimport { Scatter } from '../cartesian/Scatter';\nimport { XAxis } from '../cartesian/XAxis';\nimport { YAxis } from '../cartesian/YAxis';\nimport { ZAxis } from '../cartesian/ZAxis';\nimport { formatAxisMap } from '../util/CartesianUtils';\nexport var ScatterChart = generateCategoricalChart({\n  chartName: 'ScatterChart',\n  GraphicalChild: Scatter,\n  defaultTooltipEventType: 'item',\n  validateTooltipEventTypes: ['item'],\n  axisComponents: [{\n    axisType: 'xAxis',\n    AxisComp: XAxis\n  }, {\n    axisType: 'yAxis',\n    AxisComp: YAxis\n  }, {\n    axisType: 'zAxis',\n    AxisComp: ZAxis\n  }],\n  formatAxisMap: formatAxisMap\n});", "map": {"version": 3, "names": ["generateCategoricalChart", "<PERSON><PERSON><PERSON>", "XAxis", "YA<PERSON>s", "ZAxis", "formatAxisMap", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "chartName", "GraphicalChild", "defaultTooltipEventType", "validateTooltipEventTypes", "axisComponents", "axisType", "AxisComp"], "sources": ["D:/ecommerce/node_modules/recharts/es6/chart/ScatterChart.js"], "sourcesContent": ["/**\n * @fileOverview Scatter Chart\n */\nimport { generateCategoricalChart } from './generateCategoricalChart';\nimport { Scatter } from '../cartesian/Scatter';\nimport { XAxis } from '../cartesian/XAxis';\nimport { YAxis } from '../cartesian/YAxis';\nimport { ZAxis } from '../cartesian/ZAxis';\nimport { formatAxisMap } from '../util/CartesianUtils';\nexport var ScatterChart = generateCategoricalChart({\n  chartName: 'ScatterChart',\n  GraphicalChild: Scatter,\n  defaultTooltipEventType: 'item',\n  validateTooltipEventTypes: ['item'],\n  axisComponents: [{\n    axisType: 'xAxis',\n    AxisComp: XAxis\n  }, {\n    axisType: 'yAxis',\n    AxisComp: YAxis\n  }, {\n    axisType: 'zAxis',\n    AxisComp: ZAxis\n  }],\n  formatAxisMap: formatAxisMap\n});"], "mappings": "AAAA;AACA;AACA;AACA,SAASA,wBAAwB,QAAQ,4BAA4B;AACrE,SAASC,OAAO,QAAQ,sBAAsB;AAC9C,SAASC,KAAK,QAAQ,oBAAoB;AAC1C,SAASC,KAAK,QAAQ,oBAAoB;AAC1C,SAASC,KAAK,QAAQ,oBAAoB;AAC1C,SAASC,aAAa,QAAQ,wBAAwB;AACtD,OAAO,IAAIC,YAAY,GAAGN,wBAAwB,CAAC;EACjDO,SAAS,EAAE,cAAc;EACzBC,cAAc,EAAEP,OAAO;EACvBQ,uBAAuB,EAAE,MAAM;EAC/BC,yBAAyB,EAAE,CAAC,MAAM,CAAC;EACnCC,cAAc,EAAE,CAAC;IACfC,QAAQ,EAAE,OAAO;IACjBC,QAAQ,EAAEX;EACZ,CAAC,EAAE;IACDU,QAAQ,EAAE,OAAO;IACjBC,QAAQ,EAAEV;EACZ,CAAC,EAAE;IACDS,QAAQ,EAAE,OAAO;IACjBC,QAAQ,EAAET;EACZ,CAAC,CAAC;EACFC,aAAa,EAAEA;AACjB,CAAC,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}