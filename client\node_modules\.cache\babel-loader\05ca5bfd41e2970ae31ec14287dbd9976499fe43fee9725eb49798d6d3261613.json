{"ast": null, "code": "/**\n * This method returns `false`.\n *\n * @static\n * @memberOf _\n * @since 4.13.0\n * @category Util\n * @returns {boolean} Returns `false`.\n * @example\n *\n * _.times(2, _.stubFalse);\n * // => [false, false]\n */\nfunction stubFalse() {\n  return false;\n}\nmodule.exports = stubFalse;", "map": {"version": 3, "names": ["stubFalse", "module", "exports"], "sources": ["D:/ecommerce/node_modules/lodash/stubFalse.js"], "sourcesContent": ["/**\n * This method returns `false`.\n *\n * @static\n * @memberOf _\n * @since 4.13.0\n * @category Util\n * @returns {boolean} Returns `false`.\n * @example\n *\n * _.times(2, _.stubFalse);\n * // => [false, false]\n */\nfunction stubFalse() {\n  return false;\n}\n\nmodule.exports = stubFalse;\n"], "mappings": "AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASA,SAASA,CAAA,EAAG;EACnB,OAAO,KAAK;AACd;AAEAC,MAAM,CAACC,OAAO,GAAGF,SAAS", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}