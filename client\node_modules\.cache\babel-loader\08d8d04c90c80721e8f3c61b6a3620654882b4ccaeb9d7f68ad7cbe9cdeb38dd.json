{"ast": null, "code": "function _typeof(o) {\n  \"@babel/helpers - typeof\";\n\n  return _typeof = \"function\" == typeof Symbol && \"symbol\" == typeof Symbol.iterator ? function (o) {\n    return typeof o;\n  } : function (o) {\n    return o && \"function\" == typeof Symbol && o.constructor === Symbol && o !== Symbol.prototype ? \"symbol\" : typeof o;\n  }, _typeof(o);\n}\nfunction _extends() {\n  _extends = Object.assign ? Object.assign.bind() : function (target) {\n    for (var i = 1; i < arguments.length; i++) {\n      var source = arguments[i];\n      for (var key in source) {\n        if (Object.prototype.hasOwnProperty.call(source, key)) {\n          target[key] = source[key];\n        }\n      }\n    }\n    return target;\n  };\n  return _extends.apply(this, arguments);\n}\nfunction ownKeys(e, r) {\n  var t = Object.keys(e);\n  if (Object.getOwnPropertySymbols) {\n    var o = Object.getOwnPropertySymbols(e);\n    r && (o = o.filter(function (r) {\n      return Object.getOwnPropertyDescriptor(e, r).enumerable;\n    })), t.push.apply(t, o);\n  }\n  return t;\n}\nfunction _objectSpread(e) {\n  for (var r = 1; r < arguments.length; r++) {\n    var t = null != arguments[r] ? arguments[r] : {};\n    r % 2 ? ownKeys(Object(t), !0).forEach(function (r) {\n      _defineProperty(e, r, t[r]);\n    }) : Object.getOwnPropertyDescriptors ? Object.defineProperties(e, Object.getOwnPropertyDescriptors(t)) : ownKeys(Object(t)).forEach(function (r) {\n      Object.defineProperty(e, r, Object.getOwnPropertyDescriptor(t, r));\n    });\n  }\n  return e;\n}\nfunction _classCallCheck(instance, Constructor) {\n  if (!(instance instanceof Constructor)) {\n    throw new TypeError(\"Cannot call a class as a function\");\n  }\n}\nfunction _defineProperties(target, props) {\n  for (var i = 0; i < props.length; i++) {\n    var descriptor = props[i];\n    descriptor.enumerable = descriptor.enumerable || false;\n    descriptor.configurable = true;\n    if (\"value\" in descriptor) descriptor.writable = true;\n    Object.defineProperty(target, _toPropertyKey(descriptor.key), descriptor);\n  }\n}\nfunction _createClass(Constructor, protoProps, staticProps) {\n  if (protoProps) _defineProperties(Constructor.prototype, protoProps);\n  if (staticProps) _defineProperties(Constructor, staticProps);\n  Object.defineProperty(Constructor, \"prototype\", {\n    writable: false\n  });\n  return Constructor;\n}\nfunction _callSuper(t, o, e) {\n  return o = _getPrototypeOf(o), _possibleConstructorReturn(t, _isNativeReflectConstruct() ? Reflect.construct(o, e || [], _getPrototypeOf(t).constructor) : o.apply(t, e));\n}\nfunction _possibleConstructorReturn(self, call) {\n  if (call && (_typeof(call) === \"object\" || typeof call === \"function\")) {\n    return call;\n  } else if (call !== void 0) {\n    throw new TypeError(\"Derived constructors may only return object or undefined\");\n  }\n  return _assertThisInitialized(self);\n}\nfunction _assertThisInitialized(self) {\n  if (self === void 0) {\n    throw new ReferenceError(\"this hasn't been initialised - super() hasn't been called\");\n  }\n  return self;\n}\nfunction _isNativeReflectConstruct() {\n  try {\n    var t = !Boolean.prototype.valueOf.call(Reflect.construct(Boolean, [], function () {}));\n  } catch (t) {}\n  return (_isNativeReflectConstruct = function _isNativeReflectConstruct() {\n    return !!t;\n  })();\n}\nfunction _getPrototypeOf(o) {\n  _getPrototypeOf = Object.setPrototypeOf ? Object.getPrototypeOf.bind() : function _getPrototypeOf(o) {\n    return o.__proto__ || Object.getPrototypeOf(o);\n  };\n  return _getPrototypeOf(o);\n}\nfunction _inherits(subClass, superClass) {\n  if (typeof superClass !== \"function\" && superClass !== null) {\n    throw new TypeError(\"Super expression must either be null or a function\");\n  }\n  subClass.prototype = Object.create(superClass && superClass.prototype, {\n    constructor: {\n      value: subClass,\n      writable: true,\n      configurable: true\n    }\n  });\n  Object.defineProperty(subClass, \"prototype\", {\n    writable: false\n  });\n  if (superClass) _setPrototypeOf(subClass, superClass);\n}\nfunction _setPrototypeOf(o, p) {\n  _setPrototypeOf = Object.setPrototypeOf ? Object.setPrototypeOf.bind() : function _setPrototypeOf(o, p) {\n    o.__proto__ = p;\n    return o;\n  };\n  return _setPrototypeOf(o, p);\n}\nfunction _defineProperty(obj, key, value) {\n  key = _toPropertyKey(key);\n  if (key in obj) {\n    Object.defineProperty(obj, key, {\n      value: value,\n      enumerable: true,\n      configurable: true,\n      writable: true\n    });\n  } else {\n    obj[key] = value;\n  }\n  return obj;\n}\nfunction _toPropertyKey(t) {\n  var i = _toPrimitive(t, \"string\");\n  return \"symbol\" == _typeof(i) ? i : i + \"\";\n}\nfunction _toPrimitive(t, r) {\n  if (\"object\" != _typeof(t) || !t) return t;\n  var e = t[Symbol.toPrimitive];\n  if (void 0 !== e) {\n    var i = e.call(t, r || \"default\");\n    if (\"object\" != _typeof(i)) return i;\n    throw new TypeError(\"@@toPrimitive must return a primitive value.\");\n  }\n  return (\"string\" === r ? String : Number)(t);\n}\n/**\n * @fileOverview Brush\n */\nimport React, { PureComponent, Children } from 'react';\nimport clsx from 'clsx';\nimport { scalePoint } from 'victory-vendor/d3-scale';\nimport isFunction from 'lodash/isFunction';\nimport range from 'lodash/range';\nimport { Layer } from '../container/Layer';\nimport { Text } from '../component/Text';\nimport { getValueByDataKey } from '../util/ChartUtils';\nimport { isNumber } from '../util/DataUtils';\nimport { generatePrefixStyle } from '../util/CssPrefixUtils';\nimport { filterProps } from '../util/ReactUtils';\nvar createScale = function createScale(_ref) {\n  var data = _ref.data,\n    startIndex = _ref.startIndex,\n    endIndex = _ref.endIndex,\n    x = _ref.x,\n    width = _ref.width,\n    travellerWidth = _ref.travellerWidth;\n  if (!data || !data.length) {\n    return {};\n  }\n  var len = data.length;\n  var scale = scalePoint().domain(range(0, len)).range([x, x + width - travellerWidth]);\n  var scaleValues = scale.domain().map(function (entry) {\n    return scale(entry);\n  });\n  return {\n    isTextActive: false,\n    isSlideMoving: false,\n    isTravellerMoving: false,\n    isTravellerFocused: false,\n    startX: scale(startIndex),\n    endX: scale(endIndex),\n    scale: scale,\n    scaleValues: scaleValues\n  };\n};\nvar isTouch = function isTouch(e) {\n  return e.changedTouches && !!e.changedTouches.length;\n};\nexport var Brush = /*#__PURE__*/function (_PureComponent) {\n  function Brush(props) {\n    var _this;\n    _classCallCheck(this, Brush);\n    _this = _callSuper(this, Brush, [props]);\n    _defineProperty(_this, \"handleDrag\", function (e) {\n      if (_this.leaveTimer) {\n        clearTimeout(_this.leaveTimer);\n        _this.leaveTimer = null;\n      }\n      if (_this.state.isTravellerMoving) {\n        _this.handleTravellerMove(e);\n      } else if (_this.state.isSlideMoving) {\n        _this.handleSlideDrag(e);\n      }\n    });\n    _defineProperty(_this, \"handleTouchMove\", function (e) {\n      if (e.changedTouches != null && e.changedTouches.length > 0) {\n        _this.handleDrag(e.changedTouches[0]);\n      }\n    });\n    _defineProperty(_this, \"handleDragEnd\", function () {\n      _this.setState({\n        isTravellerMoving: false,\n        isSlideMoving: false\n      }, function () {\n        var _this$props = _this.props,\n          endIndex = _this$props.endIndex,\n          onDragEnd = _this$props.onDragEnd,\n          startIndex = _this$props.startIndex;\n        onDragEnd === null || onDragEnd === void 0 || onDragEnd({\n          endIndex: endIndex,\n          startIndex: startIndex\n        });\n      });\n      _this.detachDragEndListener();\n    });\n    _defineProperty(_this, \"handleLeaveWrapper\", function () {\n      if (_this.state.isTravellerMoving || _this.state.isSlideMoving) {\n        _this.leaveTimer = window.setTimeout(_this.handleDragEnd, _this.props.leaveTimeOut);\n      }\n    });\n    _defineProperty(_this, \"handleEnterSlideOrTraveller\", function () {\n      _this.setState({\n        isTextActive: true\n      });\n    });\n    _defineProperty(_this, \"handleLeaveSlideOrTraveller\", function () {\n      _this.setState({\n        isTextActive: false\n      });\n    });\n    _defineProperty(_this, \"handleSlideDragStart\", function (e) {\n      var event = isTouch(e) ? e.changedTouches[0] : e;\n      _this.setState({\n        isTravellerMoving: false,\n        isSlideMoving: true,\n        slideMoveStartX: event.pageX\n      });\n      _this.attachDragEndListener();\n    });\n    _this.travellerDragStartHandlers = {\n      startX: _this.handleTravellerDragStart.bind(_this, 'startX'),\n      endX: _this.handleTravellerDragStart.bind(_this, 'endX')\n    };\n    _this.state = {};\n    return _this;\n  }\n  _inherits(Brush, _PureComponent);\n  return _createClass(Brush, [{\n    key: \"componentWillUnmount\",\n    value: function componentWillUnmount() {\n      if (this.leaveTimer) {\n        clearTimeout(this.leaveTimer);\n        this.leaveTimer = null;\n      }\n      this.detachDragEndListener();\n    }\n  }, {\n    key: \"getIndex\",\n    value: function getIndex(_ref2) {\n      var startX = _ref2.startX,\n        endX = _ref2.endX;\n      var scaleValues = this.state.scaleValues;\n      var _this$props2 = this.props,\n        gap = _this$props2.gap,\n        data = _this$props2.data;\n      var lastIndex = data.length - 1;\n      var min = Math.min(startX, endX);\n      var max = Math.max(startX, endX);\n      var minIndex = Brush.getIndexInRange(scaleValues, min);\n      var maxIndex = Brush.getIndexInRange(scaleValues, max);\n      return {\n        startIndex: minIndex - minIndex % gap,\n        endIndex: maxIndex === lastIndex ? lastIndex : maxIndex - maxIndex % gap\n      };\n    }\n  }, {\n    key: \"getTextOfTick\",\n    value: function getTextOfTick(index) {\n      var _this$props3 = this.props,\n        data = _this$props3.data,\n        tickFormatter = _this$props3.tickFormatter,\n        dataKey = _this$props3.dataKey;\n      var text = getValueByDataKey(data[index], dataKey, index);\n      return isFunction(tickFormatter) ? tickFormatter(text, index) : text;\n    }\n  }, {\n    key: \"attachDragEndListener\",\n    value: function attachDragEndListener() {\n      window.addEventListener('mouseup', this.handleDragEnd, true);\n      window.addEventListener('touchend', this.handleDragEnd, true);\n      window.addEventListener('mousemove', this.handleDrag, true);\n    }\n  }, {\n    key: \"detachDragEndListener\",\n    value: function detachDragEndListener() {\n      window.removeEventListener('mouseup', this.handleDragEnd, true);\n      window.removeEventListener('touchend', this.handleDragEnd, true);\n      window.removeEventListener('mousemove', this.handleDrag, true);\n    }\n  }, {\n    key: \"handleSlideDrag\",\n    value: function handleSlideDrag(e) {\n      var _this$state = this.state,\n        slideMoveStartX = _this$state.slideMoveStartX,\n        startX = _this$state.startX,\n        endX = _this$state.endX;\n      var _this$props4 = this.props,\n        x = _this$props4.x,\n        width = _this$props4.width,\n        travellerWidth = _this$props4.travellerWidth,\n        startIndex = _this$props4.startIndex,\n        endIndex = _this$props4.endIndex,\n        onChange = _this$props4.onChange;\n      var delta = e.pageX - slideMoveStartX;\n      if (delta > 0) {\n        delta = Math.min(delta, x + width - travellerWidth - endX, x + width - travellerWidth - startX);\n      } else if (delta < 0) {\n        delta = Math.max(delta, x - startX, x - endX);\n      }\n      var newIndex = this.getIndex({\n        startX: startX + delta,\n        endX: endX + delta\n      });\n      if ((newIndex.startIndex !== startIndex || newIndex.endIndex !== endIndex) && onChange) {\n        onChange(newIndex);\n      }\n      this.setState({\n        startX: startX + delta,\n        endX: endX + delta,\n        slideMoveStartX: e.pageX\n      });\n    }\n  }, {\n    key: \"handleTravellerDragStart\",\n    value: function handleTravellerDragStart(id, e) {\n      var event = isTouch(e) ? e.changedTouches[0] : e;\n      this.setState({\n        isSlideMoving: false,\n        isTravellerMoving: true,\n        movingTravellerId: id,\n        brushMoveStartX: event.pageX\n      });\n      this.attachDragEndListener();\n    }\n  }, {\n    key: \"handleTravellerMove\",\n    value: function handleTravellerMove(e) {\n      var _this$state2 = this.state,\n        brushMoveStartX = _this$state2.brushMoveStartX,\n        movingTravellerId = _this$state2.movingTravellerId,\n        endX = _this$state2.endX,\n        startX = _this$state2.startX;\n      var prevValue = this.state[movingTravellerId];\n      var _this$props5 = this.props,\n        x = _this$props5.x,\n        width = _this$props5.width,\n        travellerWidth = _this$props5.travellerWidth,\n        onChange = _this$props5.onChange,\n        gap = _this$props5.gap,\n        data = _this$props5.data;\n      var params = {\n        startX: this.state.startX,\n        endX: this.state.endX\n      };\n      var delta = e.pageX - brushMoveStartX;\n      if (delta > 0) {\n        delta = Math.min(delta, x + width - travellerWidth - prevValue);\n      } else if (delta < 0) {\n        delta = Math.max(delta, x - prevValue);\n      }\n      params[movingTravellerId] = prevValue + delta;\n      var newIndex = this.getIndex(params);\n      var startIndex = newIndex.startIndex,\n        endIndex = newIndex.endIndex;\n      var isFullGap = function isFullGap() {\n        var lastIndex = data.length - 1;\n        if (movingTravellerId === 'startX' && (endX > startX ? startIndex % gap === 0 : endIndex % gap === 0) || endX < startX && endIndex === lastIndex || movingTravellerId === 'endX' && (endX > startX ? endIndex % gap === 0 : startIndex % gap === 0) || endX > startX && endIndex === lastIndex) {\n          return true;\n        }\n        return false;\n      };\n      this.setState(_defineProperty(_defineProperty({}, movingTravellerId, prevValue + delta), \"brushMoveStartX\", e.pageX), function () {\n        if (onChange) {\n          if (isFullGap()) {\n            onChange(newIndex);\n          }\n        }\n      });\n    }\n  }, {\n    key: \"handleTravellerMoveKeyboard\",\n    value: function handleTravellerMoveKeyboard(direction, id) {\n      var _this2 = this;\n      // scaleValues are a list of coordinates. For example: [65, 250, 435, 620, 805, 990].\n      var _this$state3 = this.state,\n        scaleValues = _this$state3.scaleValues,\n        startX = _this$state3.startX,\n        endX = _this$state3.endX;\n      // currentScaleValue refers to which coordinate the current traveller should be placed at.\n      var currentScaleValue = this.state[id];\n      var currentIndex = scaleValues.indexOf(currentScaleValue);\n      if (currentIndex === -1) {\n        return;\n      }\n      var newIndex = currentIndex + direction;\n      if (newIndex === -1 || newIndex >= scaleValues.length) {\n        return;\n      }\n      var newScaleValue = scaleValues[newIndex];\n\n      // Prevent travellers from being on top of each other or overlapping\n      if (id === 'startX' && newScaleValue >= endX || id === 'endX' && newScaleValue <= startX) {\n        return;\n      }\n      this.setState(_defineProperty({}, id, newScaleValue), function () {\n        _this2.props.onChange(_this2.getIndex({\n          startX: _this2.state.startX,\n          endX: _this2.state.endX\n        }));\n      });\n    }\n  }, {\n    key: \"renderBackground\",\n    value: function renderBackground() {\n      var _this$props6 = this.props,\n        x = _this$props6.x,\n        y = _this$props6.y,\n        width = _this$props6.width,\n        height = _this$props6.height,\n        fill = _this$props6.fill,\n        stroke = _this$props6.stroke;\n      return /*#__PURE__*/React.createElement(\"rect\", {\n        stroke: stroke,\n        fill: fill,\n        x: x,\n        y: y,\n        width: width,\n        height: height\n      });\n    }\n  }, {\n    key: \"renderPanorama\",\n    value: function renderPanorama() {\n      var _this$props7 = this.props,\n        x = _this$props7.x,\n        y = _this$props7.y,\n        width = _this$props7.width,\n        height = _this$props7.height,\n        data = _this$props7.data,\n        children = _this$props7.children,\n        padding = _this$props7.padding;\n      var chartElement = Children.only(children);\n      if (!chartElement) {\n        return null;\n      }\n      return /*#__PURE__*/React.cloneElement(chartElement, {\n        x: x,\n        y: y,\n        width: width,\n        height: height,\n        margin: padding,\n        compact: true,\n        data: data\n      });\n    }\n  }, {\n    key: \"renderTravellerLayer\",\n    value: function renderTravellerLayer(travellerX, id) {\n      var _data$startIndex,\n        _data$endIndex,\n        _this3 = this;\n      var _this$props8 = this.props,\n        y = _this$props8.y,\n        travellerWidth = _this$props8.travellerWidth,\n        height = _this$props8.height,\n        traveller = _this$props8.traveller,\n        ariaLabel = _this$props8.ariaLabel,\n        data = _this$props8.data,\n        startIndex = _this$props8.startIndex,\n        endIndex = _this$props8.endIndex;\n      var x = Math.max(travellerX, this.props.x);\n      var travellerProps = _objectSpread(_objectSpread({}, filterProps(this.props, false)), {}, {\n        x: x,\n        y: y,\n        width: travellerWidth,\n        height: height\n      });\n      var ariaLabelBrush = ariaLabel || \"Min value: \".concat((_data$startIndex = data[startIndex]) === null || _data$startIndex === void 0 ? void 0 : _data$startIndex.name, \", Max value: \").concat((_data$endIndex = data[endIndex]) === null || _data$endIndex === void 0 ? void 0 : _data$endIndex.name);\n      return /*#__PURE__*/React.createElement(Layer, {\n        tabIndex: 0,\n        role: \"slider\",\n        \"aria-label\": ariaLabelBrush,\n        \"aria-valuenow\": travellerX,\n        className: \"recharts-brush-traveller\",\n        onMouseEnter: this.handleEnterSlideOrTraveller,\n        onMouseLeave: this.handleLeaveSlideOrTraveller,\n        onMouseDown: this.travellerDragStartHandlers[id],\n        onTouchStart: this.travellerDragStartHandlers[id],\n        onKeyDown: function onKeyDown(e) {\n          if (!['ArrowLeft', 'ArrowRight'].includes(e.key)) {\n            return;\n          }\n          e.preventDefault();\n          e.stopPropagation();\n          _this3.handleTravellerMoveKeyboard(e.key === 'ArrowRight' ? 1 : -1, id);\n        },\n        onFocus: function onFocus() {\n          _this3.setState({\n            isTravellerFocused: true\n          });\n        },\n        onBlur: function onBlur() {\n          _this3.setState({\n            isTravellerFocused: false\n          });\n        },\n        style: {\n          cursor: 'col-resize'\n        }\n      }, Brush.renderTraveller(traveller, travellerProps));\n    }\n  }, {\n    key: \"renderSlide\",\n    value: function renderSlide(startX, endX) {\n      var _this$props9 = this.props,\n        y = _this$props9.y,\n        height = _this$props9.height,\n        stroke = _this$props9.stroke,\n        travellerWidth = _this$props9.travellerWidth;\n      var x = Math.min(startX, endX) + travellerWidth;\n      var width = Math.max(Math.abs(endX - startX) - travellerWidth, 0);\n      return /*#__PURE__*/React.createElement(\"rect\", {\n        className: \"recharts-brush-slide\",\n        onMouseEnter: this.handleEnterSlideOrTraveller,\n        onMouseLeave: this.handleLeaveSlideOrTraveller,\n        onMouseDown: this.handleSlideDragStart,\n        onTouchStart: this.handleSlideDragStart,\n        style: {\n          cursor: 'move'\n        },\n        stroke: \"none\",\n        fill: stroke,\n        fillOpacity: 0.2,\n        x: x,\n        y: y,\n        width: width,\n        height: height\n      });\n    }\n  }, {\n    key: \"renderText\",\n    value: function renderText() {\n      var _this$props10 = this.props,\n        startIndex = _this$props10.startIndex,\n        endIndex = _this$props10.endIndex,\n        y = _this$props10.y,\n        height = _this$props10.height,\n        travellerWidth = _this$props10.travellerWidth,\n        stroke = _this$props10.stroke;\n      var _this$state4 = this.state,\n        startX = _this$state4.startX,\n        endX = _this$state4.endX;\n      var offset = 5;\n      var attrs = {\n        pointerEvents: 'none',\n        fill: stroke\n      };\n      return /*#__PURE__*/React.createElement(Layer, {\n        className: \"recharts-brush-texts\"\n      }, /*#__PURE__*/React.createElement(Text, _extends({\n        textAnchor: \"end\",\n        verticalAnchor: \"middle\",\n        x: Math.min(startX, endX) - offset,\n        y: y + height / 2\n      }, attrs), this.getTextOfTick(startIndex)), /*#__PURE__*/React.createElement(Text, _extends({\n        textAnchor: \"start\",\n        verticalAnchor: \"middle\",\n        x: Math.max(startX, endX) + travellerWidth + offset,\n        y: y + height / 2\n      }, attrs), this.getTextOfTick(endIndex)));\n    }\n  }, {\n    key: \"render\",\n    value: function render() {\n      var _this$props11 = this.props,\n        data = _this$props11.data,\n        className = _this$props11.className,\n        children = _this$props11.children,\n        x = _this$props11.x,\n        y = _this$props11.y,\n        width = _this$props11.width,\n        height = _this$props11.height,\n        alwaysShowText = _this$props11.alwaysShowText;\n      var _this$state5 = this.state,\n        startX = _this$state5.startX,\n        endX = _this$state5.endX,\n        isTextActive = _this$state5.isTextActive,\n        isSlideMoving = _this$state5.isSlideMoving,\n        isTravellerMoving = _this$state5.isTravellerMoving,\n        isTravellerFocused = _this$state5.isTravellerFocused;\n      if (!data || !data.length || !isNumber(x) || !isNumber(y) || !isNumber(width) || !isNumber(height) || width <= 0 || height <= 0) {\n        return null;\n      }\n      var layerClass = clsx('recharts-brush', className);\n      var isPanoramic = React.Children.count(children) === 1;\n      var style = generatePrefixStyle('userSelect', 'none');\n      return /*#__PURE__*/React.createElement(Layer, {\n        className: layerClass,\n        onMouseLeave: this.handleLeaveWrapper,\n        onTouchMove: this.handleTouchMove,\n        style: style\n      }, this.renderBackground(), isPanoramic && this.renderPanorama(), this.renderSlide(startX, endX), this.renderTravellerLayer(startX, 'startX'), this.renderTravellerLayer(endX, 'endX'), (isTextActive || isSlideMoving || isTravellerMoving || isTravellerFocused || alwaysShowText) && this.renderText());\n    }\n  }], [{\n    key: \"renderDefaultTraveller\",\n    value: function renderDefaultTraveller(props) {\n      var x = props.x,\n        y = props.y,\n        width = props.width,\n        height = props.height,\n        stroke = props.stroke;\n      var lineY = Math.floor(y + height / 2) - 1;\n      return /*#__PURE__*/React.createElement(React.Fragment, null, /*#__PURE__*/React.createElement(\"rect\", {\n        x: x,\n        y: y,\n        width: width,\n        height: height,\n        fill: stroke,\n        stroke: \"none\"\n      }), /*#__PURE__*/React.createElement(\"line\", {\n        x1: x + 1,\n        y1: lineY,\n        x2: x + width - 1,\n        y2: lineY,\n        fill: \"none\",\n        stroke: \"#fff\"\n      }), /*#__PURE__*/React.createElement(\"line\", {\n        x1: x + 1,\n        y1: lineY + 2,\n        x2: x + width - 1,\n        y2: lineY + 2,\n        fill: \"none\",\n        stroke: \"#fff\"\n      }));\n    }\n  }, {\n    key: \"renderTraveller\",\n    value: function renderTraveller(option, props) {\n      var rectangle;\n      if (/*#__PURE__*/React.isValidElement(option)) {\n        rectangle = /*#__PURE__*/React.cloneElement(option, props);\n      } else if (isFunction(option)) {\n        rectangle = option(props);\n      } else {\n        rectangle = Brush.renderDefaultTraveller(props);\n      }\n      return rectangle;\n    }\n  }, {\n    key: \"getDerivedStateFromProps\",\n    value: function getDerivedStateFromProps(nextProps, prevState) {\n      var data = nextProps.data,\n        width = nextProps.width,\n        x = nextProps.x,\n        travellerWidth = nextProps.travellerWidth,\n        updateId = nextProps.updateId,\n        startIndex = nextProps.startIndex,\n        endIndex = nextProps.endIndex;\n      if (data !== prevState.prevData || updateId !== prevState.prevUpdateId) {\n        return _objectSpread({\n          prevData: data,\n          prevTravellerWidth: travellerWidth,\n          prevUpdateId: updateId,\n          prevX: x,\n          prevWidth: width\n        }, data && data.length ? createScale({\n          data: data,\n          width: width,\n          x: x,\n          travellerWidth: travellerWidth,\n          startIndex: startIndex,\n          endIndex: endIndex\n        }) : {\n          scale: null,\n          scaleValues: null\n        });\n      }\n      if (prevState.scale && (width !== prevState.prevWidth || x !== prevState.prevX || travellerWidth !== prevState.prevTravellerWidth)) {\n        prevState.scale.range([x, x + width - travellerWidth]);\n        var scaleValues = prevState.scale.domain().map(function (entry) {\n          return prevState.scale(entry);\n        });\n        return {\n          prevData: data,\n          prevTravellerWidth: travellerWidth,\n          prevUpdateId: updateId,\n          prevX: x,\n          prevWidth: width,\n          startX: prevState.scale(nextProps.startIndex),\n          endX: prevState.scale(nextProps.endIndex),\n          scaleValues: scaleValues\n        };\n      }\n      return null;\n    }\n  }, {\n    key: \"getIndexInRange\",\n    value: function getIndexInRange(valueRange, x) {\n      var len = valueRange.length;\n      var start = 0;\n      var end = len - 1;\n      while (end - start > 1) {\n        var middle = Math.floor((start + end) / 2);\n        if (valueRange[middle] > x) {\n          end = middle;\n        } else {\n          start = middle;\n        }\n      }\n      return x >= valueRange[end] ? end : start;\n    }\n  }]);\n}(PureComponent);\n_defineProperty(Brush, \"displayName\", 'Brush');\n_defineProperty(Brush, \"defaultProps\", {\n  height: 40,\n  travellerWidth: 5,\n  gap: 1,\n  fill: '#fff',\n  stroke: '#666',\n  padding: {\n    top: 1,\n    right: 1,\n    bottom: 1,\n    left: 1\n  },\n  leaveTimeOut: 1000,\n  alwaysShowText: false\n});", "map": {"version": 3, "names": ["_typeof", "o", "Symbol", "iterator", "constructor", "prototype", "_extends", "Object", "assign", "bind", "target", "i", "arguments", "length", "source", "key", "hasOwnProperty", "call", "apply", "ownKeys", "e", "r", "t", "keys", "getOwnPropertySymbols", "filter", "getOwnPropertyDescriptor", "enumerable", "push", "_objectSpread", "for<PERSON>ach", "_defineProperty", "getOwnPropertyDescriptors", "defineProperties", "defineProperty", "_classCallCheck", "instance", "<PERSON><PERSON><PERSON><PERSON>", "TypeError", "_defineProperties", "props", "descriptor", "configurable", "writable", "_to<PERSON><PERSON><PERSON><PERSON><PERSON>", "_createClass", "protoProps", "staticProps", "_callSuper", "_getPrototypeOf", "_possibleConstructorReturn", "_isNativeReflectConstruct", "Reflect", "construct", "self", "_assertThisInitialized", "ReferenceError", "Boolean", "valueOf", "setPrototypeOf", "getPrototypeOf", "__proto__", "_inherits", "subClass", "superClass", "create", "value", "_setPrototypeOf", "p", "obj", "_toPrimitive", "toPrimitive", "String", "Number", "React", "PureComponent", "Children", "clsx", "scalePoint", "isFunction", "range", "Layer", "Text", "getValueByDataKey", "isNumber", "generatePrefixStyle", "filterProps", "createScale", "_ref", "data", "startIndex", "endIndex", "x", "width", "traveller<PERSON><PERSON><PERSON>", "len", "scale", "domain", "scaleValues", "map", "entry", "isTextActive", "isSlideMoving", "isTravellerMoving", "isTravellerFocused", "startX", "endX", "is<PERSON><PERSON>ch", "changedTouches", "Brush", "_PureComponent", "_this", "leaveTimer", "clearTimeout", "state", "handleTravellerMove", "handleSlideDrag", "handleDrag", "setState", "_this$props", "onDragEnd", "detachDragEndListener", "window", "setTimeout", "handleDragEnd", "leaveTimeOut", "event", "slideMoveStartX", "pageX", "attachDragEndListener", "travellerDragStartHandlers", "handleTravellerDragStart", "componentWillUnmount", "getIndex", "_ref2", "_this$props2", "gap", "lastIndex", "min", "Math", "max", "minIndex", "getIndexInRange", "maxIndex", "getTextOfTick", "index", "_this$props3", "tick<PERSON><PERSON><PERSON><PERSON>", "dataKey", "text", "addEventListener", "removeEventListener", "_this$state", "_this$props4", "onChange", "delta", "newIndex", "id", "movingTravellerId", "brushMoveStartX", "_this$state2", "prevValue", "_this$props5", "params", "isFullGap", "handleTravellerMoveKeyboard", "direction", "_this2", "_this$state3", "currentScaleValue", "currentIndex", "indexOf", "newScaleValue", "renderBackground", "_this$props6", "y", "height", "fill", "stroke", "createElement", "renderPanorama", "_this$props7", "children", "padding", "chartElement", "only", "cloneElement", "margin", "compact", "renderTravellerLayer", "travellerX", "_data$startIndex", "_data$endIndex", "_this3", "_this$props8", "traveller", "aria<PERSON><PERSON><PERSON>", "travellerProps", "ariaLabelBrush", "concat", "name", "tabIndex", "role", "className", "onMouseEnter", "handleEnterSlideOrTraveller", "onMouseLeave", "handleLeaveSlideOrTraveller", "onMouseDown", "onTouchStart", "onKeyDown", "includes", "preventDefault", "stopPropagation", "onFocus", "onBlur", "style", "cursor", "renderTraveller", "renderSlide", "_this$props9", "abs", "handleSlideDragStart", "fillOpacity", "renderText", "_this$props10", "_this$state4", "offset", "attrs", "pointerEvents", "textAnchor", "verticalAnchor", "render", "_this$props11", "alwaysShowText", "_this$state5", "layerClass", "isPanoramic", "count", "handleLeaveWrapper", "onTouchMove", "handleTouchMove", "renderDefaultTraveller", "lineY", "floor", "Fragment", "x1", "y1", "x2", "y2", "option", "rectangle", "isValidElement", "getDerivedStateFromProps", "nextProps", "prevState", "updateId", "prevData", "prevUpdateId", "prevTravellerWidth", "prevX", "prevWidth", "valueRange", "start", "end", "middle", "top", "right", "bottom", "left"], "sources": ["D:/ecommerce/node_modules/recharts/es6/cartesian/Brush.js"], "sourcesContent": ["function _typeof(o) { \"@babel/helpers - typeof\"; return _typeof = \"function\" == typeof Symbol && \"symbol\" == typeof Symbol.iterator ? function (o) { return typeof o; } : function (o) { return o && \"function\" == typeof Symbol && o.constructor === Symbol && o !== Symbol.prototype ? \"symbol\" : typeof o; }, _typeof(o); }\nfunction _extends() { _extends = Object.assign ? Object.assign.bind() : function (target) { for (var i = 1; i < arguments.length; i++) { var source = arguments[i]; for (var key in source) { if (Object.prototype.hasOwnProperty.call(source, key)) { target[key] = source[key]; } } } return target; }; return _extends.apply(this, arguments); }\nfunction ownKeys(e, r) { var t = Object.keys(e); if (Object.getOwnPropertySymbols) { var o = Object.getOwnPropertySymbols(e); r && (o = o.filter(function (r) { return Object.getOwnPropertyDescriptor(e, r).enumerable; })), t.push.apply(t, o); } return t; }\nfunction _objectSpread(e) { for (var r = 1; r < arguments.length; r++) { var t = null != arguments[r] ? arguments[r] : {}; r % 2 ? ownKeys(Object(t), !0).forEach(function (r) { _defineProperty(e, r, t[r]); }) : Object.getOwnPropertyDescriptors ? Object.defineProperties(e, Object.getOwnPropertyDescriptors(t)) : ownKeys(Object(t)).forEach(function (r) { Object.defineProperty(e, r, Object.getOwnPropertyDescriptor(t, r)); }); } return e; }\nfunction _classCallCheck(instance, Constructor) { if (!(instance instanceof Constructor)) { throw new TypeError(\"Cannot call a class as a function\"); } }\nfunction _defineProperties(target, props) { for (var i = 0; i < props.length; i++) { var descriptor = props[i]; descriptor.enumerable = descriptor.enumerable || false; descriptor.configurable = true; if (\"value\" in descriptor) descriptor.writable = true; Object.defineProperty(target, _toPropertyKey(descriptor.key), descriptor); } }\nfunction _createClass(Constructor, protoProps, staticProps) { if (protoProps) _defineProperties(Constructor.prototype, protoProps); if (staticProps) _defineProperties(Constructor, staticProps); Object.defineProperty(Constructor, \"prototype\", { writable: false }); return Constructor; }\nfunction _callSuper(t, o, e) { return o = _getPrototypeOf(o), _possibleConstructorReturn(t, _isNativeReflectConstruct() ? Reflect.construct(o, e || [], _getPrototypeOf(t).constructor) : o.apply(t, e)); }\nfunction _possibleConstructorReturn(self, call) { if (call && (_typeof(call) === \"object\" || typeof call === \"function\")) { return call; } else if (call !== void 0) { throw new TypeError(\"Derived constructors may only return object or undefined\"); } return _assertThisInitialized(self); }\nfunction _assertThisInitialized(self) { if (self === void 0) { throw new ReferenceError(\"this hasn't been initialised - super() hasn't been called\"); } return self; }\nfunction _isNativeReflectConstruct() { try { var t = !Boolean.prototype.valueOf.call(Reflect.construct(Boolean, [], function () {})); } catch (t) {} return (_isNativeReflectConstruct = function _isNativeReflectConstruct() { return !!t; })(); }\nfunction _getPrototypeOf(o) { _getPrototypeOf = Object.setPrototypeOf ? Object.getPrototypeOf.bind() : function _getPrototypeOf(o) { return o.__proto__ || Object.getPrototypeOf(o); }; return _getPrototypeOf(o); }\nfunction _inherits(subClass, superClass) { if (typeof superClass !== \"function\" && superClass !== null) { throw new TypeError(\"Super expression must either be null or a function\"); } subClass.prototype = Object.create(superClass && superClass.prototype, { constructor: { value: subClass, writable: true, configurable: true } }); Object.defineProperty(subClass, \"prototype\", { writable: false }); if (superClass) _setPrototypeOf(subClass, superClass); }\nfunction _setPrototypeOf(o, p) { _setPrototypeOf = Object.setPrototypeOf ? Object.setPrototypeOf.bind() : function _setPrototypeOf(o, p) { o.__proto__ = p; return o; }; return _setPrototypeOf(o, p); }\nfunction _defineProperty(obj, key, value) { key = _toPropertyKey(key); if (key in obj) { Object.defineProperty(obj, key, { value: value, enumerable: true, configurable: true, writable: true }); } else { obj[key] = value; } return obj; }\nfunction _toPropertyKey(t) { var i = _toPrimitive(t, \"string\"); return \"symbol\" == _typeof(i) ? i : i + \"\"; }\nfunction _toPrimitive(t, r) { if (\"object\" != _typeof(t) || !t) return t; var e = t[Symbol.toPrimitive]; if (void 0 !== e) { var i = e.call(t, r || \"default\"); if (\"object\" != _typeof(i)) return i; throw new TypeError(\"@@toPrimitive must return a primitive value.\"); } return (\"string\" === r ? String : Number)(t); }\n/**\n * @fileOverview Brush\n */\nimport React, { PureComponent, Children } from 'react';\nimport clsx from 'clsx';\nimport { scalePoint } from 'victory-vendor/d3-scale';\nimport isFunction from 'lodash/isFunction';\nimport range from 'lodash/range';\nimport { Layer } from '../container/Layer';\nimport { Text } from '../component/Text';\nimport { getValueByDataKey } from '../util/ChartUtils';\nimport { isNumber } from '../util/DataUtils';\nimport { generatePrefixStyle } from '../util/CssPrefixUtils';\nimport { filterProps } from '../util/ReactUtils';\nvar createScale = function createScale(_ref) {\n  var data = _ref.data,\n    startIndex = _ref.startIndex,\n    endIndex = _ref.endIndex,\n    x = _ref.x,\n    width = _ref.width,\n    travellerWidth = _ref.travellerWidth;\n  if (!data || !data.length) {\n    return {};\n  }\n  var len = data.length;\n  var scale = scalePoint().domain(range(0, len)).range([x, x + width - travellerWidth]);\n  var scaleValues = scale.domain().map(function (entry) {\n    return scale(entry);\n  });\n  return {\n    isTextActive: false,\n    isSlideMoving: false,\n    isTravellerMoving: false,\n    isTravellerFocused: false,\n    startX: scale(startIndex),\n    endX: scale(endIndex),\n    scale: scale,\n    scaleValues: scaleValues\n  };\n};\nvar isTouch = function isTouch(e) {\n  return e.changedTouches && !!e.changedTouches.length;\n};\nexport var Brush = /*#__PURE__*/function (_PureComponent) {\n  function Brush(props) {\n    var _this;\n    _classCallCheck(this, Brush);\n    _this = _callSuper(this, Brush, [props]);\n    _defineProperty(_this, \"handleDrag\", function (e) {\n      if (_this.leaveTimer) {\n        clearTimeout(_this.leaveTimer);\n        _this.leaveTimer = null;\n      }\n      if (_this.state.isTravellerMoving) {\n        _this.handleTravellerMove(e);\n      } else if (_this.state.isSlideMoving) {\n        _this.handleSlideDrag(e);\n      }\n    });\n    _defineProperty(_this, \"handleTouchMove\", function (e) {\n      if (e.changedTouches != null && e.changedTouches.length > 0) {\n        _this.handleDrag(e.changedTouches[0]);\n      }\n    });\n    _defineProperty(_this, \"handleDragEnd\", function () {\n      _this.setState({\n        isTravellerMoving: false,\n        isSlideMoving: false\n      }, function () {\n        var _this$props = _this.props,\n          endIndex = _this$props.endIndex,\n          onDragEnd = _this$props.onDragEnd,\n          startIndex = _this$props.startIndex;\n        onDragEnd === null || onDragEnd === void 0 || onDragEnd({\n          endIndex: endIndex,\n          startIndex: startIndex\n        });\n      });\n      _this.detachDragEndListener();\n    });\n    _defineProperty(_this, \"handleLeaveWrapper\", function () {\n      if (_this.state.isTravellerMoving || _this.state.isSlideMoving) {\n        _this.leaveTimer = window.setTimeout(_this.handleDragEnd, _this.props.leaveTimeOut);\n      }\n    });\n    _defineProperty(_this, \"handleEnterSlideOrTraveller\", function () {\n      _this.setState({\n        isTextActive: true\n      });\n    });\n    _defineProperty(_this, \"handleLeaveSlideOrTraveller\", function () {\n      _this.setState({\n        isTextActive: false\n      });\n    });\n    _defineProperty(_this, \"handleSlideDragStart\", function (e) {\n      var event = isTouch(e) ? e.changedTouches[0] : e;\n      _this.setState({\n        isTravellerMoving: false,\n        isSlideMoving: true,\n        slideMoveStartX: event.pageX\n      });\n      _this.attachDragEndListener();\n    });\n    _this.travellerDragStartHandlers = {\n      startX: _this.handleTravellerDragStart.bind(_this, 'startX'),\n      endX: _this.handleTravellerDragStart.bind(_this, 'endX')\n    };\n    _this.state = {};\n    return _this;\n  }\n  _inherits(Brush, _PureComponent);\n  return _createClass(Brush, [{\n    key: \"componentWillUnmount\",\n    value: function componentWillUnmount() {\n      if (this.leaveTimer) {\n        clearTimeout(this.leaveTimer);\n        this.leaveTimer = null;\n      }\n      this.detachDragEndListener();\n    }\n  }, {\n    key: \"getIndex\",\n    value: function getIndex(_ref2) {\n      var startX = _ref2.startX,\n        endX = _ref2.endX;\n      var scaleValues = this.state.scaleValues;\n      var _this$props2 = this.props,\n        gap = _this$props2.gap,\n        data = _this$props2.data;\n      var lastIndex = data.length - 1;\n      var min = Math.min(startX, endX);\n      var max = Math.max(startX, endX);\n      var minIndex = Brush.getIndexInRange(scaleValues, min);\n      var maxIndex = Brush.getIndexInRange(scaleValues, max);\n      return {\n        startIndex: minIndex - minIndex % gap,\n        endIndex: maxIndex === lastIndex ? lastIndex : maxIndex - maxIndex % gap\n      };\n    }\n  }, {\n    key: \"getTextOfTick\",\n    value: function getTextOfTick(index) {\n      var _this$props3 = this.props,\n        data = _this$props3.data,\n        tickFormatter = _this$props3.tickFormatter,\n        dataKey = _this$props3.dataKey;\n      var text = getValueByDataKey(data[index], dataKey, index);\n      return isFunction(tickFormatter) ? tickFormatter(text, index) : text;\n    }\n  }, {\n    key: \"attachDragEndListener\",\n    value: function attachDragEndListener() {\n      window.addEventListener('mouseup', this.handleDragEnd, true);\n      window.addEventListener('touchend', this.handleDragEnd, true);\n      window.addEventListener('mousemove', this.handleDrag, true);\n    }\n  }, {\n    key: \"detachDragEndListener\",\n    value: function detachDragEndListener() {\n      window.removeEventListener('mouseup', this.handleDragEnd, true);\n      window.removeEventListener('touchend', this.handleDragEnd, true);\n      window.removeEventListener('mousemove', this.handleDrag, true);\n    }\n  }, {\n    key: \"handleSlideDrag\",\n    value: function handleSlideDrag(e) {\n      var _this$state = this.state,\n        slideMoveStartX = _this$state.slideMoveStartX,\n        startX = _this$state.startX,\n        endX = _this$state.endX;\n      var _this$props4 = this.props,\n        x = _this$props4.x,\n        width = _this$props4.width,\n        travellerWidth = _this$props4.travellerWidth,\n        startIndex = _this$props4.startIndex,\n        endIndex = _this$props4.endIndex,\n        onChange = _this$props4.onChange;\n      var delta = e.pageX - slideMoveStartX;\n      if (delta > 0) {\n        delta = Math.min(delta, x + width - travellerWidth - endX, x + width - travellerWidth - startX);\n      } else if (delta < 0) {\n        delta = Math.max(delta, x - startX, x - endX);\n      }\n      var newIndex = this.getIndex({\n        startX: startX + delta,\n        endX: endX + delta\n      });\n      if ((newIndex.startIndex !== startIndex || newIndex.endIndex !== endIndex) && onChange) {\n        onChange(newIndex);\n      }\n      this.setState({\n        startX: startX + delta,\n        endX: endX + delta,\n        slideMoveStartX: e.pageX\n      });\n    }\n  }, {\n    key: \"handleTravellerDragStart\",\n    value: function handleTravellerDragStart(id, e) {\n      var event = isTouch(e) ? e.changedTouches[0] : e;\n      this.setState({\n        isSlideMoving: false,\n        isTravellerMoving: true,\n        movingTravellerId: id,\n        brushMoveStartX: event.pageX\n      });\n      this.attachDragEndListener();\n    }\n  }, {\n    key: \"handleTravellerMove\",\n    value: function handleTravellerMove(e) {\n      var _this$state2 = this.state,\n        brushMoveStartX = _this$state2.brushMoveStartX,\n        movingTravellerId = _this$state2.movingTravellerId,\n        endX = _this$state2.endX,\n        startX = _this$state2.startX;\n      var prevValue = this.state[movingTravellerId];\n      var _this$props5 = this.props,\n        x = _this$props5.x,\n        width = _this$props5.width,\n        travellerWidth = _this$props5.travellerWidth,\n        onChange = _this$props5.onChange,\n        gap = _this$props5.gap,\n        data = _this$props5.data;\n      var params = {\n        startX: this.state.startX,\n        endX: this.state.endX\n      };\n      var delta = e.pageX - brushMoveStartX;\n      if (delta > 0) {\n        delta = Math.min(delta, x + width - travellerWidth - prevValue);\n      } else if (delta < 0) {\n        delta = Math.max(delta, x - prevValue);\n      }\n      params[movingTravellerId] = prevValue + delta;\n      var newIndex = this.getIndex(params);\n      var startIndex = newIndex.startIndex,\n        endIndex = newIndex.endIndex;\n      var isFullGap = function isFullGap() {\n        var lastIndex = data.length - 1;\n        if (movingTravellerId === 'startX' && (endX > startX ? startIndex % gap === 0 : endIndex % gap === 0) || endX < startX && endIndex === lastIndex || movingTravellerId === 'endX' && (endX > startX ? endIndex % gap === 0 : startIndex % gap === 0) || endX > startX && endIndex === lastIndex) {\n          return true;\n        }\n        return false;\n      };\n      this.setState(_defineProperty(_defineProperty({}, movingTravellerId, prevValue + delta), \"brushMoveStartX\", e.pageX), function () {\n        if (onChange) {\n          if (isFullGap()) {\n            onChange(newIndex);\n          }\n        }\n      });\n    }\n  }, {\n    key: \"handleTravellerMoveKeyboard\",\n    value: function handleTravellerMoveKeyboard(direction, id) {\n      var _this2 = this;\n      // scaleValues are a list of coordinates. For example: [65, 250, 435, 620, 805, 990].\n      var _this$state3 = this.state,\n        scaleValues = _this$state3.scaleValues,\n        startX = _this$state3.startX,\n        endX = _this$state3.endX;\n      // currentScaleValue refers to which coordinate the current traveller should be placed at.\n      var currentScaleValue = this.state[id];\n      var currentIndex = scaleValues.indexOf(currentScaleValue);\n      if (currentIndex === -1) {\n        return;\n      }\n      var newIndex = currentIndex + direction;\n      if (newIndex === -1 || newIndex >= scaleValues.length) {\n        return;\n      }\n      var newScaleValue = scaleValues[newIndex];\n\n      // Prevent travellers from being on top of each other or overlapping\n      if (id === 'startX' && newScaleValue >= endX || id === 'endX' && newScaleValue <= startX) {\n        return;\n      }\n      this.setState(_defineProperty({}, id, newScaleValue), function () {\n        _this2.props.onChange(_this2.getIndex({\n          startX: _this2.state.startX,\n          endX: _this2.state.endX\n        }));\n      });\n    }\n  }, {\n    key: \"renderBackground\",\n    value: function renderBackground() {\n      var _this$props6 = this.props,\n        x = _this$props6.x,\n        y = _this$props6.y,\n        width = _this$props6.width,\n        height = _this$props6.height,\n        fill = _this$props6.fill,\n        stroke = _this$props6.stroke;\n      return /*#__PURE__*/React.createElement(\"rect\", {\n        stroke: stroke,\n        fill: fill,\n        x: x,\n        y: y,\n        width: width,\n        height: height\n      });\n    }\n  }, {\n    key: \"renderPanorama\",\n    value: function renderPanorama() {\n      var _this$props7 = this.props,\n        x = _this$props7.x,\n        y = _this$props7.y,\n        width = _this$props7.width,\n        height = _this$props7.height,\n        data = _this$props7.data,\n        children = _this$props7.children,\n        padding = _this$props7.padding;\n      var chartElement = Children.only(children);\n      if (!chartElement) {\n        return null;\n      }\n      return /*#__PURE__*/React.cloneElement(chartElement, {\n        x: x,\n        y: y,\n        width: width,\n        height: height,\n        margin: padding,\n        compact: true,\n        data: data\n      });\n    }\n  }, {\n    key: \"renderTravellerLayer\",\n    value: function renderTravellerLayer(travellerX, id) {\n      var _data$startIndex,\n        _data$endIndex,\n        _this3 = this;\n      var _this$props8 = this.props,\n        y = _this$props8.y,\n        travellerWidth = _this$props8.travellerWidth,\n        height = _this$props8.height,\n        traveller = _this$props8.traveller,\n        ariaLabel = _this$props8.ariaLabel,\n        data = _this$props8.data,\n        startIndex = _this$props8.startIndex,\n        endIndex = _this$props8.endIndex;\n      var x = Math.max(travellerX, this.props.x);\n      var travellerProps = _objectSpread(_objectSpread({}, filterProps(this.props, false)), {}, {\n        x: x,\n        y: y,\n        width: travellerWidth,\n        height: height\n      });\n      var ariaLabelBrush = ariaLabel || \"Min value: \".concat((_data$startIndex = data[startIndex]) === null || _data$startIndex === void 0 ? void 0 : _data$startIndex.name, \", Max value: \").concat((_data$endIndex = data[endIndex]) === null || _data$endIndex === void 0 ? void 0 : _data$endIndex.name);\n      return /*#__PURE__*/React.createElement(Layer, {\n        tabIndex: 0,\n        role: \"slider\",\n        \"aria-label\": ariaLabelBrush,\n        \"aria-valuenow\": travellerX,\n        className: \"recharts-brush-traveller\",\n        onMouseEnter: this.handleEnterSlideOrTraveller,\n        onMouseLeave: this.handleLeaveSlideOrTraveller,\n        onMouseDown: this.travellerDragStartHandlers[id],\n        onTouchStart: this.travellerDragStartHandlers[id],\n        onKeyDown: function onKeyDown(e) {\n          if (!['ArrowLeft', 'ArrowRight'].includes(e.key)) {\n            return;\n          }\n          e.preventDefault();\n          e.stopPropagation();\n          _this3.handleTravellerMoveKeyboard(e.key === 'ArrowRight' ? 1 : -1, id);\n        },\n        onFocus: function onFocus() {\n          _this3.setState({\n            isTravellerFocused: true\n          });\n        },\n        onBlur: function onBlur() {\n          _this3.setState({\n            isTravellerFocused: false\n          });\n        },\n        style: {\n          cursor: 'col-resize'\n        }\n      }, Brush.renderTraveller(traveller, travellerProps));\n    }\n  }, {\n    key: \"renderSlide\",\n    value: function renderSlide(startX, endX) {\n      var _this$props9 = this.props,\n        y = _this$props9.y,\n        height = _this$props9.height,\n        stroke = _this$props9.stroke,\n        travellerWidth = _this$props9.travellerWidth;\n      var x = Math.min(startX, endX) + travellerWidth;\n      var width = Math.max(Math.abs(endX - startX) - travellerWidth, 0);\n      return /*#__PURE__*/React.createElement(\"rect\", {\n        className: \"recharts-brush-slide\",\n        onMouseEnter: this.handleEnterSlideOrTraveller,\n        onMouseLeave: this.handleLeaveSlideOrTraveller,\n        onMouseDown: this.handleSlideDragStart,\n        onTouchStart: this.handleSlideDragStart,\n        style: {\n          cursor: 'move'\n        },\n        stroke: \"none\",\n        fill: stroke,\n        fillOpacity: 0.2,\n        x: x,\n        y: y,\n        width: width,\n        height: height\n      });\n    }\n  }, {\n    key: \"renderText\",\n    value: function renderText() {\n      var _this$props10 = this.props,\n        startIndex = _this$props10.startIndex,\n        endIndex = _this$props10.endIndex,\n        y = _this$props10.y,\n        height = _this$props10.height,\n        travellerWidth = _this$props10.travellerWidth,\n        stroke = _this$props10.stroke;\n      var _this$state4 = this.state,\n        startX = _this$state4.startX,\n        endX = _this$state4.endX;\n      var offset = 5;\n      var attrs = {\n        pointerEvents: 'none',\n        fill: stroke\n      };\n      return /*#__PURE__*/React.createElement(Layer, {\n        className: \"recharts-brush-texts\"\n      }, /*#__PURE__*/React.createElement(Text, _extends({\n        textAnchor: \"end\",\n        verticalAnchor: \"middle\",\n        x: Math.min(startX, endX) - offset,\n        y: y + height / 2\n      }, attrs), this.getTextOfTick(startIndex)), /*#__PURE__*/React.createElement(Text, _extends({\n        textAnchor: \"start\",\n        verticalAnchor: \"middle\",\n        x: Math.max(startX, endX) + travellerWidth + offset,\n        y: y + height / 2\n      }, attrs), this.getTextOfTick(endIndex)));\n    }\n  }, {\n    key: \"render\",\n    value: function render() {\n      var _this$props11 = this.props,\n        data = _this$props11.data,\n        className = _this$props11.className,\n        children = _this$props11.children,\n        x = _this$props11.x,\n        y = _this$props11.y,\n        width = _this$props11.width,\n        height = _this$props11.height,\n        alwaysShowText = _this$props11.alwaysShowText;\n      var _this$state5 = this.state,\n        startX = _this$state5.startX,\n        endX = _this$state5.endX,\n        isTextActive = _this$state5.isTextActive,\n        isSlideMoving = _this$state5.isSlideMoving,\n        isTravellerMoving = _this$state5.isTravellerMoving,\n        isTravellerFocused = _this$state5.isTravellerFocused;\n      if (!data || !data.length || !isNumber(x) || !isNumber(y) || !isNumber(width) || !isNumber(height) || width <= 0 || height <= 0) {\n        return null;\n      }\n      var layerClass = clsx('recharts-brush', className);\n      var isPanoramic = React.Children.count(children) === 1;\n      var style = generatePrefixStyle('userSelect', 'none');\n      return /*#__PURE__*/React.createElement(Layer, {\n        className: layerClass,\n        onMouseLeave: this.handleLeaveWrapper,\n        onTouchMove: this.handleTouchMove,\n        style: style\n      }, this.renderBackground(), isPanoramic && this.renderPanorama(), this.renderSlide(startX, endX), this.renderTravellerLayer(startX, 'startX'), this.renderTravellerLayer(endX, 'endX'), (isTextActive || isSlideMoving || isTravellerMoving || isTravellerFocused || alwaysShowText) && this.renderText());\n    }\n  }], [{\n    key: \"renderDefaultTraveller\",\n    value: function renderDefaultTraveller(props) {\n      var x = props.x,\n        y = props.y,\n        width = props.width,\n        height = props.height,\n        stroke = props.stroke;\n      var lineY = Math.floor(y + height / 2) - 1;\n      return /*#__PURE__*/React.createElement(React.Fragment, null, /*#__PURE__*/React.createElement(\"rect\", {\n        x: x,\n        y: y,\n        width: width,\n        height: height,\n        fill: stroke,\n        stroke: \"none\"\n      }), /*#__PURE__*/React.createElement(\"line\", {\n        x1: x + 1,\n        y1: lineY,\n        x2: x + width - 1,\n        y2: lineY,\n        fill: \"none\",\n        stroke: \"#fff\"\n      }), /*#__PURE__*/React.createElement(\"line\", {\n        x1: x + 1,\n        y1: lineY + 2,\n        x2: x + width - 1,\n        y2: lineY + 2,\n        fill: \"none\",\n        stroke: \"#fff\"\n      }));\n    }\n  }, {\n    key: \"renderTraveller\",\n    value: function renderTraveller(option, props) {\n      var rectangle;\n      if ( /*#__PURE__*/React.isValidElement(option)) {\n        rectangle = /*#__PURE__*/React.cloneElement(option, props);\n      } else if (isFunction(option)) {\n        rectangle = option(props);\n      } else {\n        rectangle = Brush.renderDefaultTraveller(props);\n      }\n      return rectangle;\n    }\n  }, {\n    key: \"getDerivedStateFromProps\",\n    value: function getDerivedStateFromProps(nextProps, prevState) {\n      var data = nextProps.data,\n        width = nextProps.width,\n        x = nextProps.x,\n        travellerWidth = nextProps.travellerWidth,\n        updateId = nextProps.updateId,\n        startIndex = nextProps.startIndex,\n        endIndex = nextProps.endIndex;\n      if (data !== prevState.prevData || updateId !== prevState.prevUpdateId) {\n        return _objectSpread({\n          prevData: data,\n          prevTravellerWidth: travellerWidth,\n          prevUpdateId: updateId,\n          prevX: x,\n          prevWidth: width\n        }, data && data.length ? createScale({\n          data: data,\n          width: width,\n          x: x,\n          travellerWidth: travellerWidth,\n          startIndex: startIndex,\n          endIndex: endIndex\n        }) : {\n          scale: null,\n          scaleValues: null\n        });\n      }\n      if (prevState.scale && (width !== prevState.prevWidth || x !== prevState.prevX || travellerWidth !== prevState.prevTravellerWidth)) {\n        prevState.scale.range([x, x + width - travellerWidth]);\n        var scaleValues = prevState.scale.domain().map(function (entry) {\n          return prevState.scale(entry);\n        });\n        return {\n          prevData: data,\n          prevTravellerWidth: travellerWidth,\n          prevUpdateId: updateId,\n          prevX: x,\n          prevWidth: width,\n          startX: prevState.scale(nextProps.startIndex),\n          endX: prevState.scale(nextProps.endIndex),\n          scaleValues: scaleValues\n        };\n      }\n      return null;\n    }\n  }, {\n    key: \"getIndexInRange\",\n    value: function getIndexInRange(valueRange, x) {\n      var len = valueRange.length;\n      var start = 0;\n      var end = len - 1;\n      while (end - start > 1) {\n        var middle = Math.floor((start + end) / 2);\n        if (valueRange[middle] > x) {\n          end = middle;\n        } else {\n          start = middle;\n        }\n      }\n      return x >= valueRange[end] ? end : start;\n    }\n  }]);\n}(PureComponent);\n_defineProperty(Brush, \"displayName\", 'Brush');\n_defineProperty(Brush, \"defaultProps\", {\n  height: 40,\n  travellerWidth: 5,\n  gap: 1,\n  fill: '#fff',\n  stroke: '#666',\n  padding: {\n    top: 1,\n    right: 1,\n    bottom: 1,\n    left: 1\n  },\n  leaveTimeOut: 1000,\n  alwaysShowText: false\n});"], "mappings": "AAAA,SAASA,OAAOA,CAACC,CAAC,EAAE;EAAE,yBAAyB;;EAAE,OAAOD,OAAO,GAAG,UAAU,IAAI,OAAOE,MAAM,IAAI,QAAQ,IAAI,OAAOA,MAAM,CAACC,QAAQ,GAAG,UAAUF,CAAC,EAAE;IAAE,OAAO,OAAOA,CAAC;EAAE,CAAC,GAAG,UAAUA,CAAC,EAAE;IAAE,OAAOA,CAAC,IAAI,UAAU,IAAI,OAAOC,MAAM,IAAID,CAAC,CAACG,WAAW,KAAKF,MAAM,IAAID,CAAC,KAAKC,MAAM,CAACG,SAAS,GAAG,QAAQ,GAAG,OAAOJ,CAAC;EAAE,CAAC,EAAED,OAAO,CAACC,CAAC,CAAC;AAAE;AAC7T,SAASK,QAAQA,CAAA,EAAG;EAAEA,QAAQ,GAAGC,MAAM,CAACC,MAAM,GAAGD,MAAM,CAACC,MAAM,CAACC,IAAI,CAAC,CAAC,GAAG,UAAUC,MAAM,EAAE;IAAE,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGC,SAAS,CAACC,MAAM,EAAEF,CAAC,EAAE,EAAE;MAAE,IAAIG,MAAM,GAAGF,SAAS,CAACD,CAAC,CAAC;MAAE,KAAK,IAAII,GAAG,IAAID,MAAM,EAAE;QAAE,IAAIP,MAAM,CAACF,SAAS,CAACW,cAAc,CAACC,IAAI,CAACH,MAAM,EAAEC,GAAG,CAAC,EAAE;UAAEL,MAAM,CAACK,GAAG,CAAC,GAAGD,MAAM,CAACC,GAAG,CAAC;QAAE;MAAE;IAAE;IAAE,OAAOL,MAAM;EAAE,CAAC;EAAE,OAAOJ,QAAQ,CAACY,KAAK,CAAC,IAAI,EAAEN,SAAS,CAAC;AAAE;AAClV,SAASO,OAAOA,CAACC,CAAC,EAAEC,CAAC,EAAE;EAAE,IAAIC,CAAC,GAAGf,MAAM,CAACgB,IAAI,CAACH,CAAC,CAAC;EAAE,IAAIb,MAAM,CAACiB,qBAAqB,EAAE;IAAE,IAAIvB,CAAC,GAAGM,MAAM,CAACiB,qBAAqB,CAACJ,CAAC,CAAC;IAAEC,CAAC,KAAKpB,CAAC,GAAGA,CAAC,CAACwB,MAAM,CAAC,UAAUJ,CAAC,EAAE;MAAE,OAAOd,MAAM,CAACmB,wBAAwB,CAACN,CAAC,EAAEC,CAAC,CAAC,CAACM,UAAU;IAAE,CAAC,CAAC,CAAC,EAAEL,CAAC,CAACM,IAAI,CAACV,KAAK,CAACI,CAAC,EAAErB,CAAC,CAAC;EAAE;EAAE,OAAOqB,CAAC;AAAE;AAC9P,SAASO,aAAaA,CAACT,CAAC,EAAE;EAAE,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGT,SAAS,CAACC,MAAM,EAAEQ,CAAC,EAAE,EAAE;IAAE,IAAIC,CAAC,GAAG,IAAI,IAAIV,SAAS,CAACS,CAAC,CAAC,GAAGT,SAAS,CAACS,CAAC,CAAC,GAAG,CAAC,CAAC;IAAEA,CAAC,GAAG,CAAC,GAAGF,OAAO,CAACZ,MAAM,CAACe,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAACQ,OAAO,CAAC,UAAUT,CAAC,EAAE;MAAEU,eAAe,CAACX,CAAC,EAAEC,CAAC,EAAEC,CAAC,CAACD,CAAC,CAAC,CAAC;IAAE,CAAC,CAAC,GAAGd,MAAM,CAACyB,yBAAyB,GAAGzB,MAAM,CAAC0B,gBAAgB,CAACb,CAAC,EAAEb,MAAM,CAACyB,yBAAyB,CAACV,CAAC,CAAC,CAAC,GAAGH,OAAO,CAACZ,MAAM,CAACe,CAAC,CAAC,CAAC,CAACQ,OAAO,CAAC,UAAUT,CAAC,EAAE;MAAEd,MAAM,CAAC2B,cAAc,CAACd,CAAC,EAAEC,CAAC,EAAEd,MAAM,CAACmB,wBAAwB,CAACJ,CAAC,EAAED,CAAC,CAAC,CAAC;IAAE,CAAC,CAAC;EAAE;EAAE,OAAOD,CAAC;AAAE;AACtb,SAASe,eAAeA,CAACC,QAAQ,EAAEC,WAAW,EAAE;EAAE,IAAI,EAAED,QAAQ,YAAYC,WAAW,CAAC,EAAE;IAAE,MAAM,IAAIC,SAAS,CAAC,mCAAmC,CAAC;EAAE;AAAE;AACxJ,SAASC,iBAAiBA,CAAC7B,MAAM,EAAE8B,KAAK,EAAE;EAAE,KAAK,IAAI7B,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG6B,KAAK,CAAC3B,MAAM,EAAEF,CAAC,EAAE,EAAE;IAAE,IAAI8B,UAAU,GAAGD,KAAK,CAAC7B,CAAC,CAAC;IAAE8B,UAAU,CAACd,UAAU,GAAGc,UAAU,CAACd,UAAU,IAAI,KAAK;IAAEc,UAAU,CAACC,YAAY,GAAG,IAAI;IAAE,IAAI,OAAO,IAAID,UAAU,EAAEA,UAAU,CAACE,QAAQ,GAAG,IAAI;IAAEpC,MAAM,CAAC2B,cAAc,CAACxB,MAAM,EAAEkC,cAAc,CAACH,UAAU,CAAC1B,GAAG,CAAC,EAAE0B,UAAU,CAAC;EAAE;AAAE;AAC5U,SAASI,YAAYA,CAACR,WAAW,EAAES,UAAU,EAAEC,WAAW,EAAE;EAAE,IAAID,UAAU,EAAEP,iBAAiB,CAACF,WAAW,CAAChC,SAAS,EAAEyC,UAAU,CAAC;EAAE,IAAIC,WAAW,EAAER,iBAAiB,CAACF,WAAW,EAAEU,WAAW,CAAC;EAAExC,MAAM,CAAC2B,cAAc,CAACG,WAAW,EAAE,WAAW,EAAE;IAAEM,QAAQ,EAAE;EAAM,CAAC,CAAC;EAAE,OAAON,WAAW;AAAE;AAC5R,SAASW,UAAUA,CAAC1B,CAAC,EAAErB,CAAC,EAAEmB,CAAC,EAAE;EAAE,OAAOnB,CAAC,GAAGgD,eAAe,CAAChD,CAAC,CAAC,EAAEiD,0BAA0B,CAAC5B,CAAC,EAAE6B,yBAAyB,CAAC,CAAC,GAAGC,OAAO,CAACC,SAAS,CAACpD,CAAC,EAAEmB,CAAC,IAAI,EAAE,EAAE6B,eAAe,CAAC3B,CAAC,CAAC,CAAClB,WAAW,CAAC,GAAGH,CAAC,CAACiB,KAAK,CAACI,CAAC,EAAEF,CAAC,CAAC,CAAC;AAAE;AAC1M,SAAS8B,0BAA0BA,CAACI,IAAI,EAAErC,IAAI,EAAE;EAAE,IAAIA,IAAI,KAAKjB,OAAO,CAACiB,IAAI,CAAC,KAAK,QAAQ,IAAI,OAAOA,IAAI,KAAK,UAAU,CAAC,EAAE;IAAE,OAAOA,IAAI;EAAE,CAAC,MAAM,IAAIA,IAAI,KAAK,KAAK,CAAC,EAAE;IAAE,MAAM,IAAIqB,SAAS,CAAC,0DAA0D,CAAC;EAAE;EAAE,OAAOiB,sBAAsB,CAACD,IAAI,CAAC;AAAE;AAC/R,SAASC,sBAAsBA,CAACD,IAAI,EAAE;EAAE,IAAIA,IAAI,KAAK,KAAK,CAAC,EAAE;IAAE,MAAM,IAAIE,cAAc,CAAC,2DAA2D,CAAC;EAAE;EAAE,OAAOF,IAAI;AAAE;AACrK,SAASH,yBAAyBA,CAAA,EAAG;EAAE,IAAI;IAAE,IAAI7B,CAAC,GAAG,CAACmC,OAAO,CAACpD,SAAS,CAACqD,OAAO,CAACzC,IAAI,CAACmC,OAAO,CAACC,SAAS,CAACI,OAAO,EAAE,EAAE,EAAE,YAAY,CAAC,CAAC,CAAC,CAAC;EAAE,CAAC,CAAC,OAAOnC,CAAC,EAAE,CAAC;EAAE,OAAO,CAAC6B,yBAAyB,GAAG,SAASA,yBAAyBA,CAAA,EAAG;IAAE,OAAO,CAAC,CAAC7B,CAAC;EAAE,CAAC,EAAE,CAAC;AAAE;AAClP,SAAS2B,eAAeA,CAAChD,CAAC,EAAE;EAAEgD,eAAe,GAAG1C,MAAM,CAACoD,cAAc,GAAGpD,MAAM,CAACqD,cAAc,CAACnD,IAAI,CAAC,CAAC,GAAG,SAASwC,eAAeA,CAAChD,CAAC,EAAE;IAAE,OAAOA,CAAC,CAAC4D,SAAS,IAAItD,MAAM,CAACqD,cAAc,CAAC3D,CAAC,CAAC;EAAE,CAAC;EAAE,OAAOgD,eAAe,CAAChD,CAAC,CAAC;AAAE;AACnN,SAAS6D,SAASA,CAACC,QAAQ,EAAEC,UAAU,EAAE;EAAE,IAAI,OAAOA,UAAU,KAAK,UAAU,IAAIA,UAAU,KAAK,IAAI,EAAE;IAAE,MAAM,IAAI1B,SAAS,CAAC,oDAAoD,CAAC;EAAE;EAAEyB,QAAQ,CAAC1D,SAAS,GAAGE,MAAM,CAAC0D,MAAM,CAACD,UAAU,IAAIA,UAAU,CAAC3D,SAAS,EAAE;IAAED,WAAW,EAAE;MAAE8D,KAAK,EAAEH,QAAQ;MAAEpB,QAAQ,EAAE,IAAI;MAAED,YAAY,EAAE;IAAK;EAAE,CAAC,CAAC;EAAEnC,MAAM,CAAC2B,cAAc,CAAC6B,QAAQ,EAAE,WAAW,EAAE;IAAEpB,QAAQ,EAAE;EAAM,CAAC,CAAC;EAAE,IAAIqB,UAAU,EAAEG,eAAe,CAACJ,QAAQ,EAAEC,UAAU,CAAC;AAAE;AACnc,SAASG,eAAeA,CAAClE,CAAC,EAAEmE,CAAC,EAAE;EAAED,eAAe,GAAG5D,MAAM,CAACoD,cAAc,GAAGpD,MAAM,CAACoD,cAAc,CAAClD,IAAI,CAAC,CAAC,GAAG,SAAS0D,eAAeA,CAAClE,CAAC,EAAEmE,CAAC,EAAE;IAAEnE,CAAC,CAAC4D,SAAS,GAAGO,CAAC;IAAE,OAAOnE,CAAC;EAAE,CAAC;EAAE,OAAOkE,eAAe,CAAClE,CAAC,EAAEmE,CAAC,CAAC;AAAE;AACvM,SAASrC,eAAeA,CAACsC,GAAG,EAAEtD,GAAG,EAAEmD,KAAK,EAAE;EAAEnD,GAAG,GAAG6B,cAAc,CAAC7B,GAAG,CAAC;EAAE,IAAIA,GAAG,IAAIsD,GAAG,EAAE;IAAE9D,MAAM,CAAC2B,cAAc,CAACmC,GAAG,EAAEtD,GAAG,EAAE;MAAEmD,KAAK,EAAEA,KAAK;MAAEvC,UAAU,EAAE,IAAI;MAAEe,YAAY,EAAE,IAAI;MAAEC,QAAQ,EAAE;IAAK,CAAC,CAAC;EAAE,CAAC,MAAM;IAAE0B,GAAG,CAACtD,GAAG,CAAC,GAAGmD,KAAK;EAAE;EAAE,OAAOG,GAAG;AAAE;AAC3O,SAASzB,cAAcA,CAACtB,CAAC,EAAE;EAAE,IAAIX,CAAC,GAAG2D,YAAY,CAAChD,CAAC,EAAE,QAAQ,CAAC;EAAE,OAAO,QAAQ,IAAItB,OAAO,CAACW,CAAC,CAAC,GAAGA,CAAC,GAAGA,CAAC,GAAG,EAAE;AAAE;AAC5G,SAAS2D,YAAYA,CAAChD,CAAC,EAAED,CAAC,EAAE;EAAE,IAAI,QAAQ,IAAIrB,OAAO,CAACsB,CAAC,CAAC,IAAI,CAACA,CAAC,EAAE,OAAOA,CAAC;EAAE,IAAIF,CAAC,GAAGE,CAAC,CAACpB,MAAM,CAACqE,WAAW,CAAC;EAAE,IAAI,KAAK,CAAC,KAAKnD,CAAC,EAAE;IAAE,IAAIT,CAAC,GAAGS,CAAC,CAACH,IAAI,CAACK,CAAC,EAAED,CAAC,IAAI,SAAS,CAAC;IAAE,IAAI,QAAQ,IAAIrB,OAAO,CAACW,CAAC,CAAC,EAAE,OAAOA,CAAC;IAAE,MAAM,IAAI2B,SAAS,CAAC,8CAA8C,CAAC;EAAE;EAAE,OAAO,CAAC,QAAQ,KAAKjB,CAAC,GAAGmD,MAAM,GAAGC,MAAM,EAAEnD,CAAC,CAAC;AAAE;AAC3T;AACA;AACA;AACA,OAAOoD,KAAK,IAAIC,aAAa,EAAEC,QAAQ,QAAQ,OAAO;AACtD,OAAOC,IAAI,MAAM,MAAM;AACvB,SAASC,UAAU,QAAQ,yBAAyB;AACpD,OAAOC,UAAU,MAAM,mBAAmB;AAC1C,OAAOC,KAAK,MAAM,cAAc;AAChC,SAASC,KAAK,QAAQ,oBAAoB;AAC1C,SAASC,IAAI,QAAQ,mBAAmB;AACxC,SAASC,iBAAiB,QAAQ,oBAAoB;AACtD,SAASC,QAAQ,QAAQ,mBAAmB;AAC5C,SAASC,mBAAmB,QAAQ,wBAAwB;AAC5D,SAASC,WAAW,QAAQ,oBAAoB;AAChD,IAAIC,WAAW,GAAG,SAASA,WAAWA,CAACC,IAAI,EAAE;EAC3C,IAAIC,IAAI,GAAGD,IAAI,CAACC,IAAI;IAClBC,UAAU,GAAGF,IAAI,CAACE,UAAU;IAC5BC,QAAQ,GAAGH,IAAI,CAACG,QAAQ;IACxBC,CAAC,GAAGJ,IAAI,CAACI,CAAC;IACVC,KAAK,GAAGL,IAAI,CAACK,KAAK;IAClBC,cAAc,GAAGN,IAAI,CAACM,cAAc;EACtC,IAAI,CAACL,IAAI,IAAI,CAACA,IAAI,CAAC5E,MAAM,EAAE;IACzB,OAAO,CAAC,CAAC;EACX;EACA,IAAIkF,GAAG,GAAGN,IAAI,CAAC5E,MAAM;EACrB,IAAImF,KAAK,GAAGlB,UAAU,CAAC,CAAC,CAACmB,MAAM,CAACjB,KAAK,CAAC,CAAC,EAAEe,GAAG,CAAC,CAAC,CAACf,KAAK,CAAC,CAACY,CAAC,EAAEA,CAAC,GAAGC,KAAK,GAAGC,cAAc,CAAC,CAAC;EACrF,IAAII,WAAW,GAAGF,KAAK,CAACC,MAAM,CAAC,CAAC,CAACE,GAAG,CAAC,UAAUC,KAAK,EAAE;IACpD,OAAOJ,KAAK,CAACI,KAAK,CAAC;EACrB,CAAC,CAAC;EACF,OAAO;IACLC,YAAY,EAAE,KAAK;IACnBC,aAAa,EAAE,KAAK;IACpBC,iBAAiB,EAAE,KAAK;IACxBC,kBAAkB,EAAE,KAAK;IACzBC,MAAM,EAAET,KAAK,CAACN,UAAU,CAAC;IACzBgB,IAAI,EAAEV,KAAK,CAACL,QAAQ,CAAC;IACrBK,KAAK,EAAEA,KAAK;IACZE,WAAW,EAAEA;EACf,CAAC;AACH,CAAC;AACD,IAAIS,OAAO,GAAG,SAASA,OAAOA,CAACvF,CAAC,EAAE;EAChC,OAAOA,CAAC,CAACwF,cAAc,IAAI,CAAC,CAACxF,CAAC,CAACwF,cAAc,CAAC/F,MAAM;AACtD,CAAC;AACD,OAAO,IAAIgG,KAAK,GAAG,aAAa,UAAUC,cAAc,EAAE;EACxD,SAASD,KAAKA,CAACrE,KAAK,EAAE;IACpB,IAAIuE,KAAK;IACT5E,eAAe,CAAC,IAAI,EAAE0E,KAAK,CAAC;IAC5BE,KAAK,GAAG/D,UAAU,CAAC,IAAI,EAAE6D,KAAK,EAAE,CAACrE,KAAK,CAAC,CAAC;IACxCT,eAAe,CAACgF,KAAK,EAAE,YAAY,EAAE,UAAU3F,CAAC,EAAE;MAChD,IAAI2F,KAAK,CAACC,UAAU,EAAE;QACpBC,YAAY,CAACF,KAAK,CAACC,UAAU,CAAC;QAC9BD,KAAK,CAACC,UAAU,GAAG,IAAI;MACzB;MACA,IAAID,KAAK,CAACG,KAAK,CAACX,iBAAiB,EAAE;QACjCQ,KAAK,CAACI,mBAAmB,CAAC/F,CAAC,CAAC;MAC9B,CAAC,MAAM,IAAI2F,KAAK,CAACG,KAAK,CAACZ,aAAa,EAAE;QACpCS,KAAK,CAACK,eAAe,CAAChG,CAAC,CAAC;MAC1B;IACF,CAAC,CAAC;IACFW,eAAe,CAACgF,KAAK,EAAE,iBAAiB,EAAE,UAAU3F,CAAC,EAAE;MACrD,IAAIA,CAAC,CAACwF,cAAc,IAAI,IAAI,IAAIxF,CAAC,CAACwF,cAAc,CAAC/F,MAAM,GAAG,CAAC,EAAE;QAC3DkG,KAAK,CAACM,UAAU,CAACjG,CAAC,CAACwF,cAAc,CAAC,CAAC,CAAC,CAAC;MACvC;IACF,CAAC,CAAC;IACF7E,eAAe,CAACgF,KAAK,EAAE,eAAe,EAAE,YAAY;MAClDA,KAAK,CAACO,QAAQ,CAAC;QACbf,iBAAiB,EAAE,KAAK;QACxBD,aAAa,EAAE;MACjB,CAAC,EAAE,YAAY;QACb,IAAIiB,WAAW,GAAGR,KAAK,CAACvE,KAAK;UAC3BmD,QAAQ,GAAG4B,WAAW,CAAC5B,QAAQ;UAC/B6B,SAAS,GAAGD,WAAW,CAACC,SAAS;UACjC9B,UAAU,GAAG6B,WAAW,CAAC7B,UAAU;QACrC8B,SAAS,KAAK,IAAI,IAAIA,SAAS,KAAK,KAAK,CAAC,IAAIA,SAAS,CAAC;UACtD7B,QAAQ,EAAEA,QAAQ;UAClBD,UAAU,EAAEA;QACd,CAAC,CAAC;MACJ,CAAC,CAAC;MACFqB,KAAK,CAACU,qBAAqB,CAAC,CAAC;IAC/B,CAAC,CAAC;IACF1F,eAAe,CAACgF,KAAK,EAAE,oBAAoB,EAAE,YAAY;MACvD,IAAIA,KAAK,CAACG,KAAK,CAACX,iBAAiB,IAAIQ,KAAK,CAACG,KAAK,CAACZ,aAAa,EAAE;QAC9DS,KAAK,CAACC,UAAU,GAAGU,MAAM,CAACC,UAAU,CAACZ,KAAK,CAACa,aAAa,EAAEb,KAAK,CAACvE,KAAK,CAACqF,YAAY,CAAC;MACrF;IACF,CAAC,CAAC;IACF9F,eAAe,CAACgF,KAAK,EAAE,6BAA6B,EAAE,YAAY;MAChEA,KAAK,CAACO,QAAQ,CAAC;QACbjB,YAAY,EAAE;MAChB,CAAC,CAAC;IACJ,CAAC,CAAC;IACFtE,eAAe,CAACgF,KAAK,EAAE,6BAA6B,EAAE,YAAY;MAChEA,KAAK,CAACO,QAAQ,CAAC;QACbjB,YAAY,EAAE;MAChB,CAAC,CAAC;IACJ,CAAC,CAAC;IACFtE,eAAe,CAACgF,KAAK,EAAE,sBAAsB,EAAE,UAAU3F,CAAC,EAAE;MAC1D,IAAI0G,KAAK,GAAGnB,OAAO,CAACvF,CAAC,CAAC,GAAGA,CAAC,CAACwF,cAAc,CAAC,CAAC,CAAC,GAAGxF,CAAC;MAChD2F,KAAK,CAACO,QAAQ,CAAC;QACbf,iBAAiB,EAAE,KAAK;QACxBD,aAAa,EAAE,IAAI;QACnByB,eAAe,EAAED,KAAK,CAACE;MACzB,CAAC,CAAC;MACFjB,KAAK,CAACkB,qBAAqB,CAAC,CAAC;IAC/B,CAAC,CAAC;IACFlB,KAAK,CAACmB,0BAA0B,GAAG;MACjCzB,MAAM,EAAEM,KAAK,CAACoB,wBAAwB,CAAC1H,IAAI,CAACsG,KAAK,EAAE,QAAQ,CAAC;MAC5DL,IAAI,EAAEK,KAAK,CAACoB,wBAAwB,CAAC1H,IAAI,CAACsG,KAAK,EAAE,MAAM;IACzD,CAAC;IACDA,KAAK,CAACG,KAAK,GAAG,CAAC,CAAC;IAChB,OAAOH,KAAK;EACd;EACAjD,SAAS,CAAC+C,KAAK,EAAEC,cAAc,CAAC;EAChC,OAAOjE,YAAY,CAACgE,KAAK,EAAE,CAAC;IAC1B9F,GAAG,EAAE,sBAAsB;IAC3BmD,KAAK,EAAE,SAASkE,oBAAoBA,CAAA,EAAG;MACrC,IAAI,IAAI,CAACpB,UAAU,EAAE;QACnBC,YAAY,CAAC,IAAI,CAACD,UAAU,CAAC;QAC7B,IAAI,CAACA,UAAU,GAAG,IAAI;MACxB;MACA,IAAI,CAACS,qBAAqB,CAAC,CAAC;IAC9B;EACF,CAAC,EAAE;IACD1G,GAAG,EAAE,UAAU;IACfmD,KAAK,EAAE,SAASmE,QAAQA,CAACC,KAAK,EAAE;MAC9B,IAAI7B,MAAM,GAAG6B,KAAK,CAAC7B,MAAM;QACvBC,IAAI,GAAG4B,KAAK,CAAC5B,IAAI;MACnB,IAAIR,WAAW,GAAG,IAAI,CAACgB,KAAK,CAAChB,WAAW;MACxC,IAAIqC,YAAY,GAAG,IAAI,CAAC/F,KAAK;QAC3BgG,GAAG,GAAGD,YAAY,CAACC,GAAG;QACtB/C,IAAI,GAAG8C,YAAY,CAAC9C,IAAI;MAC1B,IAAIgD,SAAS,GAAGhD,IAAI,CAAC5E,MAAM,GAAG,CAAC;MAC/B,IAAI6H,GAAG,GAAGC,IAAI,CAACD,GAAG,CAACjC,MAAM,EAAEC,IAAI,CAAC;MAChC,IAAIkC,GAAG,GAAGD,IAAI,CAACC,GAAG,CAACnC,MAAM,EAAEC,IAAI,CAAC;MAChC,IAAImC,QAAQ,GAAGhC,KAAK,CAACiC,eAAe,CAAC5C,WAAW,EAAEwC,GAAG,CAAC;MACtD,IAAIK,QAAQ,GAAGlC,KAAK,CAACiC,eAAe,CAAC5C,WAAW,EAAE0C,GAAG,CAAC;MACtD,OAAO;QACLlD,UAAU,EAAEmD,QAAQ,GAAGA,QAAQ,GAAGL,GAAG;QACrC7C,QAAQ,EAAEoD,QAAQ,KAAKN,SAAS,GAAGA,SAAS,GAAGM,QAAQ,GAAGA,QAAQ,GAAGP;MACvE,CAAC;IACH;EACF,CAAC,EAAE;IACDzH,GAAG,EAAE,eAAe;IACpBmD,KAAK,EAAE,SAAS8E,aAAaA,CAACC,KAAK,EAAE;MACnC,IAAIC,YAAY,GAAG,IAAI,CAAC1G,KAAK;QAC3BiD,IAAI,GAAGyD,YAAY,CAACzD,IAAI;QACxB0D,aAAa,GAAGD,YAAY,CAACC,aAAa;QAC1CC,OAAO,GAAGF,YAAY,CAACE,OAAO;MAChC,IAAIC,IAAI,GAAGlE,iBAAiB,CAACM,IAAI,CAACwD,KAAK,CAAC,EAAEG,OAAO,EAAEH,KAAK,CAAC;MACzD,OAAOlE,UAAU,CAACoE,aAAa,CAAC,GAAGA,aAAa,CAACE,IAAI,EAAEJ,KAAK,CAAC,GAAGI,IAAI;IACtE;EACF,CAAC,EAAE;IACDtI,GAAG,EAAE,uBAAuB;IAC5BmD,KAAK,EAAE,SAAS+D,qBAAqBA,CAAA,EAAG;MACtCP,MAAM,CAAC4B,gBAAgB,CAAC,SAAS,EAAE,IAAI,CAAC1B,aAAa,EAAE,IAAI,CAAC;MAC5DF,MAAM,CAAC4B,gBAAgB,CAAC,UAAU,EAAE,IAAI,CAAC1B,aAAa,EAAE,IAAI,CAAC;MAC7DF,MAAM,CAAC4B,gBAAgB,CAAC,WAAW,EAAE,IAAI,CAACjC,UAAU,EAAE,IAAI,CAAC;IAC7D;EACF,CAAC,EAAE;IACDtG,GAAG,EAAE,uBAAuB;IAC5BmD,KAAK,EAAE,SAASuD,qBAAqBA,CAAA,EAAG;MACtCC,MAAM,CAAC6B,mBAAmB,CAAC,SAAS,EAAE,IAAI,CAAC3B,aAAa,EAAE,IAAI,CAAC;MAC/DF,MAAM,CAAC6B,mBAAmB,CAAC,UAAU,EAAE,IAAI,CAAC3B,aAAa,EAAE,IAAI,CAAC;MAChEF,MAAM,CAAC6B,mBAAmB,CAAC,WAAW,EAAE,IAAI,CAAClC,UAAU,EAAE,IAAI,CAAC;IAChE;EACF,CAAC,EAAE;IACDtG,GAAG,EAAE,iBAAiB;IACtBmD,KAAK,EAAE,SAASkD,eAAeA,CAAChG,CAAC,EAAE;MACjC,IAAIoI,WAAW,GAAG,IAAI,CAACtC,KAAK;QAC1Ba,eAAe,GAAGyB,WAAW,CAACzB,eAAe;QAC7CtB,MAAM,GAAG+C,WAAW,CAAC/C,MAAM;QAC3BC,IAAI,GAAG8C,WAAW,CAAC9C,IAAI;MACzB,IAAI+C,YAAY,GAAG,IAAI,CAACjH,KAAK;QAC3BoD,CAAC,GAAG6D,YAAY,CAAC7D,CAAC;QAClBC,KAAK,GAAG4D,YAAY,CAAC5D,KAAK;QAC1BC,cAAc,GAAG2D,YAAY,CAAC3D,cAAc;QAC5CJ,UAAU,GAAG+D,YAAY,CAAC/D,UAAU;QACpCC,QAAQ,GAAG8D,YAAY,CAAC9D,QAAQ;QAChC+D,QAAQ,GAAGD,YAAY,CAACC,QAAQ;MAClC,IAAIC,KAAK,GAAGvI,CAAC,CAAC4G,KAAK,GAAGD,eAAe;MACrC,IAAI4B,KAAK,GAAG,CAAC,EAAE;QACbA,KAAK,GAAGhB,IAAI,CAACD,GAAG,CAACiB,KAAK,EAAE/D,CAAC,GAAGC,KAAK,GAAGC,cAAc,GAAGY,IAAI,EAAEd,CAAC,GAAGC,KAAK,GAAGC,cAAc,GAAGW,MAAM,CAAC;MACjG,CAAC,MAAM,IAAIkD,KAAK,GAAG,CAAC,EAAE;QACpBA,KAAK,GAAGhB,IAAI,CAACC,GAAG,CAACe,KAAK,EAAE/D,CAAC,GAAGa,MAAM,EAAEb,CAAC,GAAGc,IAAI,CAAC;MAC/C;MACA,IAAIkD,QAAQ,GAAG,IAAI,CAACvB,QAAQ,CAAC;QAC3B5B,MAAM,EAAEA,MAAM,GAAGkD,KAAK;QACtBjD,IAAI,EAAEA,IAAI,GAAGiD;MACf,CAAC,CAAC;MACF,IAAI,CAACC,QAAQ,CAAClE,UAAU,KAAKA,UAAU,IAAIkE,QAAQ,CAACjE,QAAQ,KAAKA,QAAQ,KAAK+D,QAAQ,EAAE;QACtFA,QAAQ,CAACE,QAAQ,CAAC;MACpB;MACA,IAAI,CAACtC,QAAQ,CAAC;QACZb,MAAM,EAAEA,MAAM,GAAGkD,KAAK;QACtBjD,IAAI,EAAEA,IAAI,GAAGiD,KAAK;QAClB5B,eAAe,EAAE3G,CAAC,CAAC4G;MACrB,CAAC,CAAC;IACJ;EACF,CAAC,EAAE;IACDjH,GAAG,EAAE,0BAA0B;IAC/BmD,KAAK,EAAE,SAASiE,wBAAwBA,CAAC0B,EAAE,EAAEzI,CAAC,EAAE;MAC9C,IAAI0G,KAAK,GAAGnB,OAAO,CAACvF,CAAC,CAAC,GAAGA,CAAC,CAACwF,cAAc,CAAC,CAAC,CAAC,GAAGxF,CAAC;MAChD,IAAI,CAACkG,QAAQ,CAAC;QACZhB,aAAa,EAAE,KAAK;QACpBC,iBAAiB,EAAE,IAAI;QACvBuD,iBAAiB,EAAED,EAAE;QACrBE,eAAe,EAAEjC,KAAK,CAACE;MACzB,CAAC,CAAC;MACF,IAAI,CAACC,qBAAqB,CAAC,CAAC;IAC9B;EACF,CAAC,EAAE;IACDlH,GAAG,EAAE,qBAAqB;IAC1BmD,KAAK,EAAE,SAASiD,mBAAmBA,CAAC/F,CAAC,EAAE;MACrC,IAAI4I,YAAY,GAAG,IAAI,CAAC9C,KAAK;QAC3B6C,eAAe,GAAGC,YAAY,CAACD,eAAe;QAC9CD,iBAAiB,GAAGE,YAAY,CAACF,iBAAiB;QAClDpD,IAAI,GAAGsD,YAAY,CAACtD,IAAI;QACxBD,MAAM,GAAGuD,YAAY,CAACvD,MAAM;MAC9B,IAAIwD,SAAS,GAAG,IAAI,CAAC/C,KAAK,CAAC4C,iBAAiB,CAAC;MAC7C,IAAII,YAAY,GAAG,IAAI,CAAC1H,KAAK;QAC3BoD,CAAC,GAAGsE,YAAY,CAACtE,CAAC;QAClBC,KAAK,GAAGqE,YAAY,CAACrE,KAAK;QAC1BC,cAAc,GAAGoE,YAAY,CAACpE,cAAc;QAC5C4D,QAAQ,GAAGQ,YAAY,CAACR,QAAQ;QAChClB,GAAG,GAAG0B,YAAY,CAAC1B,GAAG;QACtB/C,IAAI,GAAGyE,YAAY,CAACzE,IAAI;MAC1B,IAAI0E,MAAM,GAAG;QACX1D,MAAM,EAAE,IAAI,CAACS,KAAK,CAACT,MAAM;QACzBC,IAAI,EAAE,IAAI,CAACQ,KAAK,CAACR;MACnB,CAAC;MACD,IAAIiD,KAAK,GAAGvI,CAAC,CAAC4G,KAAK,GAAG+B,eAAe;MACrC,IAAIJ,KAAK,GAAG,CAAC,EAAE;QACbA,KAAK,GAAGhB,IAAI,CAACD,GAAG,CAACiB,KAAK,EAAE/D,CAAC,GAAGC,KAAK,GAAGC,cAAc,GAAGmE,SAAS,CAAC;MACjE,CAAC,MAAM,IAAIN,KAAK,GAAG,CAAC,EAAE;QACpBA,KAAK,GAAGhB,IAAI,CAACC,GAAG,CAACe,KAAK,EAAE/D,CAAC,GAAGqE,SAAS,CAAC;MACxC;MACAE,MAAM,CAACL,iBAAiB,CAAC,GAAGG,SAAS,GAAGN,KAAK;MAC7C,IAAIC,QAAQ,GAAG,IAAI,CAACvB,QAAQ,CAAC8B,MAAM,CAAC;MACpC,IAAIzE,UAAU,GAAGkE,QAAQ,CAAClE,UAAU;QAClCC,QAAQ,GAAGiE,QAAQ,CAACjE,QAAQ;MAC9B,IAAIyE,SAAS,GAAG,SAASA,SAASA,CAAA,EAAG;QACnC,IAAI3B,SAAS,GAAGhD,IAAI,CAAC5E,MAAM,GAAG,CAAC;QAC/B,IAAIiJ,iBAAiB,KAAK,QAAQ,KAAKpD,IAAI,GAAGD,MAAM,GAAGf,UAAU,GAAG8C,GAAG,KAAK,CAAC,GAAG7C,QAAQ,GAAG6C,GAAG,KAAK,CAAC,CAAC,IAAI9B,IAAI,GAAGD,MAAM,IAAId,QAAQ,KAAK8C,SAAS,IAAIqB,iBAAiB,KAAK,MAAM,KAAKpD,IAAI,GAAGD,MAAM,GAAGd,QAAQ,GAAG6C,GAAG,KAAK,CAAC,GAAG9C,UAAU,GAAG8C,GAAG,KAAK,CAAC,CAAC,IAAI9B,IAAI,GAAGD,MAAM,IAAId,QAAQ,KAAK8C,SAAS,EAAE;UAC9R,OAAO,IAAI;QACb;QACA,OAAO,KAAK;MACd,CAAC;MACD,IAAI,CAACnB,QAAQ,CAACvF,eAAe,CAACA,eAAe,CAAC,CAAC,CAAC,EAAE+H,iBAAiB,EAAEG,SAAS,GAAGN,KAAK,CAAC,EAAE,iBAAiB,EAAEvI,CAAC,CAAC4G,KAAK,CAAC,EAAE,YAAY;QAChI,IAAI0B,QAAQ,EAAE;UACZ,IAAIU,SAAS,CAAC,CAAC,EAAE;YACfV,QAAQ,CAACE,QAAQ,CAAC;UACpB;QACF;MACF,CAAC,CAAC;IACJ;EACF,CAAC,EAAE;IACD7I,GAAG,EAAE,6BAA6B;IAClCmD,KAAK,EAAE,SAASmG,2BAA2BA,CAACC,SAAS,EAAET,EAAE,EAAE;MACzD,IAAIU,MAAM,GAAG,IAAI;MACjB;MACA,IAAIC,YAAY,GAAG,IAAI,CAACtD,KAAK;QAC3BhB,WAAW,GAAGsE,YAAY,CAACtE,WAAW;QACtCO,MAAM,GAAG+D,YAAY,CAAC/D,MAAM;QAC5BC,IAAI,GAAG8D,YAAY,CAAC9D,IAAI;MAC1B;MACA,IAAI+D,iBAAiB,GAAG,IAAI,CAACvD,KAAK,CAAC2C,EAAE,CAAC;MACtC,IAAIa,YAAY,GAAGxE,WAAW,CAACyE,OAAO,CAACF,iBAAiB,CAAC;MACzD,IAAIC,YAAY,KAAK,CAAC,CAAC,EAAE;QACvB;MACF;MACA,IAAId,QAAQ,GAAGc,YAAY,GAAGJ,SAAS;MACvC,IAAIV,QAAQ,KAAK,CAAC,CAAC,IAAIA,QAAQ,IAAI1D,WAAW,CAACrF,MAAM,EAAE;QACrD;MACF;MACA,IAAI+J,aAAa,GAAG1E,WAAW,CAAC0D,QAAQ,CAAC;;MAEzC;MACA,IAAIC,EAAE,KAAK,QAAQ,IAAIe,aAAa,IAAIlE,IAAI,IAAImD,EAAE,KAAK,MAAM,IAAIe,aAAa,IAAInE,MAAM,EAAE;QACxF;MACF;MACA,IAAI,CAACa,QAAQ,CAACvF,eAAe,CAAC,CAAC,CAAC,EAAE8H,EAAE,EAAEe,aAAa,CAAC,EAAE,YAAY;QAChEL,MAAM,CAAC/H,KAAK,CAACkH,QAAQ,CAACa,MAAM,CAAClC,QAAQ,CAAC;UACpC5B,MAAM,EAAE8D,MAAM,CAACrD,KAAK,CAACT,MAAM;UAC3BC,IAAI,EAAE6D,MAAM,CAACrD,KAAK,CAACR;QACrB,CAAC,CAAC,CAAC;MACL,CAAC,CAAC;IACJ;EACF,CAAC,EAAE;IACD3F,GAAG,EAAE,kBAAkB;IACvBmD,KAAK,EAAE,SAAS2G,gBAAgBA,CAAA,EAAG;MACjC,IAAIC,YAAY,GAAG,IAAI,CAACtI,KAAK;QAC3BoD,CAAC,GAAGkF,YAAY,CAAClF,CAAC;QAClBmF,CAAC,GAAGD,YAAY,CAACC,CAAC;QAClBlF,KAAK,GAAGiF,YAAY,CAACjF,KAAK;QAC1BmF,MAAM,GAAGF,YAAY,CAACE,MAAM;QAC5BC,IAAI,GAAGH,YAAY,CAACG,IAAI;QACxBC,MAAM,GAAGJ,YAAY,CAACI,MAAM;MAC9B,OAAO,aAAaxG,KAAK,CAACyG,aAAa,CAAC,MAAM,EAAE;QAC9CD,MAAM,EAAEA,MAAM;QACdD,IAAI,EAAEA,IAAI;QACVrF,CAAC,EAAEA,CAAC;QACJmF,CAAC,EAAEA,CAAC;QACJlF,KAAK,EAAEA,KAAK;QACZmF,MAAM,EAAEA;MACV,CAAC,CAAC;IACJ;EACF,CAAC,EAAE;IACDjK,GAAG,EAAE,gBAAgB;IACrBmD,KAAK,EAAE,SAASkH,cAAcA,CAAA,EAAG;MAC/B,IAAIC,YAAY,GAAG,IAAI,CAAC7I,KAAK;QAC3BoD,CAAC,GAAGyF,YAAY,CAACzF,CAAC;QAClBmF,CAAC,GAAGM,YAAY,CAACN,CAAC;QAClBlF,KAAK,GAAGwF,YAAY,CAACxF,KAAK;QAC1BmF,MAAM,GAAGK,YAAY,CAACL,MAAM;QAC5BvF,IAAI,GAAG4F,YAAY,CAAC5F,IAAI;QACxB6F,QAAQ,GAAGD,YAAY,CAACC,QAAQ;QAChCC,OAAO,GAAGF,YAAY,CAACE,OAAO;MAChC,IAAIC,YAAY,GAAG5G,QAAQ,CAAC6G,IAAI,CAACH,QAAQ,CAAC;MAC1C,IAAI,CAACE,YAAY,EAAE;QACjB,OAAO,IAAI;MACb;MACA,OAAO,aAAa9G,KAAK,CAACgH,YAAY,CAACF,YAAY,EAAE;QACnD5F,CAAC,EAAEA,CAAC;QACJmF,CAAC,EAAEA,CAAC;QACJlF,KAAK,EAAEA,KAAK;QACZmF,MAAM,EAAEA,MAAM;QACdW,MAAM,EAAEJ,OAAO;QACfK,OAAO,EAAE,IAAI;QACbnG,IAAI,EAAEA;MACR,CAAC,CAAC;IACJ;EACF,CAAC,EAAE;IACD1E,GAAG,EAAE,sBAAsB;IAC3BmD,KAAK,EAAE,SAAS2H,oBAAoBA,CAACC,UAAU,EAAEjC,EAAE,EAAE;MACnD,IAAIkC,gBAAgB;QAClBC,cAAc;QACdC,MAAM,GAAG,IAAI;MACf,IAAIC,YAAY,GAAG,IAAI,CAAC1J,KAAK;QAC3BuI,CAAC,GAAGmB,YAAY,CAACnB,CAAC;QAClBjF,cAAc,GAAGoG,YAAY,CAACpG,cAAc;QAC5CkF,MAAM,GAAGkB,YAAY,CAAClB,MAAM;QAC5BmB,SAAS,GAAGD,YAAY,CAACC,SAAS;QAClCC,SAAS,GAAGF,YAAY,CAACE,SAAS;QAClC3G,IAAI,GAAGyG,YAAY,CAACzG,IAAI;QACxBC,UAAU,GAAGwG,YAAY,CAACxG,UAAU;QACpCC,QAAQ,GAAGuG,YAAY,CAACvG,QAAQ;MAClC,IAAIC,CAAC,GAAG+C,IAAI,CAACC,GAAG,CAACkD,UAAU,EAAE,IAAI,CAACtJ,KAAK,CAACoD,CAAC,CAAC;MAC1C,IAAIyG,cAAc,GAAGxK,aAAa,CAACA,aAAa,CAAC,CAAC,CAAC,EAAEyD,WAAW,CAAC,IAAI,CAAC9C,KAAK,EAAE,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE;QACxFoD,CAAC,EAAEA,CAAC;QACJmF,CAAC,EAAEA,CAAC;QACJlF,KAAK,EAAEC,cAAc;QACrBkF,MAAM,EAAEA;MACV,CAAC,CAAC;MACF,IAAIsB,cAAc,GAAGF,SAAS,IAAI,aAAa,CAACG,MAAM,CAAC,CAACR,gBAAgB,GAAGtG,IAAI,CAACC,UAAU,CAAC,MAAM,IAAI,IAAIqG,gBAAgB,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,gBAAgB,CAACS,IAAI,EAAE,eAAe,CAAC,CAACD,MAAM,CAAC,CAACP,cAAc,GAAGvG,IAAI,CAACE,QAAQ,CAAC,MAAM,IAAI,IAAIqG,cAAc,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,cAAc,CAACQ,IAAI,CAAC;MACtS,OAAO,aAAa9H,KAAK,CAACyG,aAAa,CAAClG,KAAK,EAAE;QAC7CwH,QAAQ,EAAE,CAAC;QACXC,IAAI,EAAE,QAAQ;QACd,YAAY,EAAEJ,cAAc;QAC5B,eAAe,EAAER,UAAU;QAC3Ba,SAAS,EAAE,0BAA0B;QACrCC,YAAY,EAAE,IAAI,CAACC,2BAA2B;QAC9CC,YAAY,EAAE,IAAI,CAACC,2BAA2B;QAC9CC,WAAW,EAAE,IAAI,CAAC9E,0BAA0B,CAAC2B,EAAE,CAAC;QAChDoD,YAAY,EAAE,IAAI,CAAC/E,0BAA0B,CAAC2B,EAAE,CAAC;QACjDqD,SAAS,EAAE,SAASA,SAASA,CAAC9L,CAAC,EAAE;UAC/B,IAAI,CAAC,CAAC,WAAW,EAAE,YAAY,CAAC,CAAC+L,QAAQ,CAAC/L,CAAC,CAACL,GAAG,CAAC,EAAE;YAChD;UACF;UACAK,CAAC,CAACgM,cAAc,CAAC,CAAC;UAClBhM,CAAC,CAACiM,eAAe,CAAC,CAAC;UACnBpB,MAAM,CAAC5B,2BAA2B,CAACjJ,CAAC,CAACL,GAAG,KAAK,YAAY,GAAG,CAAC,GAAG,CAAC,CAAC,EAAE8I,EAAE,CAAC;QACzE,CAAC;QACDyD,OAAO,EAAE,SAASA,OAAOA,CAAA,EAAG;UAC1BrB,MAAM,CAAC3E,QAAQ,CAAC;YACdd,kBAAkB,EAAE;UACtB,CAAC,CAAC;QACJ,CAAC;QACD+G,MAAM,EAAE,SAASA,MAAMA,CAAA,EAAG;UACxBtB,MAAM,CAAC3E,QAAQ,CAAC;YACdd,kBAAkB,EAAE;UACtB,CAAC,CAAC;QACJ,CAAC;QACDgH,KAAK,EAAE;UACLC,MAAM,EAAE;QACV;MACF,CAAC,EAAE5G,KAAK,CAAC6G,eAAe,CAACvB,SAAS,EAAEE,cAAc,CAAC,CAAC;IACtD;EACF,CAAC,EAAE;IACDtL,GAAG,EAAE,aAAa;IAClBmD,KAAK,EAAE,SAASyJ,WAAWA,CAAClH,MAAM,EAAEC,IAAI,EAAE;MACxC,IAAIkH,YAAY,GAAG,IAAI,CAACpL,KAAK;QAC3BuI,CAAC,GAAG6C,YAAY,CAAC7C,CAAC;QAClBC,MAAM,GAAG4C,YAAY,CAAC5C,MAAM;QAC5BE,MAAM,GAAG0C,YAAY,CAAC1C,MAAM;QAC5BpF,cAAc,GAAG8H,YAAY,CAAC9H,cAAc;MAC9C,IAAIF,CAAC,GAAG+C,IAAI,CAACD,GAAG,CAACjC,MAAM,EAAEC,IAAI,CAAC,GAAGZ,cAAc;MAC/C,IAAID,KAAK,GAAG8C,IAAI,CAACC,GAAG,CAACD,IAAI,CAACkF,GAAG,CAACnH,IAAI,GAAGD,MAAM,CAAC,GAAGX,cAAc,EAAE,CAAC,CAAC;MACjE,OAAO,aAAapB,KAAK,CAACyG,aAAa,CAAC,MAAM,EAAE;QAC9CwB,SAAS,EAAE,sBAAsB;QACjCC,YAAY,EAAE,IAAI,CAACC,2BAA2B;QAC9CC,YAAY,EAAE,IAAI,CAACC,2BAA2B;QAC9CC,WAAW,EAAE,IAAI,CAACc,oBAAoB;QACtCb,YAAY,EAAE,IAAI,CAACa,oBAAoB;QACvCN,KAAK,EAAE;UACLC,MAAM,EAAE;QACV,CAAC;QACDvC,MAAM,EAAE,MAAM;QACdD,IAAI,EAAEC,MAAM;QACZ6C,WAAW,EAAE,GAAG;QAChBnI,CAAC,EAAEA,CAAC;QACJmF,CAAC,EAAEA,CAAC;QACJlF,KAAK,EAAEA,KAAK;QACZmF,MAAM,EAAEA;MACV,CAAC,CAAC;IACJ;EACF,CAAC,EAAE;IACDjK,GAAG,EAAE,YAAY;IACjBmD,KAAK,EAAE,SAAS8J,UAAUA,CAAA,EAAG;MAC3B,IAAIC,aAAa,GAAG,IAAI,CAACzL,KAAK;QAC5BkD,UAAU,GAAGuI,aAAa,CAACvI,UAAU;QACrCC,QAAQ,GAAGsI,aAAa,CAACtI,QAAQ;QACjCoF,CAAC,GAAGkD,aAAa,CAAClD,CAAC;QACnBC,MAAM,GAAGiD,aAAa,CAACjD,MAAM;QAC7BlF,cAAc,GAAGmI,aAAa,CAACnI,cAAc;QAC7CoF,MAAM,GAAG+C,aAAa,CAAC/C,MAAM;MAC/B,IAAIgD,YAAY,GAAG,IAAI,CAAChH,KAAK;QAC3BT,MAAM,GAAGyH,YAAY,CAACzH,MAAM;QAC5BC,IAAI,GAAGwH,YAAY,CAACxH,IAAI;MAC1B,IAAIyH,MAAM,GAAG,CAAC;MACd,IAAIC,KAAK,GAAG;QACVC,aAAa,EAAE,MAAM;QACrBpD,IAAI,EAAEC;MACR,CAAC;MACD,OAAO,aAAaxG,KAAK,CAACyG,aAAa,CAAClG,KAAK,EAAE;QAC7C0H,SAAS,EAAE;MACb,CAAC,EAAE,aAAajI,KAAK,CAACyG,aAAa,CAACjG,IAAI,EAAE5E,QAAQ,CAAC;QACjDgO,UAAU,EAAE,KAAK;QACjBC,cAAc,EAAE,QAAQ;QACxB3I,CAAC,EAAE+C,IAAI,CAACD,GAAG,CAACjC,MAAM,EAAEC,IAAI,CAAC,GAAGyH,MAAM;QAClCpD,CAAC,EAAEA,CAAC,GAAGC,MAAM,GAAG;MAClB,CAAC,EAAEoD,KAAK,CAAC,EAAE,IAAI,CAACpF,aAAa,CAACtD,UAAU,CAAC,CAAC,EAAE,aAAahB,KAAK,CAACyG,aAAa,CAACjG,IAAI,EAAE5E,QAAQ,CAAC;QAC1FgO,UAAU,EAAE,OAAO;QACnBC,cAAc,EAAE,QAAQ;QACxB3I,CAAC,EAAE+C,IAAI,CAACC,GAAG,CAACnC,MAAM,EAAEC,IAAI,CAAC,GAAGZ,cAAc,GAAGqI,MAAM;QACnDpD,CAAC,EAAEA,CAAC,GAAGC,MAAM,GAAG;MAClB,CAAC,EAAEoD,KAAK,CAAC,EAAE,IAAI,CAACpF,aAAa,CAACrD,QAAQ,CAAC,CAAC,CAAC;IAC3C;EACF,CAAC,EAAE;IACD5E,GAAG,EAAE,QAAQ;IACbmD,KAAK,EAAE,SAASsK,MAAMA,CAAA,EAAG;MACvB,IAAIC,aAAa,GAAG,IAAI,CAACjM,KAAK;QAC5BiD,IAAI,GAAGgJ,aAAa,CAAChJ,IAAI;QACzBkH,SAAS,GAAG8B,aAAa,CAAC9B,SAAS;QACnCrB,QAAQ,GAAGmD,aAAa,CAACnD,QAAQ;QACjC1F,CAAC,GAAG6I,aAAa,CAAC7I,CAAC;QACnBmF,CAAC,GAAG0D,aAAa,CAAC1D,CAAC;QACnBlF,KAAK,GAAG4I,aAAa,CAAC5I,KAAK;QAC3BmF,MAAM,GAAGyD,aAAa,CAACzD,MAAM;QAC7B0D,cAAc,GAAGD,aAAa,CAACC,cAAc;MAC/C,IAAIC,YAAY,GAAG,IAAI,CAACzH,KAAK;QAC3BT,MAAM,GAAGkI,YAAY,CAAClI,MAAM;QAC5BC,IAAI,GAAGiI,YAAY,CAACjI,IAAI;QACxBL,YAAY,GAAGsI,YAAY,CAACtI,YAAY;QACxCC,aAAa,GAAGqI,YAAY,CAACrI,aAAa;QAC1CC,iBAAiB,GAAGoI,YAAY,CAACpI,iBAAiB;QAClDC,kBAAkB,GAAGmI,YAAY,CAACnI,kBAAkB;MACtD,IAAI,CAACf,IAAI,IAAI,CAACA,IAAI,CAAC5E,MAAM,IAAI,CAACuE,QAAQ,CAACQ,CAAC,CAAC,IAAI,CAACR,QAAQ,CAAC2F,CAAC,CAAC,IAAI,CAAC3F,QAAQ,CAACS,KAAK,CAAC,IAAI,CAACT,QAAQ,CAAC4F,MAAM,CAAC,IAAInF,KAAK,IAAI,CAAC,IAAImF,MAAM,IAAI,CAAC,EAAE;QAC/H,OAAO,IAAI;MACb;MACA,IAAI4D,UAAU,GAAG/J,IAAI,CAAC,gBAAgB,EAAE8H,SAAS,CAAC;MAClD,IAAIkC,WAAW,GAAGnK,KAAK,CAACE,QAAQ,CAACkK,KAAK,CAACxD,QAAQ,CAAC,KAAK,CAAC;MACtD,IAAIkC,KAAK,GAAGnI,mBAAmB,CAAC,YAAY,EAAE,MAAM,CAAC;MACrD,OAAO,aAAaX,KAAK,CAACyG,aAAa,CAAClG,KAAK,EAAE;QAC7C0H,SAAS,EAAEiC,UAAU;QACrB9B,YAAY,EAAE,IAAI,CAACiC,kBAAkB;QACrCC,WAAW,EAAE,IAAI,CAACC,eAAe;QACjCzB,KAAK,EAAEA;MACT,CAAC,EAAE,IAAI,CAAC3C,gBAAgB,CAAC,CAAC,EAAEgE,WAAW,IAAI,IAAI,CAACzD,cAAc,CAAC,CAAC,EAAE,IAAI,CAACuC,WAAW,CAAClH,MAAM,EAAEC,IAAI,CAAC,EAAE,IAAI,CAACmF,oBAAoB,CAACpF,MAAM,EAAE,QAAQ,CAAC,EAAE,IAAI,CAACoF,oBAAoB,CAACnF,IAAI,EAAE,MAAM,CAAC,EAAE,CAACL,YAAY,IAAIC,aAAa,IAAIC,iBAAiB,IAAIC,kBAAkB,IAAIkI,cAAc,KAAK,IAAI,CAACV,UAAU,CAAC,CAAC,CAAC;IAC5S;EACF,CAAC,CAAC,EAAE,CAAC;IACHjN,GAAG,EAAE,wBAAwB;IAC7BmD,KAAK,EAAE,SAASgL,sBAAsBA,CAAC1M,KAAK,EAAE;MAC5C,IAAIoD,CAAC,GAAGpD,KAAK,CAACoD,CAAC;QACbmF,CAAC,GAAGvI,KAAK,CAACuI,CAAC;QACXlF,KAAK,GAAGrD,KAAK,CAACqD,KAAK;QACnBmF,MAAM,GAAGxI,KAAK,CAACwI,MAAM;QACrBE,MAAM,GAAG1I,KAAK,CAAC0I,MAAM;MACvB,IAAIiE,KAAK,GAAGxG,IAAI,CAACyG,KAAK,CAACrE,CAAC,GAAGC,MAAM,GAAG,CAAC,CAAC,GAAG,CAAC;MAC1C,OAAO,aAAatG,KAAK,CAACyG,aAAa,CAACzG,KAAK,CAAC2K,QAAQ,EAAE,IAAI,EAAE,aAAa3K,KAAK,CAACyG,aAAa,CAAC,MAAM,EAAE;QACrGvF,CAAC,EAAEA,CAAC;QACJmF,CAAC,EAAEA,CAAC;QACJlF,KAAK,EAAEA,KAAK;QACZmF,MAAM,EAAEA,MAAM;QACdC,IAAI,EAAEC,MAAM;QACZA,MAAM,EAAE;MACV,CAAC,CAAC,EAAE,aAAaxG,KAAK,CAACyG,aAAa,CAAC,MAAM,EAAE;QAC3CmE,EAAE,EAAE1J,CAAC,GAAG,CAAC;QACT2J,EAAE,EAAEJ,KAAK;QACTK,EAAE,EAAE5J,CAAC,GAAGC,KAAK,GAAG,CAAC;QACjB4J,EAAE,EAAEN,KAAK;QACTlE,IAAI,EAAE,MAAM;QACZC,MAAM,EAAE;MACV,CAAC,CAAC,EAAE,aAAaxG,KAAK,CAACyG,aAAa,CAAC,MAAM,EAAE;QAC3CmE,EAAE,EAAE1J,CAAC,GAAG,CAAC;QACT2J,EAAE,EAAEJ,KAAK,GAAG,CAAC;QACbK,EAAE,EAAE5J,CAAC,GAAGC,KAAK,GAAG,CAAC;QACjB4J,EAAE,EAAEN,KAAK,GAAG,CAAC;QACblE,IAAI,EAAE,MAAM;QACZC,MAAM,EAAE;MACV,CAAC,CAAC,CAAC;IACL;EACF,CAAC,EAAE;IACDnK,GAAG,EAAE,iBAAiB;IACtBmD,KAAK,EAAE,SAASwJ,eAAeA,CAACgC,MAAM,EAAElN,KAAK,EAAE;MAC7C,IAAImN,SAAS;MACb,IAAK,aAAajL,KAAK,CAACkL,cAAc,CAACF,MAAM,CAAC,EAAE;QAC9CC,SAAS,GAAG,aAAajL,KAAK,CAACgH,YAAY,CAACgE,MAAM,EAAElN,KAAK,CAAC;MAC5D,CAAC,MAAM,IAAIuC,UAAU,CAAC2K,MAAM,CAAC,EAAE;QAC7BC,SAAS,GAAGD,MAAM,CAAClN,KAAK,CAAC;MAC3B,CAAC,MAAM;QACLmN,SAAS,GAAG9I,KAAK,CAACqI,sBAAsB,CAAC1M,KAAK,CAAC;MACjD;MACA,OAAOmN,SAAS;IAClB;EACF,CAAC,EAAE;IACD5O,GAAG,EAAE,0BAA0B;IAC/BmD,KAAK,EAAE,SAAS2L,wBAAwBA,CAACC,SAAS,EAAEC,SAAS,EAAE;MAC7D,IAAItK,IAAI,GAAGqK,SAAS,CAACrK,IAAI;QACvBI,KAAK,GAAGiK,SAAS,CAACjK,KAAK;QACvBD,CAAC,GAAGkK,SAAS,CAAClK,CAAC;QACfE,cAAc,GAAGgK,SAAS,CAAChK,cAAc;QACzCkK,QAAQ,GAAGF,SAAS,CAACE,QAAQ;QAC7BtK,UAAU,GAAGoK,SAAS,CAACpK,UAAU;QACjCC,QAAQ,GAAGmK,SAAS,CAACnK,QAAQ;MAC/B,IAAIF,IAAI,KAAKsK,SAAS,CAACE,QAAQ,IAAID,QAAQ,KAAKD,SAAS,CAACG,YAAY,EAAE;QACtE,OAAOrO,aAAa,CAAC;UACnBoO,QAAQ,EAAExK,IAAI;UACd0K,kBAAkB,EAAErK,cAAc;UAClCoK,YAAY,EAAEF,QAAQ;UACtBI,KAAK,EAAExK,CAAC;UACRyK,SAAS,EAAExK;QACb,CAAC,EAAEJ,IAAI,IAAIA,IAAI,CAAC5E,MAAM,GAAG0E,WAAW,CAAC;UACnCE,IAAI,EAAEA,IAAI;UACVI,KAAK,EAAEA,KAAK;UACZD,CAAC,EAAEA,CAAC;UACJE,cAAc,EAAEA,cAAc;UAC9BJ,UAAU,EAAEA,UAAU;UACtBC,QAAQ,EAAEA;QACZ,CAAC,CAAC,GAAG;UACHK,KAAK,EAAE,IAAI;UACXE,WAAW,EAAE;QACf,CAAC,CAAC;MACJ;MACA,IAAI6J,SAAS,CAAC/J,KAAK,KAAKH,KAAK,KAAKkK,SAAS,CAACM,SAAS,IAAIzK,CAAC,KAAKmK,SAAS,CAACK,KAAK,IAAItK,cAAc,KAAKiK,SAAS,CAACI,kBAAkB,CAAC,EAAE;QAClIJ,SAAS,CAAC/J,KAAK,CAAChB,KAAK,CAAC,CAACY,CAAC,EAAEA,CAAC,GAAGC,KAAK,GAAGC,cAAc,CAAC,CAAC;QACtD,IAAII,WAAW,GAAG6J,SAAS,CAAC/J,KAAK,CAACC,MAAM,CAAC,CAAC,CAACE,GAAG,CAAC,UAAUC,KAAK,EAAE;UAC9D,OAAO2J,SAAS,CAAC/J,KAAK,CAACI,KAAK,CAAC;QAC/B,CAAC,CAAC;QACF,OAAO;UACL6J,QAAQ,EAAExK,IAAI;UACd0K,kBAAkB,EAAErK,cAAc;UAClCoK,YAAY,EAAEF,QAAQ;UACtBI,KAAK,EAAExK,CAAC;UACRyK,SAAS,EAAExK,KAAK;UAChBY,MAAM,EAAEsJ,SAAS,CAAC/J,KAAK,CAAC8J,SAAS,CAACpK,UAAU,CAAC;UAC7CgB,IAAI,EAAEqJ,SAAS,CAAC/J,KAAK,CAAC8J,SAAS,CAACnK,QAAQ,CAAC;UACzCO,WAAW,EAAEA;QACf,CAAC;MACH;MACA,OAAO,IAAI;IACb;EACF,CAAC,EAAE;IACDnF,GAAG,EAAE,iBAAiB;IACtBmD,KAAK,EAAE,SAAS4E,eAAeA,CAACwH,UAAU,EAAE1K,CAAC,EAAE;MAC7C,IAAIG,GAAG,GAAGuK,UAAU,CAACzP,MAAM;MAC3B,IAAI0P,KAAK,GAAG,CAAC;MACb,IAAIC,GAAG,GAAGzK,GAAG,GAAG,CAAC;MACjB,OAAOyK,GAAG,GAAGD,KAAK,GAAG,CAAC,EAAE;QACtB,IAAIE,MAAM,GAAG9H,IAAI,CAACyG,KAAK,CAAC,CAACmB,KAAK,GAAGC,GAAG,IAAI,CAAC,CAAC;QAC1C,IAAIF,UAAU,CAACG,MAAM,CAAC,GAAG7K,CAAC,EAAE;UAC1B4K,GAAG,GAAGC,MAAM;QACd,CAAC,MAAM;UACLF,KAAK,GAAGE,MAAM;QAChB;MACF;MACA,OAAO7K,CAAC,IAAI0K,UAAU,CAACE,GAAG,CAAC,GAAGA,GAAG,GAAGD,KAAK;IAC3C;EACF,CAAC,CAAC,CAAC;AACL,CAAC,CAAC5L,aAAa,CAAC;AAChB5C,eAAe,CAAC8E,KAAK,EAAE,aAAa,EAAE,OAAO,CAAC;AAC9C9E,eAAe,CAAC8E,KAAK,EAAE,cAAc,EAAE;EACrCmE,MAAM,EAAE,EAAE;EACVlF,cAAc,EAAE,CAAC;EACjB0C,GAAG,EAAE,CAAC;EACNyC,IAAI,EAAE,MAAM;EACZC,MAAM,EAAE,MAAM;EACdK,OAAO,EAAE;IACPmF,GAAG,EAAE,CAAC;IACNC,KAAK,EAAE,CAAC;IACRC,MAAM,EAAE,CAAC;IACTC,IAAI,EAAE;EACR,CAAC;EACDhJ,YAAY,EAAE,IAAI;EAClB6G,cAAc,EAAE;AAClB,CAAC,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}