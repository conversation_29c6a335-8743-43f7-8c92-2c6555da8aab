const express = require('express');
const router = express.Router();
const Order = require('../models/Order');
const Product = require('../models/Product');
const User = require('../models/User');
const { protect, admin } = require('../middleware/auth');

// Get admin dashboard analytics
router.get('/admin', protect, admin, async (req, res) => {
  try {
    // Get total counts
    const totalUsers = await User.countDocuments();
    const totalProducts = await Product.countDocuments();
    const totalOrders = await Order.countDocuments();
    
    // Get total revenue
    const revenueResult = await Order.aggregate([
      { $match: { isPaid: true } },
      { $group: { _id: null, total: { $sum: '$totalPrice' } } }
    ]);
    const totalRevenue = revenueResult[0]?.total || 0;

    // Get monthly data for the last 6 months
    const sixMonthsAgo = new Date();
    sixMonthsAgo.setMonth(sixMonthsAgo.getMonth() - 6);

    const monthlyData = await Order.aggregate([
      { $match: { createdAt: { $gte: sixMonthsAgo } } },
      {
        $group: {
          _id: {
            year: { $year: '$createdAt' },
            month: { $month: '$createdAt' }
          },
          orders: { $sum: 1 },
          revenue: { $sum: '$totalPrice' }
        }
      },
      { $sort: { '_id.year': 1, '_id.month': 1 } }
    ]);

    // Get recent orders
    const recentOrders = await Order.find()
      .populate('user', 'name email')
      .sort({ createdAt: -1 })
      .limit(10);

    // Get top selling products
    const topProducts = await Order.aggregate([
      { $unwind: '$orderItems' },
      {
        $group: {
          _id: '$orderItems.product',
          totalSold: { $sum: '$orderItems.qty' },
          revenue: { $sum: { $multiply: ['$orderItems.qty', '$orderItems.price'] } }
        }
      },
      { $sort: { totalSold: -1 } },
      { $limit: 5 },
      {
        $lookup: {
          from: 'products',
          localField: '_id',
          foreignField: '_id',
          as: 'productInfo'
        }
      }
    ]);

    // Calculate growth rates (mock data for demo)
    const lastMonthOrders = await Order.countDocuments({
      createdAt: { $gte: new Date(Date.now() - 30 * 24 * 60 * 60 * 1000) }
    });

    const analytics = {
      stats: {
        totalVisits: totalUsers * 38, // Mock calculation
        bounceRate: 46.41,
        pageViews: totalUsers * 4.4, // Mock calculation
        growthRate: ((lastMonthOrders / totalOrders) * 100).toFixed(2)
      },
      totals: {
        users: totalUsers,
        products: totalProducts,
        orders: totalOrders,
        revenue: totalRevenue
      },
      monthlyData: monthlyData.map(item => ({
        month: new Date(item._id.year, item._id.month - 1).toLocaleDateString('en-US', { month: 'short' }),
        orders: item.orders,
        revenue: item.revenue,
        users: Math.floor(item.orders * 1.5) // Mock user calculation
      })),
      recentOrders: recentOrders.map(order => ({
        id: order._id,
        customer: order.user?.name || 'Unknown',
        total: order.totalPrice,
        status: order.isDelivered ? 'Delivered' : order.isPaid ? 'Shipped' : 'Processing',
        date: order.createdAt.toISOString().split('T')[0],
        items: order.orderItems.length
      })),
      topProducts: topProducts.map(item => ({
        id: item._id,
        name: item.productInfo[0]?.name || 'Unknown Product',
        sales: item.totalSold,
        revenue: item.revenue
      })),
      trafficSources: [
        { name: 'Organic', value: 44.46, color: '#2196f3' },
        { name: 'Referral', value: 5.54, color: '#4caf50' },
        { name: 'Other', value: 50, color: '#ff9800' }
      ],
      browserStats: [
        { name: 'Google Chrome', percentage: 60, color: '#4285f4' },
        { name: 'Mozilla Firefox', percentage: 18, color: '#ff7139' },
        { name: 'Internet Explorer', percentage: 12, color: '#00bcf2' },
        { name: 'Safari', percentage: 10, color: '#000000' }
      ]
    };

    res.json(analytics);
  } catch (error) {
    console.error('Analytics error:', error);
    res.status(500).json({ message: error.message });
  }
});

// Get user dashboard analytics
router.get('/user/:userId', protect, async (req, res) => {
  try {
    const userId = req.params.userId;
    
    // Get user orders
    const userOrders = await Order.find({ user: userId }).sort({ createdAt: -1 });
    
    // Calculate user stats
    const totalOrders = userOrders.length;
    const totalSpent = userOrders.reduce((sum, order) => sum + order.totalPrice, 0);
    const loyaltyPoints = Math.floor(totalSpent * 2); // 2 points per dollar
    
    // Get recent orders
    const recentOrders = userOrders.slice(0, 5).map(order => ({
      id: order._id,
      date: order.createdAt.toISOString().split('T')[0],
      items: order.orderItems.map(item => item.name),
      total: order.totalPrice,
      status: order.isDelivered ? 'Delivered' : order.isPaid ? 'Shipped' : 'Processing'
    }));

    // Mock favorite products (in real app, this would be from user preferences)
    const favoriteProducts = await Product.find().limit(4);
    
    // Mock recommendations
    const recommendations = await Product.find().skip(4).limit(3);

    const userAnalytics = {
      user: {
        totalOrders,
        totalSpent,
        loyaltyPoints,
        nextRewardAt: 3000,
        memberSince: 'January 2023'
      },
      recentOrders,
      favoriteProducts: favoriteProducts.map(product => ({
        id: product._id,
        name: product.name,
        price: product.price,
        rating: product.rating || 4.5,
        inStock: product.countInStock > 0,
        image: product.image || '🌶️'
      })),
      recommendations: recommendations.map(product => ({
        id: product._id,
        name: product.name,
        price: product.price,
        discount: Math.floor(Math.random() * 20) + 10, // Random discount 10-30%
        image: product.image || '🌿'
      })),
      notifications: [
        { id: 1, message: 'Your recent order has been shipped', time: '2 hours ago', type: 'delivery' },
        { id: 2, message: 'New spices from Kerala are now available!', time: '1 day ago', type: 'promotion' },
        { id: 3, message: `You have ${loyaltyPoints} loyalty points`, time: '3 days ago', type: 'points' }
      ]
    };

    res.json(userAnalytics);
  } catch (error) {
    console.error('User analytics error:', error);
    res.status(500).json({ message: error.message });
  }
});

module.exports = router;
