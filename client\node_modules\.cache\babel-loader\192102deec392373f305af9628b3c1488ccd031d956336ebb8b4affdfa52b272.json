{"ast": null, "code": "var _jsxFileName = \"D:\\\\ecommerce\\\\client\\\\src\\\\components\\\\user\\\\UserDashboard.js\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect } from 'react';\nimport { Box, Grid, Paper, Typography, Card, CardContent, CardMedia, Button, Avatar, Chip, Table, TableBody, TableCell, TableContainer, TableHead, TableRow, LinearProgress, Divider, List, ListItem, ListItemText, ListItemAvatar, IconButton, Badge, CircularProgress, Alert } from '@mui/material';\nimport { ShoppingBag, FavoriteRounded, LocalShipping, Star, Notifications, AccountCircle, CreditCard, LocationOn, Settings, History, TrendingUp, ShoppingCart } from '@mui/icons-material';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst UserDashboard = () => {\n  _s();\n  const [user] = useState({\n    name: '<PERSON>',\n    email: '<EMAIL>',\n    avatar: '/api/placeholder/100/100',\n    memberSince: 'January 2023',\n    totalOrders: 24,\n    totalSpent: 1247.50,\n    loyaltyPoints: 2450,\n    nextRewardAt: 3000\n  });\n  const recentOrders = [{\n    id: 'ORD001',\n    date: '2024-01-15',\n    items: ['Cinnamon Sticks', 'Black Pepper'],\n    total: 34.99,\n    status: 'Delivered',\n    image: '🍯'\n  }, {\n    id: 'ORD002',\n    date: '2024-01-10',\n    items: ['Turmeric Powder', 'Cardamom'],\n    total: 28.50,\n    status: 'Shipped',\n    image: '🟡'\n  }, {\n    id: 'ORD003',\n    date: '2024-01-05',\n    items: ['Saffron Threads'],\n    total: 89.99,\n    status: 'Processing',\n    image: '🟠'\n  }];\n  const favoriteProducts = [{\n    id: 1,\n    name: 'Ceylon Cinnamon',\n    price: 15.99,\n    image: '🍯',\n    rating: 4.8,\n    inStock: true\n  }, {\n    id: 2,\n    name: 'Black Peppercorns',\n    price: 12.50,\n    image: '⚫',\n    rating: 4.9,\n    inStock: true\n  }, {\n    id: 3,\n    name: 'Organic Turmeric',\n    price: 18.75,\n    image: '🟡',\n    rating: 4.7,\n    inStock: false\n  }, {\n    id: 4,\n    name: 'Cardamom Pods',\n    price: 24.99,\n    image: '🟢',\n    rating: 4.6,\n    inStock: true\n  }];\n  const recommendations = [{\n    id: 1,\n    name: 'Star Anise',\n    price: 16.99,\n    image: '⭐',\n    discount: 15\n  }, {\n    id: 2,\n    name: 'Cloves',\n    price: 13.50,\n    image: '🟤',\n    discount: 10\n  }, {\n    id: 3,\n    name: 'Nutmeg',\n    price: 19.99,\n    image: '🥜',\n    discount: 20\n  }];\n  const notifications = [{\n    id: 1,\n    message: 'Your order #ORD001 has been delivered',\n    time: '2 hours ago',\n    type: 'delivery'\n  }, {\n    id: 2,\n    message: 'New spices from Kerala are now available!',\n    time: '1 day ago',\n    type: 'promotion'\n  }, {\n    id: 3,\n    message: 'You have 550 points expiring soon',\n    time: '3 days ago',\n    type: 'points'\n  }];\n  const getStatusColor = status => {\n    switch (status) {\n      case 'Delivered':\n        return 'success';\n      case 'Shipped':\n        return 'info';\n      case 'Processing':\n        return 'warning';\n      case 'Cancelled':\n        return 'error';\n      default:\n        return 'default';\n    }\n  };\n  const loyaltyProgress = user.loyaltyPoints / user.nextRewardAt * 100;\n  return /*#__PURE__*/_jsxDEV(Box, {\n    sx: {\n      p: 3,\n      backgroundColor: '#f8f9fa',\n      minHeight: '100vh'\n    },\n    children: [/*#__PURE__*/_jsxDEV(Typography, {\n      variant: \"h4\",\n      sx: {\n        mb: 3,\n        fontWeight: 'bold'\n      },\n      children: [\"Welcome back, \", user.name, \"! \\uD83D\\uDC4B\"]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 117,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Grid, {\n      container: true,\n      spacing: 3,\n      children: [/*#__PURE__*/_jsxDEV(Grid, {\n        item: true,\n        xs: 12,\n        md: 4,\n        children: /*#__PURE__*/_jsxDEV(Paper, {\n          sx: {\n            p: 3,\n            textAlign: 'center',\n            height: 'fit-content'\n          },\n          children: [/*#__PURE__*/_jsxDEV(Avatar, {\n            src: user.avatar,\n            sx: {\n              width: 80,\n              height: 80,\n              mx: 'auto',\n              mb: 2\n            }\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 125,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Typography, {\n            variant: \"h6\",\n            sx: {\n              fontWeight: 'bold'\n            },\n            children: user.name\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 129,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Typography, {\n            variant: \"body2\",\n            color: \"textSecondary\",\n            sx: {\n              mb: 2\n            },\n            children: user.email\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 132,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Typography, {\n            variant: \"caption\",\n            color: \"textSecondary\",\n            children: [\"Member since \", user.memberSince]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 135,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Divider, {\n            sx: {\n              my: 2\n            }\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 139,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Grid, {\n            container: true,\n            spacing: 2,\n            sx: {\n              textAlign: 'center'\n            },\n            children: [/*#__PURE__*/_jsxDEV(Grid, {\n              item: true,\n              xs: 4,\n              children: [/*#__PURE__*/_jsxDEV(Typography, {\n                variant: \"h6\",\n                color: \"primary\",\n                children: user.totalOrders\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 143,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                variant: \"caption\",\n                children: \"Orders\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 144,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 142,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Grid, {\n              item: true,\n              xs: 4,\n              children: [/*#__PURE__*/_jsxDEV(Typography, {\n                variant: \"h6\",\n                color: \"primary\",\n                children: [\"$\", user.totalSpent]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 147,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                variant: \"caption\",\n                children: \"Spent\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 148,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 146,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Grid, {\n              item: true,\n              xs: 4,\n              children: [/*#__PURE__*/_jsxDEV(Typography, {\n                variant: \"h6\",\n                color: \"primary\",\n                children: user.loyaltyPoints\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 151,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                variant: \"caption\",\n                children: \"Points\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 152,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 150,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 141,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Button, {\n            variant: \"contained\",\n            fullWidth: true,\n            sx: {\n              mt: 2\n            },\n            children: [/*#__PURE__*/_jsxDEV(Settings, {\n              sx: {\n                mr: 1\n              }\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 157,\n              columnNumber: 15\n            }, this), \"Edit Profile\"]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 156,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 124,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 123,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Grid, {\n        item: true,\n        xs: 12,\n        md: 8,\n        children: [/*#__PURE__*/_jsxDEV(Grid, {\n          container: true,\n          spacing: 2,\n          sx: {\n            mb: 3\n          },\n          children: [/*#__PURE__*/_jsxDEV(Grid, {\n            item: true,\n            xs: 6,\n            sm: 3,\n            children: /*#__PURE__*/_jsxDEV(Card, {\n              sx: {\n                p: 2,\n                textAlign: 'center',\n                backgroundColor: '#e3f2fd'\n              },\n              children: [/*#__PURE__*/_jsxDEV(ShoppingBag, {\n                sx: {\n                  fontSize: 40,\n                  color: '#1976d2',\n                  mb: 1\n                }\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 168,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                variant: \"h6\",\n                children: user.totalOrders\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 169,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                variant: \"caption\",\n                children: \"Total Orders\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 170,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 167,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 166,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Grid, {\n            item: true,\n            xs: 6,\n            sm: 3,\n            children: /*#__PURE__*/_jsxDEV(Card, {\n              sx: {\n                p: 2,\n                textAlign: 'center',\n                backgroundColor: '#f3e5f5'\n              },\n              children: [/*#__PURE__*/_jsxDEV(FavoriteRounded, {\n                sx: {\n                  fontSize: 40,\n                  color: '#7b1fa2',\n                  mb: 1\n                }\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 175,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                variant: \"h6\",\n                children: favoriteProducts.length\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 176,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                variant: \"caption\",\n                children: \"Favorites\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 177,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 174,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 173,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Grid, {\n            item: true,\n            xs: 6,\n            sm: 3,\n            children: /*#__PURE__*/_jsxDEV(Card, {\n              sx: {\n                p: 2,\n                textAlign: 'center',\n                backgroundColor: '#e8f5e8'\n              },\n              children: [/*#__PURE__*/_jsxDEV(LocalShipping, {\n                sx: {\n                  fontSize: 40,\n                  color: '#388e3c',\n                  mb: 1\n                }\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 182,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                variant: \"h6\",\n                children: \"3\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 183,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                variant: \"caption\",\n                children: \"In Transit\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 184,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 181,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 180,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Grid, {\n            item: true,\n            xs: 6,\n            sm: 3,\n            children: /*#__PURE__*/_jsxDEV(Card, {\n              sx: {\n                p: 2,\n                textAlign: 'center',\n                backgroundColor: '#fff3e0'\n              },\n              children: [/*#__PURE__*/_jsxDEV(Star, {\n                sx: {\n                  fontSize: 40,\n                  color: '#f57c00',\n                  mb: 1\n                }\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 189,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                variant: \"h6\",\n                children: \"4.8\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 190,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                variant: \"caption\",\n                children: \"Avg Rating\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 191,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 188,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 187,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 165,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Paper, {\n          sx: {\n            p: 3,\n            mb: 3\n          },\n          children: [/*#__PURE__*/_jsxDEV(Typography, {\n            variant: \"h6\",\n            sx: {\n              mb: 2\n            },\n            children: \"\\uD83C\\uDFAF Loyalty Program\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 198,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Box, {\n            sx: {\n              display: 'flex',\n              justifyContent: 'space-between',\n              mb: 1\n            },\n            children: [/*#__PURE__*/_jsxDEV(Typography, {\n              variant: \"body2\",\n              children: [user.loyaltyPoints, \" / \", user.nextRewardAt, \" points\"]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 202,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Typography, {\n              variant: \"body2\",\n              color: \"primary\",\n              children: [user.nextRewardAt - user.loyaltyPoints, \" points to next reward\"]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 205,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 201,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(LinearProgress, {\n            variant: \"determinate\",\n            value: loyaltyProgress,\n            sx: {\n              height: 8,\n              borderRadius: 4,\n              mb: 2\n            }\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 209,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Typography, {\n            variant: \"caption\",\n            color: \"textSecondary\",\n            children: \"Earn points with every purchase and unlock exclusive rewards!\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 214,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 197,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 164,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Grid, {\n        item: true,\n        xs: 12,\n        md: 8,\n        children: /*#__PURE__*/_jsxDEV(Paper, {\n          sx: {\n            p: 3\n          },\n          children: [/*#__PURE__*/_jsxDEV(Box, {\n            sx: {\n              display: 'flex',\n              justifyContent: 'space-between',\n              alignItems: 'center',\n              mb: 2\n            },\n            children: [/*#__PURE__*/_jsxDEV(Typography, {\n              variant: \"h6\",\n              children: \"Recent Orders\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 224,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Button, {\n              variant: \"outlined\",\n              size: \"small\",\n              children: [/*#__PURE__*/_jsxDEV(History, {\n                sx: {\n                  mr: 1\n                }\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 226,\n                columnNumber: 17\n              }, this), \"View All\"]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 225,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 223,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(TableContainer, {\n            children: /*#__PURE__*/_jsxDEV(Table, {\n              children: [/*#__PURE__*/_jsxDEV(TableHead, {\n                children: /*#__PURE__*/_jsxDEV(TableRow, {\n                  children: [/*#__PURE__*/_jsxDEV(TableCell, {\n                    children: \"Order\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 234,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n                    children: \"Items\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 235,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n                    children: \"Date\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 236,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n                    children: \"Total\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 237,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n                    children: \"Status\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 238,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n                    children: \"Action\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 239,\n                    columnNumber: 21\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 233,\n                  columnNumber: 19\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 232,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(TableBody, {\n                children: recentOrders.map(order => /*#__PURE__*/_jsxDEV(TableRow, {\n                  children: [/*#__PURE__*/_jsxDEV(TableCell, {\n                    children: /*#__PURE__*/_jsxDEV(Box, {\n                      sx: {\n                        display: 'flex',\n                        alignItems: 'center'\n                      },\n                      children: [/*#__PURE__*/_jsxDEV(Typography, {\n                        sx: {\n                          mr: 1,\n                          fontSize: '1.2em'\n                        },\n                        children: order.image\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 247,\n                        columnNumber: 27\n                      }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                        variant: \"body2\",\n                        children: order.id\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 248,\n                        columnNumber: 27\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 246,\n                      columnNumber: 25\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 245,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n                    children: /*#__PURE__*/_jsxDEV(Typography, {\n                      variant: \"body2\",\n                      children: order.items.join(', ')\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 252,\n                      columnNumber: 25\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 251,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n                    children: order.date\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 256,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n                    children: [\"$\", order.total]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 257,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n                    children: /*#__PURE__*/_jsxDEV(Chip, {\n                      label: order.status,\n                      color: getStatusColor(order.status),\n                      size: \"small\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 259,\n                      columnNumber: 25\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 258,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n                    children: /*#__PURE__*/_jsxDEV(Button, {\n                      size: \"small\",\n                      variant: \"outlined\",\n                      children: \"Track\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 266,\n                      columnNumber: 25\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 265,\n                    columnNumber: 23\n                  }, this)]\n                }, order.id, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 244,\n                  columnNumber: 21\n                }, this))\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 242,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 231,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 230,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 222,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 221,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Grid, {\n        item: true,\n        xs: 12,\n        md: 4,\n        children: /*#__PURE__*/_jsxDEV(Paper, {\n          sx: {\n            p: 3,\n            height: 'fit-content'\n          },\n          children: [/*#__PURE__*/_jsxDEV(Box, {\n            sx: {\n              display: 'flex',\n              alignItems: 'center',\n              mb: 2\n            },\n            children: [/*#__PURE__*/_jsxDEV(Badge, {\n              badgeContent: notifications.length,\n              color: \"error\",\n              children: /*#__PURE__*/_jsxDEV(Notifications, {\n                color: \"action\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 283,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 282,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Typography, {\n              variant: \"h6\",\n              sx: {\n                ml: 1\n              },\n              children: \"Notifications\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 285,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 281,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(List, {\n            dense: true,\n            children: notifications.map(notification => /*#__PURE__*/_jsxDEV(ListItem, {\n              sx: {\n                px: 0\n              },\n              children: [/*#__PURE__*/_jsxDEV(ListItemAvatar, {\n                children: /*#__PURE__*/_jsxDEV(Avatar, {\n                  sx: {\n                    width: 32,\n                    height: 32,\n                    backgroundColor: '#e3f2fd'\n                  },\n                  children: [notification.type === 'delivery' && /*#__PURE__*/_jsxDEV(LocalShipping, {\n                    sx: {\n                      fontSize: 16\n                    }\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 294,\n                    columnNumber: 60\n                  }, this), notification.type === 'promotion' && /*#__PURE__*/_jsxDEV(TrendingUp, {\n                    sx: {\n                      fontSize: 16\n                    }\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 295,\n                    columnNumber: 61\n                  }, this), notification.type === 'points' && /*#__PURE__*/_jsxDEV(Star, {\n                    sx: {\n                      fontSize: 16\n                    }\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 296,\n                    columnNumber: 58\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 293,\n                  columnNumber: 21\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 292,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(ListItemText, {\n                primary: /*#__PURE__*/_jsxDEV(Typography, {\n                  variant: \"body2\",\n                  sx: {\n                    fontSize: '0.875rem'\n                  },\n                  children: notification.message\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 301,\n                  columnNumber: 23\n                }, this),\n                secondary: notification.time\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 299,\n                columnNumber: 19\n              }, this)]\n            }, notification.id, true, {\n              fileName: _jsxFileName,\n              lineNumber: 291,\n              columnNumber: 17\n            }, this))\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 289,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 280,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 279,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Grid, {\n        item: true,\n        xs: 12,\n        md: 6,\n        children: /*#__PURE__*/_jsxDEV(Paper, {\n          sx: {\n            p: 3\n          },\n          children: [/*#__PURE__*/_jsxDEV(Typography, {\n            variant: \"h6\",\n            sx: {\n              mb: 2\n            },\n            children: \"\\u2764\\uFE0F Your Favorites\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 316,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Grid, {\n            container: true,\n            spacing: 2,\n            children: favoriteProducts.map(product => /*#__PURE__*/_jsxDEV(Grid, {\n              item: true,\n              xs: 12,\n              sm: 6,\n              children: /*#__PURE__*/_jsxDEV(Card, {\n                sx: {\n                  display: 'flex',\n                  alignItems: 'center',\n                  p: 1\n                },\n                children: [/*#__PURE__*/_jsxDEV(Typography, {\n                  sx: {\n                    fontSize: '2em',\n                    mr: 2\n                  },\n                  children: product.image\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 323,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(Box, {\n                  sx: {\n                    flexGrow: 1\n                  },\n                  children: [/*#__PURE__*/_jsxDEV(Typography, {\n                    variant: \"body2\",\n                    sx: {\n                      fontWeight: 'bold'\n                    },\n                    children: product.name\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 325,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                    variant: \"caption\",\n                    color: \"textSecondary\",\n                    children: [\"$\", product.price, \" \\u2022 \\u2B50 \", product.rating]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 328,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(Box, {\n                    sx: {\n                      mt: 1\n                    },\n                    children: /*#__PURE__*/_jsxDEV(Button, {\n                      size: \"small\",\n                      variant: \"contained\",\n                      disabled: !product.inStock,\n                      sx: {\n                        fontSize: '0.75rem',\n                        py: 0.5\n                      },\n                      children: [/*#__PURE__*/_jsxDEV(ShoppingCart, {\n                        sx: {\n                          fontSize: 14,\n                          mr: 0.5\n                        }\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 338,\n                        columnNumber: 27\n                      }, this), product.inStock ? 'Add to Cart' : 'Out of Stock']\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 332,\n                      columnNumber: 25\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 331,\n                    columnNumber: 23\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 324,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 322,\n                columnNumber: 19\n              }, this)\n            }, product.id, false, {\n              fileName: _jsxFileName,\n              lineNumber: 321,\n              columnNumber: 17\n            }, this))\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 319,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 315,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 314,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Grid, {\n        item: true,\n        xs: 12,\n        md: 6,\n        children: /*#__PURE__*/_jsxDEV(Paper, {\n          sx: {\n            p: 3\n          },\n          children: [/*#__PURE__*/_jsxDEV(Typography, {\n            variant: \"h6\",\n            sx: {\n              mb: 2\n            },\n            children: \"\\uD83C\\uDF1F Recommended for You\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 353,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Grid, {\n            container: true,\n            spacing: 2,\n            children: recommendations.map(product => /*#__PURE__*/_jsxDEV(Grid, {\n              item: true,\n              xs: 12,\n              children: /*#__PURE__*/_jsxDEV(Card, {\n                sx: {\n                  display: 'flex',\n                  alignItems: 'center',\n                  p: 2\n                },\n                children: [/*#__PURE__*/_jsxDEV(Typography, {\n                  sx: {\n                    fontSize: '2em',\n                    mr: 2\n                  },\n                  children: product.image\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 360,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(Box, {\n                  sx: {\n                    flexGrow: 1\n                  },\n                  children: [/*#__PURE__*/_jsxDEV(Typography, {\n                    variant: \"body1\",\n                    sx: {\n                      fontWeight: 'bold'\n                    },\n                    children: product.name\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 362,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(Box, {\n                    sx: {\n                      display: 'flex',\n                      alignItems: 'center',\n                      gap: 1\n                    },\n                    children: [/*#__PURE__*/_jsxDEV(Typography, {\n                      variant: \"h6\",\n                      color: \"primary\",\n                      children: [\"$\", product.price]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 366,\n                      columnNumber: 25\n                    }, this), /*#__PURE__*/_jsxDEV(Chip, {\n                      label: `${product.discount}% OFF`,\n                      color: \"error\",\n                      size: \"small\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 369,\n                      columnNumber: 25\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 365,\n                    columnNumber: 23\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 361,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(Button, {\n                  variant: \"contained\",\n                  size: \"small\",\n                  children: \"Add to Cart\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 376,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 359,\n                columnNumber: 19\n              }, this)\n            }, product.id, false, {\n              fileName: _jsxFileName,\n              lineNumber: 358,\n              columnNumber: 17\n            }, this))\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 356,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 352,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 351,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 121,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 116,\n    columnNumber: 5\n  }, this);\n};\n_s(UserDashboard, \"wToFNh+1swbsHZuxFc3ga4OOEaw=\");\n_c = UserDashboard;\nexport default UserDashboard;\nvar _c;\n$RefreshReg$(_c, \"UserDashboard\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "Box", "Grid", "Paper", "Typography", "Card", "<PERSON><PERSON><PERSON><PERSON>", "CardMedia", "<PERSON><PERSON>", "Avatar", "Chip", "Table", "TableBody", "TableCell", "TableContainer", "TableHead", "TableRow", "LinearProgress", "Divider", "List", "ListItem", "ListItemText", "ListItemAvatar", "IconButton", "Badge", "CircularProgress", "<PERSON><PERSON>", "ShoppingBag", "FavoriteRounded", "LocalShipping", "Star", "Notifications", "AccountCircle", "CreditCard", "LocationOn", "Settings", "History", "TrendingUp", "ShoppingCart", "jsxDEV", "_jsxDEV", "UserDashboard", "_s", "user", "name", "email", "avatar", "memberSince", "totalOrders", "totalSpent", "loyaltyPoints", "nextRewardAt", "recentOrders", "id", "date", "items", "total", "status", "image", "favoriteProducts", "price", "rating", "inStock", "recommendations", "discount", "notifications", "message", "time", "type", "getStatusColor", "loyaltyProgress", "sx", "p", "backgroundColor", "minHeight", "children", "variant", "mb", "fontWeight", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "container", "spacing", "item", "xs", "md", "textAlign", "height", "src", "width", "mx", "color", "my", "fullWidth", "mt", "mr", "sm", "fontSize", "length", "display", "justifyContent", "value", "borderRadius", "alignItems", "size", "map", "order", "join", "label", "badgeContent", "ml", "dense", "notification", "px", "primary", "secondary", "product", "flexGrow", "disabled", "py", "gap", "_c", "$RefreshReg$"], "sources": ["D:/ecommerce/client/src/components/user/UserDashboard.js"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\nimport {\n  Box,\n  Grid,\n  Paper,\n  Typography,\n  Card,\n  CardContent,\n  CardMedia,\n  Button,\n  Avatar,\n  Chip,\n  Table,\n  TableBody,\n  TableCell,\n  TableContainer,\n  TableHead,\n  TableRow,\n  LinearProgress,\n  Divider,\n  List,\n  ListItem,\n  ListItemText,\n  ListItemAvatar,\n  IconButton,\n  Badge,\n  CircularProgress,\n  Alert,\n} from '@mui/material';\nimport {\n  ShoppingBag,\n  FavoriteRounded,\n  LocalShipping,\n  Star,\n  Notifications,\n  AccountCircle,\n  CreditCard,\n  LocationOn,\n  Settings,\n  History,\n  TrendingUp,\n  ShoppingCart,\n} from '@mui/icons-material';\n\nconst UserDashboard = () => {\n  const [user] = useState({\n    name: '<PERSON>',\n    email: '<EMAIL>',\n    avatar: '/api/placeholder/100/100',\n    memberSince: 'January 2023',\n    totalOrders: 24,\n    totalSpent: 1247.50,\n    loyaltyPoints: 2450,\n    nextRewardAt: 3000,\n  });\n\n  const recentOrders = [\n    {\n      id: 'ORD001',\n      date: '2024-01-15',\n      items: ['Cinnamon Sticks', 'Black Pepper'],\n      total: 34.99,\n      status: 'Delivered',\n      image: '🍯'\n    },\n    {\n      id: 'ORD002',\n      date: '2024-01-10',\n      items: ['Turmeric Powder', 'Cardamom'],\n      total: 28.50,\n      status: 'Shipped',\n      image: '🟡'\n    },\n    {\n      id: 'ORD003',\n      date: '2024-01-05',\n      items: ['Saffron Threads'],\n      total: 89.99,\n      status: 'Processing',\n      image: '🟠'\n    },\n  ];\n\n  const favoriteProducts = [\n    { id: 1, name: 'Ceylon Cinnamon', price: 15.99, image: '🍯', rating: 4.8, inStock: true },\n    { id: 2, name: 'Black Peppercorns', price: 12.50, image: '⚫', rating: 4.9, inStock: true },\n    { id: 3, name: 'Organic Turmeric', price: 18.75, image: '🟡', rating: 4.7, inStock: false },\n    { id: 4, name: 'Cardamom Pods', price: 24.99, image: '🟢', rating: 4.6, inStock: true },\n  ];\n\n  const recommendations = [\n    { id: 1, name: 'Star Anise', price: 16.99, image: '⭐', discount: 15 },\n    { id: 2, name: 'Cloves', price: 13.50, image: '🟤', discount: 10 },\n    { id: 3, name: 'Nutmeg', price: 19.99, image: '🥜', discount: 20 },\n  ];\n\n  const notifications = [\n    { id: 1, message: 'Your order #ORD001 has been delivered', time: '2 hours ago', type: 'delivery' },\n    { id: 2, message: 'New spices from Kerala are now available!', time: '1 day ago', type: 'promotion' },\n    { id: 3, message: 'You have 550 points expiring soon', time: '3 days ago', type: 'points' },\n  ];\n\n  const getStatusColor = (status) => {\n    switch (status) {\n      case 'Delivered': return 'success';\n      case 'Shipped': return 'info';\n      case 'Processing': return 'warning';\n      case 'Cancelled': return 'error';\n      default: return 'default';\n    }\n  };\n\n  const loyaltyProgress = (user.loyaltyPoints / user.nextRewardAt) * 100;\n\n  return (\n    <Box sx={{ p: 3, backgroundColor: '#f8f9fa', minHeight: '100vh' }}>\n      <Typography variant=\"h4\" sx={{ mb: 3, fontWeight: 'bold' }}>\n        Welcome back, {user.name}! 👋\n      </Typography>\n\n      <Grid container spacing={3}>\n        {/* User Profile Card */}\n        <Grid item xs={12} md={4}>\n          <Paper sx={{ p: 3, textAlign: 'center', height: 'fit-content' }}>\n            <Avatar\n              src={user.avatar}\n              sx={{ width: 80, height: 80, mx: 'auto', mb: 2 }}\n            />\n            <Typography variant=\"h6\" sx={{ fontWeight: 'bold' }}>\n              {user.name}\n            </Typography>\n            <Typography variant=\"body2\" color=\"textSecondary\" sx={{ mb: 2 }}>\n              {user.email}\n            </Typography>\n            <Typography variant=\"caption\" color=\"textSecondary\">\n              Member since {user.memberSince}\n            </Typography>\n\n            <Divider sx={{ my: 2 }} />\n\n            <Grid container spacing={2} sx={{ textAlign: 'center' }}>\n              <Grid item xs={4}>\n                <Typography variant=\"h6\" color=\"primary\">{user.totalOrders}</Typography>\n                <Typography variant=\"caption\">Orders</Typography>\n              </Grid>\n              <Grid item xs={4}>\n                <Typography variant=\"h6\" color=\"primary\">${user.totalSpent}</Typography>\n                <Typography variant=\"caption\">Spent</Typography>\n              </Grid>\n              <Grid item xs={4}>\n                <Typography variant=\"h6\" color=\"primary\">{user.loyaltyPoints}</Typography>\n                <Typography variant=\"caption\">Points</Typography>\n              </Grid>\n            </Grid>\n\n            <Button variant=\"contained\" fullWidth sx={{ mt: 2 }}>\n              <Settings sx={{ mr: 1 }} />\n              Edit Profile\n            </Button>\n          </Paper>\n        </Grid>\n\n        {/* Quick Stats */}\n        <Grid item xs={12} md={8}>\n          <Grid container spacing={2} sx={{ mb: 3 }}>\n            <Grid item xs={6} sm={3}>\n              <Card sx={{ p: 2, textAlign: 'center', backgroundColor: '#e3f2fd' }}>\n                <ShoppingBag sx={{ fontSize: 40, color: '#1976d2', mb: 1 }} />\n                <Typography variant=\"h6\">{user.totalOrders}</Typography>\n                <Typography variant=\"caption\">Total Orders</Typography>\n              </Card>\n            </Grid>\n            <Grid item xs={6} sm={3}>\n              <Card sx={{ p: 2, textAlign: 'center', backgroundColor: '#f3e5f5' }}>\n                <FavoriteRounded sx={{ fontSize: 40, color: '#7b1fa2', mb: 1 }} />\n                <Typography variant=\"h6\">{favoriteProducts.length}</Typography>\n                <Typography variant=\"caption\">Favorites</Typography>\n              </Card>\n            </Grid>\n            <Grid item xs={6} sm={3}>\n              <Card sx={{ p: 2, textAlign: 'center', backgroundColor: '#e8f5e8' }}>\n                <LocalShipping sx={{ fontSize: 40, color: '#388e3c', mb: 1 }} />\n                <Typography variant=\"h6\">3</Typography>\n                <Typography variant=\"caption\">In Transit</Typography>\n              </Card>\n            </Grid>\n            <Grid item xs={6} sm={3}>\n              <Card sx={{ p: 2, textAlign: 'center', backgroundColor: '#fff3e0' }}>\n                <Star sx={{ fontSize: 40, color: '#f57c00', mb: 1 }} />\n                <Typography variant=\"h6\">4.8</Typography>\n                <Typography variant=\"caption\">Avg Rating</Typography>\n              </Card>\n            </Grid>\n          </Grid>\n\n          {/* Loyalty Program */}\n          <Paper sx={{ p: 3, mb: 3 }}>\n            <Typography variant=\"h6\" sx={{ mb: 2 }}>\n              🎯 Loyalty Program\n            </Typography>\n            <Box sx={{ display: 'flex', justifyContent: 'space-between', mb: 1 }}>\n              <Typography variant=\"body2\">\n                {user.loyaltyPoints} / {user.nextRewardAt} points\n              </Typography>\n              <Typography variant=\"body2\" color=\"primary\">\n                {user.nextRewardAt - user.loyaltyPoints} points to next reward\n              </Typography>\n            </Box>\n            <LinearProgress\n              variant=\"determinate\"\n              value={loyaltyProgress}\n              sx={{ height: 8, borderRadius: 4, mb: 2 }}\n            />\n            <Typography variant=\"caption\" color=\"textSecondary\">\n              Earn points with every purchase and unlock exclusive rewards!\n            </Typography>\n          </Paper>\n        </Grid>\n\n        {/* Recent Orders */}\n        <Grid item xs={12} md={8}>\n          <Paper sx={{ p: 3 }}>\n            <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 2 }}>\n              <Typography variant=\"h6\">Recent Orders</Typography>\n              <Button variant=\"outlined\" size=\"small\">\n                <History sx={{ mr: 1 }} />\n                View All\n              </Button>\n            </Box>\n            <TableContainer>\n              <Table>\n                <TableHead>\n                  <TableRow>\n                    <TableCell>Order</TableCell>\n                    <TableCell>Items</TableCell>\n                    <TableCell>Date</TableCell>\n                    <TableCell>Total</TableCell>\n                    <TableCell>Status</TableCell>\n                    <TableCell>Action</TableCell>\n                  </TableRow>\n                </TableHead>\n                <TableBody>\n                  {recentOrders.map((order) => (\n                    <TableRow key={order.id}>\n                      <TableCell>\n                        <Box sx={{ display: 'flex', alignItems: 'center' }}>\n                          <Typography sx={{ mr: 1, fontSize: '1.2em' }}>{order.image}</Typography>\n                          <Typography variant=\"body2\">{order.id}</Typography>\n                        </Box>\n                      </TableCell>\n                      <TableCell>\n                        <Typography variant=\"body2\">\n                          {order.items.join(', ')}\n                        </Typography>\n                      </TableCell>\n                      <TableCell>{order.date}</TableCell>\n                      <TableCell>${order.total}</TableCell>\n                      <TableCell>\n                        <Chip\n                          label={order.status}\n                          color={getStatusColor(order.status)}\n                          size=\"small\"\n                        />\n                      </TableCell>\n                      <TableCell>\n                        <Button size=\"small\" variant=\"outlined\">\n                          Track\n                        </Button>\n                      </TableCell>\n                    </TableRow>\n                  ))}\n                </TableBody>\n              </Table>\n            </TableContainer>\n          </Paper>\n        </Grid>\n\n        {/* Notifications */}\n        <Grid item xs={12} md={4}>\n          <Paper sx={{ p: 3, height: 'fit-content' }}>\n            <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>\n              <Badge badgeContent={notifications.length} color=\"error\">\n                <Notifications color=\"action\" />\n              </Badge>\n              <Typography variant=\"h6\" sx={{ ml: 1 }}>\n                Notifications\n              </Typography>\n            </Box>\n            <List dense>\n              {notifications.map((notification) => (\n                <ListItem key={notification.id} sx={{ px: 0 }}>\n                  <ListItemAvatar>\n                    <Avatar sx={{ width: 32, height: 32, backgroundColor: '#e3f2fd' }}>\n                      {notification.type === 'delivery' && <LocalShipping sx={{ fontSize: 16 }} />}\n                      {notification.type === 'promotion' && <TrendingUp sx={{ fontSize: 16 }} />}\n                      {notification.type === 'points' && <Star sx={{ fontSize: 16 }} />}\n                    </Avatar>\n                  </ListItemAvatar>\n                  <ListItemText\n                    primary={\n                      <Typography variant=\"body2\" sx={{ fontSize: '0.875rem' }}>\n                        {notification.message}\n                      </Typography>\n                    }\n                    secondary={notification.time}\n                  />\n                </ListItem>\n              ))}\n            </List>\n          </Paper>\n        </Grid>\n\n        {/* Favorite Products */}\n        <Grid item xs={12} md={6}>\n          <Paper sx={{ p: 3 }}>\n            <Typography variant=\"h6\" sx={{ mb: 2 }}>\n              ❤️ Your Favorites\n            </Typography>\n            <Grid container spacing={2}>\n              {favoriteProducts.map((product) => (\n                <Grid item xs={12} sm={6} key={product.id}>\n                  <Card sx={{ display: 'flex', alignItems: 'center', p: 1 }}>\n                    <Typography sx={{ fontSize: '2em', mr: 2 }}>{product.image}</Typography>\n                    <Box sx={{ flexGrow: 1 }}>\n                      <Typography variant=\"body2\" sx={{ fontWeight: 'bold' }}>\n                        {product.name}\n                      </Typography>\n                      <Typography variant=\"caption\" color=\"textSecondary\">\n                        ${product.price} • ⭐ {product.rating}\n                      </Typography>\n                      <Box sx={{ mt: 1 }}>\n                        <Button\n                          size=\"small\"\n                          variant=\"contained\"\n                          disabled={!product.inStock}\n                          sx={{ fontSize: '0.75rem', py: 0.5 }}\n                        >\n                          <ShoppingCart sx={{ fontSize: 14, mr: 0.5 }} />\n                          {product.inStock ? 'Add to Cart' : 'Out of Stock'}\n                        </Button>\n                      </Box>\n                    </Box>\n                  </Card>\n                </Grid>\n              ))}\n            </Grid>\n          </Paper>\n        </Grid>\n\n        {/* Recommendations */}\n        <Grid item xs={12} md={6}>\n          <Paper sx={{ p: 3 }}>\n            <Typography variant=\"h6\" sx={{ mb: 2 }}>\n              🌟 Recommended for You\n            </Typography>\n            <Grid container spacing={2}>\n              {recommendations.map((product) => (\n                <Grid item xs={12} key={product.id}>\n                  <Card sx={{ display: 'flex', alignItems: 'center', p: 2 }}>\n                    <Typography sx={{ fontSize: '2em', mr: 2 }}>{product.image}</Typography>\n                    <Box sx={{ flexGrow: 1 }}>\n                      <Typography variant=\"body1\" sx={{ fontWeight: 'bold' }}>\n                        {product.name}\n                      </Typography>\n                      <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>\n                        <Typography variant=\"h6\" color=\"primary\">\n                          ${product.price}\n                        </Typography>\n                        <Chip\n                          label={`${product.discount}% OFF`}\n                          color=\"error\"\n                          size=\"small\"\n                        />\n                      </Box>\n                    </Box>\n                    <Button variant=\"contained\" size=\"small\">\n                      Add to Cart\n                    </Button>\n                  </Card>\n                </Grid>\n              ))}\n            </Grid>\n          </Paper>\n        </Grid>\n      </Grid>\n    </Box>\n  );\n};\n\nexport default UserDashboard;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAClD,SACEC,GAAG,EACHC,IAAI,EACJC,KAAK,EACLC,UAAU,EACVC,IAAI,EACJC,WAAW,EACXC,SAAS,EACTC,MAAM,EACNC,MAAM,EACNC,IAAI,EACJC,KAAK,EACLC,SAAS,EACTC,SAAS,EACTC,cAAc,EACdC,SAAS,EACTC,QAAQ,EACRC,cAAc,EACdC,OAAO,EACPC,IAAI,EACJC,QAAQ,EACRC,YAAY,EACZC,cAAc,EACdC,UAAU,EACVC,KAAK,EACLC,gBAAgB,EAChBC,KAAK,QACA,eAAe;AACtB,SACEC,WAAW,EACXC,eAAe,EACfC,aAAa,EACbC,IAAI,EACJC,aAAa,EACbC,aAAa,EACbC,UAAU,EACVC,UAAU,EACVC,QAAQ,EACRC,OAAO,EACPC,UAAU,EACVC,YAAY,QACP,qBAAqB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAE7B,MAAMC,aAAa,GAAGA,CAAA,KAAM;EAAAC,EAAA;EAC1B,MAAM,CAACC,IAAI,CAAC,GAAG5C,QAAQ,CAAC;IACtB6C,IAAI,EAAE,UAAU;IAChBC,KAAK,EAAE,sBAAsB;IAC7BC,MAAM,EAAE,0BAA0B;IAClCC,WAAW,EAAE,cAAc;IAC3BC,WAAW,EAAE,EAAE;IACfC,UAAU,EAAE,OAAO;IACnBC,aAAa,EAAE,IAAI;IACnBC,YAAY,EAAE;EAChB,CAAC,CAAC;EAEF,MAAMC,YAAY,GAAG,CACnB;IACEC,EAAE,EAAE,QAAQ;IACZC,IAAI,EAAE,YAAY;IAClBC,KAAK,EAAE,CAAC,iBAAiB,EAAE,cAAc,CAAC;IAC1CC,KAAK,EAAE,KAAK;IACZC,MAAM,EAAE,WAAW;IACnBC,KAAK,EAAE;EACT,CAAC,EACD;IACEL,EAAE,EAAE,QAAQ;IACZC,IAAI,EAAE,YAAY;IAClBC,KAAK,EAAE,CAAC,iBAAiB,EAAE,UAAU,CAAC;IACtCC,KAAK,EAAE,KAAK;IACZC,MAAM,EAAE,SAAS;IACjBC,KAAK,EAAE;EACT,CAAC,EACD;IACEL,EAAE,EAAE,QAAQ;IACZC,IAAI,EAAE,YAAY;IAClBC,KAAK,EAAE,CAAC,iBAAiB,CAAC;IAC1BC,KAAK,EAAE,KAAK;IACZC,MAAM,EAAE,YAAY;IACpBC,KAAK,EAAE;EACT,CAAC,CACF;EAED,MAAMC,gBAAgB,GAAG,CACvB;IAAEN,EAAE,EAAE,CAAC;IAAET,IAAI,EAAE,iBAAiB;IAAEgB,KAAK,EAAE,KAAK;IAAEF,KAAK,EAAE,IAAI;IAAEG,MAAM,EAAE,GAAG;IAAEC,OAAO,EAAE;EAAK,CAAC,EACzF;IAAET,EAAE,EAAE,CAAC;IAAET,IAAI,EAAE,mBAAmB;IAAEgB,KAAK,EAAE,KAAK;IAAEF,KAAK,EAAE,GAAG;IAAEG,MAAM,EAAE,GAAG;IAAEC,OAAO,EAAE;EAAK,CAAC,EAC1F;IAAET,EAAE,EAAE,CAAC;IAAET,IAAI,EAAE,kBAAkB;IAAEgB,KAAK,EAAE,KAAK;IAAEF,KAAK,EAAE,IAAI;IAAEG,MAAM,EAAE,GAAG;IAAEC,OAAO,EAAE;EAAM,CAAC,EAC3F;IAAET,EAAE,EAAE,CAAC;IAAET,IAAI,EAAE,eAAe;IAAEgB,KAAK,EAAE,KAAK;IAAEF,KAAK,EAAE,IAAI;IAAEG,MAAM,EAAE,GAAG;IAAEC,OAAO,EAAE;EAAK,CAAC,CACxF;EAED,MAAMC,eAAe,GAAG,CACtB;IAAEV,EAAE,EAAE,CAAC;IAAET,IAAI,EAAE,YAAY;IAAEgB,KAAK,EAAE,KAAK;IAAEF,KAAK,EAAE,GAAG;IAAEM,QAAQ,EAAE;EAAG,CAAC,EACrE;IAAEX,EAAE,EAAE,CAAC;IAAET,IAAI,EAAE,QAAQ;IAAEgB,KAAK,EAAE,KAAK;IAAEF,KAAK,EAAE,IAAI;IAAEM,QAAQ,EAAE;EAAG,CAAC,EAClE;IAAEX,EAAE,EAAE,CAAC;IAAET,IAAI,EAAE,QAAQ;IAAEgB,KAAK,EAAE,KAAK;IAAEF,KAAK,EAAE,IAAI;IAAEM,QAAQ,EAAE;EAAG,CAAC,CACnE;EAED,MAAMC,aAAa,GAAG,CACpB;IAAEZ,EAAE,EAAE,CAAC;IAAEa,OAAO,EAAE,uCAAuC;IAAEC,IAAI,EAAE,aAAa;IAAEC,IAAI,EAAE;EAAW,CAAC,EAClG;IAAEf,EAAE,EAAE,CAAC;IAAEa,OAAO,EAAE,2CAA2C;IAAEC,IAAI,EAAE,WAAW;IAAEC,IAAI,EAAE;EAAY,CAAC,EACrG;IAAEf,EAAE,EAAE,CAAC;IAAEa,OAAO,EAAE,mCAAmC;IAAEC,IAAI,EAAE,YAAY;IAAEC,IAAI,EAAE;EAAS,CAAC,CAC5F;EAED,MAAMC,cAAc,GAAIZ,MAAM,IAAK;IACjC,QAAQA,MAAM;MACZ,KAAK,WAAW;QAAE,OAAO,SAAS;MAClC,KAAK,SAAS;QAAE,OAAO,MAAM;MAC7B,KAAK,YAAY;QAAE,OAAO,SAAS;MACnC,KAAK,WAAW;QAAE,OAAO,OAAO;MAChC;QAAS,OAAO,SAAS;IAC3B;EACF,CAAC;EAED,MAAMa,eAAe,GAAI3B,IAAI,CAACO,aAAa,GAAGP,IAAI,CAACQ,YAAY,GAAI,GAAG;EAEtE,oBACEX,OAAA,CAACvC,GAAG;IAACsE,EAAE,EAAE;MAAEC,CAAC,EAAE,CAAC;MAAEC,eAAe,EAAE,SAAS;MAAEC,SAAS,EAAE;IAAQ,CAAE;IAAAC,QAAA,gBAChEnC,OAAA,CAACpC,UAAU;MAACwE,OAAO,EAAC,IAAI;MAACL,EAAE,EAAE;QAAEM,EAAE,EAAE,CAAC;QAAEC,UAAU,EAAE;MAAO,CAAE;MAAAH,QAAA,GAAC,gBAC5C,EAAChC,IAAI,CAACC,IAAI,EAAC,gBAC3B;IAAA;MAAAmC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAY,CAAC,eAEb1C,OAAA,CAACtC,IAAI;MAACiF,SAAS;MAACC,OAAO,EAAE,CAAE;MAAAT,QAAA,gBAEzBnC,OAAA,CAACtC,IAAI;QAACmF,IAAI;QAACC,EAAE,EAAE,EAAG;QAACC,EAAE,EAAE,CAAE;QAAAZ,QAAA,eACvBnC,OAAA,CAACrC,KAAK;UAACoE,EAAE,EAAE;YAAEC,CAAC,EAAE,CAAC;YAAEgB,SAAS,EAAE,QAAQ;YAAEC,MAAM,EAAE;UAAc,CAAE;UAAAd,QAAA,gBAC9DnC,OAAA,CAAC/B,MAAM;YACLiF,GAAG,EAAE/C,IAAI,CAACG,MAAO;YACjByB,EAAE,EAAE;cAAEoB,KAAK,EAAE,EAAE;cAAEF,MAAM,EAAE,EAAE;cAAEG,EAAE,EAAE,MAAM;cAAEf,EAAE,EAAE;YAAE;UAAE;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAClD,CAAC,eACF1C,OAAA,CAACpC,UAAU;YAACwE,OAAO,EAAC,IAAI;YAACL,EAAE,EAAE;cAAEO,UAAU,EAAE;YAAO,CAAE;YAAAH,QAAA,EACjDhC,IAAI,CAACC;UAAI;YAAAmC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACA,CAAC,eACb1C,OAAA,CAACpC,UAAU;YAACwE,OAAO,EAAC,OAAO;YAACiB,KAAK,EAAC,eAAe;YAACtB,EAAE,EAAE;cAAEM,EAAE,EAAE;YAAE,CAAE;YAAAF,QAAA,EAC7DhC,IAAI,CAACE;UAAK;YAAAkC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACD,CAAC,eACb1C,OAAA,CAACpC,UAAU;YAACwE,OAAO,EAAC,SAAS;YAACiB,KAAK,EAAC,eAAe;YAAAlB,QAAA,GAAC,eACrC,EAAChC,IAAI,CAACI,WAAW;UAAA;YAAAgC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACpB,CAAC,eAEb1C,OAAA,CAACtB,OAAO;YAACqD,EAAE,EAAE;cAAEuB,EAAE,EAAE;YAAE;UAAE;YAAAf,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,eAE1B1C,OAAA,CAACtC,IAAI;YAACiF,SAAS;YAACC,OAAO,EAAE,CAAE;YAACb,EAAE,EAAE;cAAEiB,SAAS,EAAE;YAAS,CAAE;YAAAb,QAAA,gBACtDnC,OAAA,CAACtC,IAAI;cAACmF,IAAI;cAACC,EAAE,EAAE,CAAE;cAAAX,QAAA,gBACfnC,OAAA,CAACpC,UAAU;gBAACwE,OAAO,EAAC,IAAI;gBAACiB,KAAK,EAAC,SAAS;gBAAAlB,QAAA,EAAEhC,IAAI,CAACK;cAAW;gBAAA+B,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAa,CAAC,eACxE1C,OAAA,CAACpC,UAAU;gBAACwE,OAAO,EAAC,SAAS;gBAAAD,QAAA,EAAC;cAAM;gBAAAI,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAY,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC7C,CAAC,eACP1C,OAAA,CAACtC,IAAI;cAACmF,IAAI;cAACC,EAAE,EAAE,CAAE;cAAAX,QAAA,gBACfnC,OAAA,CAACpC,UAAU;gBAACwE,OAAO,EAAC,IAAI;gBAACiB,KAAK,EAAC,SAAS;gBAAAlB,QAAA,GAAC,GAAC,EAAChC,IAAI,CAACM,UAAU;cAAA;gBAAA8B,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAa,CAAC,eACxE1C,OAAA,CAACpC,UAAU;gBAACwE,OAAO,EAAC,SAAS;gBAAAD,QAAA,EAAC;cAAK;gBAAAI,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAY,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC5C,CAAC,eACP1C,OAAA,CAACtC,IAAI;cAACmF,IAAI;cAACC,EAAE,EAAE,CAAE;cAAAX,QAAA,gBACfnC,OAAA,CAACpC,UAAU;gBAACwE,OAAO,EAAC,IAAI;gBAACiB,KAAK,EAAC,SAAS;gBAAAlB,QAAA,EAAEhC,IAAI,CAACO;cAAa;gBAAA6B,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAa,CAAC,eAC1E1C,OAAA,CAACpC,UAAU;gBAACwE,OAAO,EAAC,SAAS;gBAAAD,QAAA,EAAC;cAAM;gBAAAI,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAY,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC7C,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,eAEP1C,OAAA,CAAChC,MAAM;YAACoE,OAAO,EAAC,WAAW;YAACmB,SAAS;YAACxB,EAAE,EAAE;cAAEyB,EAAE,EAAE;YAAE,CAAE;YAAArB,QAAA,gBAClDnC,OAAA,CAACL,QAAQ;cAACoC,EAAE,EAAE;gBAAE0B,EAAE,EAAE;cAAE;YAAE;cAAAlB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,gBAE7B;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACJ;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACJ,CAAC,eAGP1C,OAAA,CAACtC,IAAI;QAACmF,IAAI;QAACC,EAAE,EAAE,EAAG;QAACC,EAAE,EAAE,CAAE;QAAAZ,QAAA,gBACvBnC,OAAA,CAACtC,IAAI;UAACiF,SAAS;UAACC,OAAO,EAAE,CAAE;UAACb,EAAE,EAAE;YAAEM,EAAE,EAAE;UAAE,CAAE;UAAAF,QAAA,gBACxCnC,OAAA,CAACtC,IAAI;YAACmF,IAAI;YAACC,EAAE,EAAE,CAAE;YAACY,EAAE,EAAE,CAAE;YAAAvB,QAAA,eACtBnC,OAAA,CAACnC,IAAI;cAACkE,EAAE,EAAE;gBAAEC,CAAC,EAAE,CAAC;gBAAEgB,SAAS,EAAE,QAAQ;gBAAEf,eAAe,EAAE;cAAU,CAAE;cAAAE,QAAA,gBAClEnC,OAAA,CAACb,WAAW;gBAAC4C,EAAE,EAAE;kBAAE4B,QAAQ,EAAE,EAAE;kBAAEN,KAAK,EAAE,SAAS;kBAAEhB,EAAE,EAAE;gBAAE;cAAE;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,eAC9D1C,OAAA,CAACpC,UAAU;gBAACwE,OAAO,EAAC,IAAI;gBAAAD,QAAA,EAAEhC,IAAI,CAACK;cAAW;gBAAA+B,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAa,CAAC,eACxD1C,OAAA,CAACpC,UAAU;gBAACwE,OAAO,EAAC,SAAS;gBAAAD,QAAA,EAAC;cAAY;gBAAAI,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAY,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACnD;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,eACP1C,OAAA,CAACtC,IAAI;YAACmF,IAAI;YAACC,EAAE,EAAE,CAAE;YAACY,EAAE,EAAE,CAAE;YAAAvB,QAAA,eACtBnC,OAAA,CAACnC,IAAI;cAACkE,EAAE,EAAE;gBAAEC,CAAC,EAAE,CAAC;gBAAEgB,SAAS,EAAE,QAAQ;gBAAEf,eAAe,EAAE;cAAU,CAAE;cAAAE,QAAA,gBAClEnC,OAAA,CAACZ,eAAe;gBAAC2C,EAAE,EAAE;kBAAE4B,QAAQ,EAAE,EAAE;kBAAEN,KAAK,EAAE,SAAS;kBAAEhB,EAAE,EAAE;gBAAE;cAAE;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,eAClE1C,OAAA,CAACpC,UAAU;gBAACwE,OAAO,EAAC,IAAI;gBAAAD,QAAA,EAAEhB,gBAAgB,CAACyC;cAAM;gBAAArB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAa,CAAC,eAC/D1C,OAAA,CAACpC,UAAU;gBAACwE,OAAO,EAAC,SAAS;gBAAAD,QAAA,EAAC;cAAS;gBAAAI,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAY,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAChD;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,eACP1C,OAAA,CAACtC,IAAI;YAACmF,IAAI;YAACC,EAAE,EAAE,CAAE;YAACY,EAAE,EAAE,CAAE;YAAAvB,QAAA,eACtBnC,OAAA,CAACnC,IAAI;cAACkE,EAAE,EAAE;gBAAEC,CAAC,EAAE,CAAC;gBAAEgB,SAAS,EAAE,QAAQ;gBAAEf,eAAe,EAAE;cAAU,CAAE;cAAAE,QAAA,gBAClEnC,OAAA,CAACX,aAAa;gBAAC0C,EAAE,EAAE;kBAAE4B,QAAQ,EAAE,EAAE;kBAAEN,KAAK,EAAE,SAAS;kBAAEhB,EAAE,EAAE;gBAAE;cAAE;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,eAChE1C,OAAA,CAACpC,UAAU;gBAACwE,OAAO,EAAC,IAAI;gBAAAD,QAAA,EAAC;cAAC;gBAAAI,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAY,CAAC,eACvC1C,OAAA,CAACpC,UAAU;gBAACwE,OAAO,EAAC,SAAS;gBAAAD,QAAA,EAAC;cAAU;gBAAAI,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAY,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACjD;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,eACP1C,OAAA,CAACtC,IAAI;YAACmF,IAAI;YAACC,EAAE,EAAE,CAAE;YAACY,EAAE,EAAE,CAAE;YAAAvB,QAAA,eACtBnC,OAAA,CAACnC,IAAI;cAACkE,EAAE,EAAE;gBAAEC,CAAC,EAAE,CAAC;gBAAEgB,SAAS,EAAE,QAAQ;gBAAEf,eAAe,EAAE;cAAU,CAAE;cAAAE,QAAA,gBAClEnC,OAAA,CAACV,IAAI;gBAACyC,EAAE,EAAE;kBAAE4B,QAAQ,EAAE,EAAE;kBAAEN,KAAK,EAAE,SAAS;kBAAEhB,EAAE,EAAE;gBAAE;cAAE;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,eACvD1C,OAAA,CAACpC,UAAU;gBAACwE,OAAO,EAAC,IAAI;gBAAAD,QAAA,EAAC;cAAG;gBAAAI,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAY,CAAC,eACzC1C,OAAA,CAACpC,UAAU;gBAACwE,OAAO,EAAC,SAAS;gBAAAD,QAAA,EAAC;cAAU;gBAAAI,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAY,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACjD;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,eAGP1C,OAAA,CAACrC,KAAK;UAACoE,EAAE,EAAE;YAAEC,CAAC,EAAE,CAAC;YAAEK,EAAE,EAAE;UAAE,CAAE;UAAAF,QAAA,gBACzBnC,OAAA,CAACpC,UAAU;YAACwE,OAAO,EAAC,IAAI;YAACL,EAAE,EAAE;cAAEM,EAAE,EAAE;YAAE,CAAE;YAAAF,QAAA,EAAC;UAExC;YAAAI,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAY,CAAC,eACb1C,OAAA,CAACvC,GAAG;YAACsE,EAAE,EAAE;cAAE8B,OAAO,EAAE,MAAM;cAAEC,cAAc,EAAE,eAAe;cAAEzB,EAAE,EAAE;YAAE,CAAE;YAAAF,QAAA,gBACnEnC,OAAA,CAACpC,UAAU;cAACwE,OAAO,EAAC,OAAO;cAAAD,QAAA,GACxBhC,IAAI,CAACO,aAAa,EAAC,KAAG,EAACP,IAAI,CAACQ,YAAY,EAAC,SAC5C;YAAA;cAAA4B,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAY,CAAC,eACb1C,OAAA,CAACpC,UAAU;cAACwE,OAAO,EAAC,OAAO;cAACiB,KAAK,EAAC,SAAS;cAAAlB,QAAA,GACxChC,IAAI,CAACQ,YAAY,GAAGR,IAAI,CAACO,aAAa,EAAC,wBAC1C;YAAA;cAAA6B,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAY,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACV,CAAC,eACN1C,OAAA,CAACvB,cAAc;YACb2D,OAAO,EAAC,aAAa;YACrB2B,KAAK,EAAEjC,eAAgB;YACvBC,EAAE,EAAE;cAAEkB,MAAM,EAAE,CAAC;cAAEe,YAAY,EAAE,CAAC;cAAE3B,EAAE,EAAE;YAAE;UAAE;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC3C,CAAC,eACF1C,OAAA,CAACpC,UAAU;YAACwE,OAAO,EAAC,SAAS;YAACiB,KAAK,EAAC,eAAe;YAAAlB,QAAA,EAAC;UAEpD;YAAAI,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAY,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACR,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACJ,CAAC,eAGP1C,OAAA,CAACtC,IAAI;QAACmF,IAAI;QAACC,EAAE,EAAE,EAAG;QAACC,EAAE,EAAE,CAAE;QAAAZ,QAAA,eACvBnC,OAAA,CAACrC,KAAK;UAACoE,EAAE,EAAE;YAAEC,CAAC,EAAE;UAAE,CAAE;UAAAG,QAAA,gBAClBnC,OAAA,CAACvC,GAAG;YAACsE,EAAE,EAAE;cAAE8B,OAAO,EAAE,MAAM;cAAEC,cAAc,EAAE,eAAe;cAAEG,UAAU,EAAE,QAAQ;cAAE5B,EAAE,EAAE;YAAE,CAAE;YAAAF,QAAA,gBACzFnC,OAAA,CAACpC,UAAU;cAACwE,OAAO,EAAC,IAAI;cAAAD,QAAA,EAAC;YAAa;cAAAI,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAY,CAAC,eACnD1C,OAAA,CAAChC,MAAM;cAACoE,OAAO,EAAC,UAAU;cAAC8B,IAAI,EAAC,OAAO;cAAA/B,QAAA,gBACrCnC,OAAA,CAACJ,OAAO;gBAACmC,EAAE,EAAE;kBAAE0B,EAAE,EAAE;gBAAE;cAAE;gBAAAlB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,YAE5B;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACN,CAAC,eACN1C,OAAA,CAAC1B,cAAc;YAAA6D,QAAA,eACbnC,OAAA,CAAC7B,KAAK;cAAAgE,QAAA,gBACJnC,OAAA,CAACzB,SAAS;gBAAA4D,QAAA,eACRnC,OAAA,CAACxB,QAAQ;kBAAA2D,QAAA,gBACPnC,OAAA,CAAC3B,SAAS;oBAAA8D,QAAA,EAAC;kBAAK;oBAAAI,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAW,CAAC,eAC5B1C,OAAA,CAAC3B,SAAS;oBAAA8D,QAAA,EAAC;kBAAK;oBAAAI,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAW,CAAC,eAC5B1C,OAAA,CAAC3B,SAAS;oBAAA8D,QAAA,EAAC;kBAAI;oBAAAI,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAW,CAAC,eAC3B1C,OAAA,CAAC3B,SAAS;oBAAA8D,QAAA,EAAC;kBAAK;oBAAAI,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAW,CAAC,eAC5B1C,OAAA,CAAC3B,SAAS;oBAAA8D,QAAA,EAAC;kBAAM;oBAAAI,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAW,CAAC,eAC7B1C,OAAA,CAAC3B,SAAS;oBAAA8D,QAAA,EAAC;kBAAM;oBAAAI,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAW,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACrB;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACF,CAAC,eACZ1C,OAAA,CAAC5B,SAAS;gBAAA+D,QAAA,EACPvB,YAAY,CAACuD,GAAG,CAAEC,KAAK,iBACtBpE,OAAA,CAACxB,QAAQ;kBAAA2D,QAAA,gBACPnC,OAAA,CAAC3B,SAAS;oBAAA8D,QAAA,eACRnC,OAAA,CAACvC,GAAG;sBAACsE,EAAE,EAAE;wBAAE8B,OAAO,EAAE,MAAM;wBAAEI,UAAU,EAAE;sBAAS,CAAE;sBAAA9B,QAAA,gBACjDnC,OAAA,CAACpC,UAAU;wBAACmE,EAAE,EAAE;0BAAE0B,EAAE,EAAE,CAAC;0BAAEE,QAAQ,EAAE;wBAAQ,CAAE;wBAAAxB,QAAA,EAAEiC,KAAK,CAAClD;sBAAK;wBAAAqB,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAa,CAAC,eACxE1C,OAAA,CAACpC,UAAU;wBAACwE,OAAO,EAAC,OAAO;wBAAAD,QAAA,EAAEiC,KAAK,CAACvD;sBAAE;wBAAA0B,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAa,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAChD;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACG,CAAC,eACZ1C,OAAA,CAAC3B,SAAS;oBAAA8D,QAAA,eACRnC,OAAA,CAACpC,UAAU;sBAACwE,OAAO,EAAC,OAAO;sBAAAD,QAAA,EACxBiC,KAAK,CAACrD,KAAK,CAACsD,IAAI,CAAC,IAAI;oBAAC;sBAAA9B,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACb;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACJ,CAAC,eACZ1C,OAAA,CAAC3B,SAAS;oBAAA8D,QAAA,EAAEiC,KAAK,CAACtD;kBAAI;oBAAAyB,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAY,CAAC,eACnC1C,OAAA,CAAC3B,SAAS;oBAAA8D,QAAA,GAAC,GAAC,EAACiC,KAAK,CAACpD,KAAK;kBAAA;oBAAAuB,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAY,CAAC,eACrC1C,OAAA,CAAC3B,SAAS;oBAAA8D,QAAA,eACRnC,OAAA,CAAC9B,IAAI;sBACHoG,KAAK,EAAEF,KAAK,CAACnD,MAAO;sBACpBoC,KAAK,EAAExB,cAAc,CAACuC,KAAK,CAACnD,MAAM,CAAE;sBACpCiD,IAAI,EAAC;oBAAO;sBAAA3B,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACb;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACO,CAAC,eACZ1C,OAAA,CAAC3B,SAAS;oBAAA8D,QAAA,eACRnC,OAAA,CAAChC,MAAM;sBAACkG,IAAI,EAAC,OAAO;sBAAC9B,OAAO,EAAC,UAAU;sBAAAD,QAAA,EAAC;oBAExC;sBAAAI,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAQ;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACA,CAAC;gBAAA,GAzBC0B,KAAK,CAACvD,EAAE;kBAAA0B,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OA0Bb,CACX;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACO,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACP;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACM,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACZ;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACJ,CAAC,eAGP1C,OAAA,CAACtC,IAAI;QAACmF,IAAI;QAACC,EAAE,EAAE,EAAG;QAACC,EAAE,EAAE,CAAE;QAAAZ,QAAA,eACvBnC,OAAA,CAACrC,KAAK;UAACoE,EAAE,EAAE;YAAEC,CAAC,EAAE,CAAC;YAAEiB,MAAM,EAAE;UAAc,CAAE;UAAAd,QAAA,gBACzCnC,OAAA,CAACvC,GAAG;YAACsE,EAAE,EAAE;cAAE8B,OAAO,EAAE,MAAM;cAAEI,UAAU,EAAE,QAAQ;cAAE5B,EAAE,EAAE;YAAE,CAAE;YAAAF,QAAA,gBACxDnC,OAAA,CAAChB,KAAK;cAACuF,YAAY,EAAE9C,aAAa,CAACmC,MAAO;cAACP,KAAK,EAAC,OAAO;cAAAlB,QAAA,eACtDnC,OAAA,CAACT,aAAa;gBAAC8D,KAAK,EAAC;cAAQ;gBAAAd,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC3B,CAAC,eACR1C,OAAA,CAACpC,UAAU;cAACwE,OAAO,EAAC,IAAI;cAACL,EAAE,EAAE;gBAAEyC,EAAE,EAAE;cAAE,CAAE;cAAArC,QAAA,EAAC;YAExC;cAAAI,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAY,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACV,CAAC,eACN1C,OAAA,CAACrB,IAAI;YAAC8F,KAAK;YAAAtC,QAAA,EACRV,aAAa,CAAC0C,GAAG,CAAEO,YAAY,iBAC9B1E,OAAA,CAACpB,QAAQ;cAAuBmD,EAAE,EAAE;gBAAE4C,EAAE,EAAE;cAAE,CAAE;cAAAxC,QAAA,gBAC5CnC,OAAA,CAAClB,cAAc;gBAAAqD,QAAA,eACbnC,OAAA,CAAC/B,MAAM;kBAAC8D,EAAE,EAAE;oBAAEoB,KAAK,EAAE,EAAE;oBAAEF,MAAM,EAAE,EAAE;oBAAEhB,eAAe,EAAE;kBAAU,CAAE;kBAAAE,QAAA,GAC/DuC,YAAY,CAAC9C,IAAI,KAAK,UAAU,iBAAI5B,OAAA,CAACX,aAAa;oBAAC0C,EAAE,EAAE;sBAAE4B,QAAQ,EAAE;oBAAG;kBAAE;oBAAApB,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE,CAAC,EAC3EgC,YAAY,CAAC9C,IAAI,KAAK,WAAW,iBAAI5B,OAAA,CAACH,UAAU;oBAACkC,EAAE,EAAE;sBAAE4B,QAAQ,EAAE;oBAAG;kBAAE;oBAAApB,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE,CAAC,EACzEgC,YAAY,CAAC9C,IAAI,KAAK,QAAQ,iBAAI5B,OAAA,CAACV,IAAI;oBAACyC,EAAE,EAAE;sBAAE4B,QAAQ,EAAE;oBAAG;kBAAE;oBAAApB,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC3D;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACK,CAAC,eACjB1C,OAAA,CAACnB,YAAY;gBACX+F,OAAO,eACL5E,OAAA,CAACpC,UAAU;kBAACwE,OAAO,EAAC,OAAO;kBAACL,EAAE,EAAE;oBAAE4B,QAAQ,EAAE;kBAAW,CAAE;kBAAAxB,QAAA,EACtDuC,YAAY,CAAChD;gBAAO;kBAAAa,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACX,CACb;gBACDmC,SAAS,EAAEH,YAAY,CAAC/C;cAAK;gBAAAY,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC9B,CAAC;YAAA,GAfWgC,YAAY,CAAC7D,EAAE;cAAA0B,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAgBpB,CACX;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACE,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACF;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACJ,CAAC,eAGP1C,OAAA,CAACtC,IAAI;QAACmF,IAAI;QAACC,EAAE,EAAE,EAAG;QAACC,EAAE,EAAE,CAAE;QAAAZ,QAAA,eACvBnC,OAAA,CAACrC,KAAK;UAACoE,EAAE,EAAE;YAAEC,CAAC,EAAE;UAAE,CAAE;UAAAG,QAAA,gBAClBnC,OAAA,CAACpC,UAAU;YAACwE,OAAO,EAAC,IAAI;YAACL,EAAE,EAAE;cAAEM,EAAE,EAAE;YAAE,CAAE;YAAAF,QAAA,EAAC;UAExC;YAAAI,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAY,CAAC,eACb1C,OAAA,CAACtC,IAAI;YAACiF,SAAS;YAACC,OAAO,EAAE,CAAE;YAAAT,QAAA,EACxBhB,gBAAgB,CAACgD,GAAG,CAAEW,OAAO,iBAC5B9E,OAAA,CAACtC,IAAI;cAACmF,IAAI;cAACC,EAAE,EAAE,EAAG;cAACY,EAAE,EAAE,CAAE;cAAAvB,QAAA,eACvBnC,OAAA,CAACnC,IAAI;gBAACkE,EAAE,EAAE;kBAAE8B,OAAO,EAAE,MAAM;kBAAEI,UAAU,EAAE,QAAQ;kBAAEjC,CAAC,EAAE;gBAAE,CAAE;gBAAAG,QAAA,gBACxDnC,OAAA,CAACpC,UAAU;kBAACmE,EAAE,EAAE;oBAAE4B,QAAQ,EAAE,KAAK;oBAAEF,EAAE,EAAE;kBAAE,CAAE;kBAAAtB,QAAA,EAAE2C,OAAO,CAAC5D;gBAAK;kBAAAqB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAa,CAAC,eACxE1C,OAAA,CAACvC,GAAG;kBAACsE,EAAE,EAAE;oBAAEgD,QAAQ,EAAE;kBAAE,CAAE;kBAAA5C,QAAA,gBACvBnC,OAAA,CAACpC,UAAU;oBAACwE,OAAO,EAAC,OAAO;oBAACL,EAAE,EAAE;sBAAEO,UAAU,EAAE;oBAAO,CAAE;oBAAAH,QAAA,EACpD2C,OAAO,CAAC1E;kBAAI;oBAAAmC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACH,CAAC,eACb1C,OAAA,CAACpC,UAAU;oBAACwE,OAAO,EAAC,SAAS;oBAACiB,KAAK,EAAC,eAAe;oBAAAlB,QAAA,GAAC,GACjD,EAAC2C,OAAO,CAAC1D,KAAK,EAAC,iBAAK,EAAC0D,OAAO,CAACzD,MAAM;kBAAA;oBAAAkB,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAC1B,CAAC,eACb1C,OAAA,CAACvC,GAAG;oBAACsE,EAAE,EAAE;sBAAEyB,EAAE,EAAE;oBAAE,CAAE;oBAAArB,QAAA,eACjBnC,OAAA,CAAChC,MAAM;sBACLkG,IAAI,EAAC,OAAO;sBACZ9B,OAAO,EAAC,WAAW;sBACnB4C,QAAQ,EAAE,CAACF,OAAO,CAACxD,OAAQ;sBAC3BS,EAAE,EAAE;wBAAE4B,QAAQ,EAAE,SAAS;wBAAEsB,EAAE,EAAE;sBAAI,CAAE;sBAAA9C,QAAA,gBAErCnC,OAAA,CAACF,YAAY;wBAACiC,EAAE,EAAE;0BAAE4B,QAAQ,EAAE,EAAE;0BAAEF,EAAE,EAAE;wBAAI;sBAAE;wBAAAlB,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAE,CAAC,EAC9CoC,OAAO,CAACxD,OAAO,GAAG,aAAa,GAAG,cAAc;oBAAA;sBAAAiB,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAC3C;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACN,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACF;YAAC,GAtBsBoC,OAAO,CAACjE,EAAE;cAAA0B,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAuBnC,CACP;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACE,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACF;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACJ,CAAC,eAGP1C,OAAA,CAACtC,IAAI;QAACmF,IAAI;QAACC,EAAE,EAAE,EAAG;QAACC,EAAE,EAAE,CAAE;QAAAZ,QAAA,eACvBnC,OAAA,CAACrC,KAAK;UAACoE,EAAE,EAAE;YAAEC,CAAC,EAAE;UAAE,CAAE;UAAAG,QAAA,gBAClBnC,OAAA,CAACpC,UAAU;YAACwE,OAAO,EAAC,IAAI;YAACL,EAAE,EAAE;cAAEM,EAAE,EAAE;YAAE,CAAE;YAAAF,QAAA,EAAC;UAExC;YAAAI,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAY,CAAC,eACb1C,OAAA,CAACtC,IAAI;YAACiF,SAAS;YAACC,OAAO,EAAE,CAAE;YAAAT,QAAA,EACxBZ,eAAe,CAAC4C,GAAG,CAAEW,OAAO,iBAC3B9E,OAAA,CAACtC,IAAI;cAACmF,IAAI;cAACC,EAAE,EAAE,EAAG;cAAAX,QAAA,eAChBnC,OAAA,CAACnC,IAAI;gBAACkE,EAAE,EAAE;kBAAE8B,OAAO,EAAE,MAAM;kBAAEI,UAAU,EAAE,QAAQ;kBAAEjC,CAAC,EAAE;gBAAE,CAAE;gBAAAG,QAAA,gBACxDnC,OAAA,CAACpC,UAAU;kBAACmE,EAAE,EAAE;oBAAE4B,QAAQ,EAAE,KAAK;oBAAEF,EAAE,EAAE;kBAAE,CAAE;kBAAAtB,QAAA,EAAE2C,OAAO,CAAC5D;gBAAK;kBAAAqB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAa,CAAC,eACxE1C,OAAA,CAACvC,GAAG;kBAACsE,EAAE,EAAE;oBAAEgD,QAAQ,EAAE;kBAAE,CAAE;kBAAA5C,QAAA,gBACvBnC,OAAA,CAACpC,UAAU;oBAACwE,OAAO,EAAC,OAAO;oBAACL,EAAE,EAAE;sBAAEO,UAAU,EAAE;oBAAO,CAAE;oBAAAH,QAAA,EACpD2C,OAAO,CAAC1E;kBAAI;oBAAAmC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACH,CAAC,eACb1C,OAAA,CAACvC,GAAG;oBAACsE,EAAE,EAAE;sBAAE8B,OAAO,EAAE,MAAM;sBAAEI,UAAU,EAAE,QAAQ;sBAAEiB,GAAG,EAAE;oBAAE,CAAE;oBAAA/C,QAAA,gBACzDnC,OAAA,CAACpC,UAAU;sBAACwE,OAAO,EAAC,IAAI;sBAACiB,KAAK,EAAC,SAAS;sBAAAlB,QAAA,GAAC,GACtC,EAAC2C,OAAO,CAAC1D,KAAK;oBAAA;sBAAAmB,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACL,CAAC,eACb1C,OAAA,CAAC9B,IAAI;sBACHoG,KAAK,EAAE,GAAGQ,OAAO,CAACtD,QAAQ,OAAQ;sBAClC6B,KAAK,EAAC,OAAO;sBACba,IAAI,EAAC;oBAAO;sBAAA3B,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACb,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACC,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH,CAAC,eACN1C,OAAA,CAAChC,MAAM;kBAACoE,OAAO,EAAC,WAAW;kBAAC8B,IAAI,EAAC,OAAO;kBAAA/B,QAAA,EAAC;gBAEzC;kBAAAI,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACL;YAAC,GArBeoC,OAAO,CAACjE,EAAE;cAAA0B,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAsB5B,CACP;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACE,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACF;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACJ,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACJ,CAAC;AAEV,CAAC;AAACxC,EAAA,CAvVID,aAAa;AAAAkF,EAAA,GAAblF,aAAa;AAyVnB,eAAeA,aAAa;AAAC,IAAAkF,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}