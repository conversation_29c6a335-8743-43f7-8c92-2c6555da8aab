{"ast": null, "code": "var _jsxFileName = \"D:\\\\ecommerce\\\\client\\\\src\\\\App.js\";\nimport React from 'react';\nimport { BrowserRouter as Router, Routes, Route, Link } from 'react-router-dom';\nimport { Provider } from 'react-redux';\nimport { ThemeProvider, createTheme } from '@mui/material/styles';\nimport CssBaseline from '@mui/material/CssBaseline';\nimport { AppBar, Toolbar, Typography, Button, Container } from '@mui/material';\nimport { configureStore, createSlice } from '@reduxjs/toolkit';\nimport Home from './components/Home';\nimport Cart from './components/Cart';\nimport Checkout from './components/Checkout';\nimport Dashboard from './components/admin/Dashboard';\nimport './App.css';\n\n// Create a theme\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst theme = createTheme({\n  palette: {\n    primary: {\n      main: '#2c3e50'\n    },\n    secondary: {\n      main: '#e74c3c'\n    }\n  }\n});\n\n// Simple Redux store for demo purposes\n\nconst cartSlice = createSlice({\n  name: 'cart',\n  initialState: {\n    items: [],\n    total: 0\n  },\n  reducers: {\n    addToCart: (state, action) => {\n      state.items.push(action.payload);\n      state.total += action.payload.price;\n    },\n    removeFromCart: (state, action) => {\n      const index = state.items.findIndex(item => item.id === action.payload.id);\n      if (index !== -1) {\n        state.total -= state.items[index].price;\n        state.items.splice(index, 1);\n      }\n    }\n  }\n});\nconst store = configureStore({\n  reducer: {\n    cart: cartSlice.reducer\n  }\n});\nfunction App() {\n  return /*#__PURE__*/_jsxDEV(Provider, {\n    store: store,\n    children: /*#__PURE__*/_jsxDEV(ThemeProvider, {\n      theme: theme,\n      children: [/*#__PURE__*/_jsxDEV(CssBaseline, {}, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 59,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Router, {\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"App\",\n          children: [/*#__PURE__*/_jsxDEV(AppBar, {\n            position: \"static\",\n            children: /*#__PURE__*/_jsxDEV(Toolbar, {\n              children: [/*#__PURE__*/_jsxDEV(Typography, {\n                variant: \"h6\",\n                component: \"div\",\n                sx: {\n                  flexGrow: 1\n                },\n                children: \"Spice Ecommerce\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 64,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(Button, {\n                color: \"inherit\",\n                component: Link,\n                to: \"/\",\n                children: \"Home\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 67,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(Button, {\n                color: \"inherit\",\n                component: Link,\n                to: \"/cart\",\n                children: \"Cart\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 70,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(Button, {\n                color: \"inherit\",\n                component: Link,\n                to: \"/admin\",\n                children: \"Admin\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 73,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 63,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 62,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Container, {\n            maxWidth: \"lg\",\n            sx: {\n              mt: 4,\n              mb: 4\n            },\n            children: /*#__PURE__*/_jsxDEV(Routes, {\n              children: [/*#__PURE__*/_jsxDEV(Route, {\n                path: \"/\",\n                element: /*#__PURE__*/_jsxDEV(Home, {}, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 81,\n                  columnNumber: 42\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 81,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(Route, {\n                path: \"/cart\",\n                element: /*#__PURE__*/_jsxDEV(Cart, {}, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 82,\n                  columnNumber: 46\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 82,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(Route, {\n                path: \"/checkout\",\n                element: /*#__PURE__*/_jsxDEV(Checkout, {}, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 83,\n                  columnNumber: 50\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 83,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(Route, {\n                path: \"/admin\",\n                element: /*#__PURE__*/_jsxDEV(Dashboard, {}, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 84,\n                  columnNumber: 47\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 84,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 80,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 79,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"footer\", {\n            style: {\n              backgroundColor: '#2c3e50',\n              color: 'white',\n              textAlign: 'center',\n              padding: '2rem 0',\n              marginTop: '4rem'\n            },\n            children: /*#__PURE__*/_jsxDEV(Typography, {\n              variant: \"body2\",\n              children: \"\\xA9 2024 Spice Ecommerce. All rights reserved.\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 95,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 88,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 61,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 60,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 58,\n      columnNumber: 7\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 57,\n    columnNumber: 5\n  }, this);\n}\n_c = App;\nexport default App;\nvar _c;\n$RefreshReg$(_c, \"App\");", "map": {"version": 3, "names": ["React", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Router", "Routes", "Route", "Link", "Provider", "ThemeProvider", "createTheme", "CssBaseline", "AppBar", "<PERSON><PERSON><PERSON>", "Typography", "<PERSON><PERSON>", "Container", "configureStore", "createSlice", "Home", "<PERSON><PERSON>", "Checkout", "Dashboard", "jsxDEV", "_jsxDEV", "theme", "palette", "primary", "main", "secondary", "cartSlice", "name", "initialState", "items", "total", "reducers", "addToCart", "state", "action", "push", "payload", "price", "removeFromCart", "index", "findIndex", "item", "id", "splice", "store", "reducer", "cart", "App", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "className", "position", "variant", "component", "sx", "flexGrow", "color", "to", "max<PERSON><PERSON><PERSON>", "mt", "mb", "path", "element", "style", "backgroundColor", "textAlign", "padding", "marginTop", "_c", "$RefreshReg$"], "sources": ["D:/ecommerce/client/src/App.js"], "sourcesContent": ["import React from 'react';\nimport { BrowserRouter as Router, Routes, Route, Link } from 'react-router-dom';\nimport { Provider } from 'react-redux';\nimport { ThemeProvider, createTheme } from '@mui/material/styles';\nimport CssBaseline from '@mui/material/CssBaseline';\nimport { AppB<PERSON>, Toolbar, Typography, Button, Container } from '@mui/material';\nimport { configureStore, createSlice } from '@reduxjs/toolkit';\nimport Home from './components/Home';\nimport Cart from './components/Cart';\nimport Checkout from './components/Checkout';\nimport Dashboard from './components/admin/Dashboard';\nimport './App.css';\n\n// Create a theme\nconst theme = createTheme({\n  palette: {\n    primary: {\n      main: '#2c3e50',\n    },\n    secondary: {\n      main: '#e74c3c',\n    },\n  },\n});\n\n// Simple Redux store for demo purposes\n\nconst cartSlice = createSlice({\n  name: 'cart',\n  initialState: {\n    items: [],\n    total: 0,\n  },\n  reducers: {\n    addToCart: (state, action) => {\n      state.items.push(action.payload);\n      state.total += action.payload.price;\n    },\n    removeFromCart: (state, action) => {\n      const index = state.items.findIndex(item => item.id === action.payload.id);\n      if (index !== -1) {\n        state.total -= state.items[index].price;\n        state.items.splice(index, 1);\n      }\n    },\n  },\n});\n\nconst store = configureStore({\n  reducer: {\n    cart: cartSlice.reducer,\n  },\n});\n\nfunction App() {\n  return (\n    <Provider store={store}>\n      <ThemeProvider theme={theme}>\n        <CssBaseline />\n        <Router>\n          <div className=\"App\">\n            <AppBar position=\"static\">\n              <Toolbar>\n                <Typography variant=\"h6\" component=\"div\" sx={{ flexGrow: 1 }}>\n                  Spice Ecommerce\n                </Typography>\n                <Button color=\"inherit\" component={Link} to=\"/\">\n                  Home\n                </Button>\n                <Button color=\"inherit\" component={Link} to=\"/cart\">\n                  Cart\n                </Button>\n                <Button color=\"inherit\" component={Link} to=\"/admin\">\n                  Admin\n                </Button>\n              </Toolbar>\n            </AppBar>\n\n            <Container maxWidth=\"lg\" sx={{ mt: 4, mb: 4 }}>\n              <Routes>\n                <Route path=\"/\" element={<Home />} />\n                <Route path=\"/cart\" element={<Cart />} />\n                <Route path=\"/checkout\" element={<Checkout />} />\n                <Route path=\"/admin\" element={<Dashboard />} />\n              </Routes>\n            </Container>\n\n            <footer style={{\n              backgroundColor: '#2c3e50',\n              color: 'white',\n              textAlign: 'center',\n              padding: '2rem 0',\n              marginTop: '4rem'\n            }}>\n              <Typography variant=\"body2\">\n                © 2024 Spice Ecommerce. All rights reserved.\n              </Typography>\n            </footer>\n          </div>\n        </Router>\n      </ThemeProvider>\n    </Provider>\n  );\n}\n\nexport default App;\n"], "mappings": ";AAAA,OAAOA,KAAK,MAAM,OAAO;AACzB,SAASC,aAAa,IAAIC,MAAM,EAAEC,MAAM,EAAEC,KAAK,EAAEC,IAAI,QAAQ,kBAAkB;AAC/E,SAASC,QAAQ,QAAQ,aAAa;AACtC,SAASC,aAAa,EAAEC,WAAW,QAAQ,sBAAsB;AACjE,OAAOC,WAAW,MAAM,2BAA2B;AACnD,SAASC,MAAM,EAAEC,OAAO,EAAEC,UAAU,EAAEC,MAAM,EAAEC,SAAS,QAAQ,eAAe;AAC9E,SAASC,cAAc,EAAEC,WAAW,QAAQ,kBAAkB;AAC9D,OAAOC,IAAI,MAAM,mBAAmB;AACpC,OAAOC,IAAI,MAAM,mBAAmB;AACpC,OAAOC,QAAQ,MAAM,uBAAuB;AAC5C,OAAOC,SAAS,MAAM,8BAA8B;AACpD,OAAO,WAAW;;AAElB;AAAA,SAAAC,MAAA,IAAAC,OAAA;AACA,MAAMC,KAAK,GAAGf,WAAW,CAAC;EACxBgB,OAAO,EAAE;IACPC,OAAO,EAAE;MACPC,IAAI,EAAE;IACR,CAAC;IACDC,SAAS,EAAE;MACTD,IAAI,EAAE;IACR;EACF;AACF,CAAC,CAAC;;AAEF;;AAEA,MAAME,SAAS,GAAGZ,WAAW,CAAC;EAC5Ba,IAAI,EAAE,MAAM;EACZC,YAAY,EAAE;IACZC,KAAK,EAAE,EAAE;IACTC,KAAK,EAAE;EACT,CAAC;EACDC,QAAQ,EAAE;IACRC,SAAS,EAAEA,CAACC,KAAK,EAAEC,MAAM,KAAK;MAC5BD,KAAK,CAACJ,KAAK,CAACM,IAAI,CAACD,MAAM,CAACE,OAAO,CAAC;MAChCH,KAAK,CAACH,KAAK,IAAII,MAAM,CAACE,OAAO,CAACC,KAAK;IACrC,CAAC;IACDC,cAAc,EAAEA,CAACL,KAAK,EAAEC,MAAM,KAAK;MACjC,MAAMK,KAAK,GAAGN,KAAK,CAACJ,KAAK,CAACW,SAAS,CAACC,IAAI,IAAIA,IAAI,CAACC,EAAE,KAAKR,MAAM,CAACE,OAAO,CAACM,EAAE,CAAC;MAC1E,IAAIH,KAAK,KAAK,CAAC,CAAC,EAAE;QAChBN,KAAK,CAACH,KAAK,IAAIG,KAAK,CAACJ,KAAK,CAACU,KAAK,CAAC,CAACF,KAAK;QACvCJ,KAAK,CAACJ,KAAK,CAACc,MAAM,CAACJ,KAAK,EAAE,CAAC,CAAC;MAC9B;IACF;EACF;AACF,CAAC,CAAC;AAEF,MAAMK,KAAK,GAAG/B,cAAc,CAAC;EAC3BgC,OAAO,EAAE;IACPC,IAAI,EAAEpB,SAAS,CAACmB;EAClB;AACF,CAAC,CAAC;AAEF,SAASE,GAAGA,CAAA,EAAG;EACb,oBACE3B,OAAA,CAAChB,QAAQ;IAACwC,KAAK,EAAEA,KAAM;IAAAI,QAAA,eACrB5B,OAAA,CAACf,aAAa;MAACgB,KAAK,EAAEA,KAAM;MAAA2B,QAAA,gBAC1B5B,OAAA,CAACb,WAAW;QAAA0C,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC,eACfhC,OAAA,CAACpB,MAAM;QAAAgD,QAAA,eACL5B,OAAA;UAAKiC,SAAS,EAAC,KAAK;UAAAL,QAAA,gBAClB5B,OAAA,CAACZ,MAAM;YAAC8C,QAAQ,EAAC,QAAQ;YAAAN,QAAA,eACvB5B,OAAA,CAACX,OAAO;cAAAuC,QAAA,gBACN5B,OAAA,CAACV,UAAU;gBAAC6C,OAAO,EAAC,IAAI;gBAACC,SAAS,EAAC,KAAK;gBAACC,EAAE,EAAE;kBAAEC,QAAQ,EAAE;gBAAE,CAAE;gBAAAV,QAAA,EAAC;cAE9D;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAY,CAAC,eACbhC,OAAA,CAACT,MAAM;gBAACgD,KAAK,EAAC,SAAS;gBAACH,SAAS,EAAErD,IAAK;gBAACyD,EAAE,EAAC,GAAG;gBAAAZ,QAAA,EAAC;cAEhD;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC,eACThC,OAAA,CAACT,MAAM;gBAACgD,KAAK,EAAC,SAAS;gBAACH,SAAS,EAAErD,IAAK;gBAACyD,EAAE,EAAC,OAAO;gBAAAZ,QAAA,EAAC;cAEpD;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC,eACThC,OAAA,CAACT,MAAM;gBAACgD,KAAK,EAAC,SAAS;gBAACH,SAAS,EAAErD,IAAK;gBAACyD,EAAE,EAAC,QAAQ;gBAAAZ,QAAA,EAAC;cAErD;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACF;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACJ,CAAC,eAEThC,OAAA,CAACR,SAAS;YAACiD,QAAQ,EAAC,IAAI;YAACJ,EAAE,EAAE;cAAEK,EAAE,EAAE,CAAC;cAAEC,EAAE,EAAE;YAAE,CAAE;YAAAf,QAAA,eAC5C5B,OAAA,CAACnB,MAAM;cAAA+C,QAAA,gBACL5B,OAAA,CAAClB,KAAK;gBAAC8D,IAAI,EAAC,GAAG;gBAACC,OAAO,eAAE7C,OAAA,CAACL,IAAI;kBAAAkC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE;cAAE;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,eACrChC,OAAA,CAAClB,KAAK;gBAAC8D,IAAI,EAAC,OAAO;gBAACC,OAAO,eAAE7C,OAAA,CAACJ,IAAI;kBAAAiC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE;cAAE;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,eACzChC,OAAA,CAAClB,KAAK;gBAAC8D,IAAI,EAAC,WAAW;gBAACC,OAAO,eAAE7C,OAAA,CAACH,QAAQ;kBAAAgC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE;cAAE;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,eACjDhC,OAAA,CAAClB,KAAK;gBAAC8D,IAAI,EAAC,QAAQ;gBAACC,OAAO,eAAE7C,OAAA,CAACF,SAAS;kBAAA+B,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE;cAAE;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACzC;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACA,CAAC,eAEZhC,OAAA;YAAQ8C,KAAK,EAAE;cACbC,eAAe,EAAE,SAAS;cAC1BR,KAAK,EAAE,OAAO;cACdS,SAAS,EAAE,QAAQ;cACnBC,OAAO,EAAE,QAAQ;cACjBC,SAAS,EAAE;YACb,CAAE;YAAAtB,QAAA,eACA5B,OAAA,CAACV,UAAU;cAAC6C,OAAO,EAAC,OAAO;cAAAP,QAAA,EAAC;YAE5B;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAY;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACP,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACN;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACA,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACI;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACR,CAAC;AAEf;AAACmB,EAAA,GAjDQxB,GAAG;AAmDZ,eAAeA,GAAG;AAAC,IAAAwB,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}