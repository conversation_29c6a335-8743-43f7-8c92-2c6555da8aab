{"ast": null, "code": "var baseAssignValue = require('./_baseAssignValue'),\n  eq = require('./eq');\n\n/** Used for built-in method references. */\nvar objectProto = Object.prototype;\n\n/** Used to check objects for own properties. */\nvar hasOwnProperty = objectProto.hasOwnProperty;\n\n/**\n * Assigns `value` to `key` of `object` if the existing value is not equivalent\n * using [`SameValueZero`](http://ecma-international.org/ecma-262/7.0/#sec-samevaluezero)\n * for equality comparisons.\n *\n * @private\n * @param {Object} object The object to modify.\n * @param {string} key The key of the property to assign.\n * @param {*} value The value to assign.\n */\nfunction assignValue(object, key, value) {\n  var objValue = object[key];\n  if (!(hasOwnProperty.call(object, key) && eq(objValue, value)) || value === undefined && !(key in object)) {\n    baseAssignValue(object, key, value);\n  }\n}\nmodule.exports = assignValue;", "map": {"version": 3, "names": ["baseAssignValue", "require", "eq", "objectProto", "Object", "prototype", "hasOwnProperty", "assignValue", "object", "key", "value", "objValue", "call", "undefined", "module", "exports"], "sources": ["D:/ecommerce/node_modules/lodash/_assignValue.js"], "sourcesContent": ["var baseAssignValue = require('./_baseAssignValue'),\n    eq = require('./eq');\n\n/** Used for built-in method references. */\nvar objectProto = Object.prototype;\n\n/** Used to check objects for own properties. */\nvar hasOwnProperty = objectProto.hasOwnProperty;\n\n/**\n * Assigns `value` to `key` of `object` if the existing value is not equivalent\n * using [`SameValueZero`](http://ecma-international.org/ecma-262/7.0/#sec-samevaluezero)\n * for equality comparisons.\n *\n * @private\n * @param {Object} object The object to modify.\n * @param {string} key The key of the property to assign.\n * @param {*} value The value to assign.\n */\nfunction assignValue(object, key, value) {\n  var objValue = object[key];\n  if (!(hasOwnProperty.call(object, key) && eq(objValue, value)) ||\n      (value === undefined && !(key in object))) {\n    baseAssignValue(object, key, value);\n  }\n}\n\nmodule.exports = assignValue;\n"], "mappings": "AAAA,IAAIA,eAAe,GAAGC,OAAO,CAAC,oBAAoB,CAAC;EAC/CC,EAAE,GAAGD,OAAO,CAAC,MAAM,CAAC;;AAExB;AACA,IAAIE,WAAW,GAAGC,MAAM,CAACC,SAAS;;AAElC;AACA,IAAIC,cAAc,GAAGH,WAAW,CAACG,cAAc;;AAE/C;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASC,WAAWA,CAACC,MAAM,EAAEC,GAAG,EAAEC,KAAK,EAAE;EACvC,IAAIC,QAAQ,GAAGH,MAAM,CAACC,GAAG,CAAC;EAC1B,IAAI,EAAEH,cAAc,CAACM,IAAI,CAACJ,MAAM,EAAEC,GAAG,CAAC,IAAIP,EAAE,CAACS,QAAQ,EAAED,KAAK,CAAC,CAAC,IACzDA,KAAK,KAAKG,SAAS,IAAI,EAAEJ,GAAG,IAAID,MAAM,CAAE,EAAE;IAC7CR,eAAe,CAACQ,MAAM,EAAEC,GAAG,EAAEC,KAAK,CAAC;EACrC;AACF;AAEAI,MAAM,CAACC,OAAO,GAAGR,WAAW", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}