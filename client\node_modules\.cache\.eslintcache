[{"D:\\ecommerce\\client\\src\\index.js": "1", "D:\\ecommerce\\client\\src\\App.js": "2", "D:\\ecommerce\\client\\src\\components\\Cart.js": "3", "D:\\ecommerce\\client\\src\\components\\Checkout.js": "4", "D:\\ecommerce\\client\\src\\components\\Home.js": "5", "D:\\ecommerce\\client\\src\\components\\admin\\Dashboard.js": "6", "D:\\ecommerce\\client\\src\\components\\admin\\AdminDashboard.js": "7", "D:\\ecommerce\\client\\src\\components\\user\\UserDashboard.js": "8", "D:\\ecommerce\\client\\src\\components\\Navigation.js": "9", "D:\\ecommerce\\client\\src\\components\\DashboardSelector.js": "10", "D:\\ecommerce\\client\\src\\services\\api.js": "11"}, {"size": 254, "mtime": 1748675268490, "results": "12", "hashOfConfig": "13"}, {"size": 4813, "mtime": 1748676416682, "results": "14", "hashOfConfig": "13"}, {"size": 4723, "mtime": 1748675363098, "results": "15", "hashOfConfig": "13"}, {"size": 7271, "mtime": 1748673783025, "results": "16", "hashOfConfig": "13"}, {"size": 4201, "mtime": 1748675337821, "results": "17", "hashOfConfig": "13"}, {"size": 5785, "mtime": 1748675388232, "results": "18", "hashOfConfig": "13"}, {"size": 17236, "mtime": 1748676747481, "results": "19", "hashOfConfig": "13"}, {"size": 17999, "mtime": 1748676902158, "results": "20", "hashOfConfig": "13"}, {"size": 3981, "mtime": 1748676194624, "results": "21", "hashOfConfig": "13"}, {"size": 6126, "mtime": 1748676383238, "results": "22", "hashOfConfig": "13"}, {"size": 5096, "mtime": 1748677101391, "results": "23", "hashOfConfig": "13"}, {"filePath": "24", "messages": "25", "suppressedMessages": "26", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "9vwnw1", {"filePath": "27", "messages": "28", "suppressedMessages": "29", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "30", "messages": "31", "suppressedMessages": "32", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "33", "messages": "34", "suppressedMessages": "35", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "36", "messages": "37", "suppressedMessages": "38", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "39", "messages": "40", "suppressedMessages": "41", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "42", "messages": "43", "suppressedMessages": "44", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 5, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "45", "messages": "46", "suppressedMessages": "47", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 7, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "48", "messages": "49", "suppressedMessages": "50", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "51", "messages": "52", "suppressedMessages": "53", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 4, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "54", "messages": "55", "suppressedMessages": "56", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "D:\\ecommerce\\client\\src\\index.js", [], [], "D:\\ecommerce\\client\\src\\App.js", [], [], "D:\\ecommerce\\client\\src\\components\\Cart.js", [], [], "D:\\ecommerce\\client\\src\\components\\Checkout.js", [], [], "D:\\ecommerce\\client\\src\\components\\Home.js", ["57", "58", "59"], [], "D:\\ecommerce\\client\\src\\components\\admin\\Dashboard.js", [], [], "D:\\ecommerce\\client\\src\\components\\admin\\AdminDashboard.js", ["60", "61", "62", "63", "64"], [], "D:\\ecommerce\\client\\src\\components\\user\\UserDashboard.js", ["65", "66", "67", "68", "69", "70", "71"], [], "D:\\ecommerce\\client\\src\\components\\Navigation.js", [], [], "D:\\ecommerce\\client\\src\\components\\DashboardSelector.js", ["72", "73", "74", "75"], [], "D:\\ecommerce\\client\\src\\services\\api.js", [], [], {"ruleId": "76", "severity": 1, "message": "77", "line": 2, "column": 23, "nodeType": "78", "messageId": "79", "endLine": 2, "endColumn": 34}, {"ruleId": "76", "severity": 1, "message": "80", "line": 31, "column": 9, "nodeType": "78", "messageId": "79", "endLine": 31, "endColumn": 17}, {"ruleId": "76", "severity": 1, "message": "81", "line": 33, "column": 10, "nodeType": "78", "messageId": "79", "endLine": 33, "endColumn": 17}, {"ruleId": "76", "severity": 1, "message": "82", "line": 17, "column": 3, "nodeType": "78", "messageId": "79", "endLine": 17, "endColumn": 9}, {"ruleId": "76", "severity": 1, "message": "83", "line": 29, "column": 3, "nodeType": "78", "messageId": "79", "endLine": 29, "endColumn": 15}, {"ruleId": "76", "severity": 1, "message": "84", "line": 31, "column": 3, "nodeType": "78", "messageId": "79", "endLine": 31, "endColumn": 14}, {"ruleId": "76", "severity": 1, "message": "85", "line": 41, "column": 10, "nodeType": "78", "messageId": "79", "endLine": 41, "endColumn": 25}, {"ruleId": "86", "severity": 1, "message": "87", "line": 65, "column": 6, "nodeType": "88", "endLine": 65, "endColumn": 8, "suggestions": "89"}, {"ruleId": "76", "severity": 1, "message": "90", "line": 8, "column": 3, "nodeType": "78", "messageId": "79", "endLine": 8, "endColumn": 14}, {"ruleId": "76", "severity": 1, "message": "91", "line": 9, "column": 3, "nodeType": "78", "messageId": "79", "endLine": 9, "endColumn": 12}, {"ruleId": "76", "severity": 1, "message": "92", "line": 25, "column": 3, "nodeType": "78", "messageId": "79", "endLine": 25, "endColumn": 13}, {"ruleId": "76", "severity": 1, "message": "93", "line": 36, "column": 3, "nodeType": "78", "messageId": "79", "endLine": 36, "endColumn": 16}, {"ruleId": "76", "severity": 1, "message": "94", "line": 37, "column": 3, "nodeType": "78", "messageId": "79", "endLine": 37, "endColumn": 13}, {"ruleId": "76", "severity": 1, "message": "95", "line": 38, "column": 3, "nodeType": "78", "messageId": "79", "endLine": 38, "endColumn": 13}, {"ruleId": "86", "severity": 1, "message": "96", "line": 80, "column": 6, "nodeType": "88", "endLine": 80, "endColumn": 15, "suggestions": "97"}, {"ruleId": "76", "severity": 1, "message": "98", "line": 17, "column": 3, "nodeType": "78", "messageId": "79", "endLine": 17, "endColumn": 12}, {"ruleId": "76", "severity": 1, "message": "83", "line": 18, "column": 3, "nodeType": "78", "messageId": "79", "endLine": 18, "endColumn": 15}, {"ruleId": "76", "severity": 1, "message": "99", "line": 19, "column": 3, "nodeType": "78", "messageId": "79", "endLine": 19, "endColumn": 13}, {"ruleId": "76", "severity": 1, "message": "100", "line": 20, "column": 3, "nodeType": "78", "messageId": "79", "endLine": 20, "endColumn": 9}, "no-unused-vars", "'useSelector' is defined but never used.", "Identifier", "unusedVar", "'dispatch' is assigned a value but never used.", "'loading' is assigned a value but never used.", "'Avatar' is defined but never used.", "'ShoppingCart' is defined but never used.", "'AttachMoney' is defined but never used.", "'selectedProduct' is assigned a value but never used.", "react-hooks/exhaustive-deps", "React Hook useEffect has a missing dependency: 'fetchAnalytics'. Either include it or remove the dependency array.", "ArrayExpression", ["101"], "'CardContent' is defined but never used.", "'CardMedia' is defined but never used.", "'IconButton' is defined but never used.", "'AccountCircle' is defined but never used.", "'CreditCard' is defined but never used.", "'LocationOn' is defined but never used.", "React Hook useEffect has a missing dependency: 'fetchUserAnalytics'. Either include it or remove the dependency array.", ["102"], "'Analytics' is defined but never used.", "'TrendingUp' is defined but never used.", "'People' is defined but never used.", {"desc": "103", "fix": "104"}, {"desc": "105", "fix": "106"}, "Update the dependencies array to be: [fetchAnalytics]", {"range": "107", "text": "108"}, "Update the dependencies array to be: [fetchUserAnalytics, user.id]", {"range": "109", "text": "110"}, [1359, 1361], "[fetchAnalytics]", [1709, 1718], "[fetchUserAnalytics, user.id]"]