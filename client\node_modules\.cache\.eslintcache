[{"D:\\ecommerce\\client\\src\\index.js": "1", "D:\\ecommerce\\client\\src\\App.js": "2", "D:\\ecommerce\\client\\src\\components\\Cart.js": "3", "D:\\ecommerce\\client\\src\\components\\Checkout.js": "4", "D:\\ecommerce\\client\\src\\components\\Home.js": "5", "D:\\ecommerce\\client\\src\\components\\admin\\Dashboard.js": "6", "D:\\ecommerce\\client\\src\\components\\admin\\AdminDashboard.js": "7", "D:\\ecommerce\\client\\src\\components\\user\\UserDashboard.js": "8", "D:\\ecommerce\\client\\src\\components\\Navigation.js": "9", "D:\\ecommerce\\client\\src\\components\\DashboardSelector.js": "10", "D:\\ecommerce\\client\\src\\services\\api.js": "11"}, {"size": 254, "mtime": 1748675268490, "results": "12", "hashOfConfig": "13"}, {"size": 2310, "mtime": 1748677612992, "results": "14", "hashOfConfig": "13"}, {"size": 14255, "mtime": 1748677691669, "results": "15", "hashOfConfig": "13"}, {"size": 7271, "mtime": 1748673783025, "results": "16", "hashOfConfig": "13"}, {"size": 19804, "mtime": 1748677537153, "results": "17", "hashOfConfig": "13"}, {"size": 5785, "mtime": 1748675388232, "results": "18", "hashOfConfig": "13"}, {"size": 17236, "mtime": 1748676747481, "results": "19", "hashOfConfig": "13"}, {"size": 17999, "mtime": 1748676902158, "results": "20", "hashOfConfig": "13"}, {"size": 3981, "mtime": 1748676194624, "results": "21", "hashOfConfig": "13"}, {"size": 6126, "mtime": 1748676383238, "results": "22", "hashOfConfig": "13"}, {"size": 5096, "mtime": 1748677101391, "results": "23", "hashOfConfig": "13"}, {"filePath": "24", "messages": "25", "suppressedMessages": "26", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "9vwnw1", {"filePath": "27", "messages": "28", "suppressedMessages": "29", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "30", "messages": "31", "suppressedMessages": "32", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 4, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "33", "messages": "34", "suppressedMessages": "35", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "36", "messages": "37", "suppressedMessages": "38", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 11, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "39", "messages": "40", "suppressedMessages": "41", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "42", "messages": "43", "suppressedMessages": "44", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 5, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "45", "messages": "46", "suppressedMessages": "47", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 7, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "48", "messages": "49", "suppressedMessages": "50", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "51", "messages": "52", "suppressedMessages": "53", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 4, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "54", "messages": "55", "suppressedMessages": "56", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "D:\\ecommerce\\client\\src\\index.js", [], [], "D:\\ecommerce\\client\\src\\App.js", ["57", "58"], [], "D:\\ecommerce\\client\\src\\components\\Cart.js", ["59", "60", "61", "62"], [], "D:\\ecommerce\\client\\src\\components\\Checkout.js", [], [], "D:\\ecommerce\\client\\src\\components\\Home.js", ["63", "64", "65", "66", "67", "68", "69", "70", "71", "72", "73"], [], "D:\\ecommerce\\client\\src\\components\\admin\\Dashboard.js", [], [], "D:\\ecommerce\\client\\src\\components\\admin\\AdminDashboard.js", ["74", "75", "76", "77", "78"], [], "D:\\ecommerce\\client\\src\\components\\user\\UserDashboard.js", ["79", "80", "81", "82", "83", "84", "85"], [], "D:\\ecommerce\\client\\src\\components\\Navigation.js", [], [], "D:\\ecommerce\\client\\src\\components\\DashboardSelector.js", ["86", "87", "88", "89"], [], "D:\\ecommerce\\client\\src\\services\\api.js", [], [], {"ruleId": "90", "severity": 1, "message": "91", "line": 2, "column": 50, "nodeType": "92", "messageId": "93", "endLine": 2, "endColumn": 54}, {"ruleId": "90", "severity": 1, "message": "94", "line": 6, "column": 10, "nodeType": "92", "messageId": "93", "endLine": 6, "endColumn": 20}, {"ruleId": "90", "severity": 1, "message": "95", "line": 2, "column": 10, "nodeType": "92", "messageId": "93", "endLine": 2, "endColumn": 21}, {"ruleId": "90", "severity": 1, "message": "96", "line": 17, "column": 3, "nodeType": "92", "messageId": "93", "endLine": 17, "endColumn": 8}, {"ruleId": "90", "severity": 1, "message": "97", "line": 35, "column": 3, "nodeType": "92", "messageId": "93", "endLine": 35, "endColumn": 14}, {"ruleId": "90", "severity": 1, "message": "98", "line": 38, "column": 3, "nodeType": "92", "messageId": "93", "endLine": 38, "endColumn": 8}, {"ruleId": "90", "severity": 1, "message": "95", "line": 2, "column": 23, "nodeType": "92", "messageId": "93", "endLine": 2, "endColumn": 34}, {"ruleId": "90", "severity": 1, "message": "99", "line": 20, "column": 3, "nodeType": "92", "messageId": "93", "endLine": 20, "endColumn": 10}, {"ruleId": "90", "severity": 1, "message": "100", "line": 23, "column": 3, "nodeType": "92", "messageId": "93", "endLine": 23, "endColumn": 7}, {"ruleId": "90", "severity": 1, "message": "101", "line": 24, "column": 3, "nodeType": "92", "messageId": "93", "endLine": 24, "endColumn": 6}, {"ruleId": "90", "severity": 1, "message": "102", "line": 36, "column": 3, "nodeType": "92", "messageId": "93", "endLine": 36, "endColumn": 7}, {"ruleId": "90", "severity": 1, "message": "103", "line": 41, "column": 3, "nodeType": "92", "messageId": "93", "endLine": 41, "endColumn": 7}, {"ruleId": "90", "severity": 1, "message": "104", "line": 42, "column": 3, "nodeType": "92", "messageId": "93", "endLine": 42, "endColumn": 16}, {"ruleId": "90", "severity": 1, "message": "98", "line": 43, "column": 3, "nodeType": "92", "messageId": "93", "endLine": 43, "endColumn": 8}, {"ruleId": "90", "severity": 1, "message": "105", "line": 44, "column": 3, "nodeType": "92", "messageId": "93", "endLine": 44, "endColumn": 13}, {"ruleId": "90", "severity": 1, "message": "106", "line": 100, "column": 10, "nodeType": "92", "messageId": "93", "endLine": 100, "endColumn": 18}, {"ruleId": "90", "severity": 1, "message": "107", "line": 100, "column": 20, "nodeType": "92", "messageId": "93", "endLine": 100, "endColumn": 31}, {"ruleId": "90", "severity": 1, "message": "108", "line": 17, "column": 3, "nodeType": "92", "messageId": "93", "endLine": 17, "endColumn": 9}, {"ruleId": "90", "severity": 1, "message": "109", "line": 29, "column": 3, "nodeType": "92", "messageId": "93", "endLine": 29, "endColumn": 15}, {"ruleId": "90", "severity": 1, "message": "110", "line": 31, "column": 3, "nodeType": "92", "messageId": "93", "endLine": 31, "endColumn": 14}, {"ruleId": "90", "severity": 1, "message": "111", "line": 41, "column": 10, "nodeType": "92", "messageId": "93", "endLine": 41, "endColumn": 25}, {"ruleId": "112", "severity": 1, "message": "113", "line": 65, "column": 6, "nodeType": "114", "endLine": 65, "endColumn": 8, "suggestions": "115"}, {"ruleId": "90", "severity": 1, "message": "116", "line": 8, "column": 3, "nodeType": "92", "messageId": "93", "endLine": 8, "endColumn": 14}, {"ruleId": "90", "severity": 1, "message": "117", "line": 9, "column": 3, "nodeType": "92", "messageId": "93", "endLine": 9, "endColumn": 12}, {"ruleId": "90", "severity": 1, "message": "118", "line": 25, "column": 3, "nodeType": "92", "messageId": "93", "endLine": 25, "endColumn": 13}, {"ruleId": "90", "severity": 1, "message": "119", "line": 36, "column": 3, "nodeType": "92", "messageId": "93", "endLine": 36, "endColumn": 16}, {"ruleId": "90", "severity": 1, "message": "120", "line": 37, "column": 3, "nodeType": "92", "messageId": "93", "endLine": 37, "endColumn": 13}, {"ruleId": "90", "severity": 1, "message": "121", "line": 38, "column": 3, "nodeType": "92", "messageId": "93", "endLine": 38, "endColumn": 13}, {"ruleId": "112", "severity": 1, "message": "122", "line": 80, "column": 6, "nodeType": "114", "endLine": 80, "endColumn": 15, "suggestions": "123"}, {"ruleId": "90", "severity": 1, "message": "124", "line": 17, "column": 3, "nodeType": "92", "messageId": "93", "endLine": 17, "endColumn": 12}, {"ruleId": "90", "severity": 1, "message": "109", "line": 18, "column": 3, "nodeType": "92", "messageId": "93", "endLine": 18, "endColumn": 15}, {"ruleId": "90", "severity": 1, "message": "125", "line": 19, "column": 3, "nodeType": "92", "messageId": "93", "endLine": 19, "endColumn": 13}, {"ruleId": "90", "severity": 1, "message": "126", "line": 20, "column": 3, "nodeType": "92", "messageId": "93", "endLine": 20, "endColumn": 9}, "no-unused-vars", "'Link' is defined but never used.", "Identifier", "unusedVar", "'Typography' is defined but never used.", "'useSelector' is defined but never used.", "'Alert' is defined but never used.", "'CheckCircle' is defined but never used.", "'Share' is defined but never used.", "'Divider' is defined but never used.", "'Tabs' is defined but never used.", "'Tab' is defined but never used.", "'Star' is defined but never used.", "'Sort' is defined but never used.", "'CompareArrows' is defined but never used.", "'LocalOffer' is defined but never used.", "'tabValue' is assigned a value but never used.", "'setTabValue' is assigned a value but never used.", "'Avatar' is defined but never used.", "'ShoppingCart' is defined but never used.", "'AttachMoney' is defined but never used.", "'selectedProduct' is assigned a value but never used.", "react-hooks/exhaustive-deps", "React Hook useEffect has a missing dependency: 'fetchAnalytics'. Either include it or remove the dependency array.", "ArrayExpression", ["127"], "'CardContent' is defined but never used.", "'CardMedia' is defined but never used.", "'IconButton' is defined but never used.", "'AccountCircle' is defined but never used.", "'CreditCard' is defined but never used.", "'LocationOn' is defined but never used.", "React Hook useEffect has a missing dependency: 'fetchUserAnalytics'. Either include it or remove the dependency array.", ["128"], "'Analytics' is defined but never used.", "'TrendingUp' is defined but never used.", "'People' is defined but never used.", {"desc": "129", "fix": "130"}, {"desc": "131", "fix": "132"}, "Update the dependencies array to be: [fetchAnalytics]", {"range": "133", "text": "134"}, "Update the dependencies array to be: [fetchUserAnalytics, user.id]", {"range": "135", "text": "136"}, [1359, 1361], "[fetchAnalytics]", [1709, 1718], "[fetchUserAnalytics, user.id]"]