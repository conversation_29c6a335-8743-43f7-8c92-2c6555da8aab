[{"D:\\ecommerce\\client\\src\\index.js": "1", "D:\\ecommerce\\client\\src\\App.js": "2", "D:\\ecommerce\\client\\src\\components\\Cart.js": "3", "D:\\ecommerce\\client\\src\\components\\Checkout.js": "4", "D:\\ecommerce\\client\\src\\components\\Home.js": "5", "D:\\ecommerce\\client\\src\\components\\admin\\Dashboard.js": "6", "D:\\ecommerce\\client\\src\\components\\admin\\AdminDashboard.js": "7", "D:\\ecommerce\\client\\src\\components\\user\\UserDashboard.js": "8", "D:\\ecommerce\\client\\src\\components\\Navigation.js": "9", "D:\\ecommerce\\client\\src\\components\\DashboardSelector.js": "10", "D:\\ecommerce\\client\\src\\services\\api.js": "11", "D:\\ecommerce\\client\\src\\components\\auth\\Register.js": "12", "D:\\ecommerce\\client\\src\\components\\auth\\ProtectedRoute.js": "13", "D:\\ecommerce\\client\\src\\components\\auth\\Login.js": "14", "D:\\ecommerce\\client\\src\\context\\AuthContext.js": "15"}, {"size": 254, "mtime": 1748675268490, "results": "16", "hashOfConfig": "17"}, {"size": 3719, "mtime": 1748844638702, "results": "18", "hashOfConfig": "17"}, {"size": 14693, "mtime": 1748844682019, "results": "19", "hashOfConfig": "17"}, {"size": 14044, "mtime": 1748844777755, "results": "20", "hashOfConfig": "17"}, {"size": 21662, "mtime": 1748844607239, "results": "21", "hashOfConfig": "17"}, {"size": 5785, "mtime": 1748675388232, "results": "22", "hashOfConfig": "17"}, {"size": 17236, "mtime": 1748676747481, "results": "23", "hashOfConfig": "17"}, {"size": 17999, "mtime": 1748676902158, "results": "24", "hashOfConfig": "17"}, {"size": 3981, "mtime": 1748676194624, "results": "25", "hashOfConfig": "17"}, {"size": 6126, "mtime": 1748676383238, "results": "26", "hashOfConfig": "17"}, {"size": 5096, "mtime": 1748677101391, "results": "27", "hashOfConfig": "17"}, {"size": 10569, "mtime": 1748844522102, "results": "28", "hashOfConfig": "17"}, {"size": 1222, "mtime": 1748844549949, "results": "29", "hashOfConfig": "17"}, {"size": 8426, "mtime": 1748844491338, "results": "30", "hashOfConfig": "17"}, {"size": 3492, "mtime": 1748844539003, "results": "31", "hashOfConfig": "17"}, {"filePath": "32", "messages": "33", "suppressedMessages": "34", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "9vwnw1", {"filePath": "35", "messages": "36", "suppressedMessages": "37", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "38", "messages": "39", "suppressedMessages": "40", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 5, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "41", "messages": "42", "suppressedMessages": "43", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "44", "messages": "45", "suppressedMessages": "46", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 11, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "47", "messages": "48", "suppressedMessages": "49", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "50", "messages": "51", "suppressedMessages": "52", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 5, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "53", "messages": "54", "suppressedMessages": "55", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 7, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "56", "messages": "57", "suppressedMessages": "58", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "59", "messages": "60", "suppressedMessages": "61", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 4, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "62", "messages": "63", "suppressedMessages": "64", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "65", "messages": "66", "suppressedMessages": "67", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "68", "messages": "69", "suppressedMessages": "70", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "71", "messages": "72", "suppressedMessages": "73", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "74", "messages": "75", "suppressedMessages": "76", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "D:\\ecommerce\\client\\src\\index.js", [], [], "D:\\ecommerce\\client\\src\\App.js", ["77", "78"], [], "D:\\ecommerce\\client\\src\\components\\Cart.js", ["79", "80", "81", "82", "83"], [], "D:\\ecommerce\\client\\src\\components\\Checkout.js", ["84"], [], "D:\\ecommerce\\client\\src\\components\\Home.js", ["85", "86", "87", "88", "89", "90", "91", "92", "93", "94", "95"], [], "D:\\ecommerce\\client\\src\\components\\admin\\Dashboard.js", [], [], "D:\\ecommerce\\client\\src\\components\\admin\\AdminDashboard.js", ["96", "97", "98", "99", "100"], [], "D:\\ecommerce\\client\\src\\components\\user\\UserDashboard.js", ["101", "102", "103", "104", "105", "106", "107"], [], "D:\\ecommerce\\client\\src\\components\\Navigation.js", [], [], "D:\\ecommerce\\client\\src\\components\\DashboardSelector.js", ["108", "109", "110", "111"], [], "D:\\ecommerce\\client\\src\\services\\api.js", [], [], "D:\\ecommerce\\client\\src\\components\\auth\\Register.js", [], [], "D:\\ecommerce\\client\\src\\components\\auth\\ProtectedRoute.js", [], [], "D:\\ecommerce\\client\\src\\components\\auth\\Login.js", [], [], "D:\\ecommerce\\client\\src\\context\\AuthContext.js", [], [], {"ruleId": "112", "severity": 1, "message": "113", "line": 2, "column": 50, "nodeType": "114", "messageId": "115", "endLine": 2, "endColumn": 54}, {"ruleId": "112", "severity": 1, "message": "116", "line": 6, "column": 10, "nodeType": "114", "messageId": "115", "endLine": 6, "endColumn": 20}, {"ruleId": "112", "severity": 1, "message": "117", "line": 2, "column": 10, "nodeType": "114", "messageId": "115", "endLine": 2, "endColumn": 21}, {"ruleId": "112", "severity": 1, "message": "118", "line": 17, "column": 3, "nodeType": "114", "messageId": "115", "endLine": 17, "endColumn": 8}, {"ruleId": "112", "severity": 1, "message": "119", "line": 35, "column": 3, "nodeType": "114", "messageId": "115", "endLine": 35, "endColumn": 14}, {"ruleId": "112", "severity": 1, "message": "120", "line": 38, "column": 3, "nodeType": "114", "messageId": "115", "endLine": 38, "endColumn": 8}, {"ruleId": "112", "severity": 1, "message": "121", "line": 64, "column": 11, "nodeType": "114", "messageId": "115", "endLine": 64, "endColumn": 15}, {"ruleId": "112", "severity": 1, "message": "122", "line": 19, "column": 3, "nodeType": "114", "messageId": "115", "endLine": 19, "endColumn": 12}, {"ruleId": "112", "severity": 1, "message": "117", "line": 2, "column": 23, "nodeType": "114", "messageId": "115", "endLine": 2, "endColumn": 34}, {"ruleId": "112", "severity": 1, "message": "123", "line": 20, "column": 3, "nodeType": "114", "messageId": "115", "endLine": 20, "endColumn": 10}, {"ruleId": "112", "severity": 1, "message": "124", "line": 23, "column": 3, "nodeType": "114", "messageId": "115", "endLine": 23, "endColumn": 7}, {"ruleId": "112", "severity": 1, "message": "125", "line": 24, "column": 3, "nodeType": "114", "messageId": "115", "endLine": 24, "endColumn": 6}, {"ruleId": "112", "severity": 1, "message": "126", "line": 36, "column": 3, "nodeType": "114", "messageId": "115", "endLine": 36, "endColumn": 7}, {"ruleId": "112", "severity": 1, "message": "127", "line": 41, "column": 3, "nodeType": "114", "messageId": "115", "endLine": 41, "endColumn": 7}, {"ruleId": "112", "severity": 1, "message": "128", "line": 42, "column": 3, "nodeType": "114", "messageId": "115", "endLine": 42, "endColumn": 16}, {"ruleId": "112", "severity": 1, "message": "120", "line": 43, "column": 3, "nodeType": "114", "messageId": "115", "endLine": 43, "endColumn": 8}, {"ruleId": "112", "severity": 1, "message": "129", "line": 44, "column": 3, "nodeType": "114", "messageId": "115", "endLine": 44, "endColumn": 13}, {"ruleId": "112", "severity": 1, "message": "130", "line": 104, "column": 10, "nodeType": "114", "messageId": "115", "endLine": 104, "endColumn": 18}, {"ruleId": "112", "severity": 1, "message": "131", "line": 104, "column": 20, "nodeType": "114", "messageId": "115", "endLine": 104, "endColumn": 31}, {"ruleId": "112", "severity": 1, "message": "132", "line": 17, "column": 3, "nodeType": "114", "messageId": "115", "endLine": 17, "endColumn": 9}, {"ruleId": "112", "severity": 1, "message": "133", "line": 29, "column": 3, "nodeType": "114", "messageId": "115", "endLine": 29, "endColumn": 15}, {"ruleId": "112", "severity": 1, "message": "134", "line": 31, "column": 3, "nodeType": "114", "messageId": "115", "endLine": 31, "endColumn": 14}, {"ruleId": "112", "severity": 1, "message": "135", "line": 41, "column": 10, "nodeType": "114", "messageId": "115", "endLine": 41, "endColumn": 25}, {"ruleId": "136", "severity": 1, "message": "137", "line": 65, "column": 6, "nodeType": "138", "endLine": 65, "endColumn": 8, "suggestions": "139"}, {"ruleId": "112", "severity": 1, "message": "140", "line": 8, "column": 3, "nodeType": "114", "messageId": "115", "endLine": 8, "endColumn": 14}, {"ruleId": "112", "severity": 1, "message": "141", "line": 9, "column": 3, "nodeType": "114", "messageId": "115", "endLine": 9, "endColumn": 12}, {"ruleId": "112", "severity": 1, "message": "142", "line": 25, "column": 3, "nodeType": "114", "messageId": "115", "endLine": 25, "endColumn": 13}, {"ruleId": "112", "severity": 1, "message": "143", "line": 36, "column": 3, "nodeType": "114", "messageId": "115", "endLine": 36, "endColumn": 16}, {"ruleId": "112", "severity": 1, "message": "144", "line": 37, "column": 3, "nodeType": "114", "messageId": "115", "endLine": 37, "endColumn": 13}, {"ruleId": "112", "severity": 1, "message": "145", "line": 38, "column": 3, "nodeType": "114", "messageId": "115", "endLine": 38, "endColumn": 13}, {"ruleId": "136", "severity": 1, "message": "146", "line": 80, "column": 6, "nodeType": "138", "endLine": 80, "endColumn": 15, "suggestions": "147"}, {"ruleId": "112", "severity": 1, "message": "148", "line": 17, "column": 3, "nodeType": "114", "messageId": "115", "endLine": 17, "endColumn": 12}, {"ruleId": "112", "severity": 1, "message": "133", "line": 18, "column": 3, "nodeType": "114", "messageId": "115", "endLine": 18, "endColumn": 15}, {"ruleId": "112", "severity": 1, "message": "149", "line": 19, "column": 3, "nodeType": "114", "messageId": "115", "endLine": 19, "endColumn": 13}, {"ruleId": "112", "severity": 1, "message": "150", "line": 20, "column": 3, "nodeType": "114", "messageId": "115", "endLine": 20, "endColumn": 9}, "no-unused-vars", "'Link' is defined but never used.", "Identifier", "unusedVar", "'Typography' is defined but never used.", "'useSelector' is defined but never used.", "'Alert' is defined but never used.", "'CheckCircle' is defined but never used.", "'Share' is defined but never used.", "'user' is assigned a value but never used.", "'FormLabel' is defined but never used.", "'Divider' is defined but never used.", "'Tabs' is defined but never used.", "'Tab' is defined but never used.", "'Star' is defined but never used.", "'Sort' is defined but never used.", "'CompareArrows' is defined but never used.", "'LocalOffer' is defined but never used.", "'tabValue' is assigned a value but never used.", "'setTabValue' is assigned a value but never used.", "'Avatar' is defined but never used.", "'ShoppingCart' is defined but never used.", "'AttachMoney' is defined but never used.", "'selectedProduct' is assigned a value but never used.", "react-hooks/exhaustive-deps", "React Hook useEffect has a missing dependency: 'fetchAnalytics'. Either include it or remove the dependency array.", "ArrayExpression", ["151"], "'CardContent' is defined but never used.", "'CardMedia' is defined but never used.", "'IconButton' is defined but never used.", "'AccountCircle' is defined but never used.", "'CreditCard' is defined but never used.", "'LocationOn' is defined but never used.", "React Hook useEffect has a missing dependency: 'fetchUserAnalytics'. Either include it or remove the dependency array.", ["152"], "'Analytics' is defined but never used.", "'TrendingUp' is defined but never used.", "'People' is defined but never used.", {"desc": "153", "fix": "154"}, {"desc": "155", "fix": "156"}, "Update the dependencies array to be: [fetchAnalytics]", {"range": "157", "text": "158"}, "Update the dependencies array to be: [fetchUserAnalytics, user.id]", {"range": "159", "text": "160"}, [1359, 1361], "[fetchAnalytics]", [1709, 1718], "[fetchUserAnalytics, user.id]"]