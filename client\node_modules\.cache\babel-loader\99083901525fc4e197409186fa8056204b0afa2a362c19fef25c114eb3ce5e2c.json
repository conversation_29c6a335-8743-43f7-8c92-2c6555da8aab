{"ast": null, "code": "var createBaseFor = require('./_createBaseFor');\n\n/**\n * The base implementation of `baseForOwn` which iterates over `object`\n * properties returned by `keysFunc` and invokes `iteratee` for each property.\n * Iteratee functions may exit iteration early by explicitly returning `false`.\n *\n * @private\n * @param {Object} object The object to iterate over.\n * @param {Function} iteratee The function invoked per iteration.\n * @param {Function} keysFunc The function to get the keys of `object`.\n * @returns {Object} Returns `object`.\n */\nvar baseFor = createBaseFor();\nmodule.exports = baseFor;", "map": {"version": 3, "names": ["createBaseFor", "require", "baseFor", "module", "exports"], "sources": ["D:/ecommerce/node_modules/lodash/_baseFor.js"], "sourcesContent": ["var createBaseFor = require('./_createBaseFor');\n\n/**\n * The base implementation of `baseForOwn` which iterates over `object`\n * properties returned by `keysFunc` and invokes `iteratee` for each property.\n * Iteratee functions may exit iteration early by explicitly returning `false`.\n *\n * @private\n * @param {Object} object The object to iterate over.\n * @param {Function} iteratee The function invoked per iteration.\n * @param {Function} keysFunc The function to get the keys of `object`.\n * @returns {Object} Returns `object`.\n */\nvar baseFor = createBaseFor();\n\nmodule.exports = baseFor;\n"], "mappings": "AAAA,IAAIA,aAAa,GAAGC,OAAO,CAAC,kBAAkB,CAAC;;AAE/C;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,IAAIC,OAAO,GAAGF,aAAa,CAAC,CAAC;AAE7BG,MAAM,CAACC,OAAO,GAAGF,OAAO", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}