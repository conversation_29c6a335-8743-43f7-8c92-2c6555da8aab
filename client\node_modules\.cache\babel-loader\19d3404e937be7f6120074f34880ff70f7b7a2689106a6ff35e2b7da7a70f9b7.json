{"ast": null, "code": "var identity = require('./identity'),\n  overRest = require('./_overRest'),\n  setToString = require('./_setToString');\n\n/**\n * The base implementation of `_.rest` which doesn't validate or coerce arguments.\n *\n * @private\n * @param {Function} func The function to apply a rest parameter to.\n * @param {number} [start=func.length-1] The start position of the rest parameter.\n * @returns {Function} Returns the new function.\n */\nfunction baseRest(func, start) {\n  return setToString(overRest(func, start, identity), func + '');\n}\nmodule.exports = baseRest;", "map": {"version": 3, "names": ["identity", "require", "overRest", "setToString", "baseRest", "func", "start", "module", "exports"], "sources": ["D:/ecommerce/node_modules/lodash/_baseRest.js"], "sourcesContent": ["var identity = require('./identity'),\n    overRest = require('./_overRest'),\n    setToString = require('./_setToString');\n\n/**\n * The base implementation of `_.rest` which doesn't validate or coerce arguments.\n *\n * @private\n * @param {Function} func The function to apply a rest parameter to.\n * @param {number} [start=func.length-1] The start position of the rest parameter.\n * @returns {Function} Returns the new function.\n */\nfunction baseRest(func, start) {\n  return setToString(overRest(func, start, identity), func + '');\n}\n\nmodule.exports = baseRest;\n"], "mappings": "AAAA,IAAIA,QAAQ,GAAGC,OAAO,CAAC,YAAY,CAAC;EAChCC,QAAQ,GAAGD,OAAO,CAAC,aAAa,CAAC;EACjCE,WAAW,GAAGF,OAAO,CAAC,gBAAgB,CAAC;;AAE3C;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASG,QAAQA,CAACC,IAAI,EAAEC,KAAK,EAAE;EAC7B,OAAOH,WAAW,CAACD,QAAQ,CAACG,IAAI,EAAEC,KAAK,EAAEN,QAAQ,CAAC,EAAEK,IAAI,GAAG,EAAE,CAAC;AAChE;AAEAE,MAAM,CAACC,OAAO,GAAGJ,QAAQ", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}