{"ast": null, "code": "var baseEach = require('./_baseEach');\n\n/**\n * The base implementation of `_.some` without support for iteratee shorthands.\n *\n * @private\n * @param {Array|Object} collection The collection to iterate over.\n * @param {Function} predicate The function invoked per iteration.\n * @returns {boolean} Returns `true` if any element passes the predicate check,\n *  else `false`.\n */\nfunction baseSome(collection, predicate) {\n  var result;\n  baseEach(collection, function (value, index, collection) {\n    result = predicate(value, index, collection);\n    return !result;\n  });\n  return !!result;\n}\nmodule.exports = baseSome;", "map": {"version": 3, "names": ["baseEach", "require", "baseSome", "collection", "predicate", "result", "value", "index", "module", "exports"], "sources": ["D:/ecommerce/node_modules/lodash/_baseSome.js"], "sourcesContent": ["var baseEach = require('./_baseEach');\n\n/**\n * The base implementation of `_.some` without support for iteratee shorthands.\n *\n * @private\n * @param {Array|Object} collection The collection to iterate over.\n * @param {Function} predicate The function invoked per iteration.\n * @returns {boolean} Returns `true` if any element passes the predicate check,\n *  else `false`.\n */\nfunction baseSome(collection, predicate) {\n  var result;\n\n  baseEach(collection, function(value, index, collection) {\n    result = predicate(value, index, collection);\n    return !result;\n  });\n  return !!result;\n}\n\nmodule.exports = baseSome;\n"], "mappings": "AAAA,IAAIA,QAAQ,GAAGC,OAAO,CAAC,aAAa,CAAC;;AAErC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASC,QAAQA,CAACC,UAAU,EAAEC,SAAS,EAAE;EACvC,IAAIC,MAAM;EAEVL,QAAQ,CAACG,UAAU,EAAE,UAASG,KAAK,EAAEC,KAAK,EAAEJ,UAAU,EAAE;IACtDE,MAAM,GAAGD,SAAS,CAACE,KAAK,EAAEC,KAAK,EAAEJ,UAAU,CAAC;IAC5C,OAAO,CAACE,MAAM;EAChB,CAAC,CAAC;EACF,OAAO,CAAC,CAACA,MAAM;AACjB;AAEAG,MAAM,CAACC,OAAO,GAAGP,QAAQ", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}