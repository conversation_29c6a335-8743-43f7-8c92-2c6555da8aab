{"ast": null, "code": "import { bisect } from \"d3-array\";\nimport { initRange } from \"./init.js\";\nexport default function threshold() {\n  var domain = [0.5],\n    range = [0, 1],\n    unknown,\n    n = 1;\n  function scale(x) {\n    return x != null && x <= x ? range[bisect(domain, x, 0, n)] : unknown;\n  }\n  scale.domain = function (_) {\n    return arguments.length ? (domain = Array.from(_), n = Math.min(domain.length, range.length - 1), scale) : domain.slice();\n  };\n  scale.range = function (_) {\n    return arguments.length ? (range = Array.from(_), n = Math.min(domain.length, range.length - 1), scale) : range.slice();\n  };\n  scale.invertExtent = function (y) {\n    var i = range.indexOf(y);\n    return [domain[i - 1], domain[i]];\n  };\n  scale.unknown = function (_) {\n    return arguments.length ? (unknown = _, scale) : unknown;\n  };\n  scale.copy = function () {\n    return threshold().domain(domain).range(range).unknown(unknown);\n  };\n  return initRange.apply(scale, arguments);\n}", "map": {"version": 3, "names": ["bisect", "initRange", "threshold", "domain", "range", "unknown", "n", "scale", "x", "_", "arguments", "length", "Array", "from", "Math", "min", "slice", "invertExtent", "y", "i", "indexOf", "copy", "apply"], "sources": ["D:/ecommerce/node_modules/d3-scale/src/threshold.js"], "sourcesContent": ["import {bisect} from \"d3-array\";\nimport {initRange} from \"./init.js\";\n\nexport default function threshold() {\n  var domain = [0.5],\n      range = [0, 1],\n      unknown,\n      n = 1;\n\n  function scale(x) {\n    return x != null && x <= x ? range[bisect(domain, x, 0, n)] : unknown;\n  }\n\n  scale.domain = function(_) {\n    return arguments.length ? (domain = Array.from(_), n = Math.min(domain.length, range.length - 1), scale) : domain.slice();\n  };\n\n  scale.range = function(_) {\n    return arguments.length ? (range = Array.from(_), n = Math.min(domain.length, range.length - 1), scale) : range.slice();\n  };\n\n  scale.invertExtent = function(y) {\n    var i = range.indexOf(y);\n    return [domain[i - 1], domain[i]];\n  };\n\n  scale.unknown = function(_) {\n    return arguments.length ? (unknown = _, scale) : unknown;\n  };\n\n  scale.copy = function() {\n    return threshold()\n        .domain(domain)\n        .range(range)\n        .unknown(unknown);\n  };\n\n  return initRange.apply(scale, arguments);\n}\n"], "mappings": "AAAA,SAAQA,MAAM,QAAO,UAAU;AAC/B,SAAQC,SAAS,QAAO,WAAW;AAEnC,eAAe,SAASC,SAASA,CAAA,EAAG;EAClC,IAAIC,MAAM,GAAG,CAAC,GAAG,CAAC;IACdC,KAAK,GAAG,CAAC,CAAC,EAAE,CAAC,CAAC;IACdC,OAAO;IACPC,CAAC,GAAG,CAAC;EAET,SAASC,KAAKA,CAACC,CAAC,EAAE;IAChB,OAAOA,CAAC,IAAI,IAAI,IAAIA,CAAC,IAAIA,CAAC,GAAGJ,KAAK,CAACJ,MAAM,CAACG,MAAM,EAAEK,CAAC,EAAE,CAAC,EAAEF,CAAC,CAAC,CAAC,GAAGD,OAAO;EACvE;EAEAE,KAAK,CAACJ,MAAM,GAAG,UAASM,CAAC,EAAE;IACzB,OAAOC,SAAS,CAACC,MAAM,IAAIR,MAAM,GAAGS,KAAK,CAACC,IAAI,CAACJ,CAAC,CAAC,EAAEH,CAAC,GAAGQ,IAAI,CAACC,GAAG,CAACZ,MAAM,CAACQ,MAAM,EAAEP,KAAK,CAACO,MAAM,GAAG,CAAC,CAAC,EAAEJ,KAAK,IAAIJ,MAAM,CAACa,KAAK,CAAC,CAAC;EAC3H,CAAC;EAEDT,KAAK,CAACH,KAAK,GAAG,UAASK,CAAC,EAAE;IACxB,OAAOC,SAAS,CAACC,MAAM,IAAIP,KAAK,GAAGQ,KAAK,CAACC,IAAI,CAACJ,CAAC,CAAC,EAAEH,CAAC,GAAGQ,IAAI,CAACC,GAAG,CAACZ,MAAM,CAACQ,MAAM,EAAEP,KAAK,CAACO,MAAM,GAAG,CAAC,CAAC,EAAEJ,KAAK,IAAIH,KAAK,CAACY,KAAK,CAAC,CAAC;EACzH,CAAC;EAEDT,KAAK,CAACU,YAAY,GAAG,UAASC,CAAC,EAAE;IAC/B,IAAIC,CAAC,GAAGf,KAAK,CAACgB,OAAO,CAACF,CAAC,CAAC;IACxB,OAAO,CAACf,MAAM,CAACgB,CAAC,GAAG,CAAC,CAAC,EAAEhB,MAAM,CAACgB,CAAC,CAAC,CAAC;EACnC,CAAC;EAEDZ,KAAK,CAACF,OAAO,GAAG,UAASI,CAAC,EAAE;IAC1B,OAAOC,SAAS,CAACC,MAAM,IAAIN,OAAO,GAAGI,CAAC,EAAEF,KAAK,IAAIF,OAAO;EAC1D,CAAC;EAEDE,KAAK,CAACc,IAAI,GAAG,YAAW;IACtB,OAAOnB,SAAS,CAAC,CAAC,CACbC,MAAM,CAACA,MAAM,CAAC,CACdC,KAAK,CAACA,KAAK,CAAC,CACZC,OAAO,CAACA,OAAO,CAAC;EACvB,CAAC;EAED,OAAOJ,SAAS,CAACqB,KAAK,CAACf,KAAK,EAAEG,SAAS,CAAC;AAC1C", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}