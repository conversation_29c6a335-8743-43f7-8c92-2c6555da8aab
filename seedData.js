const mongoose = require('mongoose');
const bcrypt = require('bcryptjs');
const Product = require('./models/Product');
const User = require('./models/User');
const Order = require('./models/Order');

// Connect to MongoDB
mongoose.connect('mongodb://localhost:27017/spice-ecommerce', {
  useNewUrlParser: true,
  useUnifiedTopology: true,
});

const seedData = async () => {
  try {
    // Clear existing data
    await Product.deleteMany({});
    await User.deleteMany({});
    await Order.deleteMany({});

    console.log('Cleared existing data');

    // Create sample products
    const products = await Product.insertMany([
      {
        name: 'Ceylon Cinnamon Sticks',
        description: 'Premium quality Ceylon cinnamon sticks from Sri Lanka. Perfect for baking and cooking.',
        price: 15.99,
        image: 'https://images.unsplash.com/photo-1506905925346-21bda4d32df4?w=300',
        category: 'Whole Spices',
        stock: 50,
        origin: 'Sri Lanka',
        weight: 100,
        unit: 'g',
        rating: 4.8,
        numReviews: 24,
        featured: true
      },
      {
        name: 'Black Peppercorns',
        description: 'Freshly ground black peppercorns with intense flavor and aroma.',
        price: 12.50,
        image: 'https://images.unsplash.com/photo-1596040033229-a9821ebd058d?w=300',
        category: 'Whole Spices',
        stock: 30,
        origin: 'India',
        weight: 50,
        unit: 'g',
        rating: 4.9,
        numReviews: 18
      },
      {
        name: 'Organic Turmeric Powder',
        description: 'Pure organic turmeric powder with anti-inflammatory properties.',
        price: 18.75,
        image: 'https://images.unsplash.com/photo-1615485500704-8e990f9900f7?w=300',
        category: 'Ground Spices',
        stock: 25,
        origin: 'India',
        weight: 200,
        unit: 'g',
        rating: 4.7,
        numReviews: 32,
        featured: true
      },
      {
        name: 'Green Cardamom Pods',
        description: 'Aromatic green cardamom pods perfect for tea and desserts.',
        price: 24.99,
        image: 'https://images.unsplash.com/photo-1596040033229-a9821ebd058d?w=300',
        category: 'Whole Spices',
        stock: 40,
        origin: 'Guatemala',
        weight: 50,
        unit: 'g',
        rating: 4.6,
        numReviews: 15
      },
      {
        name: 'Star Anise',
        description: 'Whole star anise with sweet licorice flavor for cooking and brewing.',
        price: 16.99,
        image: 'https://images.unsplash.com/photo-1506905925346-21bda4d32df4?w=300',
        category: 'Whole Spices',
        stock: 35,
        origin: 'China',
        weight: 75,
        unit: 'g',
        rating: 4.5,
        numReviews: 12
      },
      {
        name: 'Whole Cloves',
        description: 'Premium whole cloves with intense aroma and flavor.',
        price: 13.50,
        image: 'https://images.unsplash.com/photo-1596040033229-a9821ebd058d?w=300',
        category: 'Whole Spices',
        stock: 28,
        origin: 'Madagascar',
        weight: 50,
        unit: 'g',
        rating: 4.4,
        numReviews: 9
      },
      {
        name: 'Nutmeg Whole',
        description: 'Fresh whole nutmeg for grating. Perfect for baking and cooking.',
        price: 19.99,
        image: 'https://images.unsplash.com/photo-1506905925346-21bda4d32df4?w=300',
        category: 'Whole Spices',
        stock: 22,
        origin: 'Indonesia',
        weight: 25,
        unit: 'g',
        rating: 4.3,
        numReviews: 7
      },
      {
        name: 'Saffron Threads',
        description: 'Premium saffron threads - the most expensive spice in the world.',
        price: 89.99,
        image: 'https://images.unsplash.com/photo-1615485500704-8e990f9900f7?w=300',
        category: 'Herbs',
        stock: 10,
        origin: 'Kashmir',
        weight: 1,
        unit: 'g',
        rating: 4.9,
        numReviews: 45,
        featured: true
      }
    ]);

    console.log('Created sample products');

    // Create sample users
    const hashedPassword = await bcrypt.hash('123456', 10);

    const users = await User.insertMany([
      {
        name: 'John Doe',
        email: '<EMAIL>',
        password: hashedPassword,
        isAdmin: false
      },
      {
        name: 'Jane Smith',
        email: '<EMAIL>',
        password: hashedPassword,
        isAdmin: false
      },
      {
        name: 'Admin User',
        email: '<EMAIL>',
        password: hashedPassword,
        isAdmin: true
      }
    ]);

    console.log('Created sample users');

    // Create sample orders
    const orders = await Order.insertMany([
      {
        user: users[0]._id,
        orderItems: [
          {
            name: products[0].name,
            quantity: 2,
            image: products[0].image,
            price: products[0].price,
            product: products[0]._id
          },
          {
            name: products[1].name,
            quantity: 1,
            image: products[1].image,
            price: products[1].price,
            product: products[1]._id
          }
        ],
        shippingAddress: {
          address: '123 Main St',
          city: 'New York',
          postalCode: '10001',
          country: 'USA'
        },
        paymentMethod: 'PayPal',
        itemsPrice: 44.48,
        taxPrice: 4.45,
        shippingPrice: 5.99,
        totalPrice: 54.92,
        isPaid: true,
        paidAt: new Date(),
        isDelivered: true,
        deliveredAt: new Date()
      },
      {
        user: users[1]._id,
        orderItems: [
          {
            name: products[2].name,
            quantity: 1,
            image: products[2].image,
            price: products[2].price,
            product: products[2]._id
          }
        ],
        shippingAddress: {
          address: '456 Oak Ave',
          city: 'Los Angeles',
          postalCode: '90210',
          country: 'USA'
        },
        paymentMethod: 'Credit Card',
        itemsPrice: 18.75,
        taxPrice: 1.88,
        shippingPrice: 5.99,
        totalPrice: 26.62,
        isPaid: true,
        paidAt: new Date(),
        isDelivered: false
      },
      {
        user: users[0]._id,
        orderItems: [
          {
            name: products[7].name,
            quantity: 1,
            image: products[7].image,
            price: products[7].price,
            product: products[7]._id
          }
        ],
        shippingAddress: {
          address: '123 Main St',
          city: 'New York',
          postalCode: '10001',
          country: 'USA'
        },
        paymentMethod: 'PayPal',
        itemsPrice: 89.99,
        taxPrice: 9.00,
        shippingPrice: 5.99,
        totalPrice: 104.98,
        isPaid: false,
        isDelivered: false
      }
    ]);

    console.log('Created sample orders');
    console.log('Database seeded successfully!');

    // Log some useful info
    console.log('\nSample login credentials:');
    console.log('User: <EMAIL> / 123456');
    console.log('Admin: <EMAIL> / 123456');

    process.exit(0);
  } catch (error) {
    console.error('Error seeding database:', error);
    process.exit(1);
  }
};

seedData();
