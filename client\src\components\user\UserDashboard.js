import React, { useState, useEffect } from 'react';
import {
  Box,
  Grid,
  Paper,
  Typography,
  Card,
  CardContent,
  CardMedia,
  Button,
  Avatar,
  Chip,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  LinearProgress,
  Divider,
  List,
  ListItem,
  ListItemText,
  ListItemAvatar,
  IconButton,
  Badge,
  CircularProgress,
  Alert,
} from '@mui/material';
import {
  ShoppingBag,
  FavoriteRounded,
  LocalShipping,
  Star,
  Notifications,
  AccountCircle,
  CreditCard,
  LocationOn,
  Settings,
  History,
  TrendingUp,
  ShoppingCart,
  Refresh,
} from '@mui/icons-material';
import { analyticsAPI, cartAPI, handleAPIError } from '../../services/api';

const UserDashboard = () => {
  const [userAnalytics, setUserAnalytics] = useState(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);

  // Mock user data (in real app, this would come from auth context)
  const [user] = useState({
    id: '507f1f77bcf86cd799439011', // Mock user ID
    name: '<PERSON>',
    email: '<EMAIL>',
    avatar: '/api/placeholder/100/100',
    memberSince: 'January 2023',
  });

  // Fetch user analytics data
  const fetchUserAnalytics = async () => {
    try {
      setLoading(true);
      setError(null);
      const response = await analyticsAPI.getUserAnalytics(user.id);
      setUserAnalytics(response.data);
    } catch (err) {
      setError(handleAPIError(err));
      console.error('Failed to fetch user analytics:', err);
      // Fallback to mock data
      setUserAnalytics(getMockUserAnalytics());
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchUserAnalytics();
  }, [user.id]);

  // Mock data fallback
  const getMockUserAnalytics = () => ({
    user: {
      totalOrders: 24,
      totalSpent: 1247.50,
      loyaltyPoints: 2450,
      nextRewardAt: 3000,
      memberSince: 'January 2023'
    },
    recentOrders: [
      { id: 'ORD001', date: '2024-01-15', items: ['Cinnamon Sticks', 'Black Pepper'], total: 34.99, status: 'Delivered', image: '🍯' },
      { id: 'ORD002', date: '2024-01-10', items: ['Turmeric Powder', 'Cardamom'], total: 28.50, status: 'Shipped', image: '🟡' },
      { id: 'ORD003', date: '2024-01-05', items: ['Saffron Threads'], total: 89.99, status: 'Processing', image: '🟠' },
    ],
    favoriteProducts: [
      { id: 1, name: 'Ceylon Cinnamon', price: 15.99, image: '🍯', rating: 4.8, inStock: true },
      { id: 2, name: 'Black Peppercorns', price: 12.50, image: '⚫', rating: 4.9, inStock: true },
      { id: 3, name: 'Organic Turmeric', price: 18.75, image: '🟡', rating: 4.7, inStock: false },
      { id: 4, name: 'Cardamom Pods', price: 24.99, image: '🟢', rating: 4.6, inStock: true },
    ],
    recommendations: [
      { id: 1, name: 'Star Anise', price: 16.99, image: '⭐', discount: 15 },
      { id: 2, name: 'Cloves', price: 13.50, image: '🟤', discount: 10 },
      { id: 3, name: 'Nutmeg', price: 19.99, image: '🥜', discount: 20 },
    ],
    notifications: [
      { id: 1, message: 'Your order #ORD001 has been delivered', time: '2 hours ago', type: 'delivery' },
      { id: 2, message: 'New spices from Kerala are now available!', time: '1 day ago', type: 'promotion' },
      { id: 3, message: 'You have 550 points expiring soon', time: '3 days ago', type: 'points' },
    ]
  });

  const handleAddToCart = (product) => {
    cartAPI.addToCart(product);
    // You could add a toast notification here
    console.log(`Added ${product.name} to cart`);
  };

  if (loading) {
    return (
      <Box sx={{ display: 'flex', justifyContent: 'center', alignItems: 'center', height: '100vh' }}>
        <CircularProgress size={60} />
      </Box>
    );
  }

  const userData = userAnalytics?.user || {
    totalOrders: 24,
    totalSpent: 1247.50,
    loyaltyPoints: 2450,
    nextRewardAt: 3000,
    memberSince: 'January 2023'
  };

  const recentOrders = userAnalytics?.recentOrders || [
    { id: 'ORD001', date: '2024-01-15', items: ['Cinnamon Sticks', 'Black Pepper'], total: 34.99, status: 'Delivered', image: '🍯' },
    { id: 'ORD002', date: '2024-01-10', items: ['Turmeric Powder', 'Cardamom'], total: 28.50, status: 'Shipped', image: '🟡' },
    { id: 'ORD003', date: '2024-01-05', items: ['Saffron Threads'], total: 89.99, status: 'Processing', image: '🟠' },
  ];

  const favoriteProducts = userAnalytics?.favoriteProducts || [
    { id: 1, name: 'Ceylon Cinnamon', price: 15.99, image: '🍯', rating: 4.8, inStock: true },
    { id: 2, name: 'Black Peppercorns', price: 12.50, image: '⚫', rating: 4.9, inStock: true },
    { id: 3, name: 'Organic Turmeric', price: 18.75, image: '🟡', rating: 4.7, inStock: false },
    { id: 4, name: 'Cardamom Pods', price: 24.99, image: '🟢', rating: 4.6, inStock: true },
  ];

  const recommendations = userAnalytics?.recommendations || [
    { id: 1, name: 'Star Anise', price: 16.99, image: '⭐', discount: 15 },
    { id: 2, name: 'Cloves', price: 13.50, image: '🟤', discount: 10 },
    { id: 3, name: 'Nutmeg', price: 19.99, image: '🥜', discount: 20 },
  ];

  const notifications = userAnalytics?.notifications || [
    { id: 1, message: 'Your order #ORD001 has been delivered', time: '2 hours ago', type: 'delivery' },
    { id: 2, message: 'New spices from Kerala are now available!', time: '1 day ago', type: 'promotion' },
    { id: 3, message: 'You have 550 points expiring soon', time: '3 days ago', type: 'points' },
  ];

  const getStatusColor = (status) => {
    switch (status) {
      case 'Delivered': return 'success';
      case 'Shipped': return 'info';
      case 'Processing': return 'warning';
      case 'Cancelled': return 'error';
      default: return 'default';
    }
  };

  const loyaltyProgress = (userData.loyaltyPoints / userData.nextRewardAt) * 100;

  return (
    <Box sx={{ p: 3, backgroundColor: '#f8f9fa', minHeight: '100vh' }}>
      <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 3 }}>
        <Typography variant="h4" sx={{ fontWeight: 'bold' }}>
          Welcome back, {user.name}! 👋
        </Typography>
        <Button
          variant="outlined"
          startIcon={<Refresh />}
          onClick={fetchUserAnalytics}
          disabled={loading}
        >
          Refresh
        </Button>
      </Box>

      {error && (
        <Alert severity="warning" sx={{ mb: 3 }}>
          {error} - Showing fallback data
        </Alert>
      )}

      <Grid container spacing={3}>
        {/* User Profile Card */}
        <Grid item xs={12} md={4}>
          <Paper sx={{ p: 3, textAlign: 'center', height: 'fit-content' }}>
            <Avatar
              src={user.avatar}
              sx={{ width: 80, height: 80, mx: 'auto', mb: 2 }}
            />
            <Typography variant="h6" sx={{ fontWeight: 'bold' }}>
              {user.name}
            </Typography>
            <Typography variant="body2" color="textSecondary" sx={{ mb: 2 }}>
              {user.email}
            </Typography>
            <Typography variant="caption" color="textSecondary">
              Member since {user.memberSince}
            </Typography>

            <Divider sx={{ my: 2 }} />

            <Grid container spacing={2} sx={{ textAlign: 'center' }}>
              <Grid item xs={4}>
                <Typography variant="h6" color="primary">{userData.totalOrders}</Typography>
                <Typography variant="caption">Orders</Typography>
              </Grid>
              <Grid item xs={4}>
                <Typography variant="h6" color="primary">${userData.totalSpent}</Typography>
                <Typography variant="caption">Spent</Typography>
              </Grid>
              <Grid item xs={4}>
                <Typography variant="h6" color="primary">{userData.loyaltyPoints}</Typography>
                <Typography variant="caption">Points</Typography>
              </Grid>
            </Grid>

            <Button variant="contained" fullWidth sx={{ mt: 2 }}>
              <Settings sx={{ mr: 1 }} />
              Edit Profile
            </Button>
          </Paper>
        </Grid>

        {/* Quick Stats */}
        <Grid item xs={12} md={8}>
          <Grid container spacing={2} sx={{ mb: 3 }}>
            <Grid item xs={6} sm={3}>
              <Card sx={{ p: 2, textAlign: 'center', backgroundColor: '#e3f2fd' }}>
                <ShoppingBag sx={{ fontSize: 40, color: '#1976d2', mb: 1 }} />
                <Typography variant="h6">{userData.totalOrders}</Typography>
                <Typography variant="caption">Total Orders</Typography>
              </Card>
            </Grid>
            <Grid item xs={6} sm={3}>
              <Card sx={{ p: 2, textAlign: 'center', backgroundColor: '#f3e5f5' }}>
                <FavoriteRounded sx={{ fontSize: 40, color: '#7b1fa2', mb: 1 }} />
                <Typography variant="h6">{favoriteProducts.length}</Typography>
                <Typography variant="caption">Favorites</Typography>
              </Card>
            </Grid>
            <Grid item xs={6} sm={3}>
              <Card sx={{ p: 2, textAlign: 'center', backgroundColor: '#e8f5e8' }}>
                <LocalShipping sx={{ fontSize: 40, color: '#388e3c', mb: 1 }} />
                <Typography variant="h6">3</Typography>
                <Typography variant="caption">In Transit</Typography>
              </Card>
            </Grid>
            <Grid item xs={6} sm={3}>
              <Card sx={{ p: 2, textAlign: 'center', backgroundColor: '#fff3e0' }}>
                <Star sx={{ fontSize: 40, color: '#f57c00', mb: 1 }} />
                <Typography variant="h6">4.8</Typography>
                <Typography variant="caption">Avg Rating</Typography>
              </Card>
            </Grid>
          </Grid>

          {/* Loyalty Program */}
          <Paper sx={{ p: 3, mb: 3 }}>
            <Typography variant="h6" sx={{ mb: 2 }}>
              🎯 Loyalty Program
            </Typography>
            <Box sx={{ display: 'flex', justifyContent: 'space-between', mb: 1 }}>
              <Typography variant="body2">
                {userData.loyaltyPoints} / {userData.nextRewardAt} points
              </Typography>
              <Typography variant="body2" color="primary">
                {userData.nextRewardAt - userData.loyaltyPoints} points to next reward
              </Typography>
            </Box>
            <LinearProgress
              variant="determinate"
              value={loyaltyProgress}
              sx={{ height: 8, borderRadius: 4, mb: 2 }}
            />
            <Typography variant="caption" color="textSecondary">
              Earn points with every purchase and unlock exclusive rewards!
            </Typography>
          </Paper>
        </Grid>

        {/* Recent Orders */}
        <Grid item xs={12} md={8}>
          <Paper sx={{ p: 3 }}>
            <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 2 }}>
              <Typography variant="h6">Recent Orders</Typography>
              <Button variant="outlined" size="small">
                <History sx={{ mr: 1 }} />
                View All
              </Button>
            </Box>
            <TableContainer>
              <Table>
                <TableHead>
                  <TableRow>
                    <TableCell>Order</TableCell>
                    <TableCell>Items</TableCell>
                    <TableCell>Date</TableCell>
                    <TableCell>Total</TableCell>
                    <TableCell>Status</TableCell>
                    <TableCell>Action</TableCell>
                  </TableRow>
                </TableHead>
                <TableBody>
                  {recentOrders.map((order) => (
                    <TableRow key={order.id}>
                      <TableCell>
                        <Box sx={{ display: 'flex', alignItems: 'center' }}>
                          <Typography sx={{ mr: 1, fontSize: '1.2em' }}>{order.image}</Typography>
                          <Typography variant="body2">{order.id}</Typography>
                        </Box>
                      </TableCell>
                      <TableCell>
                        <Typography variant="body2">
                          {order.items.join(', ')}
                        </Typography>
                      </TableCell>
                      <TableCell>{order.date}</TableCell>
                      <TableCell>${order.total}</TableCell>
                      <TableCell>
                        <Chip
                          label={order.status}
                          color={getStatusColor(order.status)}
                          size="small"
                        />
                      </TableCell>
                      <TableCell>
                        <Button size="small" variant="outlined">
                          Track
                        </Button>
                      </TableCell>
                    </TableRow>
                  ))}
                </TableBody>
              </Table>
            </TableContainer>
          </Paper>
        </Grid>

        {/* Notifications */}
        <Grid item xs={12} md={4}>
          <Paper sx={{ p: 3, height: 'fit-content' }}>
            <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
              <Badge badgeContent={notifications.length} color="error">
                <Notifications color="action" />
              </Badge>
              <Typography variant="h6" sx={{ ml: 1 }}>
                Notifications
              </Typography>
            </Box>
            <List dense>
              {notifications.map((notification) => (
                <ListItem key={notification.id} sx={{ px: 0 }}>
                  <ListItemAvatar>
                    <Avatar sx={{ width: 32, height: 32, backgroundColor: '#e3f2fd' }}>
                      {notification.type === 'delivery' && <LocalShipping sx={{ fontSize: 16 }} />}
                      {notification.type === 'promotion' && <TrendingUp sx={{ fontSize: 16 }} />}
                      {notification.type === 'points' && <Star sx={{ fontSize: 16 }} />}
                    </Avatar>
                  </ListItemAvatar>
                  <ListItemText
                    primary={
                      <Typography variant="body2" sx={{ fontSize: '0.875rem' }}>
                        {notification.message}
                      </Typography>
                    }
                    secondary={notification.time}
                  />
                </ListItem>
              ))}
            </List>
          </Paper>
        </Grid>

        {/* Favorite Products */}
        <Grid item xs={12} md={6}>
          <Paper sx={{ p: 3 }}>
            <Typography variant="h6" sx={{ mb: 2 }}>
              ❤️ Your Favorites
            </Typography>
            <Grid container spacing={2}>
              {favoriteProducts.map((product) => (
                <Grid item xs={12} sm={6} key={product.id}>
                  <Card sx={{ display: 'flex', alignItems: 'center', p: 1 }}>
                    <Typography sx={{ fontSize: '2em', mr: 2 }}>{product.image}</Typography>
                    <Box sx={{ flexGrow: 1 }}>
                      <Typography variant="body2" sx={{ fontWeight: 'bold' }}>
                        {product.name}
                      </Typography>
                      <Typography variant="caption" color="textSecondary">
                        ${product.price} • ⭐ {product.rating}
                      </Typography>
                      <Box sx={{ mt: 1 }}>
                        <Button
                          size="small"
                          variant="contained"
                          disabled={!product.inStock}
                          onClick={() => handleAddToCart(product)}
                          sx={{ fontSize: '0.75rem', py: 0.5 }}
                        >
                          <ShoppingCart sx={{ fontSize: 14, mr: 0.5 }} />
                          {product.inStock ? 'Add to Cart' : 'Out of Stock'}
                        </Button>
                      </Box>
                    </Box>
                  </Card>
                </Grid>
              ))}
            </Grid>
          </Paper>
        </Grid>

        {/* Recommendations */}
        <Grid item xs={12} md={6}>
          <Paper sx={{ p: 3 }}>
            <Typography variant="h6" sx={{ mb: 2 }}>
              🌟 Recommended for You
            </Typography>
            <Grid container spacing={2}>
              {recommendations.map((product) => (
                <Grid item xs={12} key={product.id}>
                  <Card sx={{ display: 'flex', alignItems: 'center', p: 2 }}>
                    <Typography sx={{ fontSize: '2em', mr: 2 }}>{product.image}</Typography>
                    <Box sx={{ flexGrow: 1 }}>
                      <Typography variant="body1" sx={{ fontWeight: 'bold' }}>
                        {product.name}
                      </Typography>
                      <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                        <Typography variant="h6" color="primary">
                          ${product.price}
                        </Typography>
                        <Chip
                          label={`${product.discount}% OFF`}
                          color="error"
                          size="small"
                        />
                      </Box>
                    </Box>
                    <Button
                      variant="contained"
                      size="small"
                      onClick={() => handleAddToCart(product)}
                    >
                      Add to Cart
                    </Button>
                  </Card>
                </Grid>
              ))}
            </Grid>
          </Paper>
        </Grid>
      </Grid>
    </Box>
  );
};

export default UserDashboard;
