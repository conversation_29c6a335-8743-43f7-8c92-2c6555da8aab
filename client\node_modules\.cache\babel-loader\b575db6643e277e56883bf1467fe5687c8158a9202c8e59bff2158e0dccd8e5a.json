{"ast": null, "code": "var _excluded = [\"offset\", \"layout\", \"width\", \"dataKey\", \"data\", \"dataPointFormatter\", \"xAxis\", \"yAxis\"];\nfunction _typeof(o) {\n  \"@babel/helpers - typeof\";\n\n  return _typeof = \"function\" == typeof Symbol && \"symbol\" == typeof Symbol.iterator ? function (o) {\n    return typeof o;\n  } : function (o) {\n    return o && \"function\" == typeof Symbol && o.constructor === Symbol && o !== Symbol.prototype ? \"symbol\" : typeof o;\n  }, _typeof(o);\n}\nfunction _extends() {\n  _extends = Object.assign ? Object.assign.bind() : function (target) {\n    for (var i = 1; i < arguments.length; i++) {\n      var source = arguments[i];\n      for (var key in source) {\n        if (Object.prototype.hasOwnProperty.call(source, key)) {\n          target[key] = source[key];\n        }\n      }\n    }\n    return target;\n  };\n  return _extends.apply(this, arguments);\n}\nfunction _slicedToArray(arr, i) {\n  return _arrayWithHoles(arr) || _iterableToArrayLimit(arr, i) || _unsupportedIterableToArray(arr, i) || _nonIterableRest();\n}\nfunction _nonIterableRest() {\n  throw new TypeError(\"Invalid attempt to destructure non-iterable instance.\\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.\");\n}\nfunction _unsupportedIterableToArray(o, minLen) {\n  if (!o) return;\n  if (typeof o === \"string\") return _arrayLikeToArray(o, minLen);\n  var n = Object.prototype.toString.call(o).slice(8, -1);\n  if (n === \"Object\" && o.constructor) n = o.constructor.name;\n  if (n === \"Map\" || n === \"Set\") return Array.from(o);\n  if (n === \"Arguments\" || /^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n)) return _arrayLikeToArray(o, minLen);\n}\nfunction _arrayLikeToArray(arr, len) {\n  if (len == null || len > arr.length) len = arr.length;\n  for (var i = 0, arr2 = new Array(len); i < len; i++) arr2[i] = arr[i];\n  return arr2;\n}\nfunction _iterableToArrayLimit(r, l) {\n  var t = null == r ? null : \"undefined\" != typeof Symbol && r[Symbol.iterator] || r[\"@@iterator\"];\n  if (null != t) {\n    var e,\n      n,\n      i,\n      u,\n      a = [],\n      f = !0,\n      o = !1;\n    try {\n      if (i = (t = t.call(r)).next, 0 === l) {\n        if (Object(t) !== t) return;\n        f = !1;\n      } else for (; !(f = (e = i.call(t)).done) && (a.push(e.value), a.length !== l); f = !0);\n    } catch (r) {\n      o = !0, n = r;\n    } finally {\n      try {\n        if (!f && null != t[\"return\"] && (u = t[\"return\"](), Object(u) !== u)) return;\n      } finally {\n        if (o) throw n;\n      }\n    }\n    return a;\n  }\n}\nfunction _arrayWithHoles(arr) {\n  if (Array.isArray(arr)) return arr;\n}\nfunction _objectWithoutProperties(source, excluded) {\n  if (source == null) return {};\n  var target = _objectWithoutPropertiesLoose(source, excluded);\n  var key, i;\n  if (Object.getOwnPropertySymbols) {\n    var sourceSymbolKeys = Object.getOwnPropertySymbols(source);\n    for (i = 0; i < sourceSymbolKeys.length; i++) {\n      key = sourceSymbolKeys[i];\n      if (excluded.indexOf(key) >= 0) continue;\n      if (!Object.prototype.propertyIsEnumerable.call(source, key)) continue;\n      target[key] = source[key];\n    }\n  }\n  return target;\n}\nfunction _objectWithoutPropertiesLoose(source, excluded) {\n  if (source == null) return {};\n  var target = {};\n  for (var key in source) {\n    if (Object.prototype.hasOwnProperty.call(source, key)) {\n      if (excluded.indexOf(key) >= 0) continue;\n      target[key] = source[key];\n    }\n  }\n  return target;\n}\nfunction _classCallCheck(instance, Constructor) {\n  if (!(instance instanceof Constructor)) {\n    throw new TypeError(\"Cannot call a class as a function\");\n  }\n}\nfunction _defineProperties(target, props) {\n  for (var i = 0; i < props.length; i++) {\n    var descriptor = props[i];\n    descriptor.enumerable = descriptor.enumerable || false;\n    descriptor.configurable = true;\n    if (\"value\" in descriptor) descriptor.writable = true;\n    Object.defineProperty(target, _toPropertyKey(descriptor.key), descriptor);\n  }\n}\nfunction _createClass(Constructor, protoProps, staticProps) {\n  if (protoProps) _defineProperties(Constructor.prototype, protoProps);\n  if (staticProps) _defineProperties(Constructor, staticProps);\n  Object.defineProperty(Constructor, \"prototype\", {\n    writable: false\n  });\n  return Constructor;\n}\nfunction _callSuper(t, o, e) {\n  return o = _getPrototypeOf(o), _possibleConstructorReturn(t, _isNativeReflectConstruct() ? Reflect.construct(o, e || [], _getPrototypeOf(t).constructor) : o.apply(t, e));\n}\nfunction _possibleConstructorReturn(self, call) {\n  if (call && (_typeof(call) === \"object\" || typeof call === \"function\")) {\n    return call;\n  } else if (call !== void 0) {\n    throw new TypeError(\"Derived constructors may only return object or undefined\");\n  }\n  return _assertThisInitialized(self);\n}\nfunction _assertThisInitialized(self) {\n  if (self === void 0) {\n    throw new ReferenceError(\"this hasn't been initialised - super() hasn't been called\");\n  }\n  return self;\n}\nfunction _isNativeReflectConstruct() {\n  try {\n    var t = !Boolean.prototype.valueOf.call(Reflect.construct(Boolean, [], function () {}));\n  } catch (t) {}\n  return (_isNativeReflectConstruct = function _isNativeReflectConstruct() {\n    return !!t;\n  })();\n}\nfunction _getPrototypeOf(o) {\n  _getPrototypeOf = Object.setPrototypeOf ? Object.getPrototypeOf.bind() : function _getPrototypeOf(o) {\n    return o.__proto__ || Object.getPrototypeOf(o);\n  };\n  return _getPrototypeOf(o);\n}\nfunction _inherits(subClass, superClass) {\n  if (typeof superClass !== \"function\" && superClass !== null) {\n    throw new TypeError(\"Super expression must either be null or a function\");\n  }\n  subClass.prototype = Object.create(superClass && superClass.prototype, {\n    constructor: {\n      value: subClass,\n      writable: true,\n      configurable: true\n    }\n  });\n  Object.defineProperty(subClass, \"prototype\", {\n    writable: false\n  });\n  if (superClass) _setPrototypeOf(subClass, superClass);\n}\nfunction _setPrototypeOf(o, p) {\n  _setPrototypeOf = Object.setPrototypeOf ? Object.setPrototypeOf.bind() : function _setPrototypeOf(o, p) {\n    o.__proto__ = p;\n    return o;\n  };\n  return _setPrototypeOf(o, p);\n}\nfunction _defineProperty(obj, key, value) {\n  key = _toPropertyKey(key);\n  if (key in obj) {\n    Object.defineProperty(obj, key, {\n      value: value,\n      enumerable: true,\n      configurable: true,\n      writable: true\n    });\n  } else {\n    obj[key] = value;\n  }\n  return obj;\n}\nfunction _toPropertyKey(t) {\n  var i = _toPrimitive(t, \"string\");\n  return \"symbol\" == _typeof(i) ? i : i + \"\";\n}\nfunction _toPrimitive(t, r) {\n  if (\"object\" != _typeof(t) || !t) return t;\n  var e = t[Symbol.toPrimitive];\n  if (void 0 !== e) {\n    var i = e.call(t, r || \"default\");\n    if (\"object\" != _typeof(i)) return i;\n    throw new TypeError(\"@@toPrimitive must return a primitive value.\");\n  }\n  return (\"string\" === r ? String : Number)(t);\n}\n/**\n * @fileOverview Render a group of error bar\n */\nimport React from 'react';\nimport invariant from 'tiny-invariant';\nimport { Layer } from '../container/Layer';\nimport { filterProps } from '../util/ReactUtils';\n// eslint-disable-next-line react/prefer-stateless-function -- requires static defaultProps\nexport var ErrorBar = /*#__PURE__*/function (_React$Component) {\n  function ErrorBar() {\n    _classCallCheck(this, ErrorBar);\n    return _callSuper(this, ErrorBar, arguments);\n  }\n  _inherits(ErrorBar, _React$Component);\n  return _createClass(ErrorBar, [{\n    key: \"render\",\n    value: function render() {\n      var _this$props = this.props,\n        offset = _this$props.offset,\n        layout = _this$props.layout,\n        width = _this$props.width,\n        dataKey = _this$props.dataKey,\n        data = _this$props.data,\n        dataPointFormatter = _this$props.dataPointFormatter,\n        xAxis = _this$props.xAxis,\n        yAxis = _this$props.yAxis,\n        others = _objectWithoutProperties(_this$props, _excluded);\n      var svgProps = filterProps(others, false);\n      !!(this.props.direction === 'x' && xAxis.type !== 'number') ? process.env.NODE_ENV !== \"production\" ? invariant(false, 'ErrorBar requires Axis type property to be \"number\".') : invariant(false) : void 0;\n      var errorBars = data.map(function (entry) {\n        var _dataPointFormatter = dataPointFormatter(entry, dataKey),\n          x = _dataPointFormatter.x,\n          y = _dataPointFormatter.y,\n          value = _dataPointFormatter.value,\n          errorVal = _dataPointFormatter.errorVal;\n        if (!errorVal) {\n          return null;\n        }\n        var lineCoordinates = [];\n        var lowBound, highBound;\n        if (Array.isArray(errorVal)) {\n          var _errorVal = _slicedToArray(errorVal, 2);\n          lowBound = _errorVal[0];\n          highBound = _errorVal[1];\n        } else {\n          lowBound = highBound = errorVal;\n        }\n        if (layout === 'vertical') {\n          // error bar for horizontal charts, the y is fixed, x is a range value\n          var scale = xAxis.scale;\n          var yMid = y + offset;\n          var yMin = yMid + width;\n          var yMax = yMid - width;\n          var xMin = scale(value - lowBound);\n          var xMax = scale(value + highBound);\n\n          // the right line of |--|\n          lineCoordinates.push({\n            x1: xMax,\n            y1: yMin,\n            x2: xMax,\n            y2: yMax\n          });\n          // the middle line of |--|\n          lineCoordinates.push({\n            x1: xMin,\n            y1: yMid,\n            x2: xMax,\n            y2: yMid\n          });\n          // the left line of |--|\n          lineCoordinates.push({\n            x1: xMin,\n            y1: yMin,\n            x2: xMin,\n            y2: yMax\n          });\n        } else if (layout === 'horizontal') {\n          // error bar for horizontal charts, the x is fixed, y is a range value\n          var _scale = yAxis.scale;\n          var xMid = x + offset;\n          var _xMin = xMid - width;\n          var _xMax = xMid + width;\n          var _yMin = _scale(value - lowBound);\n          var _yMax = _scale(value + highBound);\n\n          // the top line\n          lineCoordinates.push({\n            x1: _xMin,\n            y1: _yMax,\n            x2: _xMax,\n            y2: _yMax\n          });\n          // the middle line\n          lineCoordinates.push({\n            x1: xMid,\n            y1: _yMin,\n            x2: xMid,\n            y2: _yMax\n          });\n          // the bottom line\n          lineCoordinates.push({\n            x1: _xMin,\n            y1: _yMin,\n            x2: _xMax,\n            y2: _yMin\n          });\n        }\n        return /*#__PURE__*/React.createElement(Layer, _extends({\n          className: \"recharts-errorBar\",\n          key: \"bar-\".concat(lineCoordinates.map(function (c) {\n            return \"\".concat(c.x1, \"-\").concat(c.x2, \"-\").concat(c.y1, \"-\").concat(c.y2);\n          }))\n        }, svgProps), lineCoordinates.map(function (coordinates) {\n          return /*#__PURE__*/React.createElement(\"line\", _extends({}, coordinates, {\n            key: \"line-\".concat(coordinates.x1, \"-\").concat(coordinates.x2, \"-\").concat(coordinates.y1, \"-\").concat(coordinates.y2)\n          }));\n        }));\n      });\n      return /*#__PURE__*/React.createElement(Layer, {\n        className: \"recharts-errorBars\"\n      }, errorBars);\n    }\n  }]);\n}(React.Component);\n_defineProperty(ErrorBar, \"defaultProps\", {\n  stroke: 'black',\n  strokeWidth: 1.5,\n  width: 5,\n  offset: 0,\n  layout: 'horizontal'\n});\n_defineProperty(ErrorBar, \"displayName\", 'ErrorBar');", "map": {"version": 3, "names": ["_excluded", "_typeof", "o", "Symbol", "iterator", "constructor", "prototype", "_extends", "Object", "assign", "bind", "target", "i", "arguments", "length", "source", "key", "hasOwnProperty", "call", "apply", "_slicedToArray", "arr", "_arrayWithHoles", "_iterableToArrayLimit", "_unsupportedIterableToArray", "_nonIterableRest", "TypeError", "minLen", "_arrayLikeToArray", "n", "toString", "slice", "name", "Array", "from", "test", "len", "arr2", "r", "l", "t", "e", "u", "a", "f", "next", "done", "push", "value", "isArray", "_objectWithoutProperties", "excluded", "_objectWithoutPropertiesLoose", "getOwnPropertySymbols", "sourceSymbolKeys", "indexOf", "propertyIsEnumerable", "_classCallCheck", "instance", "<PERSON><PERSON><PERSON><PERSON>", "_defineProperties", "props", "descriptor", "enumerable", "configurable", "writable", "defineProperty", "_to<PERSON><PERSON><PERSON><PERSON><PERSON>", "_createClass", "protoProps", "staticProps", "_callSuper", "_getPrototypeOf", "_possibleConstructorReturn", "_isNativeReflectConstruct", "Reflect", "construct", "self", "_assertThisInitialized", "ReferenceError", "Boolean", "valueOf", "setPrototypeOf", "getPrototypeOf", "__proto__", "_inherits", "subClass", "superClass", "create", "_setPrototypeOf", "p", "_defineProperty", "obj", "_toPrimitive", "toPrimitive", "String", "Number", "React", "invariant", "Layer", "filterProps", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "_React$Component", "render", "_this$props", "offset", "layout", "width", "dataKey", "data", "dataPointFormatter", "xAxis", "yAxis", "others", "svgProps", "direction", "type", "process", "env", "NODE_ENV", "errorBars", "map", "entry", "_dataPointFormatter", "x", "y", "errorVal", "lineCoordinates", "lowBound", "highBound", "_errorVal", "scale", "yMid", "yMin", "yMax", "xMin", "xMax", "x1", "y1", "x2", "y2", "_scale", "xMid", "_xMin", "_xMax", "_yMin", "_yMax", "createElement", "className", "concat", "c", "coordinates", "Component", "stroke", "strokeWidth"], "sources": ["D:/ecommerce/node_modules/recharts/es6/cartesian/ErrorBar.js"], "sourcesContent": ["var _excluded = [\"offset\", \"layout\", \"width\", \"dataKey\", \"data\", \"dataPointFormatter\", \"xAxis\", \"yAxis\"];\nfunction _typeof(o) { \"@babel/helpers - typeof\"; return _typeof = \"function\" == typeof Symbol && \"symbol\" == typeof Symbol.iterator ? function (o) { return typeof o; } : function (o) { return o && \"function\" == typeof Symbol && o.constructor === Symbol && o !== Symbol.prototype ? \"symbol\" : typeof o; }, _typeof(o); }\nfunction _extends() { _extends = Object.assign ? Object.assign.bind() : function (target) { for (var i = 1; i < arguments.length; i++) { var source = arguments[i]; for (var key in source) { if (Object.prototype.hasOwnProperty.call(source, key)) { target[key] = source[key]; } } } return target; }; return _extends.apply(this, arguments); }\nfunction _slicedToArray(arr, i) { return _arrayWithHoles(arr) || _iterableToArrayLimit(arr, i) || _unsupportedIterableToArray(arr, i) || _nonIterableRest(); }\nfunction _nonIterableRest() { throw new TypeError(\"Invalid attempt to destructure non-iterable instance.\\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.\"); }\nfunction _unsupportedIterableToArray(o, minLen) { if (!o) return; if (typeof o === \"string\") return _arrayLikeToArray(o, minLen); var n = Object.prototype.toString.call(o).slice(8, -1); if (n === \"Object\" && o.constructor) n = o.constructor.name; if (n === \"Map\" || n === \"Set\") return Array.from(o); if (n === \"Arguments\" || /^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n)) return _arrayLikeToArray(o, minLen); }\nfunction _arrayLikeToArray(arr, len) { if (len == null || len > arr.length) len = arr.length; for (var i = 0, arr2 = new Array(len); i < len; i++) arr2[i] = arr[i]; return arr2; }\nfunction _iterableToArrayLimit(r, l) { var t = null == r ? null : \"undefined\" != typeof Symbol && r[Symbol.iterator] || r[\"@@iterator\"]; if (null != t) { var e, n, i, u, a = [], f = !0, o = !1; try { if (i = (t = t.call(r)).next, 0 === l) { if (Object(t) !== t) return; f = !1; } else for (; !(f = (e = i.call(t)).done) && (a.push(e.value), a.length !== l); f = !0); } catch (r) { o = !0, n = r; } finally { try { if (!f && null != t[\"return\"] && (u = t[\"return\"](), Object(u) !== u)) return; } finally { if (o) throw n; } } return a; } }\nfunction _arrayWithHoles(arr) { if (Array.isArray(arr)) return arr; }\nfunction _objectWithoutProperties(source, excluded) { if (source == null) return {}; var target = _objectWithoutPropertiesLoose(source, excluded); var key, i; if (Object.getOwnPropertySymbols) { var sourceSymbolKeys = Object.getOwnPropertySymbols(source); for (i = 0; i < sourceSymbolKeys.length; i++) { key = sourceSymbolKeys[i]; if (excluded.indexOf(key) >= 0) continue; if (!Object.prototype.propertyIsEnumerable.call(source, key)) continue; target[key] = source[key]; } } return target; }\nfunction _objectWithoutPropertiesLoose(source, excluded) { if (source == null) return {}; var target = {}; for (var key in source) { if (Object.prototype.hasOwnProperty.call(source, key)) { if (excluded.indexOf(key) >= 0) continue; target[key] = source[key]; } } return target; }\nfunction _classCallCheck(instance, Constructor) { if (!(instance instanceof Constructor)) { throw new TypeError(\"Cannot call a class as a function\"); } }\nfunction _defineProperties(target, props) { for (var i = 0; i < props.length; i++) { var descriptor = props[i]; descriptor.enumerable = descriptor.enumerable || false; descriptor.configurable = true; if (\"value\" in descriptor) descriptor.writable = true; Object.defineProperty(target, _toPropertyKey(descriptor.key), descriptor); } }\nfunction _createClass(Constructor, protoProps, staticProps) { if (protoProps) _defineProperties(Constructor.prototype, protoProps); if (staticProps) _defineProperties(Constructor, staticProps); Object.defineProperty(Constructor, \"prototype\", { writable: false }); return Constructor; }\nfunction _callSuper(t, o, e) { return o = _getPrototypeOf(o), _possibleConstructorReturn(t, _isNativeReflectConstruct() ? Reflect.construct(o, e || [], _getPrototypeOf(t).constructor) : o.apply(t, e)); }\nfunction _possibleConstructorReturn(self, call) { if (call && (_typeof(call) === \"object\" || typeof call === \"function\")) { return call; } else if (call !== void 0) { throw new TypeError(\"Derived constructors may only return object or undefined\"); } return _assertThisInitialized(self); }\nfunction _assertThisInitialized(self) { if (self === void 0) { throw new ReferenceError(\"this hasn't been initialised - super() hasn't been called\"); } return self; }\nfunction _isNativeReflectConstruct() { try { var t = !Boolean.prototype.valueOf.call(Reflect.construct(Boolean, [], function () {})); } catch (t) {} return (_isNativeReflectConstruct = function _isNativeReflectConstruct() { return !!t; })(); }\nfunction _getPrototypeOf(o) { _getPrototypeOf = Object.setPrototypeOf ? Object.getPrototypeOf.bind() : function _getPrototypeOf(o) { return o.__proto__ || Object.getPrototypeOf(o); }; return _getPrototypeOf(o); }\nfunction _inherits(subClass, superClass) { if (typeof superClass !== \"function\" && superClass !== null) { throw new TypeError(\"Super expression must either be null or a function\"); } subClass.prototype = Object.create(superClass && superClass.prototype, { constructor: { value: subClass, writable: true, configurable: true } }); Object.defineProperty(subClass, \"prototype\", { writable: false }); if (superClass) _setPrototypeOf(subClass, superClass); }\nfunction _setPrototypeOf(o, p) { _setPrototypeOf = Object.setPrototypeOf ? Object.setPrototypeOf.bind() : function _setPrototypeOf(o, p) { o.__proto__ = p; return o; }; return _setPrototypeOf(o, p); }\nfunction _defineProperty(obj, key, value) { key = _toPropertyKey(key); if (key in obj) { Object.defineProperty(obj, key, { value: value, enumerable: true, configurable: true, writable: true }); } else { obj[key] = value; } return obj; }\nfunction _toPropertyKey(t) { var i = _toPrimitive(t, \"string\"); return \"symbol\" == _typeof(i) ? i : i + \"\"; }\nfunction _toPrimitive(t, r) { if (\"object\" != _typeof(t) || !t) return t; var e = t[Symbol.toPrimitive]; if (void 0 !== e) { var i = e.call(t, r || \"default\"); if (\"object\" != _typeof(i)) return i; throw new TypeError(\"@@toPrimitive must return a primitive value.\"); } return (\"string\" === r ? String : Number)(t); }\n/**\n * @fileOverview Render a group of error bar\n */\nimport React from 'react';\nimport invariant from 'tiny-invariant';\nimport { Layer } from '../container/Layer';\nimport { filterProps } from '../util/ReactUtils';\n// eslint-disable-next-line react/prefer-stateless-function -- requires static defaultProps\nexport var ErrorBar = /*#__PURE__*/function (_React$Component) {\n  function ErrorBar() {\n    _classCallCheck(this, ErrorBar);\n    return _callSuper(this, ErrorBar, arguments);\n  }\n  _inherits(ErrorBar, _React$Component);\n  return _createClass(ErrorBar, [{\n    key: \"render\",\n    value: function render() {\n      var _this$props = this.props,\n        offset = _this$props.offset,\n        layout = _this$props.layout,\n        width = _this$props.width,\n        dataKey = _this$props.dataKey,\n        data = _this$props.data,\n        dataPointFormatter = _this$props.dataPointFormatter,\n        xAxis = _this$props.xAxis,\n        yAxis = _this$props.yAxis,\n        others = _objectWithoutProperties(_this$props, _excluded);\n      var svgProps = filterProps(others, false);\n      !!(this.props.direction === 'x' && xAxis.type !== 'number') ? process.env.NODE_ENV !== \"production\" ? invariant(false, 'ErrorBar requires Axis type property to be \"number\".') : invariant(false) : void 0;\n      var errorBars = data.map(function (entry) {\n        var _dataPointFormatter = dataPointFormatter(entry, dataKey),\n          x = _dataPointFormatter.x,\n          y = _dataPointFormatter.y,\n          value = _dataPointFormatter.value,\n          errorVal = _dataPointFormatter.errorVal;\n        if (!errorVal) {\n          return null;\n        }\n        var lineCoordinates = [];\n        var lowBound, highBound;\n        if (Array.isArray(errorVal)) {\n          var _errorVal = _slicedToArray(errorVal, 2);\n          lowBound = _errorVal[0];\n          highBound = _errorVal[1];\n        } else {\n          lowBound = highBound = errorVal;\n        }\n        if (layout === 'vertical') {\n          // error bar for horizontal charts, the y is fixed, x is a range value\n          var scale = xAxis.scale;\n          var yMid = y + offset;\n          var yMin = yMid + width;\n          var yMax = yMid - width;\n          var xMin = scale(value - lowBound);\n          var xMax = scale(value + highBound);\n\n          // the right line of |--|\n          lineCoordinates.push({\n            x1: xMax,\n            y1: yMin,\n            x2: xMax,\n            y2: yMax\n          });\n          // the middle line of |--|\n          lineCoordinates.push({\n            x1: xMin,\n            y1: yMid,\n            x2: xMax,\n            y2: yMid\n          });\n          // the left line of |--|\n          lineCoordinates.push({\n            x1: xMin,\n            y1: yMin,\n            x2: xMin,\n            y2: yMax\n          });\n        } else if (layout === 'horizontal') {\n          // error bar for horizontal charts, the x is fixed, y is a range value\n          var _scale = yAxis.scale;\n          var xMid = x + offset;\n          var _xMin = xMid - width;\n          var _xMax = xMid + width;\n          var _yMin = _scale(value - lowBound);\n          var _yMax = _scale(value + highBound);\n\n          // the top line\n          lineCoordinates.push({\n            x1: _xMin,\n            y1: _yMax,\n            x2: _xMax,\n            y2: _yMax\n          });\n          // the middle line\n          lineCoordinates.push({\n            x1: xMid,\n            y1: _yMin,\n            x2: xMid,\n            y2: _yMax\n          });\n          // the bottom line\n          lineCoordinates.push({\n            x1: _xMin,\n            y1: _yMin,\n            x2: _xMax,\n            y2: _yMin\n          });\n        }\n        return /*#__PURE__*/React.createElement(Layer, _extends({\n          className: \"recharts-errorBar\",\n          key: \"bar-\".concat(lineCoordinates.map(function (c) {\n            return \"\".concat(c.x1, \"-\").concat(c.x2, \"-\").concat(c.y1, \"-\").concat(c.y2);\n          }))\n        }, svgProps), lineCoordinates.map(function (coordinates) {\n          return /*#__PURE__*/React.createElement(\"line\", _extends({}, coordinates, {\n            key: \"line-\".concat(coordinates.x1, \"-\").concat(coordinates.x2, \"-\").concat(coordinates.y1, \"-\").concat(coordinates.y2)\n          }));\n        }));\n      });\n      return /*#__PURE__*/React.createElement(Layer, {\n        className: \"recharts-errorBars\"\n      }, errorBars);\n    }\n  }]);\n}(React.Component);\n_defineProperty(ErrorBar, \"defaultProps\", {\n  stroke: 'black',\n  strokeWidth: 1.5,\n  width: 5,\n  offset: 0,\n  layout: 'horizontal'\n});\n_defineProperty(ErrorBar, \"displayName\", 'ErrorBar');"], "mappings": "AAAA,IAAIA,SAAS,GAAG,CAAC,QAAQ,EAAE,QAAQ,EAAE,OAAO,EAAE,SAAS,EAAE,MAAM,EAAE,oBAAoB,EAAE,OAAO,EAAE,OAAO,CAAC;AACxG,SAASC,OAAOA,CAACC,CAAC,EAAE;EAAE,yBAAyB;;EAAE,OAAOD,OAAO,GAAG,UAAU,IAAI,OAAOE,MAAM,IAAI,QAAQ,IAAI,OAAOA,MAAM,CAACC,QAAQ,GAAG,UAAUF,CAAC,EAAE;IAAE,OAAO,OAAOA,CAAC;EAAE,CAAC,GAAG,UAAUA,CAAC,EAAE;IAAE,OAAOA,CAAC,IAAI,UAAU,IAAI,OAAOC,MAAM,IAAID,CAAC,CAACG,WAAW,KAAKF,MAAM,IAAID,CAAC,KAAKC,MAAM,CAACG,SAAS,GAAG,QAAQ,GAAG,OAAOJ,CAAC;EAAE,CAAC,EAAED,OAAO,CAACC,CAAC,CAAC;AAAE;AAC7T,SAASK,QAAQA,CAAA,EAAG;EAAEA,QAAQ,GAAGC,MAAM,CAACC,MAAM,GAAGD,MAAM,CAACC,MAAM,CAACC,IAAI,CAAC,CAAC,GAAG,UAAUC,MAAM,EAAE;IAAE,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGC,SAAS,CAACC,MAAM,EAAEF,CAAC,EAAE,EAAE;MAAE,IAAIG,MAAM,GAAGF,SAAS,CAACD,CAAC,CAAC;MAAE,KAAK,IAAII,GAAG,IAAID,MAAM,EAAE;QAAE,IAAIP,MAAM,CAACF,SAAS,CAACW,cAAc,CAACC,IAAI,CAACH,MAAM,EAAEC,GAAG,CAAC,EAAE;UAAEL,MAAM,CAACK,GAAG,CAAC,GAAGD,MAAM,CAACC,GAAG,CAAC;QAAE;MAAE;IAAE;IAAE,OAAOL,MAAM;EAAE,CAAC;EAAE,OAAOJ,QAAQ,CAACY,KAAK,CAAC,IAAI,EAAEN,SAAS,CAAC;AAAE;AAClV,SAASO,cAAcA,CAACC,GAAG,EAAET,CAAC,EAAE;EAAE,OAAOU,eAAe,CAACD,GAAG,CAAC,IAAIE,qBAAqB,CAACF,GAAG,EAAET,CAAC,CAAC,IAAIY,2BAA2B,CAACH,GAAG,EAAET,CAAC,CAAC,IAAIa,gBAAgB,CAAC,CAAC;AAAE;AAC7J,SAASA,gBAAgBA,CAAA,EAAG;EAAE,MAAM,IAAIC,SAAS,CAAC,2IAA2I,CAAC;AAAE;AAChM,SAASF,2BAA2BA,CAACtB,CAAC,EAAEyB,MAAM,EAAE;EAAE,IAAI,CAACzB,CAAC,EAAE;EAAQ,IAAI,OAAOA,CAAC,KAAK,QAAQ,EAAE,OAAO0B,iBAAiB,CAAC1B,CAAC,EAAEyB,MAAM,CAAC;EAAE,IAAIE,CAAC,GAAGrB,MAAM,CAACF,SAAS,CAACwB,QAAQ,CAACZ,IAAI,CAAChB,CAAC,CAAC,CAAC6B,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;EAAE,IAAIF,CAAC,KAAK,QAAQ,IAAI3B,CAAC,CAACG,WAAW,EAAEwB,CAAC,GAAG3B,CAAC,CAACG,WAAW,CAAC2B,IAAI;EAAE,IAAIH,CAAC,KAAK,KAAK,IAAIA,CAAC,KAAK,KAAK,EAAE,OAAOI,KAAK,CAACC,IAAI,CAAChC,CAAC,CAAC;EAAE,IAAI2B,CAAC,KAAK,WAAW,IAAI,0CAA0C,CAACM,IAAI,CAACN,CAAC,CAAC,EAAE,OAAOD,iBAAiB,CAAC1B,CAAC,EAAEyB,MAAM,CAAC;AAAE;AAC/Z,SAASC,iBAAiBA,CAACP,GAAG,EAAEe,GAAG,EAAE;EAAE,IAAIA,GAAG,IAAI,IAAI,IAAIA,GAAG,GAAGf,GAAG,CAACP,MAAM,EAAEsB,GAAG,GAAGf,GAAG,CAACP,MAAM;EAAE,KAAK,IAAIF,CAAC,GAAG,CAAC,EAAEyB,IAAI,GAAG,IAAIJ,KAAK,CAACG,GAAG,CAAC,EAAExB,CAAC,GAAGwB,GAAG,EAAExB,CAAC,EAAE,EAAEyB,IAAI,CAACzB,CAAC,CAAC,GAAGS,GAAG,CAACT,CAAC,CAAC;EAAE,OAAOyB,IAAI;AAAE;AAClL,SAASd,qBAAqBA,CAACe,CAAC,EAAEC,CAAC,EAAE;EAAE,IAAIC,CAAC,GAAG,IAAI,IAAIF,CAAC,GAAG,IAAI,GAAG,WAAW,IAAI,OAAOnC,MAAM,IAAImC,CAAC,CAACnC,MAAM,CAACC,QAAQ,CAAC,IAAIkC,CAAC,CAAC,YAAY,CAAC;EAAE,IAAI,IAAI,IAAIE,CAAC,EAAE;IAAE,IAAIC,CAAC;MAAEZ,CAAC;MAAEjB,CAAC;MAAE8B,CAAC;MAAEC,CAAC,GAAG,EAAE;MAAEC,CAAC,GAAG,CAAC,CAAC;MAAE1C,CAAC,GAAG,CAAC,CAAC;IAAE,IAAI;MAAE,IAAIU,CAAC,GAAG,CAAC4B,CAAC,GAAGA,CAAC,CAACtB,IAAI,CAACoB,CAAC,CAAC,EAAEO,IAAI,EAAE,CAAC,KAAKN,CAAC,EAAE;QAAE,IAAI/B,MAAM,CAACgC,CAAC,CAAC,KAAKA,CAAC,EAAE;QAAQI,CAAC,GAAG,CAAC,CAAC;MAAE,CAAC,MAAM,OAAO,EAAEA,CAAC,GAAG,CAACH,CAAC,GAAG7B,CAAC,CAACM,IAAI,CAACsB,CAAC,CAAC,EAAEM,IAAI,CAAC,KAAKH,CAAC,CAACI,IAAI,CAACN,CAAC,CAACO,KAAK,CAAC,EAAEL,CAAC,CAAC7B,MAAM,KAAKyB,CAAC,CAAC,EAAEK,CAAC,GAAG,CAAC,CAAC,CAAC;IAAE,CAAC,CAAC,OAAON,CAAC,EAAE;MAAEpC,CAAC,GAAG,CAAC,CAAC,EAAE2B,CAAC,GAAGS,CAAC;IAAE,CAAC,SAAS;MAAE,IAAI;QAAE,IAAI,CAACM,CAAC,IAAI,IAAI,IAAIJ,CAAC,CAAC,QAAQ,CAAC,KAAKE,CAAC,GAAGF,CAAC,CAAC,QAAQ,CAAC,CAAC,CAAC,EAAEhC,MAAM,CAACkC,CAAC,CAAC,KAAKA,CAAC,CAAC,EAAE;MAAQ,CAAC,SAAS;QAAE,IAAIxC,CAAC,EAAE,MAAM2B,CAAC;MAAE;IAAE;IAAE,OAAOc,CAAC;EAAE;AAAE;AACzhB,SAASrB,eAAeA,CAACD,GAAG,EAAE;EAAE,IAAIY,KAAK,CAACgB,OAAO,CAAC5B,GAAG,CAAC,EAAE,OAAOA,GAAG;AAAE;AACpE,SAAS6B,wBAAwBA,CAACnC,MAAM,EAAEoC,QAAQ,EAAE;EAAE,IAAIpC,MAAM,IAAI,IAAI,EAAE,OAAO,CAAC,CAAC;EAAE,IAAIJ,MAAM,GAAGyC,6BAA6B,CAACrC,MAAM,EAAEoC,QAAQ,CAAC;EAAE,IAAInC,GAAG,EAAEJ,CAAC;EAAE,IAAIJ,MAAM,CAAC6C,qBAAqB,EAAE;IAAE,IAAIC,gBAAgB,GAAG9C,MAAM,CAAC6C,qBAAqB,CAACtC,MAAM,CAAC;IAAE,KAAKH,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG0C,gBAAgB,CAACxC,MAAM,EAAEF,CAAC,EAAE,EAAE;MAAEI,GAAG,GAAGsC,gBAAgB,CAAC1C,CAAC,CAAC;MAAE,IAAIuC,QAAQ,CAACI,OAAO,CAACvC,GAAG,CAAC,IAAI,CAAC,EAAE;MAAU,IAAI,CAACR,MAAM,CAACF,SAAS,CAACkD,oBAAoB,CAACtC,IAAI,CAACH,MAAM,EAAEC,GAAG,CAAC,EAAE;MAAUL,MAAM,CAACK,GAAG,CAAC,GAAGD,MAAM,CAACC,GAAG,CAAC;IAAE;EAAE;EAAE,OAAOL,MAAM;AAAE;AAC3e,SAASyC,6BAA6BA,CAACrC,MAAM,EAAEoC,QAAQ,EAAE;EAAE,IAAIpC,MAAM,IAAI,IAAI,EAAE,OAAO,CAAC,CAAC;EAAE,IAAIJ,MAAM,GAAG,CAAC,CAAC;EAAE,KAAK,IAAIK,GAAG,IAAID,MAAM,EAAE;IAAE,IAAIP,MAAM,CAACF,SAAS,CAACW,cAAc,CAACC,IAAI,CAACH,MAAM,EAAEC,GAAG,CAAC,EAAE;MAAE,IAAImC,QAAQ,CAACI,OAAO,CAACvC,GAAG,CAAC,IAAI,CAAC,EAAE;MAAUL,MAAM,CAACK,GAAG,CAAC,GAAGD,MAAM,CAACC,GAAG,CAAC;IAAE;EAAE;EAAE,OAAOL,MAAM;AAAE;AACtR,SAAS8C,eAAeA,CAACC,QAAQ,EAAEC,WAAW,EAAE;EAAE,IAAI,EAAED,QAAQ,YAAYC,WAAW,CAAC,EAAE;IAAE,MAAM,IAAIjC,SAAS,CAAC,mCAAmC,CAAC;EAAE;AAAE;AACxJ,SAASkC,iBAAiBA,CAACjD,MAAM,EAAEkD,KAAK,EAAE;EAAE,KAAK,IAAIjD,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGiD,KAAK,CAAC/C,MAAM,EAAEF,CAAC,EAAE,EAAE;IAAE,IAAIkD,UAAU,GAAGD,KAAK,CAACjD,CAAC,CAAC;IAAEkD,UAAU,CAACC,UAAU,GAAGD,UAAU,CAACC,UAAU,IAAI,KAAK;IAAED,UAAU,CAACE,YAAY,GAAG,IAAI;IAAE,IAAI,OAAO,IAAIF,UAAU,EAAEA,UAAU,CAACG,QAAQ,GAAG,IAAI;IAAEzD,MAAM,CAAC0D,cAAc,CAACvD,MAAM,EAAEwD,cAAc,CAACL,UAAU,CAAC9C,GAAG,CAAC,EAAE8C,UAAU,CAAC;EAAE;AAAE;AAC5U,SAASM,YAAYA,CAACT,WAAW,EAAEU,UAAU,EAAEC,WAAW,EAAE;EAAE,IAAID,UAAU,EAAET,iBAAiB,CAACD,WAAW,CAACrD,SAAS,EAAE+D,UAAU,CAAC;EAAE,IAAIC,WAAW,EAAEV,iBAAiB,CAACD,WAAW,EAAEW,WAAW,CAAC;EAAE9D,MAAM,CAAC0D,cAAc,CAACP,WAAW,EAAE,WAAW,EAAE;IAAEM,QAAQ,EAAE;EAAM,CAAC,CAAC;EAAE,OAAON,WAAW;AAAE;AAC5R,SAASY,UAAUA,CAAC/B,CAAC,EAAEtC,CAAC,EAAEuC,CAAC,EAAE;EAAE,OAAOvC,CAAC,GAAGsE,eAAe,CAACtE,CAAC,CAAC,EAAEuE,0BAA0B,CAACjC,CAAC,EAAEkC,yBAAyB,CAAC,CAAC,GAAGC,OAAO,CAACC,SAAS,CAAC1E,CAAC,EAAEuC,CAAC,IAAI,EAAE,EAAE+B,eAAe,CAAChC,CAAC,CAAC,CAACnC,WAAW,CAAC,GAAGH,CAAC,CAACiB,KAAK,CAACqB,CAAC,EAAEC,CAAC,CAAC,CAAC;AAAE;AAC1M,SAASgC,0BAA0BA,CAACI,IAAI,EAAE3D,IAAI,EAAE;EAAE,IAAIA,IAAI,KAAKjB,OAAO,CAACiB,IAAI,CAAC,KAAK,QAAQ,IAAI,OAAOA,IAAI,KAAK,UAAU,CAAC,EAAE;IAAE,OAAOA,IAAI;EAAE,CAAC,MAAM,IAAIA,IAAI,KAAK,KAAK,CAAC,EAAE;IAAE,MAAM,IAAIQ,SAAS,CAAC,0DAA0D,CAAC;EAAE;EAAE,OAAOoD,sBAAsB,CAACD,IAAI,CAAC;AAAE;AAC/R,SAASC,sBAAsBA,CAACD,IAAI,EAAE;EAAE,IAAIA,IAAI,KAAK,KAAK,CAAC,EAAE;IAAE,MAAM,IAAIE,cAAc,CAAC,2DAA2D,CAAC;EAAE;EAAE,OAAOF,IAAI;AAAE;AACrK,SAASH,yBAAyBA,CAAA,EAAG;EAAE,IAAI;IAAE,IAAIlC,CAAC,GAAG,CAACwC,OAAO,CAAC1E,SAAS,CAAC2E,OAAO,CAAC/D,IAAI,CAACyD,OAAO,CAACC,SAAS,CAACI,OAAO,EAAE,EAAE,EAAE,YAAY,CAAC,CAAC,CAAC,CAAC;EAAE,CAAC,CAAC,OAAOxC,CAAC,EAAE,CAAC;EAAE,OAAO,CAACkC,yBAAyB,GAAG,SAASA,yBAAyBA,CAAA,EAAG;IAAE,OAAO,CAAC,CAAClC,CAAC;EAAE,CAAC,EAAE,CAAC;AAAE;AAClP,SAASgC,eAAeA,CAACtE,CAAC,EAAE;EAAEsE,eAAe,GAAGhE,MAAM,CAAC0E,cAAc,GAAG1E,MAAM,CAAC2E,cAAc,CAACzE,IAAI,CAAC,CAAC,GAAG,SAAS8D,eAAeA,CAACtE,CAAC,EAAE;IAAE,OAAOA,CAAC,CAACkF,SAAS,IAAI5E,MAAM,CAAC2E,cAAc,CAACjF,CAAC,CAAC;EAAE,CAAC;EAAE,OAAOsE,eAAe,CAACtE,CAAC,CAAC;AAAE;AACnN,SAASmF,SAASA,CAACC,QAAQ,EAAEC,UAAU,EAAE;EAAE,IAAI,OAAOA,UAAU,KAAK,UAAU,IAAIA,UAAU,KAAK,IAAI,EAAE;IAAE,MAAM,IAAI7D,SAAS,CAAC,oDAAoD,CAAC;EAAE;EAAE4D,QAAQ,CAAChF,SAAS,GAAGE,MAAM,CAACgF,MAAM,CAACD,UAAU,IAAIA,UAAU,CAACjF,SAAS,EAAE;IAAED,WAAW,EAAE;MAAE2C,KAAK,EAAEsC,QAAQ;MAAErB,QAAQ,EAAE,IAAI;MAAED,YAAY,EAAE;IAAK;EAAE,CAAC,CAAC;EAAExD,MAAM,CAAC0D,cAAc,CAACoB,QAAQ,EAAE,WAAW,EAAE;IAAErB,QAAQ,EAAE;EAAM,CAAC,CAAC;EAAE,IAAIsB,UAAU,EAAEE,eAAe,CAACH,QAAQ,EAAEC,UAAU,CAAC;AAAE;AACnc,SAASE,eAAeA,CAACvF,CAAC,EAAEwF,CAAC,EAAE;EAAED,eAAe,GAAGjF,MAAM,CAAC0E,cAAc,GAAG1E,MAAM,CAAC0E,cAAc,CAACxE,IAAI,CAAC,CAAC,GAAG,SAAS+E,eAAeA,CAACvF,CAAC,EAAEwF,CAAC,EAAE;IAAExF,CAAC,CAACkF,SAAS,GAAGM,CAAC;IAAE,OAAOxF,CAAC;EAAE,CAAC;EAAE,OAAOuF,eAAe,CAACvF,CAAC,EAAEwF,CAAC,CAAC;AAAE;AACvM,SAASC,eAAeA,CAACC,GAAG,EAAE5E,GAAG,EAAEgC,KAAK,EAAE;EAAEhC,GAAG,GAAGmD,cAAc,CAACnD,GAAG,CAAC;EAAE,IAAIA,GAAG,IAAI4E,GAAG,EAAE;IAAEpF,MAAM,CAAC0D,cAAc,CAAC0B,GAAG,EAAE5E,GAAG,EAAE;MAAEgC,KAAK,EAAEA,KAAK;MAAEe,UAAU,EAAE,IAAI;MAAEC,YAAY,EAAE,IAAI;MAAEC,QAAQ,EAAE;IAAK,CAAC,CAAC;EAAE,CAAC,MAAM;IAAE2B,GAAG,CAAC5E,GAAG,CAAC,GAAGgC,KAAK;EAAE;EAAE,OAAO4C,GAAG;AAAE;AAC3O,SAASzB,cAAcA,CAAC3B,CAAC,EAAE;EAAE,IAAI5B,CAAC,GAAGiF,YAAY,CAACrD,CAAC,EAAE,QAAQ,CAAC;EAAE,OAAO,QAAQ,IAAIvC,OAAO,CAACW,CAAC,CAAC,GAAGA,CAAC,GAAGA,CAAC,GAAG,EAAE;AAAE;AAC5G,SAASiF,YAAYA,CAACrD,CAAC,EAAEF,CAAC,EAAE;EAAE,IAAI,QAAQ,IAAIrC,OAAO,CAACuC,CAAC,CAAC,IAAI,CAACA,CAAC,EAAE,OAAOA,CAAC;EAAE,IAAIC,CAAC,GAAGD,CAAC,CAACrC,MAAM,CAAC2F,WAAW,CAAC;EAAE,IAAI,KAAK,CAAC,KAAKrD,CAAC,EAAE;IAAE,IAAI7B,CAAC,GAAG6B,CAAC,CAACvB,IAAI,CAACsB,CAAC,EAAEF,CAAC,IAAI,SAAS,CAAC;IAAE,IAAI,QAAQ,IAAIrC,OAAO,CAACW,CAAC,CAAC,EAAE,OAAOA,CAAC;IAAE,MAAM,IAAIc,SAAS,CAAC,8CAA8C,CAAC;EAAE;EAAE,OAAO,CAAC,QAAQ,KAAKY,CAAC,GAAGyD,MAAM,GAAGC,MAAM,EAAExD,CAAC,CAAC;AAAE;AAC3T;AACA;AACA;AACA,OAAOyD,KAAK,MAAM,OAAO;AACzB,OAAOC,SAAS,MAAM,gBAAgB;AACtC,SAASC,KAAK,QAAQ,oBAAoB;AAC1C,SAASC,WAAW,QAAQ,oBAAoB;AAChD;AACA,OAAO,IAAIC,QAAQ,GAAG,aAAa,UAAUC,gBAAgB,EAAE;EAC7D,SAASD,QAAQA,CAAA,EAAG;IAClB5C,eAAe,CAAC,IAAI,EAAE4C,QAAQ,CAAC;IAC/B,OAAO9B,UAAU,CAAC,IAAI,EAAE8B,QAAQ,EAAExF,SAAS,CAAC;EAC9C;EACAwE,SAAS,CAACgB,QAAQ,EAAEC,gBAAgB,CAAC;EACrC,OAAOlC,YAAY,CAACiC,QAAQ,EAAE,CAAC;IAC7BrF,GAAG,EAAE,QAAQ;IACbgC,KAAK,EAAE,SAASuD,MAAMA,CAAA,EAAG;MACvB,IAAIC,WAAW,GAAG,IAAI,CAAC3C,KAAK;QAC1B4C,MAAM,GAAGD,WAAW,CAACC,MAAM;QAC3BC,MAAM,GAAGF,WAAW,CAACE,MAAM;QAC3BC,KAAK,GAAGH,WAAW,CAACG,KAAK;QACzBC,OAAO,GAAGJ,WAAW,CAACI,OAAO;QAC7BC,IAAI,GAAGL,WAAW,CAACK,IAAI;QACvBC,kBAAkB,GAAGN,WAAW,CAACM,kBAAkB;QACnDC,KAAK,GAAGP,WAAW,CAACO,KAAK;QACzBC,KAAK,GAAGR,WAAW,CAACQ,KAAK;QACzBC,MAAM,GAAG/D,wBAAwB,CAACsD,WAAW,EAAExG,SAAS,CAAC;MAC3D,IAAIkH,QAAQ,GAAGd,WAAW,CAACa,MAAM,EAAE,KAAK,CAAC;MACzC,CAAC,EAAE,IAAI,CAACpD,KAAK,CAACsD,SAAS,KAAK,GAAG,IAAIJ,KAAK,CAACK,IAAI,KAAK,QAAQ,CAAC,GAAGC,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,GAAGrB,SAAS,CAAC,KAAK,EAAE,sDAAsD,CAAC,GAAGA,SAAS,CAAC,KAAK,CAAC,GAAG,KAAK,CAAC;MAC1M,IAAIsB,SAAS,GAAGX,IAAI,CAACY,GAAG,CAAC,UAAUC,KAAK,EAAE;QACxC,IAAIC,mBAAmB,GAAGb,kBAAkB,CAACY,KAAK,EAAEd,OAAO,CAAC;UAC1DgB,CAAC,GAAGD,mBAAmB,CAACC,CAAC;UACzBC,CAAC,GAAGF,mBAAmB,CAACE,CAAC;UACzB7E,KAAK,GAAG2E,mBAAmB,CAAC3E,KAAK;UACjC8E,QAAQ,GAAGH,mBAAmB,CAACG,QAAQ;QACzC,IAAI,CAACA,QAAQ,EAAE;UACb,OAAO,IAAI;QACb;QACA,IAAIC,eAAe,GAAG,EAAE;QACxB,IAAIC,QAAQ,EAAEC,SAAS;QACvB,IAAIhG,KAAK,CAACgB,OAAO,CAAC6E,QAAQ,CAAC,EAAE;UAC3B,IAAII,SAAS,GAAG9G,cAAc,CAAC0G,QAAQ,EAAE,CAAC,CAAC;UAC3CE,QAAQ,GAAGE,SAAS,CAAC,CAAC,CAAC;UACvBD,SAAS,GAAGC,SAAS,CAAC,CAAC,CAAC;QAC1B,CAAC,MAAM;UACLF,QAAQ,GAAGC,SAAS,GAAGH,QAAQ;QACjC;QACA,IAAIpB,MAAM,KAAK,UAAU,EAAE;UACzB;UACA,IAAIyB,KAAK,GAAGpB,KAAK,CAACoB,KAAK;UACvB,IAAIC,IAAI,GAAGP,CAAC,GAAGpB,MAAM;UACrB,IAAI4B,IAAI,GAAGD,IAAI,GAAGzB,KAAK;UACvB,IAAI2B,IAAI,GAAGF,IAAI,GAAGzB,KAAK;UACvB,IAAI4B,IAAI,GAAGJ,KAAK,CAACnF,KAAK,GAAGgF,QAAQ,CAAC;UAClC,IAAIQ,IAAI,GAAGL,KAAK,CAACnF,KAAK,GAAGiF,SAAS,CAAC;;UAEnC;UACAF,eAAe,CAAChF,IAAI,CAAC;YACnB0F,EAAE,EAAED,IAAI;YACRE,EAAE,EAAEL,IAAI;YACRM,EAAE,EAAEH,IAAI;YACRI,EAAE,EAAEN;UACN,CAAC,CAAC;UACF;UACAP,eAAe,CAAChF,IAAI,CAAC;YACnB0F,EAAE,EAAEF,IAAI;YACRG,EAAE,EAAEN,IAAI;YACRO,EAAE,EAAEH,IAAI;YACRI,EAAE,EAAER;UACN,CAAC,CAAC;UACF;UACAL,eAAe,CAAChF,IAAI,CAAC;YACnB0F,EAAE,EAAEF,IAAI;YACRG,EAAE,EAAEL,IAAI;YACRM,EAAE,EAAEJ,IAAI;YACRK,EAAE,EAAEN;UACN,CAAC,CAAC;QACJ,CAAC,MAAM,IAAI5B,MAAM,KAAK,YAAY,EAAE;UAClC;UACA,IAAImC,MAAM,GAAG7B,KAAK,CAACmB,KAAK;UACxB,IAAIW,IAAI,GAAGlB,CAAC,GAAGnB,MAAM;UACrB,IAAIsC,KAAK,GAAGD,IAAI,GAAGnC,KAAK;UACxB,IAAIqC,KAAK,GAAGF,IAAI,GAAGnC,KAAK;UACxB,IAAIsC,KAAK,GAAGJ,MAAM,CAAC7F,KAAK,GAAGgF,QAAQ,CAAC;UACpC,IAAIkB,KAAK,GAAGL,MAAM,CAAC7F,KAAK,GAAGiF,SAAS,CAAC;;UAErC;UACAF,eAAe,CAAChF,IAAI,CAAC;YACnB0F,EAAE,EAAEM,KAAK;YACTL,EAAE,EAAEQ,KAAK;YACTP,EAAE,EAAEK,KAAK;YACTJ,EAAE,EAAEM;UACN,CAAC,CAAC;UACF;UACAnB,eAAe,CAAChF,IAAI,CAAC;YACnB0F,EAAE,EAAEK,IAAI;YACRJ,EAAE,EAAEO,KAAK;YACTN,EAAE,EAAEG,IAAI;YACRF,EAAE,EAAEM;UACN,CAAC,CAAC;UACF;UACAnB,eAAe,CAAChF,IAAI,CAAC;YACnB0F,EAAE,EAAEM,KAAK;YACTL,EAAE,EAAEO,KAAK;YACTN,EAAE,EAAEK,KAAK;YACTJ,EAAE,EAAEK;UACN,CAAC,CAAC;QACJ;QACA,OAAO,aAAahD,KAAK,CAACkD,aAAa,CAAChD,KAAK,EAAE5F,QAAQ,CAAC;UACtD6I,SAAS,EAAE,mBAAmB;UAC9BpI,GAAG,EAAE,MAAM,CAACqI,MAAM,CAACtB,eAAe,CAACN,GAAG,CAAC,UAAU6B,CAAC,EAAE;YAClD,OAAO,EAAE,CAACD,MAAM,CAACC,CAAC,CAACb,EAAE,EAAE,GAAG,CAAC,CAACY,MAAM,CAACC,CAAC,CAACX,EAAE,EAAE,GAAG,CAAC,CAACU,MAAM,CAACC,CAAC,CAACZ,EAAE,EAAE,GAAG,CAAC,CAACW,MAAM,CAACC,CAAC,CAACV,EAAE,CAAC;UAC9E,CAAC,CAAC;QACJ,CAAC,EAAE1B,QAAQ,CAAC,EAAEa,eAAe,CAACN,GAAG,CAAC,UAAU8B,WAAW,EAAE;UACvD,OAAO,aAAatD,KAAK,CAACkD,aAAa,CAAC,MAAM,EAAE5I,QAAQ,CAAC,CAAC,CAAC,EAAEgJ,WAAW,EAAE;YACxEvI,GAAG,EAAE,OAAO,CAACqI,MAAM,CAACE,WAAW,CAACd,EAAE,EAAE,GAAG,CAAC,CAACY,MAAM,CAACE,WAAW,CAACZ,EAAE,EAAE,GAAG,CAAC,CAACU,MAAM,CAACE,WAAW,CAACb,EAAE,EAAE,GAAG,CAAC,CAACW,MAAM,CAACE,WAAW,CAACX,EAAE;UACxH,CAAC,CAAC,CAAC;QACL,CAAC,CAAC,CAAC;MACL,CAAC,CAAC;MACF,OAAO,aAAa3C,KAAK,CAACkD,aAAa,CAAChD,KAAK,EAAE;QAC7CiD,SAAS,EAAE;MACb,CAAC,EAAE5B,SAAS,CAAC;IACf;EACF,CAAC,CAAC,CAAC;AACL,CAAC,CAACvB,KAAK,CAACuD,SAAS,CAAC;AAClB7D,eAAe,CAACU,QAAQ,EAAE,cAAc,EAAE;EACxCoD,MAAM,EAAE,OAAO;EACfC,WAAW,EAAE,GAAG;EAChB/C,KAAK,EAAE,CAAC;EACRF,MAAM,EAAE,CAAC;EACTC,MAAM,EAAE;AACV,CAAC,CAAC;AACFf,eAAe,CAACU,QAAQ,EAAE,aAAa,EAAE,UAAU,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}