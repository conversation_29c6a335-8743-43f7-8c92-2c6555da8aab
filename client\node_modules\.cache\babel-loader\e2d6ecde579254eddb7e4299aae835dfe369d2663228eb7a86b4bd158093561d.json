{"ast": null, "code": "import define, { extend } from \"./define.js\";\nimport { Color, rgbConvert, Rgb, darker, brighter } from \"./color.js\";\nimport { degrees, radians } from \"./math.js\";\nvar A = -0.14861,\n  B = +1.78277,\n  C = -0.29227,\n  D = -0.90649,\n  E = +1.97294,\n  ED = E * D,\n  EB = E * B,\n  BC_DA = B * C - D * A;\nfunction cubehelixConvert(o) {\n  if (o instanceof Cubehelix) return new Cubehelix(o.h, o.s, o.l, o.opacity);\n  if (!(o instanceof Rgb)) o = rgbConvert(o);\n  var r = o.r / 255,\n    g = o.g / 255,\n    b = o.b / 255,\n    l = (BC_DA * b + ED * r - EB * g) / (BC_DA + ED - EB),\n    bl = b - l,\n    k = (E * (g - l) - C * bl) / D,\n    s = Math.sqrt(k * k + bl * bl) / (E * l * (1 - l)),\n    // NaN if l=0 or l=1\n    h = s ? Math.atan2(k, bl) * degrees - 120 : NaN;\n  return new Cubehelix(h < 0 ? h + 360 : h, s, l, o.opacity);\n}\nexport default function cubehelix(h, s, l, opacity) {\n  return arguments.length === 1 ? cubehelixConvert(h) : new Cubehelix(h, s, l, opacity == null ? 1 : opacity);\n}\nexport function Cubehelix(h, s, l, opacity) {\n  this.h = +h;\n  this.s = +s;\n  this.l = +l;\n  this.opacity = +opacity;\n}\ndefine(Cubehelix, cubehelix, extend(Color, {\n  brighter(k) {\n    k = k == null ? brighter : Math.pow(brighter, k);\n    return new Cubehelix(this.h, this.s, this.l * k, this.opacity);\n  },\n  darker(k) {\n    k = k == null ? darker : Math.pow(darker, k);\n    return new Cubehelix(this.h, this.s, this.l * k, this.opacity);\n  },\n  rgb() {\n    var h = isNaN(this.h) ? 0 : (this.h + 120) * radians,\n      l = +this.l,\n      a = isNaN(this.s) ? 0 : this.s * l * (1 - l),\n      cosh = Math.cos(h),\n      sinh = Math.sin(h);\n    return new Rgb(255 * (l + a * (A * cosh + B * sinh)), 255 * (l + a * (C * cosh + D * sinh)), 255 * (l + a * (E * cosh)), this.opacity);\n  }\n}));", "map": {"version": 3, "names": ["define", "extend", "Color", "rgbConvert", "Rgb", "darker", "brighter", "degrees", "radians", "A", "B", "C", "D", "E", "ED", "EB", "BC_DA", "cubehelixConvert", "o", "Cubehel<PERSON>", "h", "s", "l", "opacity", "r", "g", "b", "bl", "k", "Math", "sqrt", "atan2", "NaN", "cubehelix", "arguments", "length", "pow", "rgb", "isNaN", "a", "cosh", "cos", "sinh", "sin"], "sources": ["D:/ecommerce/node_modules/d3-color/src/cubehelix.js"], "sourcesContent": ["import define, {extend} from \"./define.js\";\nimport {Color, rgbConvert, Rgb, darker, brighter} from \"./color.js\";\nimport {degrees, radians} from \"./math.js\";\n\nvar A = -0.14861,\n    B = +1.78277,\n    C = -0.29227,\n    D = -0.90649,\n    E = +1.97294,\n    ED = E * D,\n    EB = E * B,\n    BC_DA = B * C - D * A;\n\nfunction cubehelixConvert(o) {\n  if (o instanceof Cubehelix) return new Cubehelix(o.h, o.s, o.l, o.opacity);\n  if (!(o instanceof Rgb)) o = rgbConvert(o);\n  var r = o.r / 255,\n      g = o.g / 255,\n      b = o.b / 255,\n      l = (BC_DA * b + ED * r - EB * g) / (BC_DA + ED - EB),\n      bl = b - l,\n      k = (E * (g - l) - C * bl) / D,\n      s = Math.sqrt(k * k + bl * bl) / (E * l * (1 - l)), // NaN if l=0 or l=1\n      h = s ? Math.atan2(k, bl) * degrees - 120 : NaN;\n  return new Cubehelix(h < 0 ? h + 360 : h, s, l, o.opacity);\n}\n\nexport default function cubehelix(h, s, l, opacity) {\n  return arguments.length === 1 ? cubehelixConvert(h) : new Cubehelix(h, s, l, opacity == null ? 1 : opacity);\n}\n\nexport function Cubehelix(h, s, l, opacity) {\n  this.h = +h;\n  this.s = +s;\n  this.l = +l;\n  this.opacity = +opacity;\n}\n\ndefine(Cubehelix, cubehelix, extend(Color, {\n  brighter(k) {\n    k = k == null ? brighter : Math.pow(brighter, k);\n    return new Cubehelix(this.h, this.s, this.l * k, this.opacity);\n  },\n  darker(k) {\n    k = k == null ? darker : Math.pow(darker, k);\n    return new Cubehelix(this.h, this.s, this.l * k, this.opacity);\n  },\n  rgb() {\n    var h = isNaN(this.h) ? 0 : (this.h + 120) * radians,\n        l = +this.l,\n        a = isNaN(this.s) ? 0 : this.s * l * (1 - l),\n        cosh = Math.cos(h),\n        sinh = Math.sin(h);\n    return new Rgb(\n      255 * (l + a * (A * cosh + B * sinh)),\n      255 * (l + a * (C * cosh + D * sinh)),\n      255 * (l + a * (E * cosh)),\n      this.opacity\n    );\n  }\n}));\n"], "mappings": "AAAA,OAAOA,MAAM,IAAGC,MAAM,QAAO,aAAa;AAC1C,SAAQC,KAAK,EAAEC,UAAU,EAAEC,GAAG,EAAEC,MAAM,EAAEC,QAAQ,QAAO,YAAY;AACnE,SAAQC,OAAO,EAAEC,OAAO,QAAO,WAAW;AAE1C,IAAIC,CAAC,GAAG,CAAC,OAAO;EACZC,CAAC,GAAG,CAAC,OAAO;EACZC,CAAC,GAAG,CAAC,OAAO;EACZC,CAAC,GAAG,CAAC,OAAO;EACZC,CAAC,GAAG,CAAC,OAAO;EACZC,EAAE,GAAGD,CAAC,GAAGD,CAAC;EACVG,EAAE,GAAGF,CAAC,GAAGH,CAAC;EACVM,KAAK,GAAGN,CAAC,GAAGC,CAAC,GAAGC,CAAC,GAAGH,CAAC;AAEzB,SAASQ,gBAAgBA,CAACC,CAAC,EAAE;EAC3B,IAAIA,CAAC,YAAYC,SAAS,EAAE,OAAO,IAAIA,SAAS,CAACD,CAAC,CAACE,CAAC,EAAEF,CAAC,CAACG,CAAC,EAAEH,CAAC,CAACI,CAAC,EAAEJ,CAAC,CAACK,OAAO,CAAC;EAC1E,IAAI,EAAEL,CAAC,YAAYd,GAAG,CAAC,EAAEc,CAAC,GAAGf,UAAU,CAACe,CAAC,CAAC;EAC1C,IAAIM,CAAC,GAAGN,CAAC,CAACM,CAAC,GAAG,GAAG;IACbC,CAAC,GAAGP,CAAC,CAACO,CAAC,GAAG,GAAG;IACbC,CAAC,GAAGR,CAAC,CAACQ,CAAC,GAAG,GAAG;IACbJ,CAAC,GAAG,CAACN,KAAK,GAAGU,CAAC,GAAGZ,EAAE,GAAGU,CAAC,GAAGT,EAAE,GAAGU,CAAC,KAAKT,KAAK,GAAGF,EAAE,GAAGC,EAAE,CAAC;IACrDY,EAAE,GAAGD,CAAC,GAAGJ,CAAC;IACVM,CAAC,GAAG,CAACf,CAAC,IAAIY,CAAC,GAAGH,CAAC,CAAC,GAAGX,CAAC,GAAGgB,EAAE,IAAIf,CAAC;IAC9BS,CAAC,GAAGQ,IAAI,CAACC,IAAI,CAACF,CAAC,GAAGA,CAAC,GAAGD,EAAE,GAAGA,EAAE,CAAC,IAAId,CAAC,GAAGS,CAAC,IAAI,CAAC,GAAGA,CAAC,CAAC,CAAC;IAAE;IACpDF,CAAC,GAAGC,CAAC,GAAGQ,IAAI,CAACE,KAAK,CAACH,CAAC,EAAED,EAAE,CAAC,GAAGpB,OAAO,GAAG,GAAG,GAAGyB,GAAG;EACnD,OAAO,IAAIb,SAAS,CAACC,CAAC,GAAG,CAAC,GAAGA,CAAC,GAAG,GAAG,GAAGA,CAAC,EAAEC,CAAC,EAAEC,CAAC,EAAEJ,CAAC,CAACK,OAAO,CAAC;AAC5D;AAEA,eAAe,SAASU,SAASA,CAACb,CAAC,EAAEC,CAAC,EAAEC,CAAC,EAAEC,OAAO,EAAE;EAClD,OAAOW,SAAS,CAACC,MAAM,KAAK,CAAC,GAAGlB,gBAAgB,CAACG,CAAC,CAAC,GAAG,IAAID,SAAS,CAACC,CAAC,EAAEC,CAAC,EAAEC,CAAC,EAAEC,OAAO,IAAI,IAAI,GAAG,CAAC,GAAGA,OAAO,CAAC;AAC7G;AAEA,OAAO,SAASJ,SAASA,CAACC,CAAC,EAAEC,CAAC,EAAEC,CAAC,EAAEC,OAAO,EAAE;EAC1C,IAAI,CAACH,CAAC,GAAG,CAACA,CAAC;EACX,IAAI,CAACC,CAAC,GAAG,CAACA,CAAC;EACX,IAAI,CAACC,CAAC,GAAG,CAACA,CAAC;EACX,IAAI,CAACC,OAAO,GAAG,CAACA,OAAO;AACzB;AAEAvB,MAAM,CAACmB,SAAS,EAAEc,SAAS,EAAEhC,MAAM,CAACC,KAAK,EAAE;EACzCI,QAAQA,CAACsB,CAAC,EAAE;IACVA,CAAC,GAAGA,CAAC,IAAI,IAAI,GAAGtB,QAAQ,GAAGuB,IAAI,CAACO,GAAG,CAAC9B,QAAQ,EAAEsB,CAAC,CAAC;IAChD,OAAO,IAAIT,SAAS,CAAC,IAAI,CAACC,CAAC,EAAE,IAAI,CAACC,CAAC,EAAE,IAAI,CAACC,CAAC,GAAGM,CAAC,EAAE,IAAI,CAACL,OAAO,CAAC;EAChE,CAAC;EACDlB,MAAMA,CAACuB,CAAC,EAAE;IACRA,CAAC,GAAGA,CAAC,IAAI,IAAI,GAAGvB,MAAM,GAAGwB,IAAI,CAACO,GAAG,CAAC/B,MAAM,EAAEuB,CAAC,CAAC;IAC5C,OAAO,IAAIT,SAAS,CAAC,IAAI,CAACC,CAAC,EAAE,IAAI,CAACC,CAAC,EAAE,IAAI,CAACC,CAAC,GAAGM,CAAC,EAAE,IAAI,CAACL,OAAO,CAAC;EAChE,CAAC;EACDc,GAAGA,CAAA,EAAG;IACJ,IAAIjB,CAAC,GAAGkB,KAAK,CAAC,IAAI,CAAClB,CAAC,CAAC,GAAG,CAAC,GAAG,CAAC,IAAI,CAACA,CAAC,GAAG,GAAG,IAAIZ,OAAO;MAChDc,CAAC,GAAG,CAAC,IAAI,CAACA,CAAC;MACXiB,CAAC,GAAGD,KAAK,CAAC,IAAI,CAACjB,CAAC,CAAC,GAAG,CAAC,GAAG,IAAI,CAACA,CAAC,GAAGC,CAAC,IAAI,CAAC,GAAGA,CAAC,CAAC;MAC5CkB,IAAI,GAAGX,IAAI,CAACY,GAAG,CAACrB,CAAC,CAAC;MAClBsB,IAAI,GAAGb,IAAI,CAACc,GAAG,CAACvB,CAAC,CAAC;IACtB,OAAO,IAAIhB,GAAG,CACZ,GAAG,IAAIkB,CAAC,GAAGiB,CAAC,IAAI9B,CAAC,GAAG+B,IAAI,GAAG9B,CAAC,GAAGgC,IAAI,CAAC,CAAC,EACrC,GAAG,IAAIpB,CAAC,GAAGiB,CAAC,IAAI5B,CAAC,GAAG6B,IAAI,GAAG5B,CAAC,GAAG8B,IAAI,CAAC,CAAC,EACrC,GAAG,IAAIpB,CAAC,GAAGiB,CAAC,IAAI1B,CAAC,GAAG2B,IAAI,CAAC,CAAC,EAC1B,IAAI,CAACjB,OACP,CAAC;EACH;AACF,CAAC,CAAC,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}