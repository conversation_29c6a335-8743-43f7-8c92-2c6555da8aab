{"ast": null, "code": "var _jsxFileName = \"D:\\\\ecommerce\\\\client\\\\src\\\\App.js\",\n  _s = $RefreshSig$();\nimport React from 'react';\nimport { BrowserRouter as Router, Routes, Route, Link } from 'react-router-dom';\nimport { Provider } from 'react-redux';\nimport { ThemeProvider, createTheme } from '@mui/material/styles';\nimport CssBaseline from '@mui/material/CssBaseline';\nimport { Typography } from '@mui/material';\nimport { configureStore, createSlice } from '@reduxjs/toolkit';\nimport Home from './components/Home';\nimport Cart from './components/Cart';\nimport Checkout from './components/Checkout';\nimport Dashboard from './components/admin/Dashboard';\nimport AdminDashboard from './components/admin/AdminDashboard';\nimport UserDashboard from './components/user/UserDashboard';\nimport Navigation from './components/Navigation';\nimport DashboardSelector from './components/DashboardSelector';\nimport './App.css';\n\n// Create a theme\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst theme = createTheme({\n  palette: {\n    primary: {\n      main: '#2c3e50'\n    },\n    secondary: {\n      main: '#e74c3c'\n    }\n  }\n});\n\n// Simple Redux store for demo purposes\n\nconst cartSlice = createSlice({\n  name: 'cart',\n  initialState: {\n    items: [],\n    total: 0\n  },\n  reducers: {\n    addToCart: (state, action) => {\n      state.items.push(action.payload);\n      state.total += action.payload.price;\n    },\n    removeFromCart: (state, action) => {\n      const index = state.items.findIndex(item => item.id === action.payload.id);\n      if (index !== -1) {\n        state.total -= state.items[index].price;\n        state.items.splice(index, 1);\n      }\n    }\n  }\n});\nconst store = configureStore({\n  reducer: {\n    cart: cartSlice.reducer\n  }\n});\nfunction App() {\n  _s();\n  const [drawerOpen, setDrawerOpen] = React.useState(false);\n  const toggleDrawer = () => {\n    setDrawerOpen(!drawerOpen);\n  };\n  return /*#__PURE__*/_jsxDEV(Provider, {\n    store: store,\n    children: /*#__PURE__*/_jsxDEV(ThemeProvider, {\n      theme: theme,\n      children: [/*#__PURE__*/_jsxDEV(CssBaseline, {}, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 69,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Router, {\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"App\",\n          children: /*#__PURE__*/_jsxDEV(Routes, {\n            children: [/*#__PURE__*/_jsxDEV(Route, {\n              path: \"/\",\n              element: /*#__PURE__*/_jsxDEV(Home, {}, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 73,\n                columnNumber: 40\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 73,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Route, {\n              path: \"/cart\",\n              element: /*#__PURE__*/_jsxDEV(Cart, {}, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 74,\n                columnNumber: 44\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 74,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Route, {\n              path: \"/checkout\",\n              element: /*#__PURE__*/_jsxDEV(Checkout, {}, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 75,\n                columnNumber: 48\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 75,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Route, {\n              path: \"/dashboards\",\n              element: /*#__PURE__*/_jsxDEV(DashboardSelector, {}, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 76,\n                columnNumber: 50\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 76,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Route, {\n              path: \"/dashboard\",\n              element: /*#__PURE__*/_jsxDEV(UserDashboard, {}, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 77,\n                columnNumber: 49\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 77,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Route, {\n              path: \"/admin\",\n              element: /*#__PURE__*/_jsxDEV(AdminDashboard, {}, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 78,\n                columnNumber: 45\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 78,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Route, {\n              path: \"/admin/old\",\n              element: /*#__PURE__*/_jsxDEV(Dashboard, {}, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 79,\n                columnNumber: 49\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 79,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 72,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 71,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 70,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 68,\n      columnNumber: 7\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 67,\n    columnNumber: 5\n  }, this);\n}\n_s(App, \"i0pHI9YMbVyneVc1gk5xK0P2xMQ=\");\n_c = App;\nexport default App;\nvar _c;\n$RefreshReg$(_c, \"App\");", "map": {"version": 3, "names": ["React", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Router", "Routes", "Route", "Link", "Provider", "ThemeProvider", "createTheme", "CssBaseline", "Typography", "configureStore", "createSlice", "Home", "<PERSON><PERSON>", "Checkout", "Dashboard", "AdminDashboard", "UserDashboard", "Navigation", "DashboardSelector", "jsxDEV", "_jsxDEV", "theme", "palette", "primary", "main", "secondary", "cartSlice", "name", "initialState", "items", "total", "reducers", "addToCart", "state", "action", "push", "payload", "price", "removeFromCart", "index", "findIndex", "item", "id", "splice", "store", "reducer", "cart", "App", "_s", "drawerOpen", "setDrawerOpen", "useState", "toggle<PERSON>rawer", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "className", "path", "element", "_c", "$RefreshReg$"], "sources": ["D:/ecommerce/client/src/App.js"], "sourcesContent": ["import React from 'react';\nimport { BrowserRouter as Router, Routes, Route, Link } from 'react-router-dom';\nimport { Provider } from 'react-redux';\nimport { ThemeProvider, createTheme } from '@mui/material/styles';\nimport CssBaseline from '@mui/material/CssBaseline';\nimport { Typography } from '@mui/material';\nimport { configureStore, createSlice } from '@reduxjs/toolkit';\nimport Home from './components/Home';\nimport Cart from './components/Cart';\nimport Checkout from './components/Checkout';\nimport Dashboard from './components/admin/Dashboard';\nimport AdminDashboard from './components/admin/AdminDashboard';\nimport UserDashboard from './components/user/UserDashboard';\nimport Navigation from './components/Navigation';\nimport DashboardSelector from './components/DashboardSelector';\nimport './App.css';\n\n// Create a theme\nconst theme = createTheme({\n  palette: {\n    primary: {\n      main: '#2c3e50',\n    },\n    secondary: {\n      main: '#e74c3c',\n    },\n  },\n});\n\n// Simple Redux store for demo purposes\n\nconst cartSlice = createSlice({\n  name: 'cart',\n  initialState: {\n    items: [],\n    total: 0,\n  },\n  reducers: {\n    addToCart: (state, action) => {\n      state.items.push(action.payload);\n      state.total += action.payload.price;\n    },\n    removeFromCart: (state, action) => {\n      const index = state.items.findIndex(item => item.id === action.payload.id);\n      if (index !== -1) {\n        state.total -= state.items[index].price;\n        state.items.splice(index, 1);\n      }\n    },\n  },\n});\n\nconst store = configureStore({\n  reducer: {\n    cart: cartSlice.reducer,\n  },\n});\n\nfunction App() {\n  const [drawerOpen, setDrawerOpen] = React.useState(false);\n\n  const toggleDrawer = () => {\n    setDrawerOpen(!drawerOpen);\n  };\n\n  return (\n    <Provider store={store}>\n      <ThemeProvider theme={theme}>\n        <CssBaseline />\n        <Router>\n          <div className=\"App\">\n            <Routes>\n              <Route path=\"/\" element={<Home />} />\n              <Route path=\"/cart\" element={<Cart />} />\n              <Route path=\"/checkout\" element={<Checkout />} />\n              <Route path=\"/dashboards\" element={<DashboardSelector />} />\n              <Route path=\"/dashboard\" element={<UserDashboard />} />\n              <Route path=\"/admin\" element={<AdminDashboard />} />\n              <Route path=\"/admin/old\" element={<Dashboard />} />\n            </Routes>\n          </div>\n        </Router>\n      </ThemeProvider>\n    </Provider>\n  );\n}\n\nexport default App;\n"], "mappings": ";;AAAA,OAAOA,KAAK,MAAM,OAAO;AACzB,SAASC,aAAa,IAAIC,MAAM,EAAEC,MAAM,EAAEC,KAAK,EAAEC,IAAI,QAAQ,kBAAkB;AAC/E,SAASC,QAAQ,QAAQ,aAAa;AACtC,SAASC,aAAa,EAAEC,WAAW,QAAQ,sBAAsB;AACjE,OAAOC,WAAW,MAAM,2BAA2B;AACnD,SAASC,UAAU,QAAQ,eAAe;AAC1C,SAASC,cAAc,EAAEC,WAAW,QAAQ,kBAAkB;AAC9D,OAAOC,IAAI,MAAM,mBAAmB;AACpC,OAAOC,IAAI,MAAM,mBAAmB;AACpC,OAAOC,QAAQ,MAAM,uBAAuB;AAC5C,OAAOC,SAAS,MAAM,8BAA8B;AACpD,OAAOC,cAAc,MAAM,mCAAmC;AAC9D,OAAOC,aAAa,MAAM,iCAAiC;AAC3D,OAAOC,UAAU,MAAM,yBAAyB;AAChD,OAAOC,iBAAiB,MAAM,gCAAgC;AAC9D,OAAO,WAAW;;AAElB;AAAA,SAAAC,MAAA,IAAAC,OAAA;AACA,MAAMC,KAAK,GAAGf,WAAW,CAAC;EACxBgB,OAAO,EAAE;IACPC,OAAO,EAAE;MACPC,IAAI,EAAE;IACR,CAAC;IACDC,SAAS,EAAE;MACTD,IAAI,EAAE;IACR;EACF;AACF,CAAC,CAAC;;AAEF;;AAEA,MAAME,SAAS,GAAGhB,WAAW,CAAC;EAC5BiB,IAAI,EAAE,MAAM;EACZC,YAAY,EAAE;IACZC,KAAK,EAAE,EAAE;IACTC,KAAK,EAAE;EACT,CAAC;EACDC,QAAQ,EAAE;IACRC,SAAS,EAAEA,CAACC,KAAK,EAAEC,MAAM,KAAK;MAC5BD,KAAK,CAACJ,KAAK,CAACM,IAAI,CAACD,MAAM,CAACE,OAAO,CAAC;MAChCH,KAAK,CAACH,KAAK,IAAII,MAAM,CAACE,OAAO,CAACC,KAAK;IACrC,CAAC;IACDC,cAAc,EAAEA,CAACL,KAAK,EAAEC,MAAM,KAAK;MACjC,MAAMK,KAAK,GAAGN,KAAK,CAACJ,KAAK,CAACW,SAAS,CAACC,IAAI,IAAIA,IAAI,CAACC,EAAE,KAAKR,MAAM,CAACE,OAAO,CAACM,EAAE,CAAC;MAC1E,IAAIH,KAAK,KAAK,CAAC,CAAC,EAAE;QAChBN,KAAK,CAACH,KAAK,IAAIG,KAAK,CAACJ,KAAK,CAACU,KAAK,CAAC,CAACF,KAAK;QACvCJ,KAAK,CAACJ,KAAK,CAACc,MAAM,CAACJ,KAAK,EAAE,CAAC,CAAC;MAC9B;IACF;EACF;AACF,CAAC,CAAC;AAEF,MAAMK,KAAK,GAAGnC,cAAc,CAAC;EAC3BoC,OAAO,EAAE;IACPC,IAAI,EAAEpB,SAAS,CAACmB;EAClB;AACF,CAAC,CAAC;AAEF,SAASE,GAAGA,CAAA,EAAG;EAAAC,EAAA;EACb,MAAM,CAACC,UAAU,EAAEC,aAAa,CAAC,GAAGpD,KAAK,CAACqD,QAAQ,CAAC,KAAK,CAAC;EAEzD,MAAMC,YAAY,GAAGA,CAAA,KAAM;IACzBF,aAAa,CAAC,CAACD,UAAU,CAAC;EAC5B,CAAC;EAED,oBACE7B,OAAA,CAAChB,QAAQ;IAACwC,KAAK,EAAEA,KAAM;IAAAS,QAAA,eACrBjC,OAAA,CAACf,aAAa;MAACgB,KAAK,EAAEA,KAAM;MAAAgC,QAAA,gBAC1BjC,OAAA,CAACb,WAAW;QAAA+C,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC,eACfrC,OAAA,CAACpB,MAAM;QAAAqD,QAAA,eACLjC,OAAA;UAAKsC,SAAS,EAAC,KAAK;UAAAL,QAAA,eAClBjC,OAAA,CAACnB,MAAM;YAAAoD,QAAA,gBACLjC,OAAA,CAAClB,KAAK;cAACyD,IAAI,EAAC,GAAG;cAACC,OAAO,eAAExC,OAAA,CAACT,IAAI;gBAAA2C,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAAE;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,eACrCrC,OAAA,CAAClB,KAAK;cAACyD,IAAI,EAAC,OAAO;cAACC,OAAO,eAAExC,OAAA,CAACR,IAAI;gBAAA0C,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAAE;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,eACzCrC,OAAA,CAAClB,KAAK;cAACyD,IAAI,EAAC,WAAW;cAACC,OAAO,eAAExC,OAAA,CAACP,QAAQ;gBAAAyC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAAE;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,eACjDrC,OAAA,CAAClB,KAAK;cAACyD,IAAI,EAAC,aAAa;cAACC,OAAO,eAAExC,OAAA,CAACF,iBAAiB;gBAAAoC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAAE;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,eAC5DrC,OAAA,CAAClB,KAAK;cAACyD,IAAI,EAAC,YAAY;cAACC,OAAO,eAAExC,OAAA,CAACJ,aAAa;gBAAAsC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAAE;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,eACvDrC,OAAA,CAAClB,KAAK;cAACyD,IAAI,EAAC,QAAQ;cAACC,OAAO,eAAExC,OAAA,CAACL,cAAc;gBAAAuC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAAE;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,eACpDrC,OAAA,CAAClB,KAAK;cAACyD,IAAI,EAAC,YAAY;cAACC,OAAO,eAAExC,OAAA,CAACN,SAAS;gBAAAwC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAAE;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC7C;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACN;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACA,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACI;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACR,CAAC;AAEf;AAACT,EAAA,CA3BQD,GAAG;AAAAc,EAAA,GAAHd,GAAG;AA6BZ,eAAeA,GAAG;AAAC,IAAAc,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}