{"ast": null, "code": "import ascending from \"./ascending.js\";\nexport default function (series) {\n  return ascending(series).reverse();\n}", "map": {"version": 3, "names": ["ascending", "series", "reverse"], "sources": ["D:/ecommerce/node_modules/d3-shape/src/order/descending.js"], "sourcesContent": ["import ascending from \"./ascending.js\";\n\nexport default function(series) {\n  return ascending(series).reverse();\n}\n"], "mappings": "AAAA,OAAOA,SAAS,MAAM,gBAAgB;AAEtC,eAAe,UAASC,MAAM,EAAE;EAC9B,OAAOD,SAAS,CAACC,MAAM,CAAC,CAACC,OAAO,CAAC,CAAC;AACpC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}