import React from 'react';
import {
  Box,
  Container,
  Typography,
  Card,
  CardContent,
  CardActions,
  Button,
  Grid,
  Avatar,
  Chip,
} from '@mui/material';
import {
  AdminPanelSettings,
  Person,
  Analytics,
  ShoppingCart,
  TrendingUp,
  People,
} from '@mui/icons-material';
import { useNavigate } from 'react-router-dom';

const DashboardSelector = () => {
  const navigate = useNavigate();

  const dashboards = [
    {
      title: 'User Dashboard',
      description: 'Personal shopping experience with order tracking, favorites, and recommendations',
      icon: <Person sx={{ fontSize: 40 }} />,
      color: '#2196f3',
      path: '/dashboard',
      features: [
        'Order History',
        'Favorite Products',
        'Loyalty Points',
        'Recommendations',
        'Profile Management'
      ],
      stats: {
        orders: '24 Orders',
        spent: '$1,247.50',
        points: '2,450 Points'
      }
    },
    {
      title: 'Admin Dashboard',
      description: 'Comprehensive analytics and management tools for business operations',
      icon: <AdminPanelSettings sx={{ fontSize: 40 }} />,
      color: '#f57c00',
      path: '/admin',
      features: [
        'Sales Analytics',
        'User Management',
        'Product Management',
        'Order Processing',
        'Revenue Tracking'
      ],
      stats: {
        users: '914,001 Visits',
        revenue: '$72,000',
        growth: '+46.43%'
      }
    }
  ];

  return (
    <Container maxWidth="lg" sx={{ py: 8 }}>
      <Box sx={{ textAlign: 'center', mb: 6 }}>
        <Typography variant="h3" sx={{ fontWeight: 'bold', mb: 2 }}>
          Choose Your Dashboard
        </Typography>
        <Typography variant="h6" color="textSecondary" sx={{ mb: 4 }}>
          Select the dashboard that matches your role and needs
        </Typography>
      </Box>

      <Grid container spacing={4} justifyContent="center">
        {dashboards.map((dashboard, index) => (
          <Grid item xs={12} md={6} key={index}>
            <Card 
              sx={{ 
                height: '100%',
                display: 'flex',
                flexDirection: 'column',
                transition: 'transform 0.3s, box-shadow 0.3s',
                '&:hover': {
                  transform: 'translateY(-8px)',
                  boxShadow: '0 12px 24px rgba(0,0,0,0.15)',
                },
                border: `2px solid ${dashboard.color}20`,
              }}
            >
              <CardContent sx={{ flexGrow: 1, p: 4 }}>
                <Box sx={{ display: 'flex', alignItems: 'center', mb: 3 }}>
                  <Avatar 
                    sx={{ 
                      backgroundColor: dashboard.color, 
                      width: 60, 
                      height: 60,
                      mr: 2 
                    }}
                  >
                    {dashboard.icon}
                  </Avatar>
                  <Box>
                    <Typography variant="h5" sx={{ fontWeight: 'bold', mb: 1 }}>
                      {dashboard.title}
                    </Typography>
                    <Typography variant="body2" color="textSecondary">
                      {dashboard.description}
                    </Typography>
                  </Box>
                </Box>

                <Box sx={{ mb: 3 }}>
                  <Typography variant="subtitle1" sx={{ fontWeight: 'bold', mb: 2 }}>
                    Key Features:
                  </Typography>
                  <Box sx={{ display: 'flex', flexWrap: 'wrap', gap: 1 }}>
                    {dashboard.features.map((feature, idx) => (
                      <Chip
                        key={idx}
                        label={feature}
                        size="small"
                        sx={{
                          backgroundColor: `${dashboard.color}15`,
                          color: dashboard.color,
                          fontWeight: 'medium',
                        }}
                      />
                    ))}
                  </Box>
                </Box>

                <Box sx={{ mb: 3 }}>
                  <Typography variant="subtitle1" sx={{ fontWeight: 'bold', mb: 2 }}>
                    Quick Stats:
                  </Typography>
                  <Grid container spacing={2}>
                    {Object.entries(dashboard.stats).map(([key, value], idx) => (
                      <Grid item xs={4} key={idx}>
                        <Box sx={{ textAlign: 'center' }}>
                          <Typography variant="h6" color={dashboard.color} sx={{ fontWeight: 'bold' }}>
                            {value.split(' ')[0]}
                          </Typography>
                          <Typography variant="caption" color="textSecondary">
                            {value.split(' ').slice(1).join(' ')}
                          </Typography>
                        </Box>
                      </Grid>
                    ))}
                  </Grid>
                </Box>
              </CardContent>

              <CardActions sx={{ p: 3, pt: 0 }}>
                <Button
                  variant="contained"
                  fullWidth
                  size="large"
                  onClick={() => navigate(dashboard.path)}
                  sx={{
                    backgroundColor: dashboard.color,
                    '&:hover': {
                      backgroundColor: dashboard.color,
                      filter: 'brightness(0.9)',
                    },
                    py: 1.5,
                    fontSize: '1.1rem',
                    fontWeight: 'bold',
                  }}
                >
                  Open {dashboard.title}
                </Button>
              </CardActions>
            </Card>
          </Grid>
        ))}
      </Grid>

      <Box sx={{ textAlign: 'center', mt: 6 }}>
        <Typography variant="body2" color="textSecondary">
          💡 You can switch between dashboards anytime using the navigation menu
        </Typography>
      </Box>
    </Container>
  );
};

export default DashboardSelector;
