{"ast": null, "code": "var arrayPush = require('./_arrayPush'),\n  isFlattenable = require('./_isFlattenable');\n\n/**\n * The base implementation of `_.flatten` with support for restricting flattening.\n *\n * @private\n * @param {Array} array The array to flatten.\n * @param {number} depth The maximum recursion depth.\n * @param {boolean} [predicate=isFlattenable] The function invoked per iteration.\n * @param {boolean} [isStrict] Restrict to values that pass `predicate` checks.\n * @param {Array} [result=[]] The initial result value.\n * @returns {Array} Returns the new flattened array.\n */\nfunction baseFlatten(array, depth, predicate, isStrict, result) {\n  var index = -1,\n    length = array.length;\n  predicate || (predicate = isFlattenable);\n  result || (result = []);\n  while (++index < length) {\n    var value = array[index];\n    if (depth > 0 && predicate(value)) {\n      if (depth > 1) {\n        // Recursively flatten arrays (susceptible to call stack limits).\n        baseFlatten(value, depth - 1, predicate, isStrict, result);\n      } else {\n        arrayPush(result, value);\n      }\n    } else if (!isStrict) {\n      result[result.length] = value;\n    }\n  }\n  return result;\n}\nmodule.exports = baseFlatten;", "map": {"version": 3, "names": ["arrayPush", "require", "isFlattenable", "baseFlatten", "array", "depth", "predicate", "isStrict", "result", "index", "length", "value", "module", "exports"], "sources": ["D:/ecommerce/node_modules/lodash/_baseFlatten.js"], "sourcesContent": ["var arrayPush = require('./_arrayPush'),\n    isFlattenable = require('./_isFlattenable');\n\n/**\n * The base implementation of `_.flatten` with support for restricting flattening.\n *\n * @private\n * @param {Array} array The array to flatten.\n * @param {number} depth The maximum recursion depth.\n * @param {boolean} [predicate=isFlattenable] The function invoked per iteration.\n * @param {boolean} [isStrict] Restrict to values that pass `predicate` checks.\n * @param {Array} [result=[]] The initial result value.\n * @returns {Array} Returns the new flattened array.\n */\nfunction baseFlatten(array, depth, predicate, isStrict, result) {\n  var index = -1,\n      length = array.length;\n\n  predicate || (predicate = isFlattenable);\n  result || (result = []);\n\n  while (++index < length) {\n    var value = array[index];\n    if (depth > 0 && predicate(value)) {\n      if (depth > 1) {\n        // Recursively flatten arrays (susceptible to call stack limits).\n        baseFlatten(value, depth - 1, predicate, isStrict, result);\n      } else {\n        arrayPush(result, value);\n      }\n    } else if (!isStrict) {\n      result[result.length] = value;\n    }\n  }\n  return result;\n}\n\nmodule.exports = baseFlatten;\n"], "mappings": "AAAA,IAAIA,SAAS,GAAGC,OAAO,CAAC,cAAc,CAAC;EACnCC,aAAa,GAAGD,OAAO,CAAC,kBAAkB,CAAC;;AAE/C;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASE,WAAWA,CAACC,KAAK,EAAEC,KAAK,EAAEC,SAAS,EAAEC,QAAQ,EAAEC,MAAM,EAAE;EAC9D,IAAIC,KAAK,GAAG,CAAC,CAAC;IACVC,MAAM,GAAGN,KAAK,CAACM,MAAM;EAEzBJ,SAAS,KAAKA,SAAS,GAAGJ,aAAa,CAAC;EACxCM,MAAM,KAAKA,MAAM,GAAG,EAAE,CAAC;EAEvB,OAAO,EAAEC,KAAK,GAAGC,MAAM,EAAE;IACvB,IAAIC,KAAK,GAAGP,KAAK,CAACK,KAAK,CAAC;IACxB,IAAIJ,KAAK,GAAG,CAAC,IAAIC,SAAS,CAACK,KAAK,CAAC,EAAE;MACjC,IAAIN,KAAK,GAAG,CAAC,EAAE;QACb;QACAF,WAAW,CAACQ,KAAK,EAAEN,KAAK,GAAG,CAAC,EAAEC,SAAS,EAAEC,QAAQ,EAAEC,MAAM,CAAC;MAC5D,CAAC,MAAM;QACLR,SAAS,CAACQ,MAAM,EAAEG,KAAK,CAAC;MAC1B;IACF,CAAC,MAAM,IAAI,CAACJ,QAAQ,EAAE;MACpBC,MAAM,CAACA,MAAM,CAACE,MAAM,CAAC,GAAGC,KAAK;IAC/B;EACF;EACA,OAAOH,MAAM;AACf;AAEAI,MAAM,CAACC,OAAO,GAAGV,WAAW", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}