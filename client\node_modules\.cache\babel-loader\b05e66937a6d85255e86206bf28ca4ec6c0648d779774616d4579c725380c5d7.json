{"ast": null, "code": "var _jsxFileName = \"D:\\\\ecommerce\\\\client\\\\src\\\\components\\\\Home.js\",\n  _s = $RefreshSig$();\nimport React, { useEffect, useState } from 'react';\nimport { useDispatch, useSelector } from 'react-redux';\nimport { Container, Grid, Typography, Card, CardMedia, CardContent, Button, Box, TextField, InputAdornment, Paper, Chip, Rating, IconButton, Badge, Avatar, Divider, CircularProgress, Alert, Tabs, Tab, FormControl, InputLabel, Select, MenuItem, Slider } from '@mui/material';\nimport { Search, ShoppingCart, Favorite, FavoriteBorder, Star, LocalShipping, Security, Assignment, FilterList, Sort, CompareArrows, Share, LocalOffer } from '@mui/icons-material';\nimport { styled } from '@mui/material/styles';\nimport { productsAPI, cartAPI, handleAPIError } from '../services/api';\n\n// Flipkart-style styled components\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst FlipkartHeader = styled(Paper)(({\n  theme\n}) => ({\n  backgroundColor: '#2874f0',\n  color: 'white',\n  padding: theme.spacing(1, 0),\n  boxShadow: '0 2px 4px rgba(0,0,0,0.1)'\n}));\nconst CategoryCard = styled(Card)(({\n  theme\n}) => ({\n  textAlign: 'center',\n  padding: theme.spacing(2),\n  cursor: 'pointer',\n  transition: 'all 0.3s ease',\n  '&:hover': {\n    transform: 'translateY(-4px)',\n    boxShadow: '0 8px 25px rgba(0,0,0,0.15)'\n  }\n}));\nconst ProductCard = styled(Card)(({\n  theme\n}) => ({\n  height: '100%',\n  display: 'flex',\n  flexDirection: 'column',\n  transition: 'all 0.3s ease',\n  cursor: 'pointer',\n  '&:hover': {\n    transform: 'translateY(-4px)',\n    boxShadow: '0 8px 25px rgba(0,0,0,0.15)'\n  }\n}));\n_c = ProductCard;\nconst OfferBanner = styled(Box)(({\n  theme\n}) => ({\n  background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',\n  color: 'white',\n  padding: theme.spacing(3),\n  borderRadius: theme.spacing(1),\n  textAlign: 'center',\n  margin: theme.spacing(2, 0)\n}));\nconst Home = () => {\n  _s();\n  const dispatch = useDispatch();\n  const [products, setProducts] = useState([]);\n  const [loading, setLoading] = useState(true);\n  const [error, setError] = useState(null);\n  const [searchTerm, setSearchTerm] = useState('');\n  const [selectedCategory, setSelectedCategory] = useState('All');\n  const [priceRange, setPriceRange] = useState([0, 100]);\n  const [sortBy, setSortBy] = useState('featured');\n  const [favorites, setFavorites] = useState(new Set());\n  const [cartItems, setCartItems] = useState([]);\n  const [tabValue, setTabValue] = useState(0);\n\n  // Fetch products from API\n  useEffect(() => {\n    const fetchProducts = async () => {\n      try {\n        setLoading(true);\n        const response = await productsAPI.getAll();\n        setProducts(response.data);\n        setError(null);\n      } catch (err) {\n        setError(handleAPIError(err));\n        // Fallback to mock data\n        setProducts(getMockProducts());\n      } finally {\n        setLoading(false);\n      }\n    };\n    fetchProducts();\n\n    // Load cart items\n    const cart = cartAPI.getCart();\n    setCartItems(cart.items);\n  }, []);\n\n  // Mock data fallback\n  const getMockProducts = () => [{\n    _id: '1',\n    name: 'Ceylon Cinnamon Sticks',\n    price: 15.99,\n    image: 'https://images.unsplash.com/photo-1506905925346-21bda4d32df4?w=300&h=300&fit=crop',\n    rating: 4.8,\n    numReviews: 124,\n    category: 'Whole Spices',\n    stock: 50,\n    origin: 'Sri Lanka',\n    weight: 100,\n    unit: 'g',\n    featured: true,\n    description: 'Premium quality Ceylon cinnamon sticks from Sri Lanka'\n  }, {\n    _id: '2',\n    name: 'Black Peppercorns',\n    price: 12.50,\n    image: 'https://images.unsplash.com/photo-1596040033229-a9821ebd058d?w=300&h=300&fit=crop',\n    rating: 4.9,\n    numReviews: 89,\n    category: 'Whole Spices',\n    stock: 30,\n    origin: 'India',\n    weight: 50,\n    unit: 'g',\n    featured: false,\n    description: 'Freshly ground black peppercorns with intense flavor'\n  }, {\n    _id: '3',\n    name: 'Organic Turmeric Powder',\n    price: 18.75,\n    image: 'https://images.unsplash.com/photo-1615485500704-8e990f9900f7?w=300&h=300&fit=crop',\n    rating: 4.7,\n    numReviews: 156,\n    category: 'Ground Spices',\n    stock: 25,\n    origin: 'India',\n    weight: 200,\n    unit: 'g',\n    featured: true,\n    description: 'Pure organic turmeric powder with anti-inflammatory properties'\n  }, {\n    _id: '4',\n    name: 'Green Cardamom Pods',\n    price: 24.99,\n    image: 'https://images.unsplash.com/photo-1596040033229-a9821ebd058d?w=300&h=300&fit=crop',\n    rating: 4.6,\n    numReviews: 78,\n    category: 'Whole Spices',\n    stock: 40,\n    origin: 'Guatemala',\n    weight: 50,\n    unit: 'g',\n    featured: false,\n    description: 'Aromatic green cardamom pods perfect for tea and desserts'\n  }, {\n    _id: '5',\n    name: 'Star Anise',\n    price: 16.99,\n    image: 'https://images.unsplash.com/photo-1506905925346-21bda4d32df4?w=300&h=300&fit=crop',\n    rating: 4.5,\n    numReviews: 45,\n    category: 'Whole Spices',\n    stock: 35,\n    origin: 'China',\n    weight: 75,\n    unit: 'g',\n    featured: false,\n    description: 'Whole star anise with sweet licorice flavor'\n  }, {\n    _id: '6',\n    name: 'Saffron Threads',\n    price: 89.99,\n    image: 'https://images.unsplash.com/photo-1615485500704-8e990f9900f7?w=300&h=300&fit=crop',\n    rating: 4.9,\n    numReviews: 234,\n    category: 'Herbs',\n    stock: 10,\n    origin: 'Kashmir',\n    weight: 1,\n    unit: 'g',\n    featured: true,\n    description: 'Premium saffron threads - the most expensive spice in the world'\n  }];\n  const categories = [{\n    name: 'All',\n    icon: '🛒',\n    count: products.length\n  }, {\n    name: 'Whole Spices',\n    icon: '🌿',\n    count: products.filter(p => p.category === 'Whole Spices').length\n  }, {\n    name: 'Ground Spices',\n    icon: '🥄',\n    count: products.filter(p => p.category === 'Ground Spices').length\n  }, {\n    name: 'Herbs',\n    icon: '🌱',\n    count: products.filter(p => p.category === 'Herbs').length\n  }, {\n    name: 'Spice Blends',\n    icon: '🍛',\n    count: products.filter(p => p.category === 'Spice Blends').length\n  }, {\n    name: 'Seasonings',\n    icon: '🧂',\n    count: products.filter(p => p.category === 'Seasonings').length\n  }];\n  const handleAddToCart = product => {\n    const cart = cartAPI.addToCart(product);\n    setCartItems(cart.items);\n    dispatch({\n      type: 'ADD_TO_CART',\n      payload: product\n    });\n  };\n  const toggleFavorite = productId => {\n    const newFavorites = new Set(favorites);\n    if (newFavorites.has(productId)) {\n      newFavorites.delete(productId);\n    } else {\n      newFavorites.add(productId);\n    }\n    setFavorites(newFavorites);\n  };\n\n  // Filter and sort products\n  const filteredProducts = products.filter(product => {\n    const matchesSearch = product.name.toLowerCase().includes(searchTerm.toLowerCase());\n    const matchesCategory = selectedCategory === 'All' || product.category === selectedCategory;\n    const matchesPrice = product.price >= priceRange[0] && product.price <= priceRange[1];\n    return matchesSearch && matchesCategory && matchesPrice;\n  }).sort((a, b) => {\n    switch (sortBy) {\n      case 'price-low':\n        return a.price - b.price;\n      case 'price-high':\n        return b.price - a.price;\n      case 'rating':\n        return b.rating - a.rating;\n      case 'newest':\n        return new Date(b.createdAt) - new Date(a.createdAt);\n      default:\n        return b.featured - a.featured;\n    }\n  });\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    children: [/*#__PURE__*/_jsxDEV(HeroSection, {\n      children: /*#__PURE__*/_jsxDEV(Container, {\n        children: [/*#__PURE__*/_jsxDEV(Typography, {\n          variant: \"h2\",\n          component: \"h1\",\n          gutterBottom: true,\n          children: \"Discover the World of Spices\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 267,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Typography, {\n          variant: \"h5\",\n          gutterBottom: true,\n          children: \"Premium quality spices from around the globe\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 270,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Button, {\n          variant: \"contained\",\n          color: \"primary\",\n          size: \"large\",\n          sx: {\n            mt: 2\n          },\n          children: \"Shop Now\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 273,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 266,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 265,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Container, {\n      children: [/*#__PURE__*/_jsxDEV(Typography, {\n        variant: \"h4\",\n        component: \"h2\",\n        gutterBottom: true,\n        sx: {\n          mb: 4\n        },\n        children: \"Featured Products\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 280,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Grid, {\n        container: true,\n        spacing: 4,\n        children: products === null || products === void 0 ? void 0 : products.map(product => /*#__PURE__*/_jsxDEV(Grid, {\n          item: true,\n          xs: 12,\n          sm: 6,\n          md: 4,\n          children: /*#__PURE__*/_jsxDEV(ProductCard, {\n            children: [/*#__PURE__*/_jsxDEV(CardMedia, {\n              component: \"img\",\n              height: \"200\",\n              image: product.image,\n              alt: product.name\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 288,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(CardContent, {\n              children: [/*#__PURE__*/_jsxDEV(Typography, {\n                gutterBottom: true,\n                variant: \"h5\",\n                component: \"h3\",\n                children: product.name\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 295,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                variant: \"body2\",\n                color: \"text.secondary\",\n                children: product.description\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 298,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                variant: \"h6\",\n                color: \"primary\",\n                sx: {\n                  mt: 2\n                },\n                children: [\"$\", product.price]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 301,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(Button, {\n                variant: \"contained\",\n                color: \"primary\",\n                fullWidth: true,\n                sx: {\n                  mt: 2\n                },\n                children: \"Add to Cart\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 304,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 294,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 287,\n            columnNumber: 15\n          }, this)\n        }, product._id, false, {\n          fileName: _jsxFileName,\n          lineNumber: 286,\n          columnNumber: 13\n        }, this))\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 284,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 279,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 264,\n    columnNumber: 5\n  }, this);\n};\n_s(Home, \"0vr3AZ/PJfrqmt1VSJ09JyGi4Hc=\", false, function () {\n  return [useDispatch];\n});\n_c2 = Home;\nexport default Home;\nvar _c, _c2;\n$RefreshReg$(_c, \"ProductCard\");\n$RefreshReg$(_c2, \"Home\");", "map": {"version": 3, "names": ["React", "useEffect", "useState", "useDispatch", "useSelector", "Container", "Grid", "Typography", "Card", "CardMedia", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "Box", "TextField", "InputAdornment", "Paper", "Chip", "Rating", "IconButton", "Badge", "Avatar", "Divider", "CircularProgress", "<PERSON><PERSON>", "Tabs", "Tab", "FormControl", "InputLabel", "Select", "MenuItem", "Slide<PERSON>", "Search", "ShoppingCart", "Favorite", "FavoriteBorder", "Star", "LocalShipping", "Security", "Assignment", "FilterList", "Sort", "CompareArrows", "Share", "LocalOffer", "styled", "productsAPI", "cartAPI", "handleAPIError", "jsxDEV", "_jsxDEV", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "theme", "backgroundColor", "color", "padding", "spacing", "boxShadow", "CategoryCard", "textAlign", "cursor", "transition", "transform", "ProductCard", "height", "display", "flexDirection", "_c", "OfferBanner", "background", "borderRadius", "margin", "Home", "_s", "dispatch", "products", "setProducts", "loading", "setLoading", "error", "setError", "searchTerm", "setSearchTerm", "selectedCate<PERSON><PERSON>", "setSelectedCategory", "priceRange", "setPriceRange", "sortBy", "setSortBy", "favorites", "setFavorites", "Set", "cartItems", "setCartItems", "tabValue", "setTabValue", "fetchProducts", "response", "getAll", "data", "err", "getMockProducts", "cart", "getCart", "items", "_id", "name", "price", "image", "rating", "numReviews", "category", "stock", "origin", "weight", "unit", "featured", "description", "categories", "icon", "count", "length", "filter", "p", "handleAddToCart", "product", "addToCart", "type", "payload", "toggleFavorite", "productId", "newFavorites", "has", "delete", "add", "filteredProducts", "matchesSearch", "toLowerCase", "includes", "matchesCategory", "matchesPrice", "sort", "a", "b", "Date", "createdAt", "children", "HeroSection", "variant", "component", "gutterBottom", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "size", "sx", "mt", "mb", "container", "map", "item", "xs", "sm", "md", "alt", "fullWidth", "_c2", "$RefreshReg$"], "sources": ["D:/ecommerce/client/src/components/Home.js"], "sourcesContent": ["import React, { useEffect, useState } from 'react';\r\nimport { useDispatch, useSelector } from 'react-redux';\r\nimport {\r\n  Container,\r\n  Grid,\r\n  Typography,\r\n  Card,\r\n  CardMedia,\r\n  CardContent,\r\n  Button,\r\n  Box,\r\n  TextField,\r\n  InputAdornment,\r\n  Paper,\r\n  Chip,\r\n  Rating,\r\n  IconButton,\r\n  Badge,\r\n  Avatar,\r\n  Divider,\r\n  CircularProgress,\r\n  Alert,\r\n  Tabs,\r\n  Tab,\r\n  FormControl,\r\n  InputLabel,\r\n  Select,\r\n  MenuItem,\r\n  Slider\r\n} from '@mui/material';\r\nimport {\r\n  Search,\r\n  ShoppingCart,\r\n  Favorite,\r\n  FavoriteBorder,\r\n  Star,\r\n  LocalShipping,\r\n  Security,\r\n  Assignment,\r\n  FilterList,\r\n  Sort,\r\n  CompareArrows,\r\n  Share,\r\n  LocalOffer\r\n} from '@mui/icons-material';\r\nimport { styled } from '@mui/material/styles';\r\nimport { productsAPI, cartAPI, handleAPIError } from '../services/api';\r\n\r\n// Flipkart-style styled components\r\nconst FlipkartHeader = styled(Paper)(({ theme }) => ({\r\n  backgroundColor: '#2874f0',\r\n  color: 'white',\r\n  padding: theme.spacing(1, 0),\r\n  boxShadow: '0 2px 4px rgba(0,0,0,0.1)',\r\n}));\r\n\r\nconst CategoryCard = styled(Card)(({ theme }) => ({\r\n  textAlign: 'center',\r\n  padding: theme.spacing(2),\r\n  cursor: 'pointer',\r\n  transition: 'all 0.3s ease',\r\n  '&:hover': {\r\n    transform: 'translateY(-4px)',\r\n    boxShadow: '0 8px 25px rgba(0,0,0,0.15)',\r\n  },\r\n}));\r\n\r\nconst ProductCard = styled(Card)(({ theme }) => ({\r\n  height: '100%',\r\n  display: 'flex',\r\n  flexDirection: 'column',\r\n  transition: 'all 0.3s ease',\r\n  cursor: 'pointer',\r\n  '&:hover': {\r\n    transform: 'translateY(-4px)',\r\n    boxShadow: '0 8px 25px rgba(0,0,0,0.15)',\r\n  },\r\n}));\r\n\r\nconst OfferBanner = styled(Box)(({ theme }) => ({\r\n  background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',\r\n  color: 'white',\r\n  padding: theme.spacing(3),\r\n  borderRadius: theme.spacing(1),\r\n  textAlign: 'center',\r\n  margin: theme.spacing(2, 0),\r\n}));\r\n\r\nconst Home = () => {\r\n  const dispatch = useDispatch();\r\n  const [products, setProducts] = useState([]);\r\n  const [loading, setLoading] = useState(true);\r\n  const [error, setError] = useState(null);\r\n  const [searchTerm, setSearchTerm] = useState('');\r\n  const [selectedCategory, setSelectedCategory] = useState('All');\r\n  const [priceRange, setPriceRange] = useState([0, 100]);\r\n  const [sortBy, setSortBy] = useState('featured');\r\n  const [favorites, setFavorites] = useState(new Set());\r\n  const [cartItems, setCartItems] = useState([]);\r\n  const [tabValue, setTabValue] = useState(0);\r\n\r\n  // Fetch products from API\r\n  useEffect(() => {\r\n    const fetchProducts = async () => {\r\n      try {\r\n        setLoading(true);\r\n        const response = await productsAPI.getAll();\r\n        setProducts(response.data);\r\n        setError(null);\r\n      } catch (err) {\r\n        setError(handleAPIError(err));\r\n        // Fallback to mock data\r\n        setProducts(getMockProducts());\r\n      } finally {\r\n        setLoading(false);\r\n      }\r\n    };\r\n\r\n    fetchProducts();\r\n\r\n    // Load cart items\r\n    const cart = cartAPI.getCart();\r\n    setCartItems(cart.items);\r\n  }, []);\r\n\r\n  // Mock data fallback\r\n  const getMockProducts = () => [\r\n    {\r\n      _id: '1',\r\n      name: 'Ceylon Cinnamon Sticks',\r\n      price: 15.99,\r\n      image: 'https://images.unsplash.com/photo-1506905925346-21bda4d32df4?w=300&h=300&fit=crop',\r\n      rating: 4.8,\r\n      numReviews: 124,\r\n      category: 'Whole Spices',\r\n      stock: 50,\r\n      origin: 'Sri Lanka',\r\n      weight: 100,\r\n      unit: 'g',\r\n      featured: true,\r\n      description: 'Premium quality Ceylon cinnamon sticks from Sri Lanka'\r\n    },\r\n    {\r\n      _id: '2',\r\n      name: 'Black Peppercorns',\r\n      price: 12.50,\r\n      image: 'https://images.unsplash.com/photo-1596040033229-a9821ebd058d?w=300&h=300&fit=crop',\r\n      rating: 4.9,\r\n      numReviews: 89,\r\n      category: 'Whole Spices',\r\n      stock: 30,\r\n      origin: 'India',\r\n      weight: 50,\r\n      unit: 'g',\r\n      featured: false,\r\n      description: 'Freshly ground black peppercorns with intense flavor'\r\n    },\r\n    {\r\n      _id: '3',\r\n      name: 'Organic Turmeric Powder',\r\n      price: 18.75,\r\n      image: 'https://images.unsplash.com/photo-1615485500704-8e990f9900f7?w=300&h=300&fit=crop',\r\n      rating: 4.7,\r\n      numReviews: 156,\r\n      category: 'Ground Spices',\r\n      stock: 25,\r\n      origin: 'India',\r\n      weight: 200,\r\n      unit: 'g',\r\n      featured: true,\r\n      description: 'Pure organic turmeric powder with anti-inflammatory properties'\r\n    },\r\n    {\r\n      _id: '4',\r\n      name: 'Green Cardamom Pods',\r\n      price: 24.99,\r\n      image: 'https://images.unsplash.com/photo-1596040033229-a9821ebd058d?w=300&h=300&fit=crop',\r\n      rating: 4.6,\r\n      numReviews: 78,\r\n      category: 'Whole Spices',\r\n      stock: 40,\r\n      origin: 'Guatemala',\r\n      weight: 50,\r\n      unit: 'g',\r\n      featured: false,\r\n      description: 'Aromatic green cardamom pods perfect for tea and desserts'\r\n    },\r\n    {\r\n      _id: '5',\r\n      name: 'Star Anise',\r\n      price: 16.99,\r\n      image: 'https://images.unsplash.com/photo-1506905925346-21bda4d32df4?w=300&h=300&fit=crop',\r\n      rating: 4.5,\r\n      numReviews: 45,\r\n      category: 'Whole Spices',\r\n      stock: 35,\r\n      origin: 'China',\r\n      weight: 75,\r\n      unit: 'g',\r\n      featured: false,\r\n      description: 'Whole star anise with sweet licorice flavor'\r\n    },\r\n    {\r\n      _id: '6',\r\n      name: 'Saffron Threads',\r\n      price: 89.99,\r\n      image: 'https://images.unsplash.com/photo-1615485500704-8e990f9900f7?w=300&h=300&fit=crop',\r\n      rating: 4.9,\r\n      numReviews: 234,\r\n      category: 'Herbs',\r\n      stock: 10,\r\n      origin: 'Kashmir',\r\n      weight: 1,\r\n      unit: 'g',\r\n      featured: true,\r\n      description: 'Premium saffron threads - the most expensive spice in the world'\r\n    }\r\n  ];\r\n\r\n  const categories = [\r\n    { name: 'All', icon: '🛒', count: products.length },\r\n    { name: 'Whole Spices', icon: '🌿', count: products.filter(p => p.category === 'Whole Spices').length },\r\n    { name: 'Ground Spices', icon: '🥄', count: products.filter(p => p.category === 'Ground Spices').length },\r\n    { name: 'Herbs', icon: '🌱', count: products.filter(p => p.category === 'Herbs').length },\r\n    { name: 'Spice Blends', icon: '🍛', count: products.filter(p => p.category === 'Spice Blends').length },\r\n    { name: 'Seasonings', icon: '🧂', count: products.filter(p => p.category === 'Seasonings').length }\r\n  ];\r\n\r\n  const handleAddToCart = (product) => {\r\n    const cart = cartAPI.addToCart(product);\r\n    setCartItems(cart.items);\r\n    dispatch({ type: 'ADD_TO_CART', payload: product });\r\n  };\r\n\r\n  const toggleFavorite = (productId) => {\r\n    const newFavorites = new Set(favorites);\r\n    if (newFavorites.has(productId)) {\r\n      newFavorites.delete(productId);\r\n    } else {\r\n      newFavorites.add(productId);\r\n    }\r\n    setFavorites(newFavorites);\r\n  };\r\n\r\n  // Filter and sort products\r\n  const filteredProducts = products\r\n    .filter(product => {\r\n      const matchesSearch = product.name.toLowerCase().includes(searchTerm.toLowerCase());\r\n      const matchesCategory = selectedCategory === 'All' || product.category === selectedCategory;\r\n      const matchesPrice = product.price >= priceRange[0] && product.price <= priceRange[1];\r\n      return matchesSearch && matchesCategory && matchesPrice;\r\n    })\r\n    .sort((a, b) => {\r\n      switch (sortBy) {\r\n        case 'price-low': return a.price - b.price;\r\n        case 'price-high': return b.price - a.price;\r\n        case 'rating': return b.rating - a.rating;\r\n        case 'newest': return new Date(b.createdAt) - new Date(a.createdAt);\r\n        default: return b.featured - a.featured;\r\n      }\r\n    });\r\n\r\n  return (\r\n    <div>\r\n      <HeroSection>\r\n        <Container>\r\n          <Typography variant=\"h2\" component=\"h1\" gutterBottom>\r\n            Discover the World of Spices\r\n          </Typography>\r\n          <Typography variant=\"h5\" gutterBottom>\r\n            Premium quality spices from around the globe\r\n          </Typography>\r\n          <Button variant=\"contained\" color=\"primary\" size=\"large\" sx={{ mt: 2 }}>\r\n            Shop Now\r\n          </Button>\r\n        </Container>\r\n      </HeroSection>\r\n\r\n      <Container>\r\n        <Typography variant=\"h4\" component=\"h2\" gutterBottom sx={{ mb: 4 }}>\r\n          Featured Products\r\n        </Typography>\r\n\r\n        <Grid container spacing={4}>\r\n          {products?.map((product) => (\r\n            <Grid item key={product._id} xs={12} sm={6} md={4}>\r\n              <ProductCard>\r\n                <CardMedia\r\n                  component=\"img\"\r\n                  height=\"200\"\r\n                  image={product.image}\r\n                  alt={product.name}\r\n                />\r\n                <CardContent>\r\n                  <Typography gutterBottom variant=\"h5\" component=\"h3\">\r\n                    {product.name}\r\n                  </Typography>\r\n                  <Typography variant=\"body2\" color=\"text.secondary\">\r\n                    {product.description}\r\n                  </Typography>\r\n                  <Typography variant=\"h6\" color=\"primary\" sx={{ mt: 2 }}>\r\n                    ${product.price}\r\n                  </Typography>\r\n                  <Button\r\n                    variant=\"contained\"\r\n                    color=\"primary\"\r\n                    fullWidth\r\n                    sx={{ mt: 2 }}\r\n                  >\r\n                    Add to Cart\r\n                  </Button>\r\n                </CardContent>\r\n              </ProductCard>\r\n            </Grid>\r\n          ))}\r\n        </Grid>\r\n      </Container>\r\n    </div>\r\n  );\r\n};\r\n\r\nexport default Home;"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,SAAS,EAAEC,QAAQ,QAAQ,OAAO;AAClD,SAASC,WAAW,EAAEC,WAAW,QAAQ,aAAa;AACtD,SACEC,SAAS,EACTC,IAAI,EACJC,UAAU,EACVC,IAAI,EACJC,SAAS,EACTC,WAAW,EACXC,MAAM,EACNC,GAAG,EACHC,SAAS,EACTC,cAAc,EACdC,KAAK,EACLC,IAAI,EACJC,MAAM,EACNC,UAAU,EACVC,KAAK,EACLC,MAAM,EACNC,OAAO,EACPC,gBAAgB,EAChBC,KAAK,EACLC,IAAI,EACJC,GAAG,EACHC,WAAW,EACXC,UAAU,EACVC,MAAM,EACNC,QAAQ,EACRC,MAAM,QACD,eAAe;AACtB,SACEC,MAAM,EACNC,YAAY,EACZC,QAAQ,EACRC,cAAc,EACdC,IAAI,EACJC,aAAa,EACbC,QAAQ,EACRC,UAAU,EACVC,UAAU,EACVC,IAAI,EACJC,aAAa,EACbC,KAAK,EACLC,UAAU,QACL,qBAAqB;AAC5B,SAASC,MAAM,QAAQ,sBAAsB;AAC7C,SAASC,WAAW,EAAEC,OAAO,EAAEC,cAAc,QAAQ,iBAAiB;;AAEtE;AAAA,SAAAC,MAAA,IAAAC,OAAA;AACA,MAAMC,cAAc,GAAGN,MAAM,CAAC7B,KAAK,CAAC,CAAC,CAAC;EAAEoC;AAAM,CAAC,MAAM;EACnDC,eAAe,EAAE,SAAS;EAC1BC,KAAK,EAAE,OAAO;EACdC,OAAO,EAAEH,KAAK,CAACI,OAAO,CAAC,CAAC,EAAE,CAAC,CAAC;EAC5BC,SAAS,EAAE;AACb,CAAC,CAAC,CAAC;AAEH,MAAMC,YAAY,GAAGb,MAAM,CAACpC,IAAI,CAAC,CAAC,CAAC;EAAE2C;AAAM,CAAC,MAAM;EAChDO,SAAS,EAAE,QAAQ;EACnBJ,OAAO,EAAEH,KAAK,CAACI,OAAO,CAAC,CAAC,CAAC;EACzBI,MAAM,EAAE,SAAS;EACjBC,UAAU,EAAE,eAAe;EAC3B,SAAS,EAAE;IACTC,SAAS,EAAE,kBAAkB;IAC7BL,SAAS,EAAE;EACb;AACF,CAAC,CAAC,CAAC;AAEH,MAAMM,WAAW,GAAGlB,MAAM,CAACpC,IAAI,CAAC,CAAC,CAAC;EAAE2C;AAAM,CAAC,MAAM;EAC/CY,MAAM,EAAE,MAAM;EACdC,OAAO,EAAE,MAAM;EACfC,aAAa,EAAE,QAAQ;EACvBL,UAAU,EAAE,eAAe;EAC3BD,MAAM,EAAE,SAAS;EACjB,SAAS,EAAE;IACTE,SAAS,EAAE,kBAAkB;IAC7BL,SAAS,EAAE;EACb;AACF,CAAC,CAAC,CAAC;AAACU,EAAA,GAVEJ,WAAW;AAYjB,MAAMK,WAAW,GAAGvB,MAAM,CAAChC,GAAG,CAAC,CAAC,CAAC;EAAEuC;AAAM,CAAC,MAAM;EAC9CiB,UAAU,EAAE,mDAAmD;EAC/Df,KAAK,EAAE,OAAO;EACdC,OAAO,EAAEH,KAAK,CAACI,OAAO,CAAC,CAAC,CAAC;EACzBc,YAAY,EAAElB,KAAK,CAACI,OAAO,CAAC,CAAC,CAAC;EAC9BG,SAAS,EAAE,QAAQ;EACnBY,MAAM,EAAEnB,KAAK,CAACI,OAAO,CAAC,CAAC,EAAE,CAAC;AAC5B,CAAC,CAAC,CAAC;AAEH,MAAMgB,IAAI,GAAGA,CAAA,KAAM;EAAAC,EAAA;EACjB,MAAMC,QAAQ,GAAGtE,WAAW,CAAC,CAAC;EAC9B,MAAM,CAACuE,QAAQ,EAAEC,WAAW,CAAC,GAAGzE,QAAQ,CAAC,EAAE,CAAC;EAC5C,MAAM,CAAC0E,OAAO,EAAEC,UAAU,CAAC,GAAG3E,QAAQ,CAAC,IAAI,CAAC;EAC5C,MAAM,CAAC4E,KAAK,EAAEC,QAAQ,CAAC,GAAG7E,QAAQ,CAAC,IAAI,CAAC;EACxC,MAAM,CAAC8E,UAAU,EAAEC,aAAa,CAAC,GAAG/E,QAAQ,CAAC,EAAE,CAAC;EAChD,MAAM,CAACgF,gBAAgB,EAAEC,mBAAmB,CAAC,GAAGjF,QAAQ,CAAC,KAAK,CAAC;EAC/D,MAAM,CAACkF,UAAU,EAAEC,aAAa,CAAC,GAAGnF,QAAQ,CAAC,CAAC,CAAC,EAAE,GAAG,CAAC,CAAC;EACtD,MAAM,CAACoF,MAAM,EAAEC,SAAS,CAAC,GAAGrF,QAAQ,CAAC,UAAU,CAAC;EAChD,MAAM,CAACsF,SAAS,EAAEC,YAAY,CAAC,GAAGvF,QAAQ,CAAC,IAAIwF,GAAG,CAAC,CAAC,CAAC;EACrD,MAAM,CAACC,SAAS,EAAEC,YAAY,CAAC,GAAG1F,QAAQ,CAAC,EAAE,CAAC;EAC9C,MAAM,CAAC2F,QAAQ,EAAEC,WAAW,CAAC,GAAG5F,QAAQ,CAAC,CAAC,CAAC;;EAE3C;EACAD,SAAS,CAAC,MAAM;IACd,MAAM8F,aAAa,GAAG,MAAAA,CAAA,KAAY;MAChC,IAAI;QACFlB,UAAU,CAAC,IAAI,CAAC;QAChB,MAAMmB,QAAQ,GAAG,MAAMnD,WAAW,CAACoD,MAAM,CAAC,CAAC;QAC3CtB,WAAW,CAACqB,QAAQ,CAACE,IAAI,CAAC;QAC1BnB,QAAQ,CAAC,IAAI,CAAC;MAChB,CAAC,CAAC,OAAOoB,GAAG,EAAE;QACZpB,QAAQ,CAAChC,cAAc,CAACoD,GAAG,CAAC,CAAC;QAC7B;QACAxB,WAAW,CAACyB,eAAe,CAAC,CAAC,CAAC;MAChC,CAAC,SAAS;QACRvB,UAAU,CAAC,KAAK,CAAC;MACnB;IACF,CAAC;IAEDkB,aAAa,CAAC,CAAC;;IAEf;IACA,MAAMM,IAAI,GAAGvD,OAAO,CAACwD,OAAO,CAAC,CAAC;IAC9BV,YAAY,CAACS,IAAI,CAACE,KAAK,CAAC;EAC1B,CAAC,EAAE,EAAE,CAAC;;EAEN;EACA,MAAMH,eAAe,GAAGA,CAAA,KAAM,CAC5B;IACEI,GAAG,EAAE,GAAG;IACRC,IAAI,EAAE,wBAAwB;IAC9BC,KAAK,EAAE,KAAK;IACZC,KAAK,EAAE,mFAAmF;IAC1FC,MAAM,EAAE,GAAG;IACXC,UAAU,EAAE,GAAG;IACfC,QAAQ,EAAE,cAAc;IACxBC,KAAK,EAAE,EAAE;IACTC,MAAM,EAAE,WAAW;IACnBC,MAAM,EAAE,GAAG;IACXC,IAAI,EAAE,GAAG;IACTC,QAAQ,EAAE,IAAI;IACdC,WAAW,EAAE;EACf,CAAC,EACD;IACEZ,GAAG,EAAE,GAAG;IACRC,IAAI,EAAE,mBAAmB;IACzBC,KAAK,EAAE,KAAK;IACZC,KAAK,EAAE,mFAAmF;IAC1FC,MAAM,EAAE,GAAG;IACXC,UAAU,EAAE,EAAE;IACdC,QAAQ,EAAE,cAAc;IACxBC,KAAK,EAAE,EAAE;IACTC,MAAM,EAAE,OAAO;IACfC,MAAM,EAAE,EAAE;IACVC,IAAI,EAAE,GAAG;IACTC,QAAQ,EAAE,KAAK;IACfC,WAAW,EAAE;EACf,CAAC,EACD;IACEZ,GAAG,EAAE,GAAG;IACRC,IAAI,EAAE,yBAAyB;IAC/BC,KAAK,EAAE,KAAK;IACZC,KAAK,EAAE,mFAAmF;IAC1FC,MAAM,EAAE,GAAG;IACXC,UAAU,EAAE,GAAG;IACfC,QAAQ,EAAE,eAAe;IACzBC,KAAK,EAAE,EAAE;IACTC,MAAM,EAAE,OAAO;IACfC,MAAM,EAAE,GAAG;IACXC,IAAI,EAAE,GAAG;IACTC,QAAQ,EAAE,IAAI;IACdC,WAAW,EAAE;EACf,CAAC,EACD;IACEZ,GAAG,EAAE,GAAG;IACRC,IAAI,EAAE,qBAAqB;IAC3BC,KAAK,EAAE,KAAK;IACZC,KAAK,EAAE,mFAAmF;IAC1FC,MAAM,EAAE,GAAG;IACXC,UAAU,EAAE,EAAE;IACdC,QAAQ,EAAE,cAAc;IACxBC,KAAK,EAAE,EAAE;IACTC,MAAM,EAAE,WAAW;IACnBC,MAAM,EAAE,EAAE;IACVC,IAAI,EAAE,GAAG;IACTC,QAAQ,EAAE,KAAK;IACfC,WAAW,EAAE;EACf,CAAC,EACD;IACEZ,GAAG,EAAE,GAAG;IACRC,IAAI,EAAE,YAAY;IAClBC,KAAK,EAAE,KAAK;IACZC,KAAK,EAAE,mFAAmF;IAC1FC,MAAM,EAAE,GAAG;IACXC,UAAU,EAAE,EAAE;IACdC,QAAQ,EAAE,cAAc;IACxBC,KAAK,EAAE,EAAE;IACTC,MAAM,EAAE,OAAO;IACfC,MAAM,EAAE,EAAE;IACVC,IAAI,EAAE,GAAG;IACTC,QAAQ,EAAE,KAAK;IACfC,WAAW,EAAE;EACf,CAAC,EACD;IACEZ,GAAG,EAAE,GAAG;IACRC,IAAI,EAAE,iBAAiB;IACvBC,KAAK,EAAE,KAAK;IACZC,KAAK,EAAE,mFAAmF;IAC1FC,MAAM,EAAE,GAAG;IACXC,UAAU,EAAE,GAAG;IACfC,QAAQ,EAAE,OAAO;IACjBC,KAAK,EAAE,EAAE;IACTC,MAAM,EAAE,SAAS;IACjBC,MAAM,EAAE,CAAC;IACTC,IAAI,EAAE,GAAG;IACTC,QAAQ,EAAE,IAAI;IACdC,WAAW,EAAE;EACf,CAAC,CACF;EAED,MAAMC,UAAU,GAAG,CACjB;IAAEZ,IAAI,EAAE,KAAK;IAAEa,IAAI,EAAE,IAAI;IAAEC,KAAK,EAAE7C,QAAQ,CAAC8C;EAAO,CAAC,EACnD;IAAEf,IAAI,EAAE,cAAc;IAAEa,IAAI,EAAE,IAAI;IAAEC,KAAK,EAAE7C,QAAQ,CAAC+C,MAAM,CAACC,CAAC,IAAIA,CAAC,CAACZ,QAAQ,KAAK,cAAc,CAAC,CAACU;EAAO,CAAC,EACvG;IAAEf,IAAI,EAAE,eAAe;IAAEa,IAAI,EAAE,IAAI;IAAEC,KAAK,EAAE7C,QAAQ,CAAC+C,MAAM,CAACC,CAAC,IAAIA,CAAC,CAACZ,QAAQ,KAAK,eAAe,CAAC,CAACU;EAAO,CAAC,EACzG;IAAEf,IAAI,EAAE,OAAO;IAAEa,IAAI,EAAE,IAAI;IAAEC,KAAK,EAAE7C,QAAQ,CAAC+C,MAAM,CAACC,CAAC,IAAIA,CAAC,CAACZ,QAAQ,KAAK,OAAO,CAAC,CAACU;EAAO,CAAC,EACzF;IAAEf,IAAI,EAAE,cAAc;IAAEa,IAAI,EAAE,IAAI;IAAEC,KAAK,EAAE7C,QAAQ,CAAC+C,MAAM,CAACC,CAAC,IAAIA,CAAC,CAACZ,QAAQ,KAAK,cAAc,CAAC,CAACU;EAAO,CAAC,EACvG;IAAEf,IAAI,EAAE,YAAY;IAAEa,IAAI,EAAE,IAAI;IAAEC,KAAK,EAAE7C,QAAQ,CAAC+C,MAAM,CAACC,CAAC,IAAIA,CAAC,CAACZ,QAAQ,KAAK,YAAY,CAAC,CAACU;EAAO,CAAC,CACpG;EAED,MAAMG,eAAe,GAAIC,OAAO,IAAK;IACnC,MAAMvB,IAAI,GAAGvD,OAAO,CAAC+E,SAAS,CAACD,OAAO,CAAC;IACvChC,YAAY,CAACS,IAAI,CAACE,KAAK,CAAC;IACxB9B,QAAQ,CAAC;MAAEqD,IAAI,EAAE,aAAa;MAAEC,OAAO,EAAEH;IAAQ,CAAC,CAAC;EACrD,CAAC;EAED,MAAMI,cAAc,GAAIC,SAAS,IAAK;IACpC,MAAMC,YAAY,GAAG,IAAIxC,GAAG,CAACF,SAAS,CAAC;IACvC,IAAI0C,YAAY,CAACC,GAAG,CAACF,SAAS,CAAC,EAAE;MAC/BC,YAAY,CAACE,MAAM,CAACH,SAAS,CAAC;IAChC,CAAC,MAAM;MACLC,YAAY,CAACG,GAAG,CAACJ,SAAS,CAAC;IAC7B;IACAxC,YAAY,CAACyC,YAAY,CAAC;EAC5B,CAAC;;EAED;EACA,MAAMI,gBAAgB,GAAG5D,QAAQ,CAC9B+C,MAAM,CAACG,OAAO,IAAI;IACjB,MAAMW,aAAa,GAAGX,OAAO,CAACnB,IAAI,CAAC+B,WAAW,CAAC,CAAC,CAACC,QAAQ,CAACzD,UAAU,CAACwD,WAAW,CAAC,CAAC,CAAC;IACnF,MAAME,eAAe,GAAGxD,gBAAgB,KAAK,KAAK,IAAI0C,OAAO,CAACd,QAAQ,KAAK5B,gBAAgB;IAC3F,MAAMyD,YAAY,GAAGf,OAAO,CAAClB,KAAK,IAAItB,UAAU,CAAC,CAAC,CAAC,IAAIwC,OAAO,CAAClB,KAAK,IAAItB,UAAU,CAAC,CAAC,CAAC;IACrF,OAAOmD,aAAa,IAAIG,eAAe,IAAIC,YAAY;EACzD,CAAC,CAAC,CACDC,IAAI,CAAC,CAACC,CAAC,EAAEC,CAAC,KAAK;IACd,QAAQxD,MAAM;MACZ,KAAK,WAAW;QAAE,OAAOuD,CAAC,CAACnC,KAAK,GAAGoC,CAAC,CAACpC,KAAK;MAC1C,KAAK,YAAY;QAAE,OAAOoC,CAAC,CAACpC,KAAK,GAAGmC,CAAC,CAACnC,KAAK;MAC3C,KAAK,QAAQ;QAAE,OAAOoC,CAAC,CAAClC,MAAM,GAAGiC,CAAC,CAACjC,MAAM;MACzC,KAAK,QAAQ;QAAE,OAAO,IAAImC,IAAI,CAACD,CAAC,CAACE,SAAS,CAAC,GAAG,IAAID,IAAI,CAACF,CAAC,CAACG,SAAS,CAAC;MACnE;QAAS,OAAOF,CAAC,CAAC3B,QAAQ,GAAG0B,CAAC,CAAC1B,QAAQ;IACzC;EACF,CAAC,CAAC;EAEJ,oBACElE,OAAA;IAAAgG,QAAA,gBACEhG,OAAA,CAACiG,WAAW;MAAAD,QAAA,eACVhG,OAAA,CAAC5C,SAAS;QAAA4I,QAAA,gBACRhG,OAAA,CAAC1C,UAAU;UAAC4I,OAAO,EAAC,IAAI;UAACC,SAAS,EAAC,IAAI;UAACC,YAAY;UAAAJ,QAAA,EAAC;QAErD;UAAAK,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAY,CAAC,eACbxG,OAAA,CAAC1C,UAAU;UAAC4I,OAAO,EAAC,IAAI;UAACE,YAAY;UAAAJ,QAAA,EAAC;QAEtC;UAAAK,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAY,CAAC,eACbxG,OAAA,CAACtC,MAAM;UAACwI,OAAO,EAAC,WAAW;UAAC9F,KAAK,EAAC,SAAS;UAACqG,IAAI,EAAC,OAAO;UAACC,EAAE,EAAE;YAAEC,EAAE,EAAE;UAAE,CAAE;UAAAX,QAAA,EAAC;QAExE;UAAAK,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACA;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACD,CAAC,eAEdxG,OAAA,CAAC5C,SAAS;MAAA4I,QAAA,gBACRhG,OAAA,CAAC1C,UAAU;QAAC4I,OAAO,EAAC,IAAI;QAACC,SAAS,EAAC,IAAI;QAACC,YAAY;QAACM,EAAE,EAAE;UAAEE,EAAE,EAAE;QAAE,CAAE;QAAAZ,QAAA,EAAC;MAEpE;QAAAK,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAY,CAAC,eAEbxG,OAAA,CAAC3C,IAAI;QAACwJ,SAAS;QAACvG,OAAO,EAAE,CAAE;QAAA0F,QAAA,EACxBvE,QAAQ,aAARA,QAAQ,uBAARA,QAAQ,CAAEqF,GAAG,CAAEnC,OAAO,iBACrB3E,OAAA,CAAC3C,IAAI;UAAC0J,IAAI;UAAmBC,EAAE,EAAE,EAAG;UAACC,EAAE,EAAE,CAAE;UAACC,EAAE,EAAE,CAAE;UAAAlB,QAAA,eAChDhG,OAAA,CAACa,WAAW;YAAAmF,QAAA,gBACVhG,OAAA,CAACxC,SAAS;cACR2I,SAAS,EAAC,KAAK;cACfrF,MAAM,EAAC,KAAK;cACZ4C,KAAK,EAAEiB,OAAO,CAACjB,KAAM;cACrByD,GAAG,EAAExC,OAAO,CAACnB;YAAK;cAAA6C,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACnB,CAAC,eACFxG,OAAA,CAACvC,WAAW;cAAAuI,QAAA,gBACVhG,OAAA,CAAC1C,UAAU;gBAAC8I,YAAY;gBAACF,OAAO,EAAC,IAAI;gBAACC,SAAS,EAAC,IAAI;gBAAAH,QAAA,EACjDrB,OAAO,CAACnB;cAAI;gBAAA6C,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC,eACbxG,OAAA,CAAC1C,UAAU;gBAAC4I,OAAO,EAAC,OAAO;gBAAC9F,KAAK,EAAC,gBAAgB;gBAAA4F,QAAA,EAC/CrB,OAAO,CAACR;cAAW;gBAAAkC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACV,CAAC,eACbxG,OAAA,CAAC1C,UAAU;gBAAC4I,OAAO,EAAC,IAAI;gBAAC9F,KAAK,EAAC,SAAS;gBAACsG,EAAE,EAAE;kBAAEC,EAAE,EAAE;gBAAE,CAAE;gBAAAX,QAAA,GAAC,GACrD,EAACrB,OAAO,CAAClB,KAAK;cAAA;gBAAA4C,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACL,CAAC,eACbxG,OAAA,CAACtC,MAAM;gBACLwI,OAAO,EAAC,WAAW;gBACnB9F,KAAK,EAAC,SAAS;gBACfgH,SAAS;gBACTV,EAAE,EAAE;kBAAEC,EAAE,EAAE;gBAAE,CAAE;gBAAAX,QAAA,EACf;cAED;gBAAAK,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACE,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH;QAAC,GA3BA7B,OAAO,CAACpB,GAAG;UAAA8C,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OA4BrB,CACP;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACE,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACE,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACT,CAAC;AAEV,CAAC;AAACjF,EAAA,CAvOID,IAAI;EAAA,QACSpE,WAAW;AAAA;AAAAmK,GAAA,GADxB/F,IAAI;AAyOV,eAAeA,IAAI;AAAC,IAAAL,EAAA,EAAAoG,GAAA;AAAAC,YAAA,CAAArG,EAAA;AAAAqG,YAAA,CAAAD,GAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}