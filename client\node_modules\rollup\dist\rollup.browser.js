/*
  @license
	Rollup.js v2.79.2
	Thu, 26 Sep 2024 18:44:14 GMT - commit 48aef33cf2f2a6dfb175afb3bcd6a977c81f1d5c

	https://github.com/rollup/rollup

	Released under the MIT License.
*/
var e,t;e=this,t=function(e){for(var t="2.79.2",i={},s="ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/=",n=0;n<s.length;n++)i[s.charCodeAt(n)]=n;function r(e,t,i){4===i?e.push([t[0],t[1],t[2],t[3]]):5===i?e.push([t[0],t[1],t[2],t[3],t[4]]):1===i&&e.push([t[0]])}function a(e){var t="";e=e<0?-e<<1|1:e<<1;do{var i=31&e;(e>>>=5)>0&&(i|=32),t+=s[i]}while(e>0);return t}class o{constructor(e){this.bits=e instanceof o?e.bits.slice():[]}add(e){this.bits[e>>5]|=1<<(31&e)}has(e){return!!(this.bits[e>>5]&1<<(31&e))}}class l{constructor(e,t,i){this.start=e,this.end=t,this.original=i,this.intro="",this.outro="",this.content=i,this.storeName=!1,this.edited=!1,Object.defineProperties(this,{previous:{writable:!0,value:null},next:{writable:!0,value:null}})}appendLeft(e){this.outro+=e}appendRight(e){this.intro=this.intro+e}clone(){const e=new l(this.start,this.end,this.original);return e.intro=this.intro,e.outro=this.outro,e.content=this.content,e.storeName=this.storeName,e.edited=this.edited,e}contains(e){return this.start<e&&e<this.end}eachNext(e){let t=this;for(;t;)e(t),t=t.next}eachPrevious(e){let t=this;for(;t;)e(t),t=t.previous}edit(e,t,i){return this.content=e,i||(this.intro="",this.outro=""),this.storeName=t,this.edited=!0,this}prependLeft(e){this.outro=e+this.outro}prependRight(e){this.intro=e+this.intro}split(e){const t=e-this.start,i=this.original.slice(0,t),s=this.original.slice(t);this.original=i;const n=new l(e,this.end,s);return n.outro=this.outro,this.outro="",this.end=e,this.edited?(n.edit("",!1),this.content=""):this.content=i,n.next=this.next,n.next&&(n.next.previous=n),n.previous=this,this.next=n,n}toString(){return this.intro+this.content+this.outro}trimEnd(e){if(this.outro=this.outro.replace(e,""),this.outro.length)return!0;const t=this.content.replace(e,"");return t.length?(t!==this.content&&this.split(this.start+t.length).edit("",void 0,!0),!0):(this.edit("",void 0,!0),this.intro=this.intro.replace(e,""),!!this.intro.length||void 0)}trimStart(e){if(this.intro=this.intro.replace(e,""),this.intro.length)return!0;const t=this.content.replace(e,"");return t.length?(t!==this.content&&(this.split(this.end-t.length),this.edit("",void 0,!0)),!0):(this.edit("",void 0,!0),this.outro=this.outro.replace(e,""),!!this.outro.length||void 0)}}let h=()=>{throw new Error("Unsupported environment: `window.btoa` or `Buffer` should be supported.")};"undefined"!=typeof window&&"function"==typeof window.btoa?h=e=>window.btoa(unescape(encodeURIComponent(e))):"function"==typeof Buffer&&(h=e=>Buffer.from(e,"utf-8").toString("base64"));class c{constructor(e){this.version=3,this.file=e.file,this.sources=e.sources,this.sourcesContent=e.sourcesContent,this.names=e.names,this.mappings=function(e){for(var t=0,i=0,s=0,n=0,r="",o=0;o<e.length;o++){var l=e[o];if(o>0&&(r+=";"),0!==l.length){for(var h=0,c=[],u=0,d=l;u<d.length;u++){var p=d[u],f=a(p[0]-h);h=p[0],p.length>1&&(f+=a(p[1]-t)+a(p[2]-i)+a(p[3]-s),t=p[1],i=p[2],s=p[3]),5===p.length&&(f+=a(p[4]-n),n=p[4]),c.push(f)}r+=c.join(",")}}return r}(e.mappings)}toString(){return JSON.stringify(this)}toUrl(){return"data:application/json;charset=utf-8;base64,"+h(this.toString())}}function u(e){const t=e.split("\n"),i=t.filter((e=>/^\t+/.test(e))),s=t.filter((e=>/^ {2,}/.test(e)));if(0===i.length&&0===s.length)return null;if(i.length>=s.length)return"\t";const n=s.reduce(((e,t)=>{const i=/^ +/.exec(t)[0].length;return Math.min(i,e)}),1/0);return new Array(n+1).join(" ")}function d(e,t){const i=e.split(/[/\\]/),s=t.split(/[/\\]/);for(i.pop();i[0]===s[0];)i.shift(),s.shift();if(i.length){let e=i.length;for(;e--;)i[e]=".."}return i.concat(s).join("/")}const p=Object.prototype.toString;function f(e){return"[object Object]"===p.call(e)}function m(e){const t=e.split("\n"),i=[];for(let e=0,s=0;e<t.length;e++)i.push(s),s+=t[e].length+1;return function(e){let t=0,s=i.length;for(;t<s;){const n=t+s>>1;e<i[n]?s=n:t=n+1}const n=t-1;return{line:n,column:e-i[n]}}}class g{constructor(e){this.hires=e,this.generatedCodeLine=0,this.generatedCodeColumn=0,this.raw=[],this.rawSegments=this.raw[this.generatedCodeLine]=[],this.pending=null}addEdit(e,t,i,s){if(t.length){const t=[this.generatedCodeColumn,e,i.line,i.column];s>=0&&t.push(s),this.rawSegments.push(t)}else this.pending&&this.rawSegments.push(this.pending);this.advance(t),this.pending=null}addUneditedChunk(e,t,i,s,n){let r=t.start,a=!0;for(;r<t.end;)(this.hires||a||n.has(r))&&this.rawSegments.push([this.generatedCodeColumn,e,s.line,s.column]),"\n"===i[r]?(s.line+=1,s.column=0,this.generatedCodeLine+=1,this.raw[this.generatedCodeLine]=this.rawSegments=[],this.generatedCodeColumn=0,a=!0):(s.column+=1,this.generatedCodeColumn+=1,a=!1),r+=1;this.pending=null}advance(e){if(!e)return;const t=e.split("\n");if(t.length>1){for(let e=0;e<t.length-1;e++)this.generatedCodeLine++,this.raw[this.generatedCodeLine]=this.rawSegments=[];this.generatedCodeColumn=0}this.generatedCodeColumn+=t[t.length-1].length}}const y="\n",x={insertLeft:!1,insertRight:!1,storeName:!1};class E{constructor(e,t={}){const i=new l(0,e.length,e);Object.defineProperties(this,{original:{writable:!0,value:e},outro:{writable:!0,value:""},intro:{writable:!0,value:""},firstChunk:{writable:!0,value:i},lastChunk:{writable:!0,value:i},lastSearchedChunk:{writable:!0,value:i},byStart:{writable:!0,value:{}},byEnd:{writable:!0,value:{}},filename:{writable:!0,value:t.filename},indentExclusionRanges:{writable:!0,value:t.indentExclusionRanges},sourcemapLocations:{writable:!0,value:new o},storedNames:{writable:!0,value:{}},indentStr:{writable:!0,value:u(e)}}),this.byStart[0]=i,this.byEnd[e.length]=i}addSourcemapLocation(e){this.sourcemapLocations.add(e)}append(e){if("string"!=typeof e)throw new TypeError("outro content must be a string");return this.outro+=e,this}appendLeft(e,t){if("string"!=typeof t)throw new TypeError("inserted content must be a string");this._split(e);const i=this.byEnd[e];return i?i.appendLeft(t):this.intro+=t,this}appendRight(e,t){if("string"!=typeof t)throw new TypeError("inserted content must be a string");this._split(e);const i=this.byStart[e];return i?i.appendRight(t):this.outro+=t,this}clone(){const e=new E(this.original,{filename:this.filename});let t=this.firstChunk,i=e.firstChunk=e.lastSearchedChunk=t.clone();for(;t;){e.byStart[i.start]=i,e.byEnd[i.end]=i;const s=t.next,n=s&&s.clone();n&&(i.next=n,n.previous=i,i=n),t=s}return e.lastChunk=i,this.indentExclusionRanges&&(e.indentExclusionRanges=this.indentExclusionRanges.slice()),e.sourcemapLocations=new o(this.sourcemapLocations),e.intro=this.intro,e.outro=this.outro,e}generateDecodedMap(e){e=e||{};const t=Object.keys(this.storedNames),i=new g(e.hires),s=m(this.original);return this.intro&&i.advance(this.intro),this.firstChunk.eachNext((e=>{const n=s(e.start);e.intro.length&&i.advance(e.intro),e.edited?i.addEdit(0,e.content,n,e.storeName?t.indexOf(e.original):-1):i.addUneditedChunk(0,e,this.original,n,this.sourcemapLocations),e.outro.length&&i.advance(e.outro)})),{file:e.file?e.file.split(/[/\\]/).pop():null,sources:[e.source?d(e.file||"",e.source):null],sourcesContent:e.includeContent?[this.original]:[null],names:t,mappings:i.raw}}generateMap(e){return new c(this.generateDecodedMap(e))}getIndentString(){return null===this.indentStr?"\t":this.indentStr}indent(e,t){const i=/^[^\r\n]/gm;if(f(e)&&(t=e,e=void 0),""===(e=void 0!==e?e:this.indentStr||"\t"))return this;const s={};(t=t||{}).exclude&&("number"==typeof t.exclude[0]?[t.exclude]:t.exclude).forEach((e=>{for(let t=e[0];t<e[1];t+=1)s[t]=!0}));let n=!1!==t.indentStart;const r=t=>n?`${e}${t}`:(n=!0,t);this.intro=this.intro.replace(i,r);let a=0,o=this.firstChunk;for(;o;){const t=o.end;if(o.edited)s[a]||(o.content=o.content.replace(i,r),o.content.length&&(n="\n"===o.content[o.content.length-1]));else for(a=o.start;a<t;){if(!s[a]){const t=this.original[a];"\n"===t?n=!0:"\r"!==t&&n&&(n=!1,a===o.start||(this._splitChunk(o,a),o=o.next),o.prependRight(e))}a+=1}a=o.end,o=o.next}return this.outro=this.outro.replace(i,r),this}insert(){throw new Error("magicString.insert(...) is deprecated. Use prependRight(...) or appendLeft(...)")}insertLeft(e,t){return x.insertLeft||(console.warn("magicString.insertLeft(...) is deprecated. Use magicString.appendLeft(...) instead"),x.insertLeft=!0),this.appendLeft(e,t)}insertRight(e,t){return x.insertRight||(console.warn("magicString.insertRight(...) is deprecated. Use magicString.prependRight(...) instead"),x.insertRight=!0),this.prependRight(e,t)}move(e,t,i){if(i>=e&&i<=t)throw new Error("Cannot move a selection inside itself");this._split(e),this._split(t),this._split(i);const s=this.byStart[e],n=this.byEnd[t],r=s.previous,a=n.next,o=this.byStart[i];if(!o&&n===this.lastChunk)return this;const l=o?o.previous:this.lastChunk;return r&&(r.next=a),a&&(a.previous=r),l&&(l.next=s),o&&(o.previous=n),s.previous||(this.firstChunk=n.next),n.next||(this.lastChunk=s.previous,this.lastChunk.next=null),s.previous=l,n.next=o||null,l||(this.firstChunk=s),o||(this.lastChunk=n),this}overwrite(e,t,i,s){if("string"!=typeof i)throw new TypeError("replacement content must be a string");for(;e<0;)e+=this.original.length;for(;t<0;)t+=this.original.length;if(t>this.original.length)throw new Error("end is out of bounds");if(e===t)throw new Error("Cannot overwrite a zero-length range – use appendLeft or prependRight instead");this._split(e),this._split(t),!0===s&&(x.storeName||(console.warn("The final argument to magicString.overwrite(...) should be an options object. See https://github.com/rich-harris/magic-string"),x.storeName=!0),s={storeName:!0});const n=void 0!==s&&s.storeName,r=void 0!==s&&s.contentOnly;if(n){const i=this.original.slice(e,t);Object.defineProperty(this.storedNames,i,{writable:!0,value:!0,enumerable:!0})}const a=this.byStart[e],o=this.byEnd[t];if(a){let e=a;for(;e!==o;){if(e.next!==this.byStart[e.end])throw new Error("Cannot overwrite across a split point");e=e.next,e.edit("",!1)}a.edit(i,n,r)}else{const s=new l(e,t,"").edit(i,n);o.next=s,s.previous=o}return this}prepend(e){if("string"!=typeof e)throw new TypeError("outro content must be a string");return this.intro=e+this.intro,this}prependLeft(e,t){if("string"!=typeof t)throw new TypeError("inserted content must be a string");this._split(e);const i=this.byEnd[e];return i?i.prependLeft(t):this.intro=t+this.intro,this}prependRight(e,t){if("string"!=typeof t)throw new TypeError("inserted content must be a string");this._split(e);const i=this.byStart[e];return i?i.prependRight(t):this.outro=t+this.outro,this}remove(e,t){for(;e<0;)e+=this.original.length;for(;t<0;)t+=this.original.length;if(e===t)return this;if(e<0||t>this.original.length)throw new Error("Character is out of bounds");if(e>t)throw new Error("end must be greater than start");this._split(e),this._split(t);let i=this.byStart[e];for(;i;)i.intro="",i.outro="",i.edit(""),i=t>i.end?this.byStart[i.end]:null;return this}lastChar(){if(this.outro.length)return this.outro[this.outro.length-1];let e=this.lastChunk;do{if(e.outro.length)return e.outro[e.outro.length-1];if(e.content.length)return e.content[e.content.length-1];if(e.intro.length)return e.intro[e.intro.length-1]}while(e=e.previous);return this.intro.length?this.intro[this.intro.length-1]:""}lastLine(){let e=this.outro.lastIndexOf(y);if(-1!==e)return this.outro.substr(e+1);let t=this.outro,i=this.lastChunk;do{if(i.outro.length>0){if(e=i.outro.lastIndexOf(y),-1!==e)return i.outro.substr(e+1)+t;t=i.outro+t}if(i.content.length>0){if(e=i.content.lastIndexOf(y),-1!==e)return i.content.substr(e+1)+t;t=i.content+t}if(i.intro.length>0){if(e=i.intro.lastIndexOf(y),-1!==e)return i.intro.substr(e+1)+t;t=i.intro+t}}while(i=i.previous);return e=this.intro.lastIndexOf(y),-1!==e?this.intro.substr(e+1)+t:this.intro+t}slice(e=0,t=this.original.length){for(;e<0;)e+=this.original.length;for(;t<0;)t+=this.original.length;let i="",s=this.firstChunk;for(;s&&(s.start>e||s.end<=e);){if(s.start<t&&s.end>=t)return i;s=s.next}if(s&&s.edited&&s.start!==e)throw new Error(`Cannot use replaced character ${e} as slice start anchor.`);const n=s;for(;s;){!s.intro||n===s&&s.start!==e||(i+=s.intro);const r=s.start<t&&s.end>=t;if(r&&s.edited&&s.end!==t)throw new Error(`Cannot use replaced character ${t} as slice end anchor.`);const a=n===s?e-s.start:0,o=r?s.content.length+t-s.end:s.content.length;if(i+=s.content.slice(a,o),!s.outro||r&&s.end!==t||(i+=s.outro),r)break;s=s.next}return i}snip(e,t){const i=this.clone();return i.remove(0,e),i.remove(t,i.original.length),i}_split(e){if(this.byStart[e]||this.byEnd[e])return;let t=this.lastSearchedChunk;const i=e>t.end;for(;t;){if(t.contains(e))return this._splitChunk(t,e);t=i?this.byStart[t.end]:this.byEnd[t.start]}}_splitChunk(e,t){if(e.edited&&e.content.length){const i=m(this.original)(t);throw new Error(`Cannot split a chunk that has already been edited (${i.line}:${i.column} – "${e.original}")`)}const i=e.split(t);return this.byEnd[t]=e,this.byStart[t]=i,this.byEnd[i.end]=i,e===this.lastChunk&&(this.lastChunk=i),this.lastSearchedChunk=e,!0}toString(){let e=this.intro,t=this.firstChunk;for(;t;)e+=t.toString(),t=t.next;return e+this.outro}isEmpty(){let e=this.firstChunk;do{if(e.intro.length&&e.intro.trim()||e.content.length&&e.content.trim()||e.outro.length&&e.outro.trim())return!1}while(e=e.next);return!0}length(){let e=this.firstChunk,t=0;do{t+=e.intro.length+e.content.length+e.outro.length}while(e=e.next);return t}trimLines(){return this.trim("[\\r\\n]")}trim(e){return this.trimStart(e).trimEnd(e)}trimEndAborted(e){const t=new RegExp((e||"\\s")+"+$");if(this.outro=this.outro.replace(t,""),this.outro.length)return!0;let i=this.lastChunk;do{const e=i.end,s=i.trimEnd(t);if(i.end!==e&&(this.lastChunk===i&&(this.lastChunk=i.next),this.byEnd[i.end]=i,this.byStart[i.next.start]=i.next,this.byEnd[i.next.end]=i.next),s)return!0;i=i.previous}while(i);return!1}trimEnd(e){return this.trimEndAborted(e),this}trimStartAborted(e){const t=new RegExp("^"+(e||"\\s")+"+");if(this.intro=this.intro.replace(t,""),this.intro.length)return!0;let i=this.firstChunk;do{const e=i.end,s=i.trimStart(t);if(i.end!==e&&(i===this.lastChunk&&(this.lastChunk=i.next),this.byEnd[i.end]=i,this.byStart[i.next.start]=i.next,this.byEnd[i.next.end]=i.next),s)return!0;i=i.next}while(i);return!1}trimStart(e){return this.trimStartAborted(e),this}hasChanged(){return this.original!==this.toString()}replace(e,t){function i(e,i){return"string"==typeof t?t.replace(/\$(\$|&|\d+)/g,((t,i)=>"$"===i?"$":"&"===i?e[0]:+i<e.length?e[+i]:`$${i}`)):t(...e,e.index,i,e.groups)}if("string"!=typeof e&&e.global)(function(e,t){let i;const s=[];for(;i=e.exec(t);)s.push(i);return s})(e,this.original).forEach((e=>{null!=e.index&&this.overwrite(e.index,e.index+e[0].length,i(e,this.original))}));else{const t=this.original.match(e);t&&null!=t.index&&this.overwrite(t.index,t.index+t[0].length,i(t,this.original))}return this}}const b=Object.prototype.hasOwnProperty;class v{constructor(e={}){this.intro=e.intro||"",this.separator=void 0!==e.separator?e.separator:"\n",this.sources=[],this.uniqueSources=[],this.uniqueSourceIndexByFilename={}}addSource(e){if(e instanceof E)return this.addSource({content:e,filename:e.filename,separator:this.separator});if(!f(e)||!e.content)throw new Error("bundle.addSource() takes an object with a `content` property, which should be an instance of MagicString, and an optional `filename`");if(["filename","indentExclusionRanges","separator"].forEach((t=>{b.call(e,t)||(e[t]=e.content[t])})),void 0===e.separator&&(e.separator=this.separator),e.filename)if(b.call(this.uniqueSourceIndexByFilename,e.filename)){const t=this.uniqueSources[this.uniqueSourceIndexByFilename[e.filename]];if(e.content.original!==t.content)throw new Error(`Illegal source: same filename (${e.filename}), different contents`)}else this.uniqueSourceIndexByFilename[e.filename]=this.uniqueSources.length,this.uniqueSources.push({filename:e.filename,content:e.content.original});return this.sources.push(e),this}append(e,t){return this.addSource({content:new E(e),separator:t&&t.separator||""}),this}clone(){const e=new v({intro:this.intro,separator:this.separator});return this.sources.forEach((t=>{e.addSource({filename:t.filename,content:t.content.clone(),separator:t.separator})})),e}generateDecodedMap(e={}){const t=[];this.sources.forEach((e=>{Object.keys(e.content.storedNames).forEach((e=>{~t.indexOf(e)||t.push(e)}))}));const i=new g(e.hires);return this.intro&&i.advance(this.intro),this.sources.forEach(((e,s)=>{s>0&&i.advance(this.separator);const n=e.filename?this.uniqueSourceIndexByFilename[e.filename]:-1,r=e.content,a=m(r.original);r.intro&&i.advance(r.intro),r.firstChunk.eachNext((s=>{const o=a(s.start);s.intro.length&&i.advance(s.intro),e.filename?s.edited?i.addEdit(n,s.content,o,s.storeName?t.indexOf(s.original):-1):i.addUneditedChunk(n,s,r.original,o,r.sourcemapLocations):i.advance(s.content),s.outro.length&&i.advance(s.outro)})),r.outro&&i.advance(r.outro)})),{file:e.file?e.file.split(/[/\\]/).pop():null,sources:this.uniqueSources.map((t=>e.file?d(e.file,t.filename):t.filename)),sourcesContent:this.uniqueSources.map((t=>e.includeContent?t.content:null)),names:t,mappings:i.raw}}generateMap(e){return new c(this.generateDecodedMap(e))}getIndentString(){const e={};return this.sources.forEach((t=>{const i=t.content.indentStr;null!==i&&(e[i]||(e[i]=0),e[i]+=1)})),Object.keys(e).sort(((t,i)=>e[t]-e[i]))[0]||"\t"}indent(e){if(arguments.length||(e=this.getIndentString()),""===e)return this;let t=!this.intro||"\n"===this.intro.slice(-1);return this.sources.forEach(((i,s)=>{const n=void 0!==i.separator?i.separator:this.separator,r=t||s>0&&/\r?\n$/.test(n);i.content.indent(e,{exclude:i.indentExclusionRanges,indentStart:r}),t="\n"===i.content.lastChar()})),this.intro&&(this.intro=e+this.intro.replace(/^[^\n]/gm,((t,i)=>i>0?e+t:t))),this}prepend(e){return this.intro=e+this.intro,this}toString(){const e=this.sources.map(((e,t)=>{const i=void 0!==e.separator?e.separator:this.separator;return(t>0?i:"")+e.content.toString()})).join("");return this.intro+e}isEmpty(){return!(this.intro.length&&this.intro.trim()||this.sources.some((e=>!e.content.isEmpty())))}length(){return this.sources.reduce(((e,t)=>e+t.content.length()),this.intro.length)}trimLines(){return this.trim("[\\r\\n]")}trim(e){return this.trimStart(e).trimEnd(e)}trimStart(e){const t=new RegExp("^"+(e||"\\s")+"+");if(this.intro=this.intro.replace(t,""),!this.intro){let t,i=0;do{if(t=this.sources[i++],!t)break}while(!t.content.trimStartAborted(e))}return this}trimEnd(e){const t=new RegExp((e||"\\s")+"+$");let i,s=this.sources.length-1;do{if(i=this.sources[s--],!i){this.intro=this.intro.replace(t,"");break}}while(!i.content.trimEndAborted(e));return this}}const S=/^(?:\/|(?:[A-Za-z]:)?[\\|/])/,A=/^\.?\.\//,I=/\\/g,P=/[/\\]/,k=/\.[^.]+$/;function w(e){return S.test(e)}function C(e){return A.test(e)}function N(e){return e.replace(I,"/")}function _(e){return e.split(P).pop()||""}function $(e){const t=/[/\\][^/\\]*$/.exec(e);if(!t)return".";const i=e.slice(0,-t[0].length);return i||"/"}function T(e){const t=k.exec(_(e));return t?t[0]:""}function O(e,t){const i=e.split(P).filter(Boolean),s=t.split(P).filter(Boolean);for("."===i[0]&&i.shift(),"."===s[0]&&s.shift();i[0]&&s[0]&&i[0]===s[0];)i.shift(),s.shift();for(;".."===s[0]&&i.length>0;)s.shift(),i.pop();for(;i.pop();)s.unshift("..");return s.join("/")}function R(...e){const t=e.shift();if(!t)return"/";let i=t.split(P);for(const t of e)if(w(t))i=t.split(P);else{const e=t.split(P);for(;"."===e[0]||".."===e[0];)".."===e.shift()&&i.pop();i.push(...e)}return i.join("/")}function M(e,t,i){const s=e.get(t);if(s)return s;const n=i();return e.set(t,n),n}const D=Symbol("Unknown Key"),L=Symbol("Unknown Non-Accessor Key"),V=Symbol("Unknown Integer"),B=[],F=[D],z=[L],j=[V],U=Symbol("Entities");class G{constructor(){this.entityPaths=Object.create(null,{[U]:{value:new Set}})}trackEntityAtPathAndGetIfTracked(e,t){const i=this.getEntities(e);return!!i.has(t)||(i.add(t),!1)}withTrackedEntityAtPath(e,t,i,s){const n=this.getEntities(e);if(n.has(t))return s;n.add(t);const r=i();return n.delete(t),r}getEntities(e){let t=this.entityPaths;for(const i of e)t=t[i]=t[i]||Object.create(null,{[U]:{value:new Set}});return t[U]}}const H=new G;class W{constructor(){this.entityPaths=Object.create(null,{[U]:{value:new Map}})}trackEntityAtPathAndGetIfTracked(e,t,i){let s=this.entityPaths;for(const t of e)s=s[t]=s[t]||Object.create(null,{[U]:{value:new Map}});const n=M(s[U],t,(()=>new Set));return!!n.has(i)||(n.add(i),!1)}}const q=Symbol("Unknown Value"),K=Symbol("Unknown Truthy Value");class X{constructor(){this.included=!1}deoptimizePath(e){}deoptimizeThisOnInteractionAtPath({thisArg:e},t,i){e.deoptimizePath(F)}getLiteralValueAtPath(e,t,i){return q}getReturnExpressionWhenCalledAtPath(e,t,i,s){return Y}hasEffectsOnInteractionAtPath(e,t,i){return!0}include(e,t,i){this.included=!0}includeCallArguments(e,t){for(const i of t)i.include(e,!1)}shouldBeIncluded(e){return!0}}const Y=new class extends X{},Q={thisArg:null,type:0},J={args:[Y],thisArg:null,type:1},Z=[],ee={args:Z,thisArg:null,type:2,withNew:!1};class te extends X{constructor(e){super(),this.name=e,this.alwaysRendered=!1,this.initReached=!1,this.isId=!1,this.isReassigned=!1,this.kind=null,this.renderBaseName=null,this.renderName=null}addReference(e){}getBaseVariableName(){return this.renderBaseName||this.renderName||this.name}getName(e){const t=this.renderName||this.name;return this.renderBaseName?`${this.renderBaseName}${e(t)}`:t}hasEffectsOnInteractionAtPath(e,{type:t},i){return 0!==t||e.length>0}include(){this.included=!0}markCalledFromTryStatement(){}setRenderNames(e,t){this.renderBaseName=e,this.renderName=t}}class ie extends te{constructor(e,t){super(t),this.referenced=!1,this.module=e,this.isNamespace="*"===t}addReference(e){this.referenced=!0,"default"!==this.name&&"*"!==this.name||this.module.suggestName(e.name)}hasEffectsOnInteractionAtPath(e,{type:t}){return 0!==t||e.length>(this.isNamespace?1:0)}include(){this.included||(this.included=!0,this.module.used=!0)}}const se=Object.freeze(Object.create(null)),ne=Object.freeze({}),re=Object.freeze([]);function ae(e,t,i){if("number"==typeof i)throw new Error("locate takes a { startIndex, offsetLine, offsetColumn } object as the third argument");return function(e,t){void 0===t&&(t={});var i=t.offsetLine||0,s=t.offsetColumn||0,n=e.split("\n"),r=0,a=n.map((function(e,t){var i=r+e.length+1,s={start:r,end:i,line:t};return r=i,s})),o=0;function l(e,t){return e.start<=t&&t<e.end}function h(e,t){return{line:i+e.line,column:s+t-e.start,character:t}}return function(t,i){"string"==typeof t&&(t=e.indexOf(t,i||0));for(var s=a[o],n=t>=s.end?1:-1;s;){if(l(s,t))return h(s,t);s=a[o+=n]}}}(e,i)(t,i&&i.startIndex)}function oe(e){return e.replace(/^\t+/,(e=>e.split("\t").join("  ")))}function le(e,t){const i=e.length<=1,s=e.map((e=>`"${e}"`));let n=i?s[0]:`${s.slice(0,-1).join(", ")} and ${s.slice(-1)[0]}`;return t&&(n+=` ${i?t[0]:t[1]}`),n}function he(e){const t=_(e);return t.substring(0,t.length-T(e).length)}function ce(e){return w(e)?O(R(),e):e}function ue(e){return"/"===e[0]||"."===e[0]&&("/"===e[1]||"."===e[1])||w(e)}const de=/^(\.\.\/)*\.\.$/;function pe(e,t,i,s){let n=N(O($(e),t));if(i&&n.endsWith(".js")&&(n=n.slice(0,-3)),s){if(""===n)return"../"+_(t);if(de.test(n))return n.split("/").concat(["..",_(t)]).join("/")}return n?n.startsWith("..")?n:"./"+n:"."}function fe(e){throw e instanceof Error||(e=Object.assign(new Error(e.message),e)),e}function me(e,t,i,s){if("object"==typeof t){const{line:i,column:n}=t;e.loc={column:n,file:s,line:i}}else{e.pos=t;const{line:n,column:r}=ae(i,t,{offsetLine:1});e.loc={column:r,file:s,line:n}}if(void 0===e.frame){const{line:t,column:s}=e.loc;e.frame=function(e,t,i){let s=e.split("\n");const n=Math.max(0,t-3);let r=Math.min(t+2,s.length);for(s=s.slice(n,r);!/\S/.test(s[s.length-1]);)s.pop(),r-=1;const a=String(r).length;return s.map(((e,s)=>{const r=n+s+1===t;let o=String(s+n+1);for(;o.length<a;)o=` ${o}`;if(r){const t=function(e){let t="";for(;e--;)t+=" ";return t}(a+2+oe(e.slice(0,i)).length)+"^";return`${o}: ${oe(e)}\n${t}`}return`${o}: ${oe(e)}`})).join("\n")}(i,t,s)}}var ge;function ye({fileName:e,code:t},i){const s={code:ge.CHUNK_INVALID,message:`Chunk "${e}" is not valid JavaScript: ${i.message}.`};return me(s,i.loc,t,e),s}function xe(e,t,i){return{code:"INVALID_EXPORT_OPTION",message:`"${e}" was specified for "output.exports", but entry module "${ce(i)}" has the following exports: ${t.join(", ")}`}}function Ee(e,t,i,s){return{code:ge.INVALID_OPTION,message:`Invalid value ${void 0!==s?`${JSON.stringify(s)} `:""}for option "${e}" - ${i}.`,url:`https://rollupjs.org/guide/en/#${t}`}}function be(e,t,i){return{code:ge.MISSING_EXPORT,message:`'${e}' is not exported by ${ce(i)}, imported by ${ce(t)}`,url:"https://rollupjs.org/guide/en/#error-name-is-not-exported-by-module"}}function ve(e){const t=Array.from(e.implicitlyLoadedBefore,(e=>ce(e.id))).sort();return{code:ge.MISSING_IMPLICIT_DEPENDANT,message:`Module "${ce(e.id)}" that should be implicitly loaded before ${le(t)} is not included in the module graph. Either it was not imported by an included module or only via a tree-shaken dynamic import, or no imported bindings were used and it had otherwise no side-effects.`}}function Se(e,t,i){const s=i?"reexport":"import";return{code:ge.UNEXPECTED_NAMED_IMPORT,id:e,message:`The named export "${t}" was ${s}ed from the external module ${ce(e)} even though its interop type is "defaultOnly". Either remove or change this ${s} or change the value of the "output.interop" option.`,url:"https://rollupjs.org/guide/en/#outputinterop"}}function Ae(e){return{code:ge.UNEXPECTED_NAMED_IMPORT,id:e,message:`There was a namespace "*" reexport from the external module ${ce(e)} even though its interop type is "defaultOnly". This will be ignored as namespace reexports only reexport named exports. If this is not intended, either remove or change this reexport or change the value of the "output.interop" option.`,url:"https://rollupjs.org/guide/en/#outputinterop"}}function Ie(e){return{code:ge.VALIDATION_ERROR,message:e}}function Pe(){return{code:ge.ALREADY_CLOSED,message:'Bundle is already closed, no more calls to "generate" or "write" are allowed.'}}function ke(e,t,i){we(e,t,i.onwarn,i.strictDeprecations)}function we(e,t,i,s){if(t||s){const t=function(e){return{code:ge.DEPRECATED_FEATURE,..."string"==typeof e?{message:e}:e}}(e);if(s)return fe(t);i(t)}}!function(e){e.ALREADY_CLOSED="ALREADY_CLOSED",e.ASSET_NOT_FINALISED="ASSET_NOT_FINALISED",e.ASSET_NOT_FOUND="ASSET_NOT_FOUND",e.ASSET_SOURCE_ALREADY_SET="ASSET_SOURCE_ALREADY_SET",e.ASSET_SOURCE_MISSING="ASSET_SOURCE_MISSING",e.BAD_LOADER="BAD_LOADER",e.CANNOT_EMIT_FROM_OPTIONS_HOOK="CANNOT_EMIT_FROM_OPTIONS_HOOK",e.CHUNK_NOT_GENERATED="CHUNK_NOT_GENERATED",e.CHUNK_INVALID="CHUNK_INVALID",e.CIRCULAR_REEXPORT="CIRCULAR_REEXPORT",e.CYCLIC_CROSS_CHUNK_REEXPORT="CYCLIC_CROSS_CHUNK_REEXPORT",e.DEPRECATED_FEATURE="DEPRECATED_FEATURE",e.EXTERNAL_SYNTHETIC_EXPORTS="EXTERNAL_SYNTHETIC_EXPORTS",e.FILE_NAME_CONFLICT="FILE_NAME_CONFLICT",e.FILE_NOT_FOUND="FILE_NOT_FOUND",e.INPUT_HOOK_IN_OUTPUT_PLUGIN="INPUT_HOOK_IN_OUTPUT_PLUGIN",e.INVALID_CHUNK="INVALID_CHUNK",e.INVALID_EXPORT_OPTION="INVALID_EXPORT_OPTION",e.INVALID_EXTERNAL_ID="INVALID_EXTERNAL_ID",e.INVALID_OPTION="INVALID_OPTION",e.INVALID_PLUGIN_HOOK="INVALID_PLUGIN_HOOK",e.INVALID_ROLLUP_PHASE="INVALID_ROLLUP_PHASE",e.MISSING_EXPORT="MISSING_EXPORT",e.MISSING_IMPLICIT_DEPENDANT="MISSING_IMPLICIT_DEPENDANT",e.MIXED_EXPORTS="MIXED_EXPORTS",e.NAMESPACE_CONFLICT="NAMESPACE_CONFLICT",e.AMBIGUOUS_EXTERNAL_NAMESPACES="AMBIGUOUS_EXTERNAL_NAMESPACES",e.NO_TRANSFORM_MAP_OR_AST_WITHOUT_CODE="NO_TRANSFORM_MAP_OR_AST_WITHOUT_CODE",e.PLUGIN_ERROR="PLUGIN_ERROR",e.PREFER_NAMED_EXPORTS="PREFER_NAMED_EXPORTS",e.SYNTHETIC_NAMED_EXPORTS_NEED_NAMESPACE_EXPORT="SYNTHETIC_NAMED_EXPORTS_NEED_NAMESPACE_EXPORT",e.UNEXPECTED_NAMED_IMPORT="UNEXPECTED_NAMED_IMPORT",e.UNRESOLVED_ENTRY="UNRESOLVED_ENTRY",e.UNRESOLVED_IMPORT="UNRESOLVED_IMPORT",e.VALIDATION_ERROR="VALIDATION_ERROR"}(ge||(ge={}));var Ce=new Set(["await","break","case","catch","class","const","continue","debugger","default","delete","do","else","enum","eval","export","extends","false","finally","for","function","if","implements","import","in","instanceof","interface","let","NaN","new","null","package","private","protected","public","return","static","super","switch","this","throw","true","try","typeof","undefined","var","void","while","with","yield"]);const Ne=/[^$_a-zA-Z0-9]/g,_e=e=>(e=>/\d/.test(e[0]))(e)||Ce.has(e)||"arguments"===e;function $e(e){return e=e.replace(/-(\w)/g,((e,t)=>t.toUpperCase())).replace(Ne,"_"),_e(e)&&(e=`_${e}`),e||"_"}class Te{constructor(e,t,i,s,n){this.options=e,this.id=t,this.renormalizeRenderPath=n,this.declarations=new Map,this.defaultVariableName="",this.dynamicImporters=[],this.execIndex=1/0,this.exportedVariables=new Map,this.importers=[],this.mostCommonSuggestion=0,this.nameSuggestions=new Map,this.namespaceVariableName="",this.reexported=!1,this.renderPath=void 0,this.used=!1,this.variableName="",this.suggestedVariableName=$e(t.split(/[\\/]/).pop());const{importers:r,dynamicImporters:a}=this,o=this.info={ast:null,code:null,dynamicallyImportedIdResolutions:re,dynamicallyImportedIds:re,get dynamicImporters(){return a.sort()},hasDefaultExport:null,get hasModuleSideEffects(){return ke("Accessing ModuleInfo.hasModuleSideEffects from plugins is deprecated. Please use ModuleInfo.moduleSideEffects instead.",!1,e),o.moduleSideEffects},id:t,implicitlyLoadedAfterOneOf:re,implicitlyLoadedBefore:re,importedIdResolutions:re,importedIds:re,get importers(){return r.sort()},isEntry:!1,isExternal:!0,isIncluded:null,meta:s,moduleSideEffects:i,syntheticNamedExports:!1};Object.defineProperty(this.info,"hasModuleSideEffects",{enumerable:!1})}getVariableForExportName(e){const t=this.declarations.get(e);if(t)return[t];const i=new ie(this,e);return this.declarations.set(e,i),this.exportedVariables.set(i,e),[i]}setRenderPath(e,t){this.renderPath="function"==typeof e.paths?e.paths(this.id):e.paths[this.id],this.renderPath||(this.renderPath=this.renormalizeRenderPath?N(O(t,this.id)):this.id)}suggestName(e){var t;const i=(null!==(t=this.nameSuggestions.get(e))&&void 0!==t?t:0)+1;this.nameSuggestions.set(e,i),i>this.mostCommonSuggestion&&(this.mostCommonSuggestion=i,this.suggestedVariableName=e)}warnUnusedImports(){const e=Array.from(this.declarations).filter((([e,t])=>"*"!==e&&!t.included&&!this.reexported&&!t.referenced)).map((([e])=>e));if(0===e.length)return;const t=new Set;for(const i of e)for(const e of this.declarations.get(i).module.importers)t.add(e);const i=[...t];this.options.onwarn({code:"UNUSED_EXTERNAL_IMPORT",message:`${le(e,["is","are"])} imported from external module "${this.id}" but never used in ${le(i.map((e=>ce(e))))}.`,names:e,source:this.id,sources:i})}}const Oe={ArrayPattern(e,t){for(const i of t.elements)i&&Oe[i.type](e,i)},AssignmentPattern(e,t){Oe[t.left.type](e,t.left)},Identifier(e,t){e.push(t.name)},MemberExpression(){},ObjectPattern(e,t){for(const i of t.properties)"RestElement"===i.type?Oe.RestElement(e,i):Oe[i.value.type](e,i.value)},RestElement(e,t){Oe[t.argument.type](e,t.argument)}},Re=function(e){const t=[];return Oe[e.type](t,e),t};function Me(){return{brokenFlow:0,includedCallArguments:new Set,includedLabels:new Set}}function De(){return{accessed:new G,assigned:new G,brokenFlow:0,called:new W,ignore:{breaks:!1,continues:!1,labels:new Set,returnYield:!1},includedLabels:new Set,instantiated:new W,replacedVariableInits:new Map}}function Le(e,t=null){return Object.create(t,e)}new Set("break case class catch const continue debugger default delete do else export extends finally for function if import in instanceof let new return super switch this throw try typeof var void while with yield enum await implements package protected static interface private public arguments Infinity NaN undefined null true false eval uneval isFinite isNaN parseFloat parseInt decodeURI decodeURIComponent encodeURI encodeURIComponent escape unescape Object Function Boolean Symbol Error EvalError InternalError RangeError ReferenceError SyntaxError TypeError URIError Number Math Date String RegExp Array Int8Array Uint8Array Uint8ClampedArray Int16Array Uint16Array Int32Array Uint32Array Float32Array Float64Array Map Set WeakMap WeakSet SIMD ArrayBuffer DataView JSON Promise Generator GeneratorFunction Reflect Proxy Intl".split(" ")).add("");const Ve=new class extends X{getLiteralValueAtPath(){}},Be={value:{hasEffectsWhenCalled:null,returns:Y}},Fe=new class extends X{getReturnExpressionWhenCalledAtPath(e){return 1===e.length?Je(Ke,e[0]):Y}hasEffectsOnInteractionAtPath(e,t,i){return 0===t.type?e.length>1:2!==t.type||1!==e.length||Qe(Ke,e[0],t,i)}},ze={value:{hasEffectsWhenCalled:null,returns:Fe}},je=new class extends X{getReturnExpressionWhenCalledAtPath(e){return 1===e.length?Je(Xe,e[0]):Y}hasEffectsOnInteractionAtPath(e,t,i){return 0===t.type?e.length>1:2!==t.type||1!==e.length||Qe(Xe,e[0],t,i)}},Ue={value:{hasEffectsWhenCalled:null,returns:je}},Ge=new class extends X{getReturnExpressionWhenCalledAtPath(e){return 1===e.length?Je(Ye,e[0]):Y}hasEffectsOnInteractionAtPath(e,t,i){return 0===t.type?e.length>1:2!==t.type||1!==e.length||Qe(Ye,e[0],t,i)}},He={value:{hasEffectsWhenCalled:null,returns:Ge}},We={value:{hasEffectsWhenCalled({args:e},t){const i=e[1];return e.length<2||"symbol"==typeof i.getLiteralValueAtPath(B,H,{deoptimizeCache(){}})&&i.hasEffectsOnInteractionAtPath(B,ee,t)},returns:Ge}},qe=Le({hasOwnProperty:ze,isPrototypeOf:ze,propertyIsEnumerable:ze,toLocaleString:He,toString:He,valueOf:Be}),Ke=Le({valueOf:ze},qe),Xe=Le({toExponential:He,toFixed:He,toLocaleString:He,toPrecision:He,valueOf:Ue},qe),Ye=Le({anchor:He,at:Be,big:He,blink:He,bold:He,charAt:He,charCodeAt:Ue,codePointAt:Be,concat:He,endsWith:ze,fixed:He,fontcolor:He,fontsize:He,includes:ze,indexOf:Ue,italics:He,lastIndexOf:Ue,link:He,localeCompare:Ue,match:Be,matchAll:Be,normalize:He,padEnd:He,padStart:He,repeat:He,replace:We,replaceAll:We,search:Ue,slice:He,small:He,split:Be,startsWith:ze,strike:He,sub:He,substr:He,substring:He,sup:He,toLocaleLowerCase:He,toLocaleUpperCase:He,toLowerCase:He,toString:He,toUpperCase:He,trim:He,trimEnd:He,trimLeft:He,trimRight:He,trimStart:He,valueOf:He},qe);function Qe(e,t,i,s){var n,r;return"string"!=typeof t||!e[t]||(null===(r=(n=e[t]).hasEffectsWhenCalled)||void 0===r?void 0:r.call(n,i,s))||!1}function Je(e,t){return"string"==typeof t&&e[t]?e[t].returns:Y}function Ze(e,t,i){i(e,t)}function et(e,t,i){}var tt={};tt.Program=tt.BlockStatement=tt.StaticBlock=function(e,t,i){for(var s=0,n=e.body;s<n.length;s+=1)i(n[s],t,"Statement")},tt.Statement=Ze,tt.EmptyStatement=et,tt.ExpressionStatement=tt.ParenthesizedExpression=tt.ChainExpression=function(e,t,i){return i(e.expression,t,"Expression")},tt.IfStatement=function(e,t,i){i(e.test,t,"Expression"),i(e.consequent,t,"Statement"),e.alternate&&i(e.alternate,t,"Statement")},tt.LabeledStatement=function(e,t,i){return i(e.body,t,"Statement")},tt.BreakStatement=tt.ContinueStatement=et,tt.WithStatement=function(e,t,i){i(e.object,t,"Expression"),i(e.body,t,"Statement")},tt.SwitchStatement=function(e,t,i){i(e.discriminant,t,"Expression");for(var s=0,n=e.cases;s<n.length;s+=1){var r=n[s];r.test&&i(r.test,t,"Expression");for(var a=0,o=r.consequent;a<o.length;a+=1)i(o[a],t,"Statement")}},tt.SwitchCase=function(e,t,i){e.test&&i(e.test,t,"Expression");for(var s=0,n=e.consequent;s<n.length;s+=1)i(n[s],t,"Statement")},tt.ReturnStatement=tt.YieldExpression=tt.AwaitExpression=function(e,t,i){e.argument&&i(e.argument,t,"Expression")},tt.ThrowStatement=tt.SpreadElement=function(e,t,i){return i(e.argument,t,"Expression")},tt.TryStatement=function(e,t,i){i(e.block,t,"Statement"),e.handler&&i(e.handler,t),e.finalizer&&i(e.finalizer,t,"Statement")},tt.CatchClause=function(e,t,i){e.param&&i(e.param,t,"Pattern"),i(e.body,t,"Statement")},tt.WhileStatement=tt.DoWhileStatement=function(e,t,i){i(e.test,t,"Expression"),i(e.body,t,"Statement")},tt.ForStatement=function(e,t,i){e.init&&i(e.init,t,"ForInit"),e.test&&i(e.test,t,"Expression"),e.update&&i(e.update,t,"Expression"),i(e.body,t,"Statement")},tt.ForInStatement=tt.ForOfStatement=function(e,t,i){i(e.left,t,"ForInit"),i(e.right,t,"Expression"),i(e.body,t,"Statement")},tt.ForInit=function(e,t,i){"VariableDeclaration"===e.type?i(e,t):i(e,t,"Expression")},tt.DebuggerStatement=et,tt.FunctionDeclaration=function(e,t,i){return i(e,t,"Function")},tt.VariableDeclaration=function(e,t,i){for(var s=0,n=e.declarations;s<n.length;s+=1)i(n[s],t)},tt.VariableDeclarator=function(e,t,i){i(e.id,t,"Pattern"),e.init&&i(e.init,t,"Expression")},tt.Function=function(e,t,i){e.id&&i(e.id,t,"Pattern");for(var s=0,n=e.params;s<n.length;s+=1)i(n[s],t,"Pattern");i(e.body,t,e.expression?"Expression":"Statement")},tt.Pattern=function(e,t,i){"Identifier"===e.type?i(e,t,"VariablePattern"):"MemberExpression"===e.type?i(e,t,"MemberPattern"):i(e,t)},tt.VariablePattern=et,tt.MemberPattern=Ze,tt.RestElement=function(e,t,i){return i(e.argument,t,"Pattern")},tt.ArrayPattern=function(e,t,i){for(var s=0,n=e.elements;s<n.length;s+=1){var r=n[s];r&&i(r,t,"Pattern")}},tt.ObjectPattern=function(e,t,i){for(var s=0,n=e.properties;s<n.length;s+=1){var r=n[s];"Property"===r.type?(r.computed&&i(r.key,t,"Expression"),i(r.value,t,"Pattern")):"RestElement"===r.type&&i(r.argument,t,"Pattern")}},tt.Expression=Ze,tt.ThisExpression=tt.Super=tt.MetaProperty=et,tt.ArrayExpression=function(e,t,i){for(var s=0,n=e.elements;s<n.length;s+=1){var r=n[s];r&&i(r,t,"Expression")}},tt.ObjectExpression=function(e,t,i){for(var s=0,n=e.properties;s<n.length;s+=1)i(n[s],t)},tt.FunctionExpression=tt.ArrowFunctionExpression=tt.FunctionDeclaration,tt.SequenceExpression=function(e,t,i){for(var s=0,n=e.expressions;s<n.length;s+=1)i(n[s],t,"Expression")},tt.TemplateLiteral=function(e,t,i){for(var s=0,n=e.quasis;s<n.length;s+=1)i(n[s],t);for(var r=0,a=e.expressions;r<a.length;r+=1)i(a[r],t,"Expression")},tt.TemplateElement=et,tt.UnaryExpression=tt.UpdateExpression=function(e,t,i){i(e.argument,t,"Expression")},tt.BinaryExpression=tt.LogicalExpression=function(e,t,i){i(e.left,t,"Expression"),i(e.right,t,"Expression")},tt.AssignmentExpression=tt.AssignmentPattern=function(e,t,i){i(e.left,t,"Pattern"),i(e.right,t,"Expression")},tt.ConditionalExpression=function(e,t,i){i(e.test,t,"Expression"),i(e.consequent,t,"Expression"),i(e.alternate,t,"Expression")},tt.NewExpression=tt.CallExpression=function(e,t,i){if(i(e.callee,t,"Expression"),e.arguments)for(var s=0,n=e.arguments;s<n.length;s+=1)i(n[s],t,"Expression")},tt.MemberExpression=function(e,t,i){i(e.object,t,"Expression"),e.computed&&i(e.property,t,"Expression")},tt.ExportNamedDeclaration=tt.ExportDefaultDeclaration=function(e,t,i){e.declaration&&i(e.declaration,t,"ExportNamedDeclaration"===e.type||e.declaration.id?"Statement":"Expression"),e.source&&i(e.source,t,"Expression")},tt.ExportAllDeclaration=function(e,t,i){e.exported&&i(e.exported,t),i(e.source,t,"Expression")},tt.ImportDeclaration=function(e,t,i){for(var s=0,n=e.specifiers;s<n.length;s+=1)i(n[s],t);i(e.source,t,"Expression")},tt.ImportExpression=function(e,t,i){i(e.source,t,"Expression")},tt.ImportSpecifier=tt.ImportDefaultSpecifier=tt.ImportNamespaceSpecifier=tt.Identifier=tt.PrivateIdentifier=tt.Literal=et,tt.TaggedTemplateExpression=function(e,t,i){i(e.tag,t,"Expression"),i(e.quasi,t,"Expression")},tt.ClassDeclaration=tt.ClassExpression=function(e,t,i){return i(e,t,"Class")},tt.Class=function(e,t,i){e.id&&i(e.id,t,"Pattern"),e.superClass&&i(e.superClass,t,"Expression"),i(e.body,t)},tt.ClassBody=function(e,t,i){for(var s=0,n=e.body;s<n.length;s+=1)i(n[s],t)},tt.MethodDefinition=tt.PropertyDefinition=tt.Property=function(e,t,i){e.computed&&i(e.key,t,"Expression"),e.value&&i(e.value,t,"Expression")};const it="ArrowFunctionExpression",st="BlockStatement",nt="CallExpression",rt="ExpressionStatement",at="Identifier",ot="Program";let lt="sourceMa";lt+="ppingURL";const ht=new RegExp("^#[ \\f\\r\\t\\v\\u00a0\\u1680\\u2000-\\u200a\\u2028\\u2029\\u202f\\u205f\\u3000\\ufeff]+sourceMappingURL=.+"),ct="_rollupAnnotations",ut="_rollupRemoved";function dt(e,t,i=e.type){const{annotations:s}=t;let n=s[t.annotationIndex];for(;n&&e.start>=n.end;)mt(e,n,t.code),n=s[++t.annotationIndex];if(n&&n.end<=e.end)for(tt[i](e,t,dt);(n=s[t.annotationIndex])&&n.end<=e.end;)++t.annotationIndex,xt(e,n,!1)}const pt=/[^\s(]/g,ft=/\S/g;function mt(e,t,i){const s=[];let n;if(gt(i.slice(t.end,e.start),pt)){const t=e.start;for(;;){switch(s.push(e),e.type){case rt:case"ChainExpression":e=e.expression;continue;case"SequenceExpression":if(gt(i.slice(t,e.start),ft)){e=e.expressions[0];continue}n=!0;break;case"ConditionalExpression":if(gt(i.slice(t,e.start),ft)){e=e.test;continue}n=!0;break;case"LogicalExpression":case"BinaryExpression":if(gt(i.slice(t,e.start),ft)){e=e.left;continue}n=!0;break;case nt:case"NewExpression":break;default:n=!0}break}}else n=!0;if(n)xt(e,t,!1);else for(const e of s)xt(e,t,!0)}function gt(e,t){let i;for(;null!==(i=t.exec(e));){if("/"===i[0]){const i=e.charCodeAt(t.lastIndex);if(42===i){t.lastIndex=e.indexOf("*/",t.lastIndex+1)+2;continue}if(47===i){t.lastIndex=e.indexOf("\n",t.lastIndex+1)+1;continue}}return t.lastIndex=0,!1}return!0}const yt=/[@#]__PURE__/;function xt(e,t,i){const s=i?ct:ut,n=e[s];n?n.push(t):e[s]=[t]}const Et={Literal:[],Program:["body"]},bt="variables";class vt extends X{constructor(e,t,i){super(),this.deoptimized=!1,this.esTreeNode=e,this.keys=Et[e.type]||function(e){return Et[e.type]=Object.keys(e).filter((t=>"object"==typeof e[t]&&95!==t.charCodeAt(0))),Et[e.type]}(e),this.parent=t,this.context=t.context,this.createScope(i),this.parseNode(e),this.initialise(),this.context.magicString.addSourcemapLocation(this.start),this.context.magicString.addSourcemapLocation(this.end)}addExportedVariables(e,t){}bind(){for(const e of this.keys){const t=this[e];if(null!==t)if(Array.isArray(t))for(const e of t)null==e||e.bind();else t.bind()}}createScope(e){this.scope=e}hasEffects(e){this.deoptimized||this.applyDeoptimizations();for(const t of this.keys){const i=this[t];if(null!==i)if(Array.isArray(i)){for(const t of i)if(null==t?void 0:t.hasEffects(e))return!0}else if(i.hasEffects(e))return!0}return!1}hasEffectsAsAssignmentTarget(e,t){return this.hasEffects(e)||this.hasEffectsOnInteractionAtPath(B,this.assignmentInteraction,e)}include(e,t,i){this.deoptimized||this.applyDeoptimizations(),this.included=!0;for(const i of this.keys){const s=this[i];if(null!==s)if(Array.isArray(s))for(const i of s)null==i||i.include(e,t);else s.include(e,t)}}includeAsAssignmentTarget(e,t,i){this.include(e,t)}initialise(){}insertSemicolon(e){";"!==e.original[this.end-1]&&e.appendLeft(this.end,";")}parseNode(e){for(const[t,i]of Object.entries(e))if(!this.hasOwnProperty(t))if(95===t.charCodeAt(0)){if(t===ct)this.annotations=i;else if(t===ut)for(const{start:e,end:t}of i)this.context.magicString.remove(e,t)}else if("object"!=typeof i||null===i)this[t]=i;else if(Array.isArray(i)){this[t]=[];for(const e of i)this[t].push(null===e?null:new(this.context.getNodeConstructor(e.type))(e,this,this.scope))}else this[t]=new(this.context.getNodeConstructor(i.type))(i,this,this.scope)}render(e,t){for(const i of this.keys){const s=this[i];if(null!==s)if(Array.isArray(s))for(const i of s)null==i||i.render(e,t);else s.render(e,t)}}setAssignedValue(e){this.assignmentInteraction={args:[e],thisArg:null,type:1}}shouldBeIncluded(e){return this.included||!e.brokenFlow&&this.hasEffects(De())}applyDeoptimizations(){this.deoptimized=!0;for(const e of this.keys){const t=this[e];if(null!==t)if(Array.isArray(t))for(const e of t)null==e||e.deoptimizePath(F);else t.deoptimizePath(F)}this.context.requestTreeshakingPass()}}class St extends vt{deoptimizeThisOnInteractionAtPath(e,t,i){t.length>0&&this.argument.deoptimizeThisOnInteractionAtPath(e,[D,...t],i)}hasEffects(e){this.deoptimized||this.applyDeoptimizations();const{propertyReadSideEffects:t}=this.context.options.treeshake;return this.argument.hasEffects(e)||t&&("always"===t||this.argument.hasEffectsOnInteractionAtPath(F,Q,e))}applyDeoptimizations(){this.deoptimized=!0,this.argument.deoptimizePath([D,D]),this.context.requestTreeshakingPass()}}class At extends X{constructor(e){super(),this.description=e}deoptimizeThisOnInteractionAtPath({type:e,thisArg:t},i){2===e&&0===i.length&&this.description.mutatesSelfAsArray&&t.deoptimizePath(j)}getReturnExpressionWhenCalledAtPath(e,{thisArg:t}){return e.length>0?Y:this.description.returnsPrimitive||("self"===this.description.returns?t||Y:this.description.returns())}hasEffectsOnInteractionAtPath(e,t,i){var s,n;const{type:r}=t;if(e.length>(0===r?1:0))return!0;if(2===r){if(!0===this.description.mutatesSelfAsArray&&(null===(s=t.thisArg)||void 0===s?void 0:s.hasEffectsOnInteractionAtPath(j,J,i)))return!0;if(this.description.callsArgs)for(const e of this.description.callsArgs)if(null===(n=t.args[e])||void 0===n?void 0:n.hasEffectsOnInteractionAtPath(B,ee,i))return!0}return!1}}const It=[new At({callsArgs:null,mutatesSelfAsArray:!1,returns:null,returnsPrimitive:Fe})],Pt=[new At({callsArgs:null,mutatesSelfAsArray:!1,returns:null,returnsPrimitive:Ge})],kt=[new At({callsArgs:null,mutatesSelfAsArray:!1,returns:null,returnsPrimitive:je})],wt=[new At({callsArgs:null,mutatesSelfAsArray:!1,returns:null,returnsPrimitive:Y})],Ct=/^\d+$/;class Nt extends X{constructor(e,t,i=!1){if(super(),this.prototypeExpression=t,this.immutable=i,this.allProperties=[],this.deoptimizedPaths=Object.create(null),this.expressionsToBeDeoptimizedByKey=Object.create(null),this.gettersByKey=Object.create(null),this.hasLostTrack=!1,this.hasUnknownDeoptimizedInteger=!1,this.hasUnknownDeoptimizedProperty=!1,this.propertiesAndGettersByKey=Object.create(null),this.propertiesAndSettersByKey=Object.create(null),this.settersByKey=Object.create(null),this.thisParametersToBeDeoptimized=new Set,this.unknownIntegerProps=[],this.unmatchableGetters=[],this.unmatchablePropertiesAndGetters=[],this.unmatchableSetters=[],Array.isArray(e))this.buildPropertyMaps(e);else{this.propertiesAndGettersByKey=this.propertiesAndSettersByKey=e;for(const t of Object.values(e))this.allProperties.push(...t)}}deoptimizeAllProperties(e){var t;const i=this.hasLostTrack||this.hasUnknownDeoptimizedProperty;if(e?this.hasUnknownDeoptimizedProperty=!0:this.hasLostTrack=!0,!i){for(const e of Object.values(this.propertiesAndGettersByKey).concat(Object.values(this.settersByKey)))for(const t of e)t.deoptimizePath(F);null===(t=this.prototypeExpression)||void 0===t||t.deoptimizePath([D,D]),this.deoptimizeCachedEntities()}}deoptimizeIntegerProperties(){if(!(this.hasLostTrack||this.hasUnknownDeoptimizedProperty||this.hasUnknownDeoptimizedInteger)){this.hasUnknownDeoptimizedInteger=!0;for(const[e,t]of Object.entries(this.propertiesAndGettersByKey))if(Ct.test(e))for(const e of t)e.deoptimizePath(F);this.deoptimizeCachedIntegerEntities()}}deoptimizePath(e){var t;if(this.hasLostTrack||this.immutable)return;const i=e[0];if(1===e.length){if("string"!=typeof i)return i===V?this.deoptimizeIntegerProperties():this.deoptimizeAllProperties(i===L);if(!this.deoptimizedPaths[i]){this.deoptimizedPaths[i]=!0;const e=this.expressionsToBeDeoptimizedByKey[i];if(e)for(const t of e)t.deoptimizeCache()}}const s=1===e.length?F:e.slice(1);for(const e of"string"==typeof i?(this.propertiesAndGettersByKey[i]||this.unmatchablePropertiesAndGetters).concat(this.settersByKey[i]||this.unmatchableSetters):this.allProperties)e.deoptimizePath(s);null===(t=this.prototypeExpression)||void 0===t||t.deoptimizePath(1===e.length?[...e,D]:e)}deoptimizeThisOnInteractionAtPath(e,t,i){var s;const[n,...r]=t;if(this.hasLostTrack||(2===e.type||t.length>1)&&(this.hasUnknownDeoptimizedProperty||"string"==typeof n&&this.deoptimizedPaths[n]))return void e.thisArg.deoptimizePath(F);const[a,o,l]=2===e.type||t.length>1?[this.propertiesAndGettersByKey,this.propertiesAndGettersByKey,this.unmatchablePropertiesAndGetters]:0===e.type?[this.propertiesAndGettersByKey,this.gettersByKey,this.unmatchableGetters]:[this.propertiesAndSettersByKey,this.settersByKey,this.unmatchableSetters];if("string"==typeof n){if(a[n]){const t=o[n];if(t)for(const s of t)s.deoptimizeThisOnInteractionAtPath(e,r,i);return void(this.immutable||this.thisParametersToBeDeoptimized.add(e.thisArg))}for(const t of l)t.deoptimizeThisOnInteractionAtPath(e,r,i);if(Ct.test(n))for(const t of this.unknownIntegerProps)t.deoptimizeThisOnInteractionAtPath(e,r,i)}else{for(const t of Object.values(o).concat([l]))for(const s of t)s.deoptimizeThisOnInteractionAtPath(e,r,i);for(const t of this.unknownIntegerProps)t.deoptimizeThisOnInteractionAtPath(e,r,i)}this.immutable||this.thisParametersToBeDeoptimized.add(e.thisArg),null===(s=this.prototypeExpression)||void 0===s||s.deoptimizeThisOnInteractionAtPath(e,t,i)}getLiteralValueAtPath(e,t,i){if(0===e.length)return K;const s=e[0],n=this.getMemberExpressionAndTrackDeopt(s,i);return n?n.getLiteralValueAtPath(e.slice(1),t,i):this.prototypeExpression?this.prototypeExpression.getLiteralValueAtPath(e,t,i):1!==e.length?q:void 0}getReturnExpressionWhenCalledAtPath(e,t,i,s){if(0===e.length)return Y;const[n,...r]=e,a=this.getMemberExpressionAndTrackDeopt(n,s);return a?a.getReturnExpressionWhenCalledAtPath(r,t,i,s):this.prototypeExpression?this.prototypeExpression.getReturnExpressionWhenCalledAtPath(e,t,i,s):Y}hasEffectsOnInteractionAtPath(e,t,i){const[s,...n]=e;if(n.length||2===t.type){const r=this.getMemberExpression(s);return r?r.hasEffectsOnInteractionAtPath(n,t,i):!this.prototypeExpression||this.prototypeExpression.hasEffectsOnInteractionAtPath(e,t,i)}if(s===L)return!1;if(this.hasLostTrack)return!0;const[r,a,o]=0===t.type?[this.propertiesAndGettersByKey,this.gettersByKey,this.unmatchableGetters]:[this.propertiesAndSettersByKey,this.settersByKey,this.unmatchableSetters];if("string"==typeof s){if(r[s]){const e=a[s];if(e)for(const s of e)if(s.hasEffectsOnInteractionAtPath(n,t,i))return!0;return!1}for(const e of o)if(e.hasEffectsOnInteractionAtPath(n,t,i))return!0}else for(const e of Object.values(a).concat([o]))for(const s of e)if(s.hasEffectsOnInteractionAtPath(n,t,i))return!0;return!!this.prototypeExpression&&this.prototypeExpression.hasEffectsOnInteractionAtPath(e,t,i)}buildPropertyMaps(e){const{allProperties:t,propertiesAndGettersByKey:i,propertiesAndSettersByKey:s,settersByKey:n,gettersByKey:r,unknownIntegerProps:a,unmatchablePropertiesAndGetters:o,unmatchableGetters:l,unmatchableSetters:h}=this,c=[];for(let u=e.length-1;u>=0;u--){const{key:d,kind:p,property:f}=e[u];if(t.push(f),"string"!=typeof d){if(d===V){a.push(f);continue}"set"===p&&h.push(f),"get"===p&&l.push(f),"get"!==p&&c.push(f),"set"!==p&&o.push(f)}else"set"===p?s[d]||(s[d]=[f,...c],n[d]=[f,...h]):"get"===p?i[d]||(i[d]=[f,...o],r[d]=[f,...l]):(s[d]||(s[d]=[f,...c]),i[d]||(i[d]=[f,...o]))}}deoptimizeCachedEntities(){for(const e of Object.values(this.expressionsToBeDeoptimizedByKey))for(const t of e)t.deoptimizeCache();for(const e of this.thisParametersToBeDeoptimized)e.deoptimizePath(F)}deoptimizeCachedIntegerEntities(){for(const[e,t]of Object.entries(this.expressionsToBeDeoptimizedByKey))if(Ct.test(e))for(const e of t)e.deoptimizeCache();for(const e of this.thisParametersToBeDeoptimized)e.deoptimizePath(j)}getMemberExpression(e){if(this.hasLostTrack||this.hasUnknownDeoptimizedProperty||"string"!=typeof e||this.hasUnknownDeoptimizedInteger&&Ct.test(e)||this.deoptimizedPaths[e])return Y;const t=this.propertiesAndGettersByKey[e];return 1===(null==t?void 0:t.length)?t[0]:t||this.unmatchablePropertiesAndGetters.length>0||this.unknownIntegerProps.length&&Ct.test(e)?Y:null}getMemberExpressionAndTrackDeopt(e,t){if("string"!=typeof e)return Y;const i=this.getMemberExpression(e);return i===Y||this.immutable||(this.expressionsToBeDeoptimizedByKey[e]=this.expressionsToBeDeoptimizedByKey[e]||[]).push(t),i}}const _t=e=>"string"==typeof e&&/^\d+$/.test(e),$t=new class extends X{deoptimizeThisOnInteractionAtPath({type:e,thisArg:t},i){2!==e||1!==i.length||_t(i[0])||t.deoptimizePath(F)}getLiteralValueAtPath(e){return 1===e.length&&_t(e[0])?void 0:q}hasEffectsOnInteractionAtPath(e,{type:t}){return e.length>1||2===t}},Tt=new Nt({__proto__:null,hasOwnProperty:It,isPrototypeOf:It,propertyIsEnumerable:It,toLocaleString:Pt,toString:Pt,valueOf:wt},$t,!0),Ot=[{key:V,kind:"init",property:Y},{key:"length",kind:"init",property:je}],Rt=[new At({callsArgs:[0],mutatesSelfAsArray:"deopt-only",returns:null,returnsPrimitive:Fe})],Mt=[new At({callsArgs:[0],mutatesSelfAsArray:"deopt-only",returns:null,returnsPrimitive:je})],Dt=[new At({callsArgs:null,mutatesSelfAsArray:!0,returns:()=>new Nt(Ot,Ht),returnsPrimitive:null})],Lt=[new At({callsArgs:null,mutatesSelfAsArray:"deopt-only",returns:()=>new Nt(Ot,Ht),returnsPrimitive:null})],Vt=[new At({callsArgs:[0],mutatesSelfAsArray:"deopt-only",returns:()=>new Nt(Ot,Ht),returnsPrimitive:null})],Bt=[new At({callsArgs:null,mutatesSelfAsArray:!0,returns:null,returnsPrimitive:je})],Ft=[new At({callsArgs:null,mutatesSelfAsArray:!0,returns:null,returnsPrimitive:Y})],zt=[new At({callsArgs:null,mutatesSelfAsArray:"deopt-only",returns:null,returnsPrimitive:Y})],jt=[new At({callsArgs:[0],mutatesSelfAsArray:"deopt-only",returns:null,returnsPrimitive:Y})],Ut=[new At({callsArgs:null,mutatesSelfAsArray:!0,returns:"self",returnsPrimitive:null})],Gt=[new At({callsArgs:[0],mutatesSelfAsArray:!0,returns:"self",returnsPrimitive:null})],Ht=new Nt({__proto__:null,at:zt,concat:Lt,copyWithin:Ut,entries:Lt,every:Rt,fill:Ut,filter:Vt,find:jt,findIndex:Mt,findLast:jt,findLastIndex:Mt,flat:Lt,flatMap:Vt,forEach:jt,group:jt,groupToMap:jt,includes:It,indexOf:kt,join:Pt,keys:wt,lastIndexOf:kt,map:Vt,pop:Ft,push:Bt,reduce:jt,reduceRight:jt,reverse:Ut,shift:Ft,slice:Lt,some:Rt,sort:Gt,splice:Dt,toLocaleString:Pt,toString:Pt,unshift:Bt,values:zt},Tt,!0);class Wt extends te{constructor(e,t,i,s){super(e),this.calledFromTryStatement=!1,this.additionalInitializers=null,this.expressionsToBeDeoptimized=[],this.declarations=t?[t]:[],this.init=i,this.deoptimizationTracker=s.deoptimizationTracker,this.module=s.module}addDeclaration(e,t){this.declarations.push(e);const i=this.markInitializersForDeoptimization();null!==t&&i.push(t)}consolidateInitializers(){if(null!==this.additionalInitializers){for(const e of this.additionalInitializers)e.deoptimizePath(F);this.additionalInitializers=null}}deoptimizePath(e){var t,i;if(!this.isReassigned&&!this.deoptimizationTracker.trackEntityAtPathAndGetIfTracked(e,this))if(0===e.length){if(!this.isReassigned){this.isReassigned=!0;const e=this.expressionsToBeDeoptimized;this.expressionsToBeDeoptimized=[];for(const t of e)t.deoptimizeCache();null===(t=this.init)||void 0===t||t.deoptimizePath(F)}}else null===(i=this.init)||void 0===i||i.deoptimizePath(e)}deoptimizeThisOnInteractionAtPath(e,t,i){if(this.isReassigned||!this.init)return e.thisArg.deoptimizePath(F);i.withTrackedEntityAtPath(t,this.init,(()=>this.init.deoptimizeThisOnInteractionAtPath(e,t,i)),void 0)}getLiteralValueAtPath(e,t,i){return this.isReassigned||!this.init?q:t.withTrackedEntityAtPath(e,this.init,(()=>(this.expressionsToBeDeoptimized.push(i),this.init.getLiteralValueAtPath(e,t,i))),q)}getReturnExpressionWhenCalledAtPath(e,t,i,s){return this.isReassigned||!this.init?Y:i.withTrackedEntityAtPath(e,this.init,(()=>(this.expressionsToBeDeoptimized.push(s),this.init.getReturnExpressionWhenCalledAtPath(e,t,i,s))),Y)}hasEffectsOnInteractionAtPath(e,t,i){switch(t.type){case 0:return!!this.isReassigned||this.init&&!i.accessed.trackEntityAtPathAndGetIfTracked(e,this)&&this.init.hasEffectsOnInteractionAtPath(e,t,i);case 1:return!!this.included||0!==e.length&&(!!this.isReassigned||this.init&&!i.assigned.trackEntityAtPathAndGetIfTracked(e,this)&&this.init.hasEffectsOnInteractionAtPath(e,t,i));case 2:return!!this.isReassigned||this.init&&!(t.withNew?i.instantiated:i.called).trackEntityAtPathAndGetIfTracked(e,t.args,this)&&this.init.hasEffectsOnInteractionAtPath(e,t,i)}}include(){if(!this.included){this.included=!0;for(const e of this.declarations){e.included||e.include(Me(),!1);let t=e.parent;for(;!t.included&&(t.included=!0,t.type!==ot);)t=t.parent}}}includeCallArguments(e,t){if(this.isReassigned||this.init&&e.includedCallArguments.has(this.init))for(const i of t)i.include(e,!1);else this.init&&(e.includedCallArguments.add(this.init),this.init.includeCallArguments(e,t),e.includedCallArguments.delete(this.init))}markCalledFromTryStatement(){this.calledFromTryStatement=!0}markInitializersForDeoptimization(){return null===this.additionalInitializers&&(this.additionalInitializers=null===this.init?[]:[this.init],this.init=Y,this.isReassigned=!0),this.additionalInitializers}}function qt(e){let t="";do{const i=e%64;e=Math.floor(e/64),t="0123456789abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ_$"[i]+t}while(0!==e);return t}function Kt(e,t){let i=e,s=1;for(;t.has(i)||Ce.has(i);)i=`${e}$${qt(s++)}`;return t.add(i),i}class Xt{constructor(){this.children=[],this.variables=new Map}addDeclaration(e,t,i,s){const n=e.name;let r=this.variables.get(n);return r?r.addDeclaration(e,i):(r=new Wt(e.name,e,i||Ve,t),this.variables.set(n,r)),r}contains(e){return this.variables.has(e)}findVariable(e){throw new Error("Internal Error: findVariable needs to be implemented by a subclass")}}class Yt extends Xt{constructor(e){super(),this.accessedOutsideVariables=new Map,this.parent=e,e.children.push(this)}addAccessedDynamicImport(e){(this.accessedDynamicImports||(this.accessedDynamicImports=new Set)).add(e),this.parent instanceof Yt&&this.parent.addAccessedDynamicImport(e)}addAccessedGlobals(e,t){const i=t.get(this)||new Set;for(const t of e)i.add(t);t.set(this,i),this.parent instanceof Yt&&this.parent.addAccessedGlobals(e,t)}addNamespaceMemberAccess(e,t){this.accessedOutsideVariables.set(e,t),this.parent.addNamespaceMemberAccess(e,t)}addReturnExpression(e){this.parent instanceof Yt&&this.parent.addReturnExpression(e)}addUsedOutsideNames(e,t,i,s){for(const s of this.accessedOutsideVariables.values())s.included&&(e.add(s.getBaseVariableName()),"system"===t&&i.has(s)&&e.add("exports"));const n=s.get(this);if(n)for(const t of n)e.add(t)}contains(e){return this.variables.has(e)||this.parent.contains(e)}deconflict(e,t,i){const s=new Set;if(this.addUsedOutsideNames(s,e,t,i),this.accessedDynamicImports)for(const e of this.accessedDynamicImports)e.inlineNamespace&&s.add(e.inlineNamespace.getBaseVariableName());for(const[e,t]of this.variables)(t.included||t.alwaysRendered)&&t.setRenderNames(null,Kt(e,s));for(const s of this.children)s.deconflict(e,t,i)}findLexicalBoundary(){return this.parent.findLexicalBoundary()}findVariable(e){const t=this.variables.get(e)||this.accessedOutsideVariables.get(e);if(t)return t;const i=this.parent.findVariable(e);return this.accessedOutsideVariables.set(e,i),i}}class Qt extends Yt{constructor(e,t){super(e),this.parameters=[],this.hasRest=!1,this.context=t,this.hoistedBodyVarScope=new Yt(this)}addParameterDeclaration(e){const t=e.name;let i=this.hoistedBodyVarScope.variables.get(t);return i?i.addDeclaration(e,null):i=new Wt(t,e,Y,this.context),this.variables.set(t,i),i}addParameterVariables(e,t){this.parameters=e;for(const t of e)for(const e of t)e.alwaysRendered=!0;this.hasRest=t}includeCallArguments(e,t){let i=!1,s=!1;const n=this.hasRest&&this.parameters[this.parameters.length-1];for(const i of t)if(i instanceof St){for(const i of t)i.include(e,!1);break}for(let r=t.length-1;r>=0;r--){const a=this.parameters[r]||n,o=t[r];if(a)if(i=!1,0===a.length)s=!0;else for(const e of a)e.included&&(s=!0),e.calledFromTryStatement&&(i=!0);!s&&o.shouldBeIncluded(e)&&(s=!0),s&&o.include(e,i)}}}class Jt extends Qt{constructor(){super(...arguments),this.returnExpression=null,this.returnExpressions=[]}addReturnExpression(e){this.returnExpressions.push(e)}getReturnExpression(){return null===this.returnExpression&&this.updateReturnExpression(),this.returnExpression}updateReturnExpression(){if(1===this.returnExpressions.length)this.returnExpression=this.returnExpressions[0];else{this.returnExpression=Y;for(const e of this.returnExpressions)e.deoptimizePath(F)}}}function Zt(e,t){if("MemberExpression"===e.type)return!e.computed&&Zt(e.object,e);if("Identifier"===e.type){if(!t)return!0;switch(t.type){case"MemberExpression":return t.computed||e===t.object;case"MethodDefinition":return t.computed;case"PropertyDefinition":case"Property":return t.computed||e===t.value;case"ExportSpecifier":case"ImportSpecifier":return e===t.local;case"LabeledStatement":case"BreakStatement":case"ContinueStatement":return!1;default:return!0}}return!1}const ei=Symbol("Value Properties"),ti={hasEffectsWhenCalled:()=>!1},ii={hasEffectsWhenCalled:()=>!0},si={__proto__:null,[ei]:ii},ni={__proto__:null,[ei]:ti},ri={__proto__:null,[ei]:{hasEffectsWhenCalled:({args:e},t)=>!e.length||e[0].hasEffectsOnInteractionAtPath(z,J,t)}},ai={__proto__:null,[ei]:ii,prototype:si},oi={__proto__:null,[ei]:ti,prototype:si},li={__proto__:null,[ei]:ti,from:ni,of:ni,prototype:si},hi={__proto__:null,[ei]:ti,supportedLocalesOf:oi},ci={global:si,globalThis:si,self:si,window:si,__proto__:null,[ei]:ii,Array:{__proto__:null,[ei]:ii,from:si,isArray:ni,of:ni,prototype:si},ArrayBuffer:{__proto__:null,[ei]:ti,isView:ni,prototype:si},Atomics:si,BigInt:ai,BigInt64Array:ai,BigUint64Array:ai,Boolean:oi,constructor:ai,DataView:oi,Date:{__proto__:null,[ei]:ti,now:ni,parse:ni,prototype:si,UTC:ni},decodeURI:ni,decodeURIComponent:ni,encodeURI:ni,encodeURIComponent:ni,Error:oi,escape:ni,eval:si,EvalError:oi,Float32Array:li,Float64Array:li,Function:ai,hasOwnProperty:si,Infinity:si,Int16Array:li,Int32Array:li,Int8Array:li,isFinite:ni,isNaN:ni,isPrototypeOf:si,JSON:si,Map:oi,Math:{__proto__:null,[ei]:ii,abs:ni,acos:ni,acosh:ni,asin:ni,asinh:ni,atan:ni,atan2:ni,atanh:ni,cbrt:ni,ceil:ni,clz32:ni,cos:ni,cosh:ni,exp:ni,expm1:ni,floor:ni,fround:ni,hypot:ni,imul:ni,log:ni,log10:ni,log1p:ni,log2:ni,max:ni,min:ni,pow:ni,random:ni,round:ni,sign:ni,sin:ni,sinh:ni,sqrt:ni,tan:ni,tanh:ni,trunc:ni},NaN:si,Number:{__proto__:null,[ei]:ti,isFinite:ni,isInteger:ni,isNaN:ni,isSafeInteger:ni,parseFloat:ni,parseInt:ni,prototype:si},Object:{__proto__:null,[ei]:ti,create:ni,defineProperty:ri,defineProperties:ri,getOwnPropertyDescriptor:ni,getOwnPropertyNames:ni,getOwnPropertySymbols:ni,getPrototypeOf:ni,hasOwn:ni,is:ni,isExtensible:ni,isFrozen:ni,isSealed:ni,keys:ni,fromEntries:ni,entries:ni,prototype:si},parseFloat:ni,parseInt:ni,Promise:{__proto__:null,[ei]:ii,all:si,prototype:si,race:si,reject:si,resolve:si},propertyIsEnumerable:si,Proxy:si,RangeError:oi,ReferenceError:oi,Reflect:si,RegExp:oi,Set:oi,SharedArrayBuffer:ai,String:{__proto__:null,[ei]:ti,fromCharCode:ni,fromCodePoint:ni,prototype:si,raw:ni},Symbol:{__proto__:null,[ei]:ti,for:ni,keyFor:ni,prototype:si},SyntaxError:oi,toLocaleString:si,toString:si,TypeError:oi,Uint16Array:li,Uint32Array:li,Uint8Array:li,Uint8ClampedArray:li,unescape:ni,URIError:oi,valueOf:si,WeakMap:oi,WeakSet:oi,clearInterval:ai,clearTimeout:ai,console:si,Intl:{__proto__:null,[ei]:ii,Collator:hi,DateTimeFormat:hi,ListFormat:hi,NumberFormat:hi,PluralRules:hi,RelativeTimeFormat:hi},setInterval:ai,setTimeout:ai,TextDecoder:ai,TextEncoder:ai,URL:ai,URLSearchParams:ai,AbortController:ai,AbortSignal:ai,addEventListener:si,alert:si,AnalyserNode:ai,Animation:ai,AnimationEvent:ai,applicationCache:si,ApplicationCache:ai,ApplicationCacheErrorEvent:ai,atob:si,Attr:ai,Audio:ai,AudioBuffer:ai,AudioBufferSourceNode:ai,AudioContext:ai,AudioDestinationNode:ai,AudioListener:ai,AudioNode:ai,AudioParam:ai,AudioProcessingEvent:ai,AudioScheduledSourceNode:ai,AudioWorkletNode:ai,BarProp:ai,BaseAudioContext:ai,BatteryManager:ai,BeforeUnloadEvent:ai,BiquadFilterNode:ai,Blob:ai,BlobEvent:ai,blur:si,BroadcastChannel:ai,btoa:si,ByteLengthQueuingStrategy:ai,Cache:ai,caches:si,CacheStorage:ai,cancelAnimationFrame:si,cancelIdleCallback:si,CanvasCaptureMediaStreamTrack:ai,CanvasGradient:ai,CanvasPattern:ai,CanvasRenderingContext2D:ai,ChannelMergerNode:ai,ChannelSplitterNode:ai,CharacterData:ai,clientInformation:si,ClipboardEvent:ai,close:si,closed:si,CloseEvent:ai,Comment:ai,CompositionEvent:ai,confirm:si,ConstantSourceNode:ai,ConvolverNode:ai,CountQueuingStrategy:ai,createImageBitmap:si,Credential:ai,CredentialsContainer:ai,crypto:si,Crypto:ai,CryptoKey:ai,CSS:ai,CSSConditionRule:ai,CSSFontFaceRule:ai,CSSGroupingRule:ai,CSSImportRule:ai,CSSKeyframeRule:ai,CSSKeyframesRule:ai,CSSMediaRule:ai,CSSNamespaceRule:ai,CSSPageRule:ai,CSSRule:ai,CSSRuleList:ai,CSSStyleDeclaration:ai,CSSStyleRule:ai,CSSStyleSheet:ai,CSSSupportsRule:ai,CustomElementRegistry:ai,customElements:si,CustomEvent:ai,DataTransfer:ai,DataTransferItem:ai,DataTransferItemList:ai,defaultstatus:si,defaultStatus:si,DelayNode:ai,DeviceMotionEvent:ai,DeviceOrientationEvent:ai,devicePixelRatio:si,dispatchEvent:si,document:si,Document:ai,DocumentFragment:ai,DocumentType:ai,DOMError:ai,DOMException:ai,DOMImplementation:ai,DOMMatrix:ai,DOMMatrixReadOnly:ai,DOMParser:ai,DOMPoint:ai,DOMPointReadOnly:ai,DOMQuad:ai,DOMRect:ai,DOMRectReadOnly:ai,DOMStringList:ai,DOMStringMap:ai,DOMTokenList:ai,DragEvent:ai,DynamicsCompressorNode:ai,Element:ai,ErrorEvent:ai,Event:ai,EventSource:ai,EventTarget:ai,external:si,fetch:si,File:ai,FileList:ai,FileReader:ai,find:si,focus:si,FocusEvent:ai,FontFace:ai,FontFaceSetLoadEvent:ai,FormData:ai,frames:si,GainNode:ai,Gamepad:ai,GamepadButton:ai,GamepadEvent:ai,getComputedStyle:si,getSelection:si,HashChangeEvent:ai,Headers:ai,history:si,History:ai,HTMLAllCollection:ai,HTMLAnchorElement:ai,HTMLAreaElement:ai,HTMLAudioElement:ai,HTMLBaseElement:ai,HTMLBodyElement:ai,HTMLBRElement:ai,HTMLButtonElement:ai,HTMLCanvasElement:ai,HTMLCollection:ai,HTMLContentElement:ai,HTMLDataElement:ai,HTMLDataListElement:ai,HTMLDetailsElement:ai,HTMLDialogElement:ai,HTMLDirectoryElement:ai,HTMLDivElement:ai,HTMLDListElement:ai,HTMLDocument:ai,HTMLElement:ai,HTMLEmbedElement:ai,HTMLFieldSetElement:ai,HTMLFontElement:ai,HTMLFormControlsCollection:ai,HTMLFormElement:ai,HTMLFrameElement:ai,HTMLFrameSetElement:ai,HTMLHeadElement:ai,HTMLHeadingElement:ai,HTMLHRElement:ai,HTMLHtmlElement:ai,HTMLIFrameElement:ai,HTMLImageElement:ai,HTMLInputElement:ai,HTMLLabelElement:ai,HTMLLegendElement:ai,HTMLLIElement:ai,HTMLLinkElement:ai,HTMLMapElement:ai,HTMLMarqueeElement:ai,HTMLMediaElement:ai,HTMLMenuElement:ai,HTMLMetaElement:ai,HTMLMeterElement:ai,HTMLModElement:ai,HTMLObjectElement:ai,HTMLOListElement:ai,HTMLOptGroupElement:ai,HTMLOptionElement:ai,HTMLOptionsCollection:ai,HTMLOutputElement:ai,HTMLParagraphElement:ai,HTMLParamElement:ai,HTMLPictureElement:ai,HTMLPreElement:ai,HTMLProgressElement:ai,HTMLQuoteElement:ai,HTMLScriptElement:ai,HTMLSelectElement:ai,HTMLShadowElement:ai,HTMLSlotElement:ai,HTMLSourceElement:ai,HTMLSpanElement:ai,HTMLStyleElement:ai,HTMLTableCaptionElement:ai,HTMLTableCellElement:ai,HTMLTableColElement:ai,HTMLTableElement:ai,HTMLTableRowElement:ai,HTMLTableSectionElement:ai,HTMLTemplateElement:ai,HTMLTextAreaElement:ai,HTMLTimeElement:ai,HTMLTitleElement:ai,HTMLTrackElement:ai,HTMLUListElement:ai,HTMLUnknownElement:ai,HTMLVideoElement:ai,IDBCursor:ai,IDBCursorWithValue:ai,IDBDatabase:ai,IDBFactory:ai,IDBIndex:ai,IDBKeyRange:ai,IDBObjectStore:ai,IDBOpenDBRequest:ai,IDBRequest:ai,IDBTransaction:ai,IDBVersionChangeEvent:ai,IdleDeadline:ai,IIRFilterNode:ai,Image:ai,ImageBitmap:ai,ImageBitmapRenderingContext:ai,ImageCapture:ai,ImageData:ai,indexedDB:si,innerHeight:si,innerWidth:si,InputEvent:ai,IntersectionObserver:ai,IntersectionObserverEntry:ai,isSecureContext:si,KeyboardEvent:ai,KeyframeEffect:ai,length:si,localStorage:si,location:si,Location:ai,locationbar:si,matchMedia:si,MediaDeviceInfo:ai,MediaDevices:ai,MediaElementAudioSourceNode:ai,MediaEncryptedEvent:ai,MediaError:ai,MediaKeyMessageEvent:ai,MediaKeySession:ai,MediaKeyStatusMap:ai,MediaKeySystemAccess:ai,MediaList:ai,MediaQueryList:ai,MediaQueryListEvent:ai,MediaRecorder:ai,MediaSettingsRange:ai,MediaSource:ai,MediaStream:ai,MediaStreamAudioDestinationNode:ai,MediaStreamAudioSourceNode:ai,MediaStreamEvent:ai,MediaStreamTrack:ai,MediaStreamTrackEvent:ai,menubar:si,MessageChannel:ai,MessageEvent:ai,MessagePort:ai,MIDIAccess:ai,MIDIConnectionEvent:ai,MIDIInput:ai,MIDIInputMap:ai,MIDIMessageEvent:ai,MIDIOutput:ai,MIDIOutputMap:ai,MIDIPort:ai,MimeType:ai,MimeTypeArray:ai,MouseEvent:ai,moveBy:si,moveTo:si,MutationEvent:ai,MutationObserver:ai,MutationRecord:ai,name:si,NamedNodeMap:ai,NavigationPreloadManager:ai,navigator:si,Navigator:ai,NetworkInformation:ai,Node:ai,NodeFilter:si,NodeIterator:ai,NodeList:ai,Notification:ai,OfflineAudioCompletionEvent:ai,OfflineAudioContext:ai,offscreenBuffering:si,OffscreenCanvas:ai,open:si,openDatabase:si,Option:ai,origin:si,OscillatorNode:ai,outerHeight:si,outerWidth:si,PageTransitionEvent:ai,pageXOffset:si,pageYOffset:si,PannerNode:ai,parent:si,Path2D:ai,PaymentAddress:ai,PaymentRequest:ai,PaymentRequestUpdateEvent:ai,PaymentResponse:ai,performance:si,Performance:ai,PerformanceEntry:ai,PerformanceLongTaskTiming:ai,PerformanceMark:ai,PerformanceMeasure:ai,PerformanceNavigation:ai,PerformanceNavigationTiming:ai,PerformanceObserver:ai,PerformanceObserverEntryList:ai,PerformancePaintTiming:ai,PerformanceResourceTiming:ai,PerformanceTiming:ai,PeriodicWave:ai,Permissions:ai,PermissionStatus:ai,personalbar:si,PhotoCapabilities:ai,Plugin:ai,PluginArray:ai,PointerEvent:ai,PopStateEvent:ai,postMessage:si,Presentation:ai,PresentationAvailability:ai,PresentationConnection:ai,PresentationConnectionAvailableEvent:ai,PresentationConnectionCloseEvent:ai,PresentationConnectionList:ai,PresentationReceiver:ai,PresentationRequest:ai,print:si,ProcessingInstruction:ai,ProgressEvent:ai,PromiseRejectionEvent:ai,prompt:si,PushManager:ai,PushSubscription:ai,PushSubscriptionOptions:ai,queueMicrotask:si,RadioNodeList:ai,Range:ai,ReadableStream:ai,RemotePlayback:ai,removeEventListener:si,Request:ai,requestAnimationFrame:si,requestIdleCallback:si,resizeBy:si,ResizeObserver:ai,ResizeObserverEntry:ai,resizeTo:si,Response:ai,RTCCertificate:ai,RTCDataChannel:ai,RTCDataChannelEvent:ai,RTCDtlsTransport:ai,RTCIceCandidate:ai,RTCIceTransport:ai,RTCPeerConnection:ai,RTCPeerConnectionIceEvent:ai,RTCRtpReceiver:ai,RTCRtpSender:ai,RTCSctpTransport:ai,RTCSessionDescription:ai,RTCStatsReport:ai,RTCTrackEvent:ai,screen:si,Screen:ai,screenLeft:si,ScreenOrientation:ai,screenTop:si,screenX:si,screenY:si,ScriptProcessorNode:ai,scroll:si,scrollbars:si,scrollBy:si,scrollTo:si,scrollX:si,scrollY:si,SecurityPolicyViolationEvent:ai,Selection:ai,ServiceWorker:ai,ServiceWorkerContainer:ai,ServiceWorkerRegistration:ai,sessionStorage:si,ShadowRoot:ai,SharedWorker:ai,SourceBuffer:ai,SourceBufferList:ai,speechSynthesis:si,SpeechSynthesisEvent:ai,SpeechSynthesisUtterance:ai,StaticRange:ai,status:si,statusbar:si,StereoPannerNode:ai,stop:si,Storage:ai,StorageEvent:ai,StorageManager:ai,styleMedia:si,StyleSheet:ai,StyleSheetList:ai,SubtleCrypto:ai,SVGAElement:ai,SVGAngle:ai,SVGAnimatedAngle:ai,SVGAnimatedBoolean:ai,SVGAnimatedEnumeration:ai,SVGAnimatedInteger:ai,SVGAnimatedLength:ai,SVGAnimatedLengthList:ai,SVGAnimatedNumber:ai,SVGAnimatedNumberList:ai,SVGAnimatedPreserveAspectRatio:ai,SVGAnimatedRect:ai,SVGAnimatedString:ai,SVGAnimatedTransformList:ai,SVGAnimateElement:ai,SVGAnimateMotionElement:ai,SVGAnimateTransformElement:ai,SVGAnimationElement:ai,SVGCircleElement:ai,SVGClipPathElement:ai,SVGComponentTransferFunctionElement:ai,SVGDefsElement:ai,SVGDescElement:ai,SVGDiscardElement:ai,SVGElement:ai,SVGEllipseElement:ai,SVGFEBlendElement:ai,SVGFEColorMatrixElement:ai,SVGFEComponentTransferElement:ai,SVGFECompositeElement:ai,SVGFEConvolveMatrixElement:ai,SVGFEDiffuseLightingElement:ai,SVGFEDisplacementMapElement:ai,SVGFEDistantLightElement:ai,SVGFEDropShadowElement:ai,SVGFEFloodElement:ai,SVGFEFuncAElement:ai,SVGFEFuncBElement:ai,SVGFEFuncGElement:ai,SVGFEFuncRElement:ai,SVGFEGaussianBlurElement:ai,SVGFEImageElement:ai,SVGFEMergeElement:ai,SVGFEMergeNodeElement:ai,SVGFEMorphologyElement:ai,SVGFEOffsetElement:ai,SVGFEPointLightElement:ai,SVGFESpecularLightingElement:ai,SVGFESpotLightElement:ai,SVGFETileElement:ai,SVGFETurbulenceElement:ai,SVGFilterElement:ai,SVGForeignObjectElement:ai,SVGGElement:ai,SVGGeometryElement:ai,SVGGradientElement:ai,SVGGraphicsElement:ai,SVGImageElement:ai,SVGLength:ai,SVGLengthList:ai,SVGLinearGradientElement:ai,SVGLineElement:ai,SVGMarkerElement:ai,SVGMaskElement:ai,SVGMatrix:ai,SVGMetadataElement:ai,SVGMPathElement:ai,SVGNumber:ai,SVGNumberList:ai,SVGPathElement:ai,SVGPatternElement:ai,SVGPoint:ai,SVGPointList:ai,SVGPolygonElement:ai,SVGPolylineElement:ai,SVGPreserveAspectRatio:ai,SVGRadialGradientElement:ai,SVGRect:ai,SVGRectElement:ai,SVGScriptElement:ai,SVGSetElement:ai,SVGStopElement:ai,SVGStringList:ai,SVGStyleElement:ai,SVGSVGElement:ai,SVGSwitchElement:ai,SVGSymbolElement:ai,SVGTextContentElement:ai,SVGTextElement:ai,SVGTextPathElement:ai,SVGTextPositioningElement:ai,SVGTitleElement:ai,SVGTransform:ai,SVGTransformList:ai,SVGTSpanElement:ai,SVGUnitTypes:ai,SVGUseElement:ai,SVGViewElement:ai,TaskAttributionTiming:ai,Text:ai,TextEvent:ai,TextMetrics:ai,TextTrack:ai,TextTrackCue:ai,TextTrackCueList:ai,TextTrackList:ai,TimeRanges:ai,toolbar:si,top:si,Touch:ai,TouchEvent:ai,TouchList:ai,TrackEvent:ai,TransitionEvent:ai,TreeWalker:ai,UIEvent:ai,ValidityState:ai,visualViewport:si,VisualViewport:ai,VTTCue:ai,WaveShaperNode:ai,WebAssembly:si,WebGL2RenderingContext:ai,WebGLActiveInfo:ai,WebGLBuffer:ai,WebGLContextEvent:ai,WebGLFramebuffer:ai,WebGLProgram:ai,WebGLQuery:ai,WebGLRenderbuffer:ai,WebGLRenderingContext:ai,WebGLSampler:ai,WebGLShader:ai,WebGLShaderPrecisionFormat:ai,WebGLSync:ai,WebGLTexture:ai,WebGLTransformFeedback:ai,WebGLUniformLocation:ai,WebGLVertexArrayObject:ai,WebSocket:ai,WheelEvent:ai,Window:ai,Worker:ai,WritableStream:ai,XMLDocument:ai,XMLHttpRequest:ai,XMLHttpRequestEventTarget:ai,XMLHttpRequestUpload:ai,XMLSerializer:ai,XPathEvaluator:ai,XPathExpression:ai,XPathResult:ai,XSLTProcessor:ai};for(const e of["window","global","self","globalThis"])ci[e]=ci;function ui(e){let t=ci;for(const i of e){if("string"!=typeof i)return null;if(t=t[i],!t)return null}return t[ei]}class di extends te{constructor(){super(...arguments),this.isReassigned=!0}getLiteralValueAtPath(e,t,i){return ui([this.name,...e])?K:q}hasEffectsOnInteractionAtPath(e,t,i){switch(t.type){case 0:return 0===e.length?"undefined"!==this.name&&!ui([this.name]):!ui([this.name,...e].slice(0,-1));case 1:return!0;case 2:{const s=ui([this.name,...e]);return!s||s.hasEffectsWhenCalled(t,i)}}}}const pi={__proto__:null,class:!0,const:!0,let:!0,var:!0};class fi extends vt{constructor(){super(...arguments),this.variable=null,this.isTDZAccess=null}addExportedVariables(e,t){t.has(this.variable)&&e.push(this.variable)}bind(){!this.variable&&Zt(this,this.parent)&&(this.variable=this.scope.findVariable(this.name),this.variable.addReference(this))}declare(e,t){let i;const{treeshake:s}=this.context.options;switch(e){case"var":i=this.scope.addDeclaration(this,this.context,t,!0),s&&s.correctVarValueBeforeDeclaration&&i.markInitializersForDeoptimization();break;case"function":case"let":case"const":case"class":i=this.scope.addDeclaration(this,this.context,t,!1);break;case"parameter":i=this.scope.addParameterDeclaration(this);break;default:throw new Error(`Internal Error: Unexpected identifier kind ${e}.`)}return i.kind=e,[this.variable=i]}deoptimizePath(e){var t;0!==e.length||this.scope.contains(this.name)||this.disallowImportReassignment(),null===(t=this.variable)||void 0===t||t.deoptimizePath(e)}deoptimizeThisOnInteractionAtPath(e,t,i){this.variable.deoptimizeThisOnInteractionAtPath(e,t,i)}getLiteralValueAtPath(e,t,i){return this.getVariableRespectingTDZ().getLiteralValueAtPath(e,t,i)}getReturnExpressionWhenCalledAtPath(e,t,i,s){return this.getVariableRespectingTDZ().getReturnExpressionWhenCalledAtPath(e,t,i,s)}hasEffects(e){return this.deoptimized||this.applyDeoptimizations(),!(!this.isPossibleTDZ()||"var"===this.variable.kind)||this.context.options.treeshake.unknownGlobalSideEffects&&this.variable instanceof di&&this.variable.hasEffectsOnInteractionAtPath(B,Q,e)}hasEffectsOnInteractionAtPath(e,t,i){switch(t.type){case 0:return null!==this.variable&&this.getVariableRespectingTDZ().hasEffectsOnInteractionAtPath(e,t,i);case 1:return(e.length>0?this.getVariableRespectingTDZ():this.variable).hasEffectsOnInteractionAtPath(e,t,i);case 2:return this.getVariableRespectingTDZ().hasEffectsOnInteractionAtPath(e,t,i)}}include(){this.deoptimized||this.applyDeoptimizations(),this.included||(this.included=!0,null!==this.variable&&this.context.includeVariableInModule(this.variable))}includeCallArguments(e,t){this.variable.includeCallArguments(e,t)}isPossibleTDZ(){if(null!==this.isTDZAccess)return this.isTDZAccess;if(!(this.variable instanceof Wt&&this.variable.kind&&this.variable.kind in pi))return this.isTDZAccess=!1;let e;return this.variable.declarations&&1===this.variable.declarations.length&&(e=this.variable.declarations[0])&&this.start<e.start&&mi(this)===mi(e)?this.isTDZAccess=!0:this.variable.initReached?this.isTDZAccess=!1:this.isTDZAccess=!0}markDeclarationReached(){this.variable.initReached=!0}render(e,{snippets:{getPropertyAccess:t}},{renderedParentType:i,isCalleeOfRenderedParent:s,isShorthandProperty:n}=se){if(this.variable){const r=this.variable.getName(t);r!==this.name&&(e.overwrite(this.start,this.end,r,{contentOnly:!0,storeName:!0}),n&&e.prependRight(this.start,`${this.name}: `)),"eval"===r&&i===nt&&s&&e.appendRight(this.start,"0, ")}}applyDeoptimizations(){this.deoptimized=!0,this.variable instanceof Wt&&(this.variable.consolidateInitializers(),this.context.requestTreeshakingPass())}disallowImportReassignment(){return this.context.error({code:"ILLEGAL_REASSIGNMENT",message:`Illegal reassignment to import '${this.name}'`},this.start)}getVariableRespectingTDZ(){return this.isPossibleTDZ()?Y:this.variable}}function mi(e){for(;e&&!/^Program|Function/.test(e.type);)e=e.parent;return e}function gi(e,t,i,s){if(t.remove(i,s),e.annotations)for(const s of e.annotations){if(!(s.start<i))return;t.remove(s.start,s.end)}}function yi(e,t){if(e.annotations||e.parent.type!==rt||(e=e.parent),e.annotations)for(const i of e.annotations)t.remove(i.start,i.end)}const xi={isNoStatement:!0};function Ei(e,t,i=0){let s,n;for(s=e.indexOf(t,i);;){if(-1===(i=e.indexOf("/",i))||i>=s)return s;n=e.charCodeAt(++i),++i,(i=47===n?e.indexOf("\n",i)+1:e.indexOf("*/",i)+2)>s&&(s=e.indexOf(t,i))}}const bi=/\S/g;function vi(e,t){return bi.lastIndex=t,bi.exec(e).index}function Si(e){let t,i,s=0;for(t=e.indexOf("\n",s);;){if(s=e.indexOf("/",s),-1===s||s>t)return[t,t+1];if(i=e.charCodeAt(s+1),47===i)return[s,t+1];s=e.indexOf("*/",s+3)+2,s>t&&(t=e.indexOf("\n",s))}}function Ai(e,t,i,s,n){let r,a,o,l,h=e[0],c=!h.included||h.needsBoundaries;c&&(l=i+Si(t.original.slice(i,h.start))[1]);for(let i=1;i<=e.length;i++)r=h,a=l,o=c,h=e[i],c=void 0!==h&&(!h.included||h.needsBoundaries),o||c?(l=r.end+Si(t.original.slice(r.end,void 0===h?s:h.start))[1],r.included?o?r.render(t,n,{end:l,start:a}):r.render(t,n):gi(r,t,a,l)):r.render(t,n)}function Ii(e,t,i,s){const n=[];let r,a,o,l,h,c=i-1;for(let s=0;s<e.length;s++){for(a=e[s],void 0!==r&&(c=r.end+Ei(t.original.slice(r.end,a.start),",")),o=l=c+1+Si(t.original.slice(c+1,a.start))[1];h=t.original.charCodeAt(o),32===h||9===h||10===h||13===h;)o++;void 0!==r&&n.push({contentEnd:l,end:o,node:r,separator:c,start:i}),r=a,i=o}return n.push({contentEnd:s,end:s,node:r,separator:null,start:i}),n}function Pi(e,t,i){for(;;){const[s,n]=Si(e.original.slice(t,i));if(-1===s)break;e.remove(t+s,t+=n)}}class ki extends Yt{addDeclaration(e,t,i,s){if(s){const n=this.parent.addDeclaration(e,t,i,s);return n.markInitializersForDeoptimization(),n}return super.addDeclaration(e,t,i,!1)}}class wi extends vt{initialise(){this.directive&&"use strict"!==this.directive&&this.parent.type===ot&&this.context.warn({code:"MODULE_LEVEL_DIRECTIVE",message:`Module level directives cause errors when bundled, '${this.directive}' was ignored.`},this.start)}render(e,t){super.render(e,t),this.included&&this.insertSemicolon(e)}shouldBeIncluded(e){return this.directive&&"use strict"!==this.directive?this.parent.type!==ot:super.shouldBeIncluded(e)}applyDeoptimizations(){}}class Ci extends vt{constructor(){super(...arguments),this.directlyIncluded=!1}addImplicitReturnExpressionToScope(){const e=this.body[this.body.length-1];e&&"ReturnStatement"===e.type||this.scope.addReturnExpression(Y)}createScope(e){this.scope=this.parent.preventChildBlockScope?e:new ki(e)}hasEffects(e){if(this.deoptimizeBody)return!0;for(const t of this.body){if(e.brokenFlow)break;if(t.hasEffects(e))return!0}return!1}include(e,t){if(!this.deoptimizeBody||!this.directlyIncluded){this.included=!0,this.directlyIncluded=!0,this.deoptimizeBody&&(t=!0);for(const i of this.body)(t||i.shouldBeIncluded(e))&&i.include(e,t)}}initialise(){const e=this.body[0];this.deoptimizeBody=e instanceof wi&&"use asm"===e.directive}render(e,t){this.body.length?Ai(this.body,e,this.start+1,this.end-1,t):super.render(e,t)}}class Ni extends vt{constructor(){super(...arguments),this.declarationInit=null}addExportedVariables(e,t){this.argument.addExportedVariables(e,t)}declare(e,t){return this.declarationInit=t,this.argument.declare(e,Y)}deoptimizePath(e){0===e.length&&this.argument.deoptimizePath(B)}hasEffectsOnInteractionAtPath(e,t,i){return e.length>0||this.argument.hasEffectsOnInteractionAtPath(B,t,i)}markDeclarationReached(){this.argument.markDeclarationReached()}applyDeoptimizations(){this.deoptimized=!0,null!==this.declarationInit&&(this.declarationInit.deoptimizePath([D,D]),this.context.requestTreeshakingPass())}}class _i extends vt{constructor(){super(...arguments),this.objectEntity=null,this.deoptimizedReturn=!1}deoptimizePath(e){this.getObjectEntity().deoptimizePath(e),1===e.length&&e[0]===D&&this.scope.getReturnExpression().deoptimizePath(F)}deoptimizeThisOnInteractionAtPath(e,t,i){t.length>0&&this.getObjectEntity().deoptimizeThisOnInteractionAtPath(e,t,i)}getLiteralValueAtPath(e,t,i){return this.getObjectEntity().getLiteralValueAtPath(e,t,i)}getReturnExpressionWhenCalledAtPath(e,t,i,s){return e.length>0?this.getObjectEntity().getReturnExpressionWhenCalledAtPath(e,t,i,s):this.async?(this.deoptimizedReturn||(this.deoptimizedReturn=!0,this.scope.getReturnExpression().deoptimizePath(F),this.context.requestTreeshakingPass()),Y):this.scope.getReturnExpression()}hasEffectsOnInteractionAtPath(e,t,i){if(e.length>0||2!==t.type)return this.getObjectEntity().hasEffectsOnInteractionAtPath(e,t,i);if(this.async){const{propertyReadSideEffects:e}=this.context.options.treeshake,t=this.scope.getReturnExpression();if(t.hasEffectsOnInteractionAtPath(["then"],ee,i)||e&&("always"===e||t.hasEffectsOnInteractionAtPath(["then"],Q,i)))return!0}for(const e of this.params)if(e.hasEffects(i))return!0;return!1}include(e,t){this.deoptimized||this.applyDeoptimizations(),this.included=!0;const{brokenFlow:i}=e;e.brokenFlow=0,this.body.include(e,t),e.brokenFlow=i}includeCallArguments(e,t){this.scope.includeCallArguments(e,t)}initialise(){this.scope.addParameterVariables(this.params.map((e=>e.declare("parameter",Y))),this.params[this.params.length-1]instanceof Ni),this.body instanceof Ci?this.body.addImplicitReturnExpressionToScope():this.scope.addReturnExpression(this.body)}parseNode(e){e.body.type===st&&(this.body=new Ci(e.body,this,this.scope.hoistedBodyVarScope)),super.parseNode(e)}applyDeoptimizations(){}}_i.prototype.preventChildBlockScope=!0;class $i extends _i{constructor(){super(...arguments),this.objectEntity=null}createScope(e){this.scope=new Jt(e,this.context)}hasEffects(){return this.deoptimized||this.applyDeoptimizations(),!1}hasEffectsOnInteractionAtPath(e,t,i){if(super.hasEffectsOnInteractionAtPath(e,t,i))return!0;if(2===t.type){const{ignore:e,brokenFlow:t}=i;if(i.ignore={breaks:!1,continues:!1,labels:new Set,returnYield:!0},this.body.hasEffects(i))return!0;i.ignore=e,i.brokenFlow=t}return!1}include(e,t){super.include(e,t);for(const i of this.params)i instanceof fi||i.include(e,t)}getObjectEntity(){return null!==this.objectEntity?this.objectEntity:this.objectEntity=new Nt([],Tt)}}function Ti(e,{exportNamesByVariable:t,snippets:{_:i,getObject:s,getPropertyAccess:n}},r=""){if(1===e.length&&1===t.get(e[0]).length){const s=e[0];return`exports('${t.get(s)}',${i}${s.getName(n)}${r})`}{const i=[];for(const s of e)for(const e of t.get(s))i.push([e,s.getName(n)+r]);return`exports(${s(i,{lineBreakIndent:null})})`}}function Oi(e,t,i,s,{exportNamesByVariable:n,snippets:{_:r}}){s.prependRight(t,`exports('${n.get(e)}',${r}`),s.appendLeft(i,")")}function Ri(e,t,i,s,n,r){const{_:a,getPropertyAccess:o}=r.snippets;n.appendLeft(i,`,${a}${Ti([e],r)},${a}${e.getName(o)}`),s&&(n.prependRight(t,"("),n.appendLeft(i,")"))}class Mi extends vt{addExportedVariables(e,t){for(const i of this.properties)"Property"===i.type?i.value.addExportedVariables(e,t):i.argument.addExportedVariables(e,t)}declare(e,t){const i=[];for(const s of this.properties)i.push(...s.declare(e,t));return i}deoptimizePath(e){if(0===e.length)for(const t of this.properties)t.deoptimizePath(e)}hasEffectsOnInteractionAtPath(e,t,i){for(const e of this.properties)if(e.hasEffectsOnInteractionAtPath(B,t,i))return!0;return!1}markDeclarationReached(){for(const e of this.properties)e.markDeclarationReached()}}class Di extends Wt{constructor(e){super("arguments",null,Y,e)}hasEffectsOnInteractionAtPath(e,{type:t}){return 0!==t||e.length>1}}class Li extends Wt{constructor(e){super("this",null,null,e),this.deoptimizedPaths=[],this.entitiesToBeDeoptimized=new Set,this.thisDeoptimizationList=[],this.thisDeoptimizations=new W}addEntityToBeDeoptimized(e){for(const t of this.deoptimizedPaths)e.deoptimizePath(t);for(const{interaction:t,path:i}of this.thisDeoptimizationList)e.deoptimizeThisOnInteractionAtPath(t,i,H);this.entitiesToBeDeoptimized.add(e)}deoptimizePath(e){if(0!==e.length&&!this.deoptimizationTracker.trackEntityAtPathAndGetIfTracked(e,this)){this.deoptimizedPaths.push(e);for(const t of this.entitiesToBeDeoptimized)t.deoptimizePath(e)}}deoptimizeThisOnInteractionAtPath(e,t){const i={interaction:e,path:t};if(!this.thisDeoptimizations.trackEntityAtPathAndGetIfTracked(t,e.type,e.thisArg)){for(const i of this.entitiesToBeDeoptimized)i.deoptimizeThisOnInteractionAtPath(e,t,H);this.thisDeoptimizationList.push(i)}}hasEffectsOnInteractionAtPath(e,t,i){return this.getInit(i).hasEffectsOnInteractionAtPath(e,t,i)||super.hasEffectsOnInteractionAtPath(e,t,i)}getInit(e){return e.replacedVariableInits.get(this)||Y}}class Vi extends Jt{constructor(e,t){super(e,t),this.variables.set("arguments",this.argumentsVariable=new Di(t)),this.variables.set("this",this.thisVariable=new Li(t))}findLexicalBoundary(){return this}includeCallArguments(e,t){if(super.includeCallArguments(e,t),this.argumentsVariable.included)for(const i of t)i.included||i.include(e,!1)}}class Bi extends _i{constructor(){super(...arguments),this.objectEntity=null}createScope(e){this.scope=new Vi(e,this.context)}deoptimizeThisOnInteractionAtPath(e,t,i){super.deoptimizeThisOnInteractionAtPath(e,t,i),2===e.type&&0===t.length&&this.scope.thisVariable.addEntityToBeDeoptimized(e.thisArg)}hasEffects(e){var t;return this.deoptimized||this.applyDeoptimizations(),!!(null===(t=this.id)||void 0===t?void 0:t.hasEffects(e))}hasEffectsOnInteractionAtPath(e,t,i){if(super.hasEffectsOnInteractionAtPath(e,t,i))return!0;if(2===t.type){const e=i.replacedVariableInits.get(this.scope.thisVariable);i.replacedVariableInits.set(this.scope.thisVariable,t.withNew?new Nt(Object.create(null),Tt):Y);const{brokenFlow:s,ignore:n}=i;if(i.ignore={breaks:!1,continues:!1,labels:new Set,returnYield:!0},this.body.hasEffects(i))return!0;i.brokenFlow=s,e?i.replacedVariableInits.set(this.scope.thisVariable,e):i.replacedVariableInits.delete(this.scope.thisVariable),i.ignore=n}return!1}include(e,t){var i;super.include(e,t),null===(i=this.id)||void 0===i||i.include();const s=this.scope.argumentsVariable.included;for(const i of this.params)i instanceof fi&&!s||i.include(e,t)}initialise(){var e;super.initialise(),null===(e=this.id)||void 0===e||e.declare("function",this)}getObjectEntity(){return null!==this.objectEntity?this.objectEntity:this.objectEntity=new Nt([{key:"prototype",kind:"init",property:new Nt([],Tt)}],Tt)}}const Fi={"!=":(e,t)=>e!=t,"!==":(e,t)=>e!==t,"%":(e,t)=>e%t,"&":(e,t)=>e&t,"*":(e,t)=>e*t,"**":(e,t)=>e**t,"+":(e,t)=>e+t,"-":(e,t)=>e-t,"/":(e,t)=>e/t,"<":(e,t)=>e<t,"<<":(e,t)=>e<<t,"<=":(e,t)=>e<=t,"==":(e,t)=>e==t,"===":(e,t)=>e===t,">":(e,t)=>e>t,">=":(e,t)=>e>=t,">>":(e,t)=>e>>t,">>>":(e,t)=>e>>>t,"^":(e,t)=>e^t,"|":(e,t)=>e|t};function zi(e,t,i){if(i.arguments.length>0)if(i.arguments[i.arguments.length-1].included)for(const s of i.arguments)s.render(e,t);else{let s=i.arguments.length-2;for(;s>=0&&!i.arguments[s].included;)s--;if(s>=0){for(let n=0;n<=s;n++)i.arguments[n].render(e,t);e.remove(Ei(e.original,",",i.arguments[s].end),i.end-1)}else e.remove(Ei(e.original,"(",i.callee.end)+1,i.end-1)}}class ji extends vt{deoptimizeThisOnInteractionAtPath(){}getLiteralValueAtPath(e){return e.length>0||null===this.value&&110!==this.context.code.charCodeAt(this.start)||"bigint"==typeof this.value||47===this.context.code.charCodeAt(this.start)?q:this.value}getReturnExpressionWhenCalledAtPath(e){return 1!==e.length?Y:Je(this.members,e[0])}hasEffectsOnInteractionAtPath(e,t,i){switch(t.type){case 0:return e.length>(null===this.value?0:1);case 1:return!0;case 2:return 1!==e.length||Qe(this.members,e[0],t,i)}}initialise(){this.members=function(e){switch(typeof e){case"boolean":return Ke;case"number":return Xe;case"string":return Ye}return Object.create(null)}(this.value)}parseNode(e){this.value=e.value,this.regex=e.regex,super.parseNode(e)}render(e){"string"==typeof this.value&&e.indentExclusionRanges.push([this.start+1,this.end-1])}}function Ui(e){return e.computed?(t=e.property)instanceof ji?String(t.value):null:e.property.name;var t}function Gi(e){const t=e.propertyKey,i=e.object;if("string"==typeof t){if(i instanceof fi)return[{key:i.name,pos:i.start},{key:t,pos:e.property.start}];if(i instanceof Hi){const s=Gi(i);return s&&[...s,{key:t,pos:e.property.start}]}}return null}class Hi extends vt{constructor(){super(...arguments),this.variable=null,this.assignmentDeoptimized=!1,this.bound=!1,this.expressionsToBeDeoptimized=[],this.replacement=null}bind(){this.bound=!0;const e=Gi(this),t=e&&this.scope.findVariable(e[0].key);if(t&&t.isNamespace){const i=Wi(t,e.slice(1),this.context);i?"string"==typeof i?this.replacement=i:(this.variable=i,this.scope.addNamespaceMemberAccess(function(e){let t=e[0].key;for(let i=1;i<e.length;i++)t+="."+e[i].key;return t}(e),i)):super.bind()}else super.bind()}deoptimizeCache(){const e=this.expressionsToBeDeoptimized;this.expressionsToBeDeoptimized=[],this.propertyKey=D,this.object.deoptimizePath(F);for(const t of e)t.deoptimizeCache()}deoptimizePath(e){if(0===e.length&&this.disallowNamespaceReassignment(),this.variable)this.variable.deoptimizePath(e);else if(!this.replacement&&e.length<7){const t=this.getPropertyKey();this.object.deoptimizePath([t===D?L:t,...e])}}deoptimizeThisOnInteractionAtPath(e,t,i){this.variable?this.variable.deoptimizeThisOnInteractionAtPath(e,t,i):this.replacement||(t.length<7?this.object.deoptimizeThisOnInteractionAtPath(e,[this.getPropertyKey(),...t],i):e.thisArg.deoptimizePath(F))}getLiteralValueAtPath(e,t,i){return this.variable?this.variable.getLiteralValueAtPath(e,t,i):this.replacement?q:(this.expressionsToBeDeoptimized.push(i),e.length<7?this.object.getLiteralValueAtPath([this.getPropertyKey(),...e],t,i):q)}getReturnExpressionWhenCalledAtPath(e,t,i,s){return this.variable?this.variable.getReturnExpressionWhenCalledAtPath(e,t,i,s):this.replacement?Y:(this.expressionsToBeDeoptimized.push(s),e.length<7?this.object.getReturnExpressionWhenCalledAtPath([this.getPropertyKey(),...e],t,i,s):Y)}hasEffects(e){return this.deoptimized||this.applyDeoptimizations(),this.property.hasEffects(e)||this.object.hasEffects(e)||this.hasAccessEffect(e)}hasEffectsAsAssignmentTarget(e,t){return t&&!this.deoptimized&&this.applyDeoptimizations(),this.assignmentDeoptimized||this.applyAssignmentDeoptimization(),this.property.hasEffects(e)||this.object.hasEffects(e)||t&&this.hasAccessEffect(e)||this.hasEffectsOnInteractionAtPath(B,this.assignmentInteraction,e)}hasEffectsOnInteractionAtPath(e,t,i){return this.variable?this.variable.hasEffectsOnInteractionAtPath(e,t,i):!!this.replacement||!(e.length<7)||this.object.hasEffectsOnInteractionAtPath([this.getPropertyKey(),...e],t,i)}include(e,t){this.deoptimized||this.applyDeoptimizations(),this.includeProperties(e,t)}includeAsAssignmentTarget(e,t,i){this.assignmentDeoptimized||this.applyAssignmentDeoptimization(),i?this.include(e,t):this.includeProperties(e,t)}includeCallArguments(e,t){this.variable?this.variable.includeCallArguments(e,t):super.includeCallArguments(e,t)}initialise(){this.propertyKey=Ui(this),this.accessInteraction={thisArg:this.object,type:0}}render(e,t,{renderedParentType:i,isCalleeOfRenderedParent:s,renderedSurroundingElement:n}=se){if(this.variable||this.replacement){const{snippets:{getPropertyAccess:n}}=t;let r=this.variable?this.variable.getName(n):this.replacement;i&&s&&(r="0, "+r),e.overwrite(this.start,this.end,r,{contentOnly:!0,storeName:!0})}else i&&s&&e.appendRight(this.start,"0, "),this.object.render(e,t,{renderedSurroundingElement:n}),this.property.render(e,t)}setAssignedValue(e){this.assignmentInteraction={args:[e],thisArg:this.object,type:1}}applyDeoptimizations(){this.deoptimized=!0;const{propertyReadSideEffects:e}=this.context.options.treeshake;if(this.bound&&e&&!this.variable&&!this.replacement){const e=this.getPropertyKey();this.object.deoptimizeThisOnInteractionAtPath(this.accessInteraction,[e],H),this.context.requestTreeshakingPass()}}applyAssignmentDeoptimization(){this.assignmentDeoptimized=!0;const{propertyReadSideEffects:e}=this.context.options.treeshake;this.bound&&e&&!this.variable&&!this.replacement&&(this.object.deoptimizeThisOnInteractionAtPath(this.assignmentInteraction,[this.getPropertyKey()],H),this.context.requestTreeshakingPass())}disallowNamespaceReassignment(){this.object instanceof fi&&this.scope.findVariable(this.object.name).isNamespace&&(this.variable&&this.context.includeVariableInModule(this.variable),this.context.warn({code:"ILLEGAL_NAMESPACE_REASSIGNMENT",message:`Illegal reassignment to import '${this.object.name}'`},this.start))}getPropertyKey(){if(null===this.propertyKey){this.propertyKey=D;const e=this.property.getLiteralValueAtPath(B,H,this);return this.propertyKey="symbol"==typeof e?D:String(e)}return this.propertyKey}hasAccessEffect(e){const{propertyReadSideEffects:t}=this.context.options.treeshake;return!(this.variable||this.replacement)&&t&&("always"===t||this.object.hasEffectsOnInteractionAtPath([this.getPropertyKey()],this.accessInteraction,e))}includeProperties(e,t){this.included||(this.included=!0,this.variable&&this.context.includeVariableInModule(this.variable)),this.object.include(e,t),this.property.include(e,t)}}function Wi(e,t,i){if(0===t.length)return e;if(!e.isNamespace||e instanceof ie)return null;const s=t[0].key,n=e.context.traceExport(s);if(!n){const n=e.context.fileName;return i.warn({code:"MISSING_EXPORT",exporter:ce(n),importer:ce(i.fileName),message:`'${s}' is not exported by '${ce(n)}'`,missing:s,url:"https://rollupjs.org/guide/en/#error-name-is-not-exported-by-module"},t[0].pos),"undefined"}return Wi(n,t.slice(1),i)}class qi extends vt{constructor(){super(...arguments),this.returnExpression=null,this.deoptimizableDependentExpressions=[],this.expressionsToBeDeoptimized=new Set}deoptimizeCache(){if(this.returnExpression!==Y){this.returnExpression=Y;for(const e of this.deoptimizableDependentExpressions)e.deoptimizeCache();for(const e of this.expressionsToBeDeoptimized)e.deoptimizePath(F)}}deoptimizePath(e){if(0===e.length||this.context.deoptimizationTracker.trackEntityAtPathAndGetIfTracked(e,this))return;const t=this.getReturnExpression();t!==Y&&t.deoptimizePath(e)}deoptimizeThisOnInteractionAtPath(e,t,i){const s=this.getReturnExpression(i);s===Y?e.thisArg.deoptimizePath(F):i.withTrackedEntityAtPath(t,s,(()=>{this.expressionsToBeDeoptimized.add(e.thisArg),s.deoptimizeThisOnInteractionAtPath(e,t,i)}),void 0)}getLiteralValueAtPath(e,t,i){const s=this.getReturnExpression(t);return s===Y?q:t.withTrackedEntityAtPath(e,s,(()=>(this.deoptimizableDependentExpressions.push(i),s.getLiteralValueAtPath(e,t,i))),q)}getReturnExpressionWhenCalledAtPath(e,t,i,s){const n=this.getReturnExpression(i);return this.returnExpression===Y?Y:i.withTrackedEntityAtPath(e,n,(()=>(this.deoptimizableDependentExpressions.push(s),n.getReturnExpressionWhenCalledAtPath(e,t,i,s))),Y)}hasEffectsOnInteractionAtPath(e,t,i){const{type:s}=t;if(2===s){if((t.withNew?i.instantiated:i.called).trackEntityAtPathAndGetIfTracked(e,t.args,this))return!1}else if((1===s?i.assigned:i.accessed).trackEntityAtPathAndGetIfTracked(e,this))return!1;return this.getReturnExpression().hasEffectsOnInteractionAtPath(e,t,i)}}class Ki extends Qt{addDeclaration(e,t,i,s){const n=this.variables.get(e.name);return n?(this.parent.addDeclaration(e,t,Ve,s),n.addDeclaration(e,i),n):this.parent.addDeclaration(e,t,i,s)}}class Xi extends Yt{constructor(e,t,i){super(e),this.variables.set("this",this.thisVariable=new Wt("this",null,t,i)),this.instanceScope=new Yt(this),this.instanceScope.variables.set("this",new Li(i))}findLexicalBoundary(){return this}}class Yi extends vt{constructor(){super(...arguments),this.accessedValue=null}deoptimizeCache(){}deoptimizePath(e){this.getAccessedValue().deoptimizePath(e)}deoptimizeThisOnInteractionAtPath(e,t,i){return 0===e.type&&"get"===this.kind&&0===t.length?this.value.deoptimizeThisOnInteractionAtPath({args:Z,thisArg:e.thisArg,type:2,withNew:!1},B,i):1===e.type&&"set"===this.kind&&0===t.length?this.value.deoptimizeThisOnInteractionAtPath({args:e.args,thisArg:e.thisArg,type:2,withNew:!1},B,i):void this.getAccessedValue().deoptimizeThisOnInteractionAtPath(e,t,i)}getLiteralValueAtPath(e,t,i){return this.getAccessedValue().getLiteralValueAtPath(e,t,i)}getReturnExpressionWhenCalledAtPath(e,t,i,s){return this.getAccessedValue().getReturnExpressionWhenCalledAtPath(e,t,i,s)}hasEffects(e){return this.key.hasEffects(e)}hasEffectsOnInteractionAtPath(e,t,i){return"get"===this.kind&&0===t.type&&0===e.length?this.value.hasEffectsOnInteractionAtPath(B,{args:Z,thisArg:t.thisArg,type:2,withNew:!1},i):"set"===this.kind&&1===t.type?this.value.hasEffectsOnInteractionAtPath(B,{args:t.args,thisArg:t.thisArg,type:2,withNew:!1},i):this.getAccessedValue().hasEffectsOnInteractionAtPath(e,t,i)}applyDeoptimizations(){}getAccessedValue(){return null===this.accessedValue?"get"===this.kind?(this.accessedValue=Y,this.accessedValue=this.value.getReturnExpressionWhenCalledAtPath(B,ee,H,this)):this.accessedValue=this.value:this.accessedValue}}class Qi extends Yi{applyDeoptimizations(){}}class Ji extends X{constructor(e,t){super(),this.object=e,this.key=t}deoptimizePath(e){this.object.deoptimizePath([this.key,...e])}deoptimizeThisOnInteractionAtPath(e,t,i){this.object.deoptimizeThisOnInteractionAtPath(e,[this.key,...t],i)}getLiteralValueAtPath(e,t,i){return this.object.getLiteralValueAtPath([this.key,...e],t,i)}getReturnExpressionWhenCalledAtPath(e,t,i,s){return this.object.getReturnExpressionWhenCalledAtPath([this.key,...e],t,i,s)}hasEffectsOnInteractionAtPath(e,t,i){return this.object.hasEffectsOnInteractionAtPath([this.key,...e],t,i)}}class Zi extends vt{constructor(){super(...arguments),this.objectEntity=null}createScope(e){this.scope=new Yt(e)}deoptimizeCache(){this.getObjectEntity().deoptimizeAllProperties()}deoptimizePath(e){this.getObjectEntity().deoptimizePath(e)}deoptimizeThisOnInteractionAtPath(e,t,i){this.getObjectEntity().deoptimizeThisOnInteractionAtPath(e,t,i)}getLiteralValueAtPath(e,t,i){return this.getObjectEntity().getLiteralValueAtPath(e,t,i)}getReturnExpressionWhenCalledAtPath(e,t,i,s){return this.getObjectEntity().getReturnExpressionWhenCalledAtPath(e,t,i,s)}hasEffects(e){var t,i;this.deoptimized||this.applyDeoptimizations();const s=(null===(t=this.superClass)||void 0===t?void 0:t.hasEffects(e))||this.body.hasEffects(e);return null===(i=this.id)||void 0===i||i.markDeclarationReached(),s||super.hasEffects(e)}hasEffectsOnInteractionAtPath(e,t,i){var s;return 2===t.type&&0===e.length?!t.withNew||(null!==this.classConstructor?this.classConstructor.hasEffectsOnInteractionAtPath(e,t,i):null===(s=this.superClass)||void 0===s?void 0:s.hasEffectsOnInteractionAtPath(e,t,i))||!1:this.getObjectEntity().hasEffectsOnInteractionAtPath(e,t,i)}include(e,t){var i;this.deoptimized||this.applyDeoptimizations(),this.included=!0,null===(i=this.superClass)||void 0===i||i.include(e,t),this.body.include(e,t),this.id&&(this.id.markDeclarationReached(),this.id.include())}initialise(){var e;null===(e=this.id)||void 0===e||e.declare("class",this);for(const e of this.body.body)if(e instanceof Qi&&"constructor"===e.kind)return void(this.classConstructor=e);this.classConstructor=null}applyDeoptimizations(){this.deoptimized=!0;for(const e of this.body.body)e.static||e instanceof Qi&&"constructor"===e.kind||e.deoptimizePath(F);this.context.requestTreeshakingPass()}getObjectEntity(){if(null!==this.objectEntity)return this.objectEntity;const e=[],t=[];for(const i of this.body.body){const s=i.static?e:t,n=i.kind;if(s===t&&!n)continue;const r="set"===n||"get"===n?n:"init";let a;if(i.computed){const e=i.key.getLiteralValueAtPath(B,H,this);if("symbol"==typeof e){s.push({key:D,kind:r,property:i});continue}a=String(e)}else a=i.key instanceof fi?i.key.name:String(i.key.value);s.push({key:a,kind:r,property:i})}return e.unshift({key:"prototype",kind:"init",property:new Nt(t,this.superClass?new Ji(this.superClass,"prototype"):Tt)}),this.objectEntity=new Nt(e,this.superClass||Tt)}}class es extends Zi{initialise(){super.initialise(),null!==this.id&&(this.id.variable.isId=!0)}parseNode(e){null!==e.id&&(this.id=new fi(e.id,this,this.scope.parent)),super.parseNode(e)}render(e,t){const{exportNamesByVariable:i,format:s,snippets:{_:n}}=t;"system"===s&&this.id&&i.has(this.id.variable)&&e.appendLeft(this.end,`${n}${Ti([this.id.variable],t)};`),super.render(e,t)}}class ts extends X{constructor(e){super(),this.expressions=e,this.included=!1}deoptimizePath(e){for(const t of this.expressions)t.deoptimizePath(e)}getReturnExpressionWhenCalledAtPath(e,t,i,s){return new ts(this.expressions.map((n=>n.getReturnExpressionWhenCalledAtPath(e,t,i,s))))}hasEffectsOnInteractionAtPath(e,t,i){for(const s of this.expressions)if(s.hasEffectsOnInteractionAtPath(e,t,i))return!0;return!1}}class is extends vt{hasEffects(){return!1}initialise(){this.context.addExport(this)}render(e,t,i){e.remove(i.start,i.end)}applyDeoptimizations(){}}is.prototype.needsBoundaries=!0;class ss extends Bi{initialise(){super.initialise(),null!==this.id&&(this.id.variable.isId=!0)}parseNode(e){null!==e.id&&(this.id=new fi(e.id,this,this.scope.parent)),super.parseNode(e)}}class ns extends vt{include(e,t){super.include(e,t),t&&this.context.includeVariableInModule(this.variable)}initialise(){const e=this.declaration;this.declarationName=e.id&&e.id.name||this.declaration.name,this.variable=this.scope.addExportDefaultDeclaration(this.declarationName||this.context.getModuleName(),this,this.context),this.context.addExport(this)}render(e,t,i){const{start:s,end:n}=i,r=function(e,t){return vi(e,Ei(e,"default",t)+7)}(e.original,this.start);if(this.declaration instanceof ss)this.renderNamedDeclaration(e,r,"function","(",null===this.declaration.id,t);else if(this.declaration instanceof es)this.renderNamedDeclaration(e,r,"class","{",null===this.declaration.id,t);else{if(this.variable.getOriginalVariable()!==this.variable)return void gi(this,e,s,n);if(!this.variable.included)return e.remove(this.start,r),this.declaration.render(e,t,{renderedSurroundingElement:rt}),void(";"!==e.original[this.end-1]&&e.appendLeft(this.end,";"));this.renderVariableDeclaration(e,r,t)}this.declaration.render(e,t)}applyDeoptimizations(){}renderNamedDeclaration(e,t,i,s,n,r){const{exportNamesByVariable:a,format:o,snippets:{getPropertyAccess:l}}=r,h=this.variable.getName(l);e.remove(this.start,t),n&&e.appendLeft(function(e,t,i,s){const n=Ei(e,t,s)+t.length;e=e.slice(n,Ei(e,i,n));const r=Ei(e,"*");return-1===r?n:n+r+1}(e.original,i,s,t),` ${h}`),"system"===o&&this.declaration instanceof es&&a.has(this.variable)&&e.appendLeft(this.end,` ${Ti([this.variable],r)};`)}renderVariableDeclaration(e,t,{format:i,exportNamesByVariable:s,snippets:{cnst:n,getPropertyAccess:r}}){const a=59===e.original.charCodeAt(this.end-1),o="system"===i&&s.get(this.variable);o?(e.overwrite(this.start,t,`${n} ${this.variable.getName(r)} = exports('${o[0]}', `),e.appendRight(a?this.end-1:this.end,")"+(a?"":";"))):(e.overwrite(this.start,t,`${n} ${this.variable.getName(r)} = `),a||e.appendLeft(this.end,";"))}}ns.prototype.needsBoundaries=!0;class rs extends vt{bind(){var e;null===(e=this.declaration)||void 0===e||e.bind()}hasEffects(e){var t;return!!(null===(t=this.declaration)||void 0===t?void 0:t.hasEffects(e))}initialise(){this.context.addExport(this)}render(e,t,i){const{start:s,end:n}=i;null===this.declaration?e.remove(s,n):(e.remove(this.start,this.declaration.start),this.declaration.render(e,t,{end:n,start:s}))}applyDeoptimizations(){}}rs.prototype.needsBoundaries=!0;class as extends ki{constructor(){super(...arguments),this.hoistedDeclarations=[]}addDeclaration(e,t,i,s){return this.hoistedDeclarations.push(e),super.addDeclaration(e,t,i,s)}}const os=Symbol("unset");class ls extends vt{constructor(){super(...arguments),this.testValue=os}deoptimizeCache(){this.testValue=q}hasEffects(e){var t;if(this.test.hasEffects(e))return!0;const i=this.getTestValue();if("symbol"==typeof i){const{brokenFlow:t}=e;if(this.consequent.hasEffects(e))return!0;const i=e.brokenFlow;return e.brokenFlow=t,null!==this.alternate&&(!!this.alternate.hasEffects(e)||(e.brokenFlow=e.brokenFlow<i?e.brokenFlow:i,!1))}return i?this.consequent.hasEffects(e):!!(null===(t=this.alternate)||void 0===t?void 0:t.hasEffects(e))}include(e,t){if(this.included=!0,t)this.includeRecursively(t,e);else{const t=this.getTestValue();"symbol"==typeof t?this.includeUnknownTest(e):this.includeKnownTest(e,t)}}parseNode(e){this.consequentScope=new as(this.scope),this.consequent=new(this.context.getNodeConstructor(e.consequent.type))(e.consequent,this,this.consequentScope),e.alternate&&(this.alternateScope=new as(this.scope),this.alternate=new(this.context.getNodeConstructor(e.alternate.type))(e.alternate,this,this.alternateScope)),super.parseNode(e)}render(e,t){const{snippets:{getPropertyAccess:i}}=t,s=this.getTestValue(),n=[],r=this.test.included,a=!this.context.options.treeshake;r?this.test.render(e,t):e.remove(this.start,this.consequent.start),this.consequent.included&&(a||"symbol"==typeof s||s)?this.consequent.render(e,t):(e.overwrite(this.consequent.start,this.consequent.end,r?";":""),n.push(...this.consequentScope.hoistedDeclarations)),this.alternate&&(!this.alternate.included||!a&&"symbol"!=typeof s&&s?(r&&this.shouldKeepAlternateBranch()?e.overwrite(this.alternate.start,this.end,";"):e.remove(this.consequent.end,this.end),n.push(...this.alternateScope.hoistedDeclarations)):(r?101===e.original.charCodeAt(this.alternate.start-1)&&e.prependLeft(this.alternate.start," "):e.remove(this.consequent.end,this.alternate.start),this.alternate.render(e,t))),this.renderHoistedDeclarations(n,e,i)}applyDeoptimizations(){}getTestValue(){return this.testValue===os?this.testValue=this.test.getLiteralValueAtPath(B,H,this):this.testValue}includeKnownTest(e,t){var i;this.test.shouldBeIncluded(e)&&this.test.include(e,!1),t&&this.consequent.shouldBeIncluded(e)&&this.consequent.include(e,!1,{asSingleStatement:!0}),!t&&(null===(i=this.alternate)||void 0===i?void 0:i.shouldBeIncluded(e))&&this.alternate.include(e,!1,{asSingleStatement:!0})}includeRecursively(e,t){var i;this.test.include(t,e),this.consequent.include(t,e),null===(i=this.alternate)||void 0===i||i.include(t,e)}includeUnknownTest(e){var t;this.test.include(e,!1);const{brokenFlow:i}=e;let s=0;this.consequent.shouldBeIncluded(e)&&(this.consequent.include(e,!1,{asSingleStatement:!0}),s=e.brokenFlow,e.brokenFlow=i),(null===(t=this.alternate)||void 0===t?void 0:t.shouldBeIncluded(e))&&(this.alternate.include(e,!1,{asSingleStatement:!0}),e.brokenFlow=e.brokenFlow<s?e.brokenFlow:s)}renderHoistedDeclarations(e,t,i){const s=[...new Set(e.map((e=>{const t=e.variable;return t.included?t.getName(i):""})))].filter(Boolean).join(", ");if(s){const e=this.parent.type,i=e!==ot&&e!==st;t.prependRight(this.start,`${i?"{ ":""}var ${s}; `),i&&t.appendLeft(this.end," }")}}shouldKeepAlternateBranch(){let e=this.parent;do{if(e instanceof ls&&e.alternate)return!0;if(e instanceof Ci)return!1;e=e.parent}while(e);return!1}}class hs extends vt{bind(){}hasEffects(){return!1}initialise(){this.context.addImport(this)}render(e,t,i){e.remove(i.start,i.end)}applyDeoptimizations(){}}hs.prototype.needsBoundaries=!0;const cs="_interopDefault",us="_interopDefaultLegacy",ds="_interopNamespace",ps="_interopNamespaceDefault",fs="_interopNamespaceDefaultOnly",ms="_mergeNamespaces",gs={auto:cs,default:null,defaultOnly:null,esModule:null,false:null,true:us},ys=(e,t)=>"esModule"===e||t&&("auto"===e||"true"===e),xs={auto:ds,default:ps,defaultOnly:fs,esModule:null,false:null,true:ds},Es=(e,t)=>ys(e,t)&&gs[e]===cs,bs=(e,t,i,s,n,r,a)=>{const o=new Set(e);for(const e of Os)t.has(e)&&o.add(e);return Os.map((e=>o.has(e)?vs[e](i,s,n,r,a,o):"")).join("")},vs={[us](e,t,i){const{_:s,getDirectReturnFunction:n,n:r}=t,[a,o]=n(["e"],{functionReturn:!0,lineBreakIndent:null,name:us});return`${a}e${s}&&${s}typeof e${s}===${s}'object'${s}&&${s}'default'${s}in e${s}?${s}${i?Ss(t):As(t)}${o}${r}${r}`},[cs](e,t,i){const{_:s,getDirectReturnFunction:n,n:r}=t,[a,o]=n(["e"],{functionReturn:!0,lineBreakIndent:null,name:cs});return`${a}e${s}&&${s}e.__esModule${s}?${s}${i?Ss(t):As(t)}${o}${r}${r}`},[fs](e,t,i,s,n){const{getDirectReturnFunction:r,getObject:a,n:o}=t,[l,h]=r(["e"],{functionReturn:!0,lineBreakIndent:null,name:fs});return`${l}${$s(s,Ts(n,a([["__proto__","null"],["default","e"]],{lineBreakIndent:null}),t))}${h}${o}${o}`},[ps](e,t,i,s,n){const{_:r,n:a}=t;return`function _interopNamespaceDefault(e)${r}{${a}`+Is(e,e,t,i,s,n)+`}${a}${a}`},[ds](e,t,i,s,n,r){const{_:a,getDirectReturnFunction:o,n:l}=t;if(r.has(ps)){const[e,t]=o(["e"],{functionReturn:!0,lineBreakIndent:null,name:ds});return`${e}e${a}&&${a}e.__esModule${a}?${a}e${a}:${a}_interopNamespaceDefault(e)${t}${l}${l}`}return`function _interopNamespace(e)${a}{${l}${e}if${a}(e${a}&&${a}e.__esModule)${a}return e;${l}`+Is(e,e,t,i,s,n)+`}${l}${l}`},[ms](e,t,i,s,n){const{_:r,cnst:a,n:o}=t,l="var"===a&&i;return`function _mergeNamespaces(n, m)${r}{${o}${e}${ks(`{${o}${e}${e}${e}if${r}(k${r}!==${r}'default'${r}&&${r}!(k in n))${r}{${o}`+(i?l?Cs:Ns:_s)(e,e+e+e+e,t)+`${e}${e}${e}}${o}`+`${e}${e}}`,l,e,t)}${o}${e}return ${$s(s,Ts(n,"n",t))};${o}}${o}${o}`}},Ss=({_:e,getObject:t})=>`e${e}:${e}${t([["default","e"]],{lineBreakIndent:null})}`,As=({_:e,getPropertyAccess:t})=>`e${t("default")}${e}:${e}e`,Is=(e,t,i,s,n,r)=>{const{_:a,cnst:o,getObject:l,getPropertyAccess:h,n:c,s:u}=i,d=`{${c}`+(s?ws:_s)(e,t+e+e,i)+`${t}${e}}`;return`${t}${o} n${a}=${a}Object.create(null${r?`,${a}{${a}[Symbol.toStringTag]:${a}${Rs(l)}${a}}`:""});${c}${t}if${a}(e)${a}{${c}${t}${e}${Ps(d,!s,i)}${c}${t}}${c}${t}n${h("default")}${a}=${a}e;${c}${t}return ${$s(n,"n")}${u}${c}`},Ps=(e,t,{_:i,cnst:s,getFunctionIntro:n,s:r})=>"var"!==s||t?`for${i}(${s} k in e)${i}${e}`:`Object.keys(e).forEach(${n(["k"],{isAsync:!1,name:null})}${e})${r}`,ks=(e,t,i,{_:s,cnst:n,getDirectReturnFunction:r,getFunctionIntro:a,n:o})=>{if(t){const[t,n]=r(["e"],{functionReturn:!1,lineBreakIndent:{base:i,t:i},name:null});return`m.forEach(${t}e${s}&&${s}typeof e${s}!==${s}'string'${s}&&${s}!Array.isArray(e)${s}&&${s}Object.keys(e).forEach(${a(["k"],{isAsync:!1,name:null})}${e})${n});`}return`for${s}(var i${s}=${s}0;${s}i${s}<${s}m.length;${s}i++)${s}{${o}${i}${i}${n} e${s}=${s}m[i];${o}${i}${i}if${s}(typeof e${s}!==${s}'string'${s}&&${s}!Array.isArray(e))${s}{${s}for${s}(${n} k in e)${s}${e}${s}}${o}${i}}`},ws=(e,t,i)=>{const{_:s,n:n}=i;return`${t}if${s}(k${s}!==${s}'default')${s}{${n}`+Cs(e,t+e,i)+`${t}}${n}`},Cs=(e,t,{_:i,cnst:s,getDirectReturnFunction:n,n:r})=>{const[a,o]=n([],{functionReturn:!0,lineBreakIndent:null,name:null});return`${t}${s} d${i}=${i}Object.getOwnPropertyDescriptor(e,${i}k);${r}${t}Object.defineProperty(n,${i}k,${i}d.get${i}?${i}d${i}:${i}{${r}${t}${e}enumerable:${i}true,${r}${t}${e}get:${i}${a}e[k]${o}${r}${t}});${r}`},Ns=(e,t,{_:i,cnst:s,getDirectReturnFunction:n,n:r})=>{const[a,o]=n([],{functionReturn:!0,lineBreakIndent:null,name:null});return`${t}${s} d${i}=${i}Object.getOwnPropertyDescriptor(e,${i}k);${r}${t}if${i}(d)${i}{${r}${t}${e}Object.defineProperty(n,${i}k,${i}d.get${i}?${i}d${i}:${i}{${r}${t}${e}${e}enumerable:${i}true,${r}${t}${e}${e}get:${i}${a}e[k]${o}${r}${t}${e}});${r}${t}}${r}`},_s=(e,t,{_:i,n:s})=>`${t}n[k]${i}=${i}e[k];${s}`,$s=(e,t)=>e?`Object.freeze(${t})`:t,Ts=(e,t,{_:i,getObject:s})=>e?`Object.defineProperty(${t},${i}Symbol.toStringTag,${i}${Rs(s)})`:t,Os=Object.keys(vs);function Rs(e){return e([["value","'Module'"]],{lineBreakIndent:null})}function Ms(e,t,i){return"external"===t?xs[String(i(e instanceof Te?e.id:null))]:"default"===t?fs:null}const Ds={amd:["require"],cjs:["require"],system:["module"]},Ls="ROLLUP_ASSET_URL_",Vs="ROLLUP_CHUNK_URL_",Bs="ROLLUP_FILE_URL_",Fs={amd:["document","module","URL"],cjs:["document","require","URL"],es:[],iife:["document","URL"],system:["module"],umd:["document","require","URL"]},zs={amd:["document","require","URL"],cjs:["document","require","URL"],es:[],iife:["document","URL"],system:["module","URL"],umd:["document","require","URL"]},js=(e,t="URL")=>`new ${t}(${e}).href`,Us=(e,t=!1)=>js(`'${e}', ${t?"typeof document === 'undefined' ? location.href : ":""}document.currentScript && document.currentScript.tagName.toUpperCase() === 'SCRIPT' && document.currentScript.src || document.baseURI`),Gs=e=>(t,{chunkId:i})=>{const s=e(i);return null===t?`({ url: ${s} })`:"url"===t?s:"undefined"},Hs=(e,t=!1)=>`${t?"typeof document === 'undefined' ? location.href : ":""}(document.currentScript && document.currentScript.tagName.toUpperCase() === 'SCRIPT' && document.currentScript.src || new URL('${e}', document.baseURI).href)`,Ws={amd:e=>("."!==e[0]&&(e="./"+e),js(`require.toUrl('${e}'), document.baseURI`)),cjs:e=>`(typeof document === 'undefined' ? ${js(`'file:' + __dirname + '/${e}'`,"(require('u' + 'rl').URL)")} : ${Us(e)})`,es:e=>js(`'${e}', import.meta.url`),iife:e=>Us(e),system:e=>js(`'${e}', module.meta.url`),umd:e=>`(typeof document === 'undefined' && typeof location === 'undefined' ? ${js(`'file:' + __dirname + '/${e}'`,"(require('u' + 'rl').URL)")} : ${Us(e,!0)})`},qs={amd:Gs((()=>js("module.uri, document.baseURI"))),cjs:Gs((e=>`(typeof document === 'undefined' ? ${js("'file:' + __filename","(require('u' + 'rl').URL)")} : ${Hs(e)})`)),iife:Gs((e=>Hs(e))),system:(e,{snippets:{getPropertyAccess:t}})=>null===e?"module.meta":`module.meta${t(e)}`,umd:Gs((e=>`(typeof document === 'undefined' && typeof location === 'undefined' ? ${js("'file:' + __filename","(require('u' + 'rl').URL)")} : ${Hs(e,!0)})`))};class Ks extends vt{constructor(){super(...arguments),this.hasCachedEffect=!1}hasEffects(e){if(this.hasCachedEffect)return!0;for(const t of this.body)if(t.hasEffects(e))return this.hasCachedEffect=!0;return!1}include(e,t){this.included=!0;for(const i of this.body)(t||i.shouldBeIncluded(e))&&i.include(e,t)}render(e,t){this.body.length?Ai(this.body,e,this.start,this.end,t):super.render(e,t)}applyDeoptimizations(){}}class Xs extends vt{hasEffects(e){var t;if(null===(t=this.test)||void 0===t?void 0:t.hasEffects(e))return!0;for(const t of this.consequent){if(e.brokenFlow)break;if(t.hasEffects(e))return!0}return!1}include(e,t){var i;this.included=!0,null===(i=this.test)||void 0===i||i.include(e,t);for(const i of this.consequent)(t||i.shouldBeIncluded(e))&&i.include(e,t)}render(e,t,i){if(this.consequent.length){this.test&&this.test.render(e,t);const s=this.test?this.test.end:Ei(e.original,"default",this.start)+7,n=Ei(e.original,":",s)+1;Ai(this.consequent,e,n,i.end,t)}else super.render(e,t)}}Xs.prototype.needsBoundaries=!0;class Ys extends vt{deoptimizeThisOnInteractionAtPath(){}getLiteralValueAtPath(e){return e.length>0||1!==this.quasis.length?q:this.quasis[0].value.cooked}getReturnExpressionWhenCalledAtPath(e){return 1!==e.length?Y:Je(Ye,e[0])}hasEffectsOnInteractionAtPath(e,t,i){return 0===t.type?e.length>1:2!==t.type||1!==e.length||Qe(Ye,e[0],t,i)}render(e,t){e.indentExclusionRanges.push([this.start,this.end]),super.render(e,t)}}class Qs extends te{constructor(){super("undefined")}getLiteralValueAtPath(){}}class Js extends Wt{constructor(e,t,i){super(e,t,t.declaration,i),this.hasId=!1,this.originalId=null,this.originalVariable=null;const s=t.declaration;(s instanceof ss||s instanceof es)&&s.id?(this.hasId=!0,this.originalId=s.id):s instanceof fi&&(this.originalId=s)}addReference(e){this.hasId||(this.name=e.name)}getAssignedVariableName(){return this.originalId&&this.originalId.name||null}getBaseVariableName(){const e=this.getOriginalVariable();return e===this?super.getBaseVariableName():e.getBaseVariableName()}getDirectOriginalVariable(){return!this.originalId||!this.hasId&&(this.originalId.isPossibleTDZ()||this.originalId.variable.isReassigned||this.originalId.variable instanceof Qs||"syntheticNamespace"in this.originalId.variable)?null:this.originalId.variable}getName(e){const t=this.getOriginalVariable();return t===this?super.getName(e):t.getName(e)}getOriginalVariable(){if(this.originalVariable)return this.originalVariable;let e,t=this;const i=new Set;do{i.add(t),e=t,t=e.getDirectOriginalVariable()}while(t instanceof Js&&!i.has(t));return this.originalVariable=t||e}}class Zs extends Yt{constructor(e,t){super(e),this.context=t,this.variables.set("this",new Wt("this",null,Ve,t))}addExportDefaultDeclaration(e,t,i){const s=new Js(e,t,i);return this.variables.set("default",s),s}addNamespaceMemberAccess(){}deconflict(e,t,i){for(const s of this.children)s.deconflict(e,t,i)}findLexicalBoundary(){return this}findVariable(e){const t=this.variables.get(e)||this.accessedOutsideVariables.get(e);if(t)return t;const i=this.context.traceVariable(e)||this.parent.findVariable(e);return i instanceof di&&this.accessedOutsideVariables.set(e,i),i}}const en={"!":e=>!e,"+":e=>+e,"-":e=>-e,delete:()=>q,typeof:e=>typeof e,void:()=>{},"~":e=>~e};function tn(e,t){return null!==e.renderBaseName&&t.has(e)&&e.isReassigned}class sn extends vt{deoptimizePath(){for(const e of this.declarations)e.deoptimizePath(B)}hasEffectsOnInteractionAtPath(){return!1}include(e,t,{asSingleStatement:i}=se){this.included=!0;for(const s of this.declarations)(t||s.shouldBeIncluded(e))&&s.include(e,t),i&&s.id.include(e,t)}initialise(){for(const e of this.declarations)e.declareDeclarator(this.kind)}render(e,t,i=se){if(function(e,t){for(const i of e){if(!i.id.included)return!1;if(i.id.type===at){if(t.has(i.id.variable))return!1}else{const e=[];if(i.id.addExportedVariables(e,t),e.length>0)return!1}}return!0}(this.declarations,t.exportNamesByVariable)){for(const i of this.declarations)i.render(e,t);i.isNoStatement||59===e.original.charCodeAt(this.end-1)||e.appendLeft(this.end,";")}else this.renderReplacedDeclarations(e,t)}applyDeoptimizations(){}renderDeclarationEnd(e,t,i,s,n,r,a){59===e.original.charCodeAt(this.end-1)&&e.remove(this.end-1,this.end),t+=";",null!==i?(10!==e.original.charCodeAt(s-1)||10!==e.original.charCodeAt(this.end)&&13!==e.original.charCodeAt(this.end)||(s--,13===e.original.charCodeAt(s)&&s--),s===i+1?e.overwrite(i,n,t):(e.overwrite(i,i+1,t),e.remove(s,n))):e.appendLeft(n,t),r.length>0&&e.appendLeft(n,` ${Ti(r,a)};`)}renderReplacedDeclarations(e,t){const i=Ii(this.declarations,e,this.start+this.kind.length,this.end-(59===e.original.charCodeAt(this.end-1)?1:0));let s,n;n=vi(e.original,this.start+this.kind.length);let r=n-1;e.remove(this.start,r);let a,o,l=!1,h=!1,c="";const u=[],d=function(e,t,i){var s;let n=null;if("system"===t.format){for(const{node:r}of e)r.id instanceof fi&&r.init&&0===i.length&&1===(null===(s=t.exportNamesByVariable.get(r.id.variable))||void 0===s?void 0:s.length)?(n=r.id.variable,i.push(n)):r.id.addExportedVariables(i,t.exportNamesByVariable);i.length>1?n=null:n&&(i.length=0)}return n}(i,t,u);for(const{node:u,start:p,separator:f,contentEnd:m,end:g}of i)if(u.included){if(u.render(e,t),a="",o="",!u.id.included||u.id instanceof fi&&tn(u.id.variable,t.exportNamesByVariable))h&&(c+=";"),l=!1;else{if(d&&d===u.id.variable){const i=Ei(e.original,"=",u.id.end);Oi(d,vi(e.original,i+1),null===f?m:f,e,t)}l?c+=",":(h&&(c+=";"),a+=`${this.kind} `,l=!0)}n===r+1?e.overwrite(r,n,c+a):(e.overwrite(r,r+1,c),e.appendLeft(n,a)),s=m,n=g,h=!0,r=f,c=""}else e.remove(p,g);this.renderDeclarationEnd(e,c,r,s,n,u,t)}}const nn={ArrayExpression:class extends vt{constructor(){super(...arguments),this.objectEntity=null}deoptimizePath(e){this.getObjectEntity().deoptimizePath(e)}deoptimizeThisOnInteractionAtPath(e,t,i){this.getObjectEntity().deoptimizeThisOnInteractionAtPath(e,t,i)}getLiteralValueAtPath(e,t,i){return this.getObjectEntity().getLiteralValueAtPath(e,t,i)}getReturnExpressionWhenCalledAtPath(e,t,i,s){return this.getObjectEntity().getReturnExpressionWhenCalledAtPath(e,t,i,s)}hasEffectsOnInteractionAtPath(e,t,i){return this.getObjectEntity().hasEffectsOnInteractionAtPath(e,t,i)}applyDeoptimizations(){this.deoptimized=!0;let e=!1;for(let t=0;t<this.elements.length;t++){const i=this.elements[t];i&&(e||i instanceof St)&&(e=!0,i.deoptimizePath(F))}this.context.requestTreeshakingPass()}getObjectEntity(){if(null!==this.objectEntity)return this.objectEntity;const e=[{key:"length",kind:"init",property:je}];let t=!1;for(let i=0;i<this.elements.length;i++){const s=this.elements[i];t||s instanceof St?s&&(t=!0,e.unshift({key:V,kind:"init",property:s})):s?e.push({key:String(i),kind:"init",property:s}):e.push({key:String(i),kind:"init",property:Ve})}return this.objectEntity=new Nt(e,Ht)}},ArrayPattern:class extends vt{addExportedVariables(e,t){for(const i of this.elements)null==i||i.addExportedVariables(e,t)}declare(e){const t=[];for(const i of this.elements)null!==i&&t.push(...i.declare(e,Y));return t}deoptimizePath(){for(const e of this.elements)null==e||e.deoptimizePath(B)}hasEffectsOnInteractionAtPath(e,t,i){for(const e of this.elements)if(null==e?void 0:e.hasEffectsOnInteractionAtPath(B,t,i))return!0;return!1}markDeclarationReached(){for(const e of this.elements)null==e||e.markDeclarationReached()}},ArrowFunctionExpression:$i,AssignmentExpression:class extends vt{hasEffects(e){const{deoptimized:t,left:i,right:s}=this;return t||this.applyDeoptimizations(),s.hasEffects(e)||i.hasEffectsAsAssignmentTarget(e,"="!==this.operator)}hasEffectsOnInteractionAtPath(e,t,i){return this.right.hasEffectsOnInteractionAtPath(e,t,i)}include(e,t){const{deoptimized:i,left:s,right:n,operator:r}=this;i||this.applyDeoptimizations(),this.included=!0,(t||"="!==r||s.included||s.hasEffectsAsAssignmentTarget(De(),!1))&&s.includeAsAssignmentTarget(e,t,"="!==r),n.include(e,t)}initialise(){this.left.setAssignedValue(this.right)}render(e,t,{preventASI:i,renderedParentType:s,renderedSurroundingElement:n}=se){const{left:r,right:a,start:o,end:l,parent:h}=this;if(r.included)r.render(e,t),a.render(e,t);else{const l=vi(e.original,Ei(e.original,"=",r.end)+1);e.remove(o,l),i&&Pi(e,l,a.start),a.render(e,t,{renderedParentType:s||h.type,renderedSurroundingElement:n||h.type})}if("system"===t.format)if(r instanceof fi){const i=r.variable,s=t.exportNamesByVariable.get(i);if(s)return void(1===s.length?Oi(i,o,l,e,t):Ri(i,o,l,h.type!==rt,e,t))}else{const i=[];if(r.addExportedVariables(i,t.exportNamesByVariable),i.length>0)return void function(e,t,i,s,n,r){const{_:a,getDirectReturnIifeLeft:o}=r.snippets;n.prependRight(t,o(["v"],`${Ti(e,r)},${a}v`,{needsArrowReturnParens:!0,needsWrappedFunction:s})),n.appendLeft(i,")")}(i,o,l,n===rt,e,t)}r.included&&r instanceof Mi&&(n===rt||n===it)&&(e.appendRight(o,"("),e.prependLeft(l,")"))}applyDeoptimizations(){this.deoptimized=!0,this.left.deoptimizePath(B),this.right.deoptimizePath(F),this.context.requestTreeshakingPass()}},AssignmentPattern:class extends vt{addExportedVariables(e,t){this.left.addExportedVariables(e,t)}declare(e,t){return this.left.declare(e,t)}deoptimizePath(e){0===e.length&&this.left.deoptimizePath(e)}hasEffectsOnInteractionAtPath(e,t,i){return e.length>0||this.left.hasEffectsOnInteractionAtPath(B,t,i)}markDeclarationReached(){this.left.markDeclarationReached()}render(e,t,{isShorthandProperty:i}=se){this.left.render(e,t,{isShorthandProperty:i}),this.right.render(e,t)}applyDeoptimizations(){this.deoptimized=!0,this.left.deoptimizePath(B),this.right.deoptimizePath(F),this.context.requestTreeshakingPass()}},AwaitExpression:class extends vt{hasEffects(){return this.deoptimized||this.applyDeoptimizations(),!0}include(e,t){if(this.deoptimized||this.applyDeoptimizations(),!this.included){this.included=!0;e:if(!this.context.usesTopLevelAwait){let e=this.parent;do{if(e instanceof Bi||e instanceof $i)break e}while(e=e.parent);this.context.usesTopLevelAwait=!0}}this.argument.include(e,t)}},BinaryExpression:class extends vt{deoptimizeCache(){}getLiteralValueAtPath(e,t,i){if(e.length>0)return q;const s=this.left.getLiteralValueAtPath(B,t,i);if("symbol"==typeof s)return q;const n=this.right.getLiteralValueAtPath(B,t,i);if("symbol"==typeof n)return q;const r=Fi[this.operator];return r?r(s,n):q}hasEffects(e){return"+"===this.operator&&this.parent instanceof wi&&""===this.left.getLiteralValueAtPath(B,H,this)||super.hasEffects(e)}hasEffectsOnInteractionAtPath(e,{type:t}){return 0!==t||e.length>1}render(e,t,{renderedSurroundingElement:i}=se){this.left.render(e,t,{renderedSurroundingElement:i}),this.right.render(e,t)}},BlockStatement:Ci,BreakStatement:class extends vt{hasEffects(e){if(this.label){if(!e.ignore.labels.has(this.label.name))return!0;e.includedLabels.add(this.label.name),e.brokenFlow=2}else{if(!e.ignore.breaks)return!0;e.brokenFlow=1}return!1}include(e){this.included=!0,this.label&&(this.label.include(),e.includedLabels.add(this.label.name)),e.brokenFlow=this.label?2:1}},CallExpression:class extends qi{bind(){super.bind(),this.callee instanceof fi&&(this.scope.findVariable(this.callee.name).isNamespace&&this.context.warn({code:"CANNOT_CALL_NAMESPACE",message:`Cannot call a namespace ('${this.callee.name}')`},this.start),"eval"===this.callee.name&&this.context.warn({code:"EVAL",message:"Use of eval is strongly discouraged, as it poses security risks and may cause issues with minification",url:"https://rollupjs.org/guide/en/#avoiding-eval"},this.start)),this.interaction={args:this.arguments,thisArg:this.callee instanceof Hi&&!this.callee.variable?this.callee.object:null,type:2,withNew:!1}}hasEffects(e){try{for(const t of this.arguments)if(t.hasEffects(e))return!0;return(!this.context.options.treeshake.annotations||!this.annotations)&&(this.callee.hasEffects(e)||this.callee.hasEffectsOnInteractionAtPath(B,this.interaction,e))}finally{this.deoptimized||this.applyDeoptimizations()}}include(e,t){this.deoptimized||this.applyDeoptimizations(),t?(super.include(e,t),t===bt&&this.callee instanceof fi&&this.callee.variable&&this.callee.variable.markCalledFromTryStatement()):(this.included=!0,this.callee.include(e,!1)),this.callee.includeCallArguments(e,this.arguments)}render(e,t,{renderedSurroundingElement:i}=se){this.callee.render(e,t,{isCalleeOfRenderedParent:!0,renderedSurroundingElement:i}),zi(e,t,this)}applyDeoptimizations(){this.deoptimized=!0,this.interaction.thisArg&&this.callee.deoptimizeThisOnInteractionAtPath(this.interaction,B,H);for(const e of this.arguments)e.deoptimizePath(F);this.context.requestTreeshakingPass()}getReturnExpression(e=H){return null===this.returnExpression?(this.returnExpression=Y,this.returnExpression=this.callee.getReturnExpressionWhenCalledAtPath(B,this.interaction,e,this)):this.returnExpression}},CatchClause:class extends vt{createScope(e){this.scope=new Ki(e,this.context)}parseNode(e){const{param:t}=e;t&&(this.param=new(this.context.getNodeConstructor(t.type))(t,this,this.scope),this.param.declare("parameter",Y)),super.parseNode(e)}},ChainExpression:class extends vt{},ClassBody:class extends vt{createScope(e){this.scope=new Xi(e,this.parent,this.context)}include(e,t){this.included=!0,this.context.includeVariableInModule(this.scope.thisVariable);for(const i of this.body)i.include(e,t)}parseNode(e){const t=this.body=[];for(const i of e.body)t.push(new(this.context.getNodeConstructor(i.type))(i,this,i.static?this.scope:this.scope.instanceScope));super.parseNode(e)}applyDeoptimizations(){}},ClassDeclaration:es,ClassExpression:class extends Zi{render(e,t,{renderedSurroundingElement:i}=se){super.render(e,t),i===rt&&(e.appendRight(this.start,"("),e.prependLeft(this.end,")"))}},ConditionalExpression:class extends vt{constructor(){super(...arguments),this.expressionsToBeDeoptimized=[],this.isBranchResolutionAnalysed=!1,this.usedBranch=null}deoptimizeCache(){if(null!==this.usedBranch){const e=this.usedBranch===this.consequent?this.alternate:this.consequent;this.usedBranch=null,e.deoptimizePath(F);for(const e of this.expressionsToBeDeoptimized)e.deoptimizeCache()}}deoptimizePath(e){const t=this.getUsedBranch();t?t.deoptimizePath(e):(this.consequent.deoptimizePath(e),this.alternate.deoptimizePath(e))}deoptimizeThisOnInteractionAtPath(e,t,i){this.consequent.deoptimizeThisOnInteractionAtPath(e,t,i),this.alternate.deoptimizeThisOnInteractionAtPath(e,t,i)}getLiteralValueAtPath(e,t,i){const s=this.getUsedBranch();return s?(this.expressionsToBeDeoptimized.push(i),s.getLiteralValueAtPath(e,t,i)):q}getReturnExpressionWhenCalledAtPath(e,t,i,s){const n=this.getUsedBranch();return n?(this.expressionsToBeDeoptimized.push(s),n.getReturnExpressionWhenCalledAtPath(e,t,i,s)):new ts([this.consequent.getReturnExpressionWhenCalledAtPath(e,t,i,s),this.alternate.getReturnExpressionWhenCalledAtPath(e,t,i,s)])}hasEffects(e){if(this.test.hasEffects(e))return!0;const t=this.getUsedBranch();return t?t.hasEffects(e):this.consequent.hasEffects(e)||this.alternate.hasEffects(e)}hasEffectsOnInteractionAtPath(e,t,i){const s=this.getUsedBranch();return s?s.hasEffectsOnInteractionAtPath(e,t,i):this.consequent.hasEffectsOnInteractionAtPath(e,t,i)||this.alternate.hasEffectsOnInteractionAtPath(e,t,i)}include(e,t){this.included=!0;const i=this.getUsedBranch();t||this.test.shouldBeIncluded(e)||null===i?(this.test.include(e,t),this.consequent.include(e,t),this.alternate.include(e,t)):i.include(e,t)}includeCallArguments(e,t){const i=this.getUsedBranch();i?i.includeCallArguments(e,t):(this.consequent.includeCallArguments(e,t),this.alternate.includeCallArguments(e,t))}render(e,t,{isCalleeOfRenderedParent:i,preventASI:s,renderedParentType:n,renderedSurroundingElement:r}=se){const a=this.getUsedBranch();if(this.test.included)this.test.render(e,t,{renderedSurroundingElement:r}),this.consequent.render(e,t),this.alternate.render(e,t);else{const o=Ei(e.original,":",this.consequent.end),l=vi(e.original,(this.consequent.included?Ei(e.original,"?",this.test.end):o)+1);s&&Pi(e,l,a.start),e.remove(this.start,l),this.consequent.included&&e.remove(o,this.end),yi(this,e),a.render(e,t,{isCalleeOfRenderedParent:i,preventASI:!0,renderedParentType:n||this.parent.type,renderedSurroundingElement:r||this.parent.type})}}getUsedBranch(){if(this.isBranchResolutionAnalysed)return this.usedBranch;this.isBranchResolutionAnalysed=!0;const e=this.test.getLiteralValueAtPath(B,H,this);return"symbol"==typeof e?null:this.usedBranch=e?this.consequent:this.alternate}},ContinueStatement:class extends vt{hasEffects(e){if(this.label){if(!e.ignore.labels.has(this.label.name))return!0;e.includedLabels.add(this.label.name),e.brokenFlow=2}else{if(!e.ignore.continues)return!0;e.brokenFlow=1}return!1}include(e){this.included=!0,this.label&&(this.label.include(),e.includedLabels.add(this.label.name)),e.brokenFlow=this.label?2:1}},DoWhileStatement:class extends vt{hasEffects(e){if(this.test.hasEffects(e))return!0;const{brokenFlow:t,ignore:{breaks:i,continues:s}}=e;return e.ignore.breaks=!0,e.ignore.continues=!0,!!this.body.hasEffects(e)||(e.ignore.breaks=i,e.ignore.continues=s,e.brokenFlow=t,!1)}include(e,t){this.included=!0,this.test.include(e,t);const{brokenFlow:i}=e;this.body.include(e,t,{asSingleStatement:!0}),e.brokenFlow=i}},EmptyStatement:class extends vt{hasEffects(){return!1}},ExportAllDeclaration:is,ExportDefaultDeclaration:ns,ExportNamedDeclaration:rs,ExportSpecifier:class extends vt{applyDeoptimizations(){}},ExpressionStatement:wi,ForInStatement:class extends vt{createScope(e){this.scope=new ki(e)}hasEffects(e){const{deoptimized:t,left:i,right:s}=this;if(t||this.applyDeoptimizations(),i.hasEffectsAsAssignmentTarget(e,!1)||s.hasEffects(e))return!0;const{brokenFlow:n,ignore:{breaks:r,continues:a}}=e;return e.ignore.breaks=!0,e.ignore.continues=!0,!!this.body.hasEffects(e)||(e.ignore.breaks=r,e.ignore.continues=a,e.brokenFlow=n,!1)}include(e,t){const{body:i,deoptimized:s,left:n,right:r}=this;s||this.applyDeoptimizations(),this.included=!0,n.includeAsAssignmentTarget(e,t||!0,!1),r.include(e,t);const{brokenFlow:a}=e;i.include(e,t,{asSingleStatement:!0}),e.brokenFlow=a}initialise(){this.left.setAssignedValue(Y)}render(e,t){this.left.render(e,t,xi),this.right.render(e,t,xi),110===e.original.charCodeAt(this.right.start-1)&&e.prependLeft(this.right.start," "),this.body.render(e,t)}applyDeoptimizations(){this.deoptimized=!0,this.left.deoptimizePath(B),this.context.requestTreeshakingPass()}},ForOfStatement:class extends vt{createScope(e){this.scope=new ki(e)}hasEffects(){return this.deoptimized||this.applyDeoptimizations(),!0}include(e,t){const{body:i,deoptimized:s,left:n,right:r}=this;s||this.applyDeoptimizations(),this.included=!0,n.includeAsAssignmentTarget(e,t||!0,!1),r.include(e,t);const{brokenFlow:a}=e;i.include(e,t,{asSingleStatement:!0}),e.brokenFlow=a}initialise(){this.left.setAssignedValue(Y)}render(e,t){this.left.render(e,t,xi),this.right.render(e,t,xi),102===e.original.charCodeAt(this.right.start-1)&&e.prependLeft(this.right.start," "),this.body.render(e,t)}applyDeoptimizations(){this.deoptimized=!0,this.left.deoptimizePath(B),this.context.requestTreeshakingPass()}},ForStatement:class extends vt{createScope(e){this.scope=new ki(e)}hasEffects(e){var t,i,s;if((null===(t=this.init)||void 0===t?void 0:t.hasEffects(e))||(null===(i=this.test)||void 0===i?void 0:i.hasEffects(e))||(null===(s=this.update)||void 0===s?void 0:s.hasEffects(e)))return!0;const{brokenFlow:n,ignore:{breaks:r,continues:a}}=e;return e.ignore.breaks=!0,e.ignore.continues=!0,!!this.body.hasEffects(e)||(e.ignore.breaks=r,e.ignore.continues=a,e.brokenFlow=n,!1)}include(e,t){var i,s,n;this.included=!0,null===(i=this.init)||void 0===i||i.include(e,t,{asSingleStatement:!0}),null===(s=this.test)||void 0===s||s.include(e,t);const{brokenFlow:r}=e;null===(n=this.update)||void 0===n||n.include(e,t),this.body.include(e,t,{asSingleStatement:!0}),e.brokenFlow=r}render(e,t){var i,s,n;null===(i=this.init)||void 0===i||i.render(e,t,xi),null===(s=this.test)||void 0===s||s.render(e,t,xi),null===(n=this.update)||void 0===n||n.render(e,t,xi),this.body.render(e,t)}},FunctionDeclaration:ss,FunctionExpression:class extends Bi{render(e,t,{renderedSurroundingElement:i}=se){super.render(e,t),i===rt&&(e.appendRight(this.start,"("),e.prependLeft(this.end,")"))}},Identifier:fi,IfStatement:ls,ImportDeclaration:hs,ImportDefaultSpecifier:class extends vt{applyDeoptimizations(){}},ImportExpression:class extends vt{constructor(){super(...arguments),this.inlineNamespace=null,this.mechanism=null,this.resolution=null}hasEffects(){return!0}include(e,t){this.included||(this.included=!0,this.context.includeDynamicImport(this),this.scope.addAccessedDynamicImport(this)),this.source.include(e,t)}initialise(){this.context.addDynamicImport(this)}render(e,t){if(this.inlineNamespace){const{snippets:{getDirectReturnFunction:i,getPropertyAccess:s}}=t,[n,r]=i([],{functionReturn:!0,lineBreakIndent:null,name:null});e.overwrite(this.start,this.end,`Promise.resolve().then(${n}${this.inlineNamespace.getName(s)}${r})`,{contentOnly:!0})}else this.mechanism&&(e.overwrite(this.start,Ei(e.original,"(",this.start+6)+1,this.mechanism.left,{contentOnly:!0}),e.overwrite(this.end-1,this.end,this.mechanism.right,{contentOnly:!0})),this.source.render(e,t)}renderFinalResolution(e,t,i,{getDirectReturnFunction:s}){if(e.overwrite(this.source.start,this.source.end,t),i){const[t,n]=s(["n"],{functionReturn:!0,lineBreakIndent:null,name:null});e.prependLeft(this.end,`.then(${t}n.${i}${n})`)}}setExternalResolution(e,t,i,s,n,r){const{format:a}=i;this.inlineNamespace=null,this.resolution=t;const o=[...Ds[a]||[]];let l;({helper:l,mechanism:this.mechanism}=this.getDynamicImportMechanismAndHelper(t,e,i,s,n)),l&&o.push(l),o.length>0&&this.scope.addAccessedGlobals(o,r)}setInternalResolution(e){this.inlineNamespace=e}applyDeoptimizations(){}getDynamicImportMechanismAndHelper(e,t,{compact:i,dynamicImportFunction:s,format:n,generatedCode:{arrowFunctions:r},interop:a},{_:o,getDirectReturnFunction:l,getDirectReturnIifeLeft:h},c){const u=c.hookFirstSync("renderDynamicImport",[{customResolution:"string"==typeof this.resolution?this.resolution:null,format:n,moduleId:this.context.module.id,targetModuleId:this.resolution&&"string"!=typeof this.resolution?this.resolution.id:null}]);if(u)return{helper:null,mechanism:u};const d=!this.resolution||"string"==typeof this.resolution;switch(n){case"cjs":{const i=Ms(e,t,a);let s="require(",n=")";i&&(s=`/*#__PURE__*/${i}(${s}`,n+=")");const[o,c]=l([],{functionReturn:!0,lineBreakIndent:null,name:null});return s=`Promise.resolve().then(${o}${s}`,n+=`${c})`,!r&&d&&(s=h(["t"],`${s}t${n}`,{needsArrowReturnParens:!1,needsWrappedFunction:!0}),n=")"),{helper:i,mechanism:{left:s,right:n}}}case"amd":{const s=i?"c":"resolve",n=i?"e":"reject",c=Ms(e,t,a),[u,p]=l(["m"],{functionReturn:!1,lineBreakIndent:null,name:null}),f=c?`${u}${s}(/*#__PURE__*/${c}(m))${p}`:s,[m,g]=l([s,n],{functionReturn:!1,lineBreakIndent:null,name:null});let y=`new Promise(${m}require([`,x=`],${o}${f},${o}${n})${g})`;return!r&&d&&(y=h(["t"],`${y}t${x}`,{needsArrowReturnParens:!1,needsWrappedFunction:!0}),x=")"),{helper:c,mechanism:{left:y,right:x}}}case"system":return{helper:null,mechanism:{left:"module.import(",right:")"}};case"es":if(s)return{helper:null,mechanism:{left:`${s}(`,right:")"}}}return{helper:null,mechanism:null}}},ImportNamespaceSpecifier:class extends vt{applyDeoptimizations(){}},ImportSpecifier:class extends vt{applyDeoptimizations(){}},LabeledStatement:class extends vt{hasEffects(e){const t=e.brokenFlow;return e.ignore.labels.add(this.label.name),!!this.body.hasEffects(e)||(e.ignore.labels.delete(this.label.name),e.includedLabels.has(this.label.name)&&(e.includedLabels.delete(this.label.name),e.brokenFlow=t),!1)}include(e,t){this.included=!0;const i=e.brokenFlow;this.body.include(e,t),(t||e.includedLabels.has(this.label.name))&&(this.label.include(),e.includedLabels.delete(this.label.name),e.brokenFlow=i)}render(e,t){this.label.included?this.label.render(e,t):e.remove(this.start,vi(e.original,Ei(e.original,":",this.label.end)+1)),this.body.render(e,t)}},Literal:ji,LogicalExpression:class extends vt{constructor(){super(...arguments),this.expressionsToBeDeoptimized=[],this.isBranchResolutionAnalysed=!1,this.usedBranch=null}deoptimizeCache(){if(this.usedBranch){const e=this.usedBranch===this.left?this.right:this.left;this.usedBranch=null,e.deoptimizePath(F);for(const e of this.expressionsToBeDeoptimized)e.deoptimizeCache();this.context.requestTreeshakingPass()}}deoptimizePath(e){const t=this.getUsedBranch();t?t.deoptimizePath(e):(this.left.deoptimizePath(e),this.right.deoptimizePath(e))}deoptimizeThisOnInteractionAtPath(e,t,i){this.left.deoptimizeThisOnInteractionAtPath(e,t,i),this.right.deoptimizeThisOnInteractionAtPath(e,t,i)}getLiteralValueAtPath(e,t,i){const s=this.getUsedBranch();return s?(this.expressionsToBeDeoptimized.push(i),s.getLiteralValueAtPath(e,t,i)):q}getReturnExpressionWhenCalledAtPath(e,t,i,s){const n=this.getUsedBranch();return n?(this.expressionsToBeDeoptimized.push(s),n.getReturnExpressionWhenCalledAtPath(e,t,i,s)):new ts([this.left.getReturnExpressionWhenCalledAtPath(e,t,i,s),this.right.getReturnExpressionWhenCalledAtPath(e,t,i,s)])}hasEffects(e){return!!this.left.hasEffects(e)||this.getUsedBranch()!==this.left&&this.right.hasEffects(e)}hasEffectsOnInteractionAtPath(e,t,i){const s=this.getUsedBranch();return s?s.hasEffectsOnInteractionAtPath(e,t,i):this.left.hasEffectsOnInteractionAtPath(e,t,i)||this.right.hasEffectsOnInteractionAtPath(e,t,i)}include(e,t){this.included=!0;const i=this.getUsedBranch();t||i===this.right&&this.left.shouldBeIncluded(e)||!i?(this.left.include(e,t),this.right.include(e,t)):i.include(e,t)}render(e,t,{isCalleeOfRenderedParent:i,preventASI:s,renderedParentType:n,renderedSurroundingElement:r}=se){if(this.left.included&&this.right.included)this.left.render(e,t,{preventASI:s,renderedSurroundingElement:r}),this.right.render(e,t);else{const a=Ei(e.original,this.operator,this.left.end);if(this.right.included){const t=vi(e.original,a+2);e.remove(this.start,t),s&&Pi(e,t,this.right.start)}else e.remove(a,this.end);yi(this,e),this.getUsedBranch().render(e,t,{isCalleeOfRenderedParent:i,preventASI:s,renderedParentType:n||this.parent.type,renderedSurroundingElement:r||this.parent.type})}}getUsedBranch(){if(!this.isBranchResolutionAnalysed){this.isBranchResolutionAnalysed=!0;const e=this.left.getLiteralValueAtPath(B,H,this);if("symbol"==typeof e)return null;this.usedBranch="||"===this.operator&&e||"&&"===this.operator&&!e||"??"===this.operator&&null!=e?this.left:this.right}return this.usedBranch}},MemberExpression:Hi,MetaProperty:class extends vt{addAccessedGlobals(e,t){const i=this.metaProperty,s=(i&&(i.startsWith(Bs)||i.startsWith(Ls)||i.startsWith(Vs))?zs:Fs)[e];s.length>0&&this.scope.addAccessedGlobals(s,t)}getReferencedFileName(e){const t=this.metaProperty;return t&&t.startsWith(Bs)?e.getFileName(t.substring(Bs.length)):null}hasEffects(){return!1}hasEffectsOnInteractionAtPath(e,{type:t}){return e.length>1||0!==t}include(){if(!this.included&&(this.included=!0,"import"===this.meta.name)){this.context.addImportMeta(this);const e=this.parent;this.metaProperty=e instanceof Hi&&"string"==typeof e.propertyKey?e.propertyKey:null}}renderFinalMechanism(e,t,i,s,n){var r;const a=this.parent,o=this.metaProperty;if(o&&(o.startsWith(Bs)||o.startsWith(Ls)||o.startsWith(Vs))){let s,r=null,l=null,h=null;o.startsWith(Bs)?(r=o.substring(Bs.length),s=n.getFileName(r)):o.startsWith(Ls)?(ke(`Using the "${Ls}" prefix to reference files is deprecated. Use the "${Bs}" prefix instead.`,!0,this.context.options),l=o.substring(Ls.length),s=n.getFileName(l)):(ke(`Using the "${Vs}" prefix to reference files is deprecated. Use the "${Bs}" prefix instead.`,!0,this.context.options),h=o.substring(Vs.length),s=n.getFileName(h));const c=N(O($(t),s));let u;return null!==l&&(u=n.hookFirstSync("resolveAssetUrl",[{assetFileName:s,chunkId:t,format:i,moduleId:this.context.module.id,relativeAssetPath:c}])),u||(u=n.hookFirstSync("resolveFileUrl",[{assetReferenceId:l,chunkId:t,chunkReferenceId:h,fileName:s,format:i,moduleId:this.context.module.id,referenceId:r||l||h,relativePath:c}])||Ws[i](c)),void e.overwrite(a.start,a.end,u,{contentOnly:!0})}const l=n.hookFirstSync("resolveImportMeta",[o,{chunkId:t,format:i,moduleId:this.context.module.id}])||(null===(r=qs[i])||void 0===r?void 0:r.call(qs,o,{chunkId:t,snippets:s}));"string"==typeof l&&(a instanceof Hi?e.overwrite(a.start,a.end,l,{contentOnly:!0}):e.overwrite(this.start,this.end,l,{contentOnly:!0}))}},MethodDefinition:Qi,NewExpression:class extends vt{hasEffects(e){try{for(const t of this.arguments)if(t.hasEffects(e))return!0;return(!this.context.options.treeshake.annotations||!this.annotations)&&(this.callee.hasEffects(e)||this.callee.hasEffectsOnInteractionAtPath(B,this.interaction,e))}finally{this.deoptimized||this.applyDeoptimizations()}}hasEffectsOnInteractionAtPath(e,{type:t}){return e.length>0||0!==t}include(e,t){this.deoptimized||this.applyDeoptimizations(),t?super.include(e,t):(this.included=!0,this.callee.include(e,!1)),this.callee.includeCallArguments(e,this.arguments)}initialise(){this.interaction={args:this.arguments,thisArg:null,type:2,withNew:!0}}render(e,t){this.callee.render(e,t),zi(e,t,this)}applyDeoptimizations(){this.deoptimized=!0;for(const e of this.arguments)e.deoptimizePath(F);this.context.requestTreeshakingPass()}},ObjectExpression:class extends vt{constructor(){super(...arguments),this.objectEntity=null}deoptimizeCache(){this.getObjectEntity().deoptimizeAllProperties()}deoptimizePath(e){this.getObjectEntity().deoptimizePath(e)}deoptimizeThisOnInteractionAtPath(e,t,i){this.getObjectEntity().deoptimizeThisOnInteractionAtPath(e,t,i)}getLiteralValueAtPath(e,t,i){return this.getObjectEntity().getLiteralValueAtPath(e,t,i)}getReturnExpressionWhenCalledAtPath(e,t,i,s){return this.getObjectEntity().getReturnExpressionWhenCalledAtPath(e,t,i,s)}hasEffectsOnInteractionAtPath(e,t,i){return this.getObjectEntity().hasEffectsOnInteractionAtPath(e,t,i)}render(e,t,{renderedSurroundingElement:i}=se){super.render(e,t),i!==rt&&i!==it||(e.appendRight(this.start,"("),e.prependLeft(this.end,")"))}applyDeoptimizations(){}getObjectEntity(){if(null!==this.objectEntity)return this.objectEntity;let e=Tt;const t=[];for(const i of this.properties){if(i instanceof St){t.push({key:D,kind:"init",property:i});continue}let s;if(i.computed){const e=i.key.getLiteralValueAtPath(B,H,this);if("symbol"==typeof e){t.push({key:D,kind:i.kind,property:i});continue}s=String(e)}else if(s=i.key instanceof fi?i.key.name:String(i.key.value),"__proto__"===s&&"init"===i.kind){e=i.value instanceof ji&&null===i.value.value?null:i.value;continue}t.push({key:s,kind:i.kind,property:i})}return this.objectEntity=new Nt(t,e)}},ObjectPattern:Mi,PrivateIdentifier:class extends vt{},Program:Ks,Property:class extends Yi{constructor(){super(...arguments),this.declarationInit=null}declare(e,t){return this.declarationInit=t,this.value.declare(e,Y)}hasEffects(e){this.deoptimized||this.applyDeoptimizations();const t=this.context.options.treeshake.propertyReadSideEffects;return"ObjectPattern"===this.parent.type&&"always"===t||this.key.hasEffects(e)||this.value.hasEffects(e)}markDeclarationReached(){this.value.markDeclarationReached()}render(e,t){this.shorthand||this.key.render(e,t),this.value.render(e,t,{isShorthandProperty:this.shorthand})}applyDeoptimizations(){this.deoptimized=!0,null!==this.declarationInit&&(this.declarationInit.deoptimizePath([D,D]),this.context.requestTreeshakingPass())}},PropertyDefinition:class extends vt{deoptimizePath(e){var t;null===(t=this.value)||void 0===t||t.deoptimizePath(e)}deoptimizeThisOnInteractionAtPath(e,t,i){var s;null===(s=this.value)||void 0===s||s.deoptimizeThisOnInteractionAtPath(e,t,i)}getLiteralValueAtPath(e,t,i){return this.value?this.value.getLiteralValueAtPath(e,t,i):q}getReturnExpressionWhenCalledAtPath(e,t,i,s){return this.value?this.value.getReturnExpressionWhenCalledAtPath(e,t,i,s):Y}hasEffects(e){var t;return this.key.hasEffects(e)||this.static&&!!(null===(t=this.value)||void 0===t?void 0:t.hasEffects(e))}hasEffectsOnInteractionAtPath(e,t,i){return!this.value||this.value.hasEffectsOnInteractionAtPath(e,t,i)}applyDeoptimizations(){}},RestElement:Ni,ReturnStatement:class extends vt{hasEffects(e){var t;return!(e.ignore.returnYield&&!(null===(t=this.argument)||void 0===t?void 0:t.hasEffects(e))&&(e.brokenFlow=2,1))}include(e,t){var i;this.included=!0,null===(i=this.argument)||void 0===i||i.include(e,t),e.brokenFlow=2}initialise(){this.scope.addReturnExpression(this.argument||Y)}render(e,t){this.argument&&(this.argument.render(e,t,{preventASI:!0}),this.argument.start===this.start+6&&e.prependLeft(this.start+6," "))}},SequenceExpression:class extends vt{deoptimizePath(e){this.expressions[this.expressions.length-1].deoptimizePath(e)}deoptimizeThisOnInteractionAtPath(e,t,i){this.expressions[this.expressions.length-1].deoptimizeThisOnInteractionAtPath(e,t,i)}getLiteralValueAtPath(e,t,i){return this.expressions[this.expressions.length-1].getLiteralValueAtPath(e,t,i)}hasEffects(e){for(const t of this.expressions)if(t.hasEffects(e))return!0;return!1}hasEffectsOnInteractionAtPath(e,t,i){return this.expressions[this.expressions.length-1].hasEffectsOnInteractionAtPath(e,t,i)}include(e,t){this.included=!0;const i=this.expressions[this.expressions.length-1];for(const s of this.expressions)(t||s===i&&!(this.parent instanceof wi)||s.shouldBeIncluded(e))&&s.include(e,t)}render(e,t,{renderedParentType:i,isCalleeOfRenderedParent:s,preventASI:n}=se){let r=0,a=null;const o=this.expressions[this.expressions.length-1];for(const{node:l,separator:h,start:c,end:u}of Ii(this.expressions,e,this.start,this.end))if(l.included)if(r++,a=h,1===r&&n&&Pi(e,c,l.start),1===r){const n=i||this.parent.type;l.render(e,t,{isCalleeOfRenderedParent:s&&l===o,renderedParentType:n,renderedSurroundingElement:n})}else l.render(e,t);else gi(l,e,c,u);a&&e.remove(a,this.end)}},SpreadElement:St,StaticBlock:class extends vt{createScope(e){this.scope=new ki(e)}hasEffects(e){for(const t of this.body)if(t.hasEffects(e))return!0;return!1}include(e,t){this.included=!0;for(const i of this.body)(t||i.shouldBeIncluded(e))&&i.include(e,t)}render(e,t){this.body.length?Ai(this.body,e,this.start+1,this.end-1,t):super.render(e,t)}},Super:class extends vt{bind(){this.variable=this.scope.findVariable("this")}deoptimizePath(e){this.variable.deoptimizePath(e)}deoptimizeThisOnInteractionAtPath(e,t,i){this.variable.deoptimizeThisOnInteractionAtPath(e,t,i)}include(){this.included||(this.included=!0,this.context.includeVariableInModule(this.variable))}},SwitchCase:Xs,SwitchStatement:class extends vt{createScope(e){this.scope=new ki(e)}hasEffects(e){if(this.discriminant.hasEffects(e))return!0;const{brokenFlow:t,ignore:{breaks:i}}=e;let s=1/0;e.ignore.breaks=!0;for(const i of this.cases){if(i.hasEffects(e))return!0;s=e.brokenFlow<s?e.brokenFlow:s,e.brokenFlow=t}return null!==this.defaultCase&&1!==s&&(e.brokenFlow=s),e.ignore.breaks=i,!1}include(e,t){this.included=!0,this.discriminant.include(e,t);const{brokenFlow:i}=e;let s=1/0,n=t||null!==this.defaultCase&&this.defaultCase<this.cases.length-1;for(let r=this.cases.length-1;r>=0;r--){const a=this.cases[r];if(a.included&&(n=!0),!n){const e=De();e.ignore.breaks=!0,n=a.hasEffects(e)}n?(a.include(e,t),s=s<e.brokenFlow?s:e.brokenFlow,e.brokenFlow=i):s=i}n&&null!==this.defaultCase&&1!==s&&(e.brokenFlow=s)}initialise(){for(let e=0;e<this.cases.length;e++)if(null===this.cases[e].test)return void(this.defaultCase=e);this.defaultCase=null}render(e,t){this.discriminant.render(e,t),this.cases.length>0&&Ai(this.cases,e,this.cases[0].start,this.end-1,t)}},TaggedTemplateExpression:class extends qi{bind(){if(super.bind(),this.tag.type===at){const e=this.tag.name;this.scope.findVariable(e).isNamespace&&this.context.warn({code:"CANNOT_CALL_NAMESPACE",message:`Cannot call a namespace ('${e}')`},this.start)}}hasEffects(e){try{for(const t of this.quasi.expressions)if(t.hasEffects(e))return!0;return this.tag.hasEffects(e)||this.tag.hasEffectsOnInteractionAtPath(B,this.interaction,e)}finally{this.deoptimized||this.applyDeoptimizations()}}include(e,t){this.deoptimized||this.applyDeoptimizations(),t?super.include(e,t):(this.included=!0,this.tag.include(e,t),this.quasi.include(e,t)),this.tag.includeCallArguments(e,this.interaction.args);const i=this.getReturnExpression();i.included||i.include(e,!1)}initialise(){this.interaction={args:[Y,...this.quasi.expressions],thisArg:this.tag instanceof Hi&&!this.tag.variable?this.tag.object:null,type:2,withNew:!1}}render(e,t){this.tag.render(e,t,{isCalleeOfRenderedParent:!0}),this.quasi.render(e,t)}applyDeoptimizations(){this.deoptimized=!0,this.interaction.thisArg&&this.tag.deoptimizeThisOnInteractionAtPath(this.interaction,B,H);for(const e of this.quasi.expressions)e.deoptimizePath(F);this.context.requestTreeshakingPass()}getReturnExpression(e=H){return null===this.returnExpression?(this.returnExpression=Y,this.returnExpression=this.tag.getReturnExpressionWhenCalledAtPath(B,this.interaction,e,this)):this.returnExpression}},TemplateElement:class extends vt{bind(){}hasEffects(){return!1}include(){this.included=!0}parseNode(e){this.value=e.value,super.parseNode(e)}render(){}},TemplateLiteral:Ys,ThisExpression:class extends vt{bind(){this.variable=this.scope.findVariable("this")}deoptimizePath(e){this.variable.deoptimizePath(e)}deoptimizeThisOnInteractionAtPath(e,t,i){this.variable.deoptimizeThisOnInteractionAtPath(e.thisArg===this?{...e,thisArg:this.variable}:e,t,i)}hasEffectsOnInteractionAtPath(e,t,i){return 0===e.length?0!==t.type:this.variable.hasEffectsOnInteractionAtPath(e,t,i)}include(){this.included||(this.included=!0,this.context.includeVariableInModule(this.variable))}initialise(){this.alias=this.scope.findLexicalBoundary()instanceof Zs?this.context.moduleContext:null,"undefined"===this.alias&&this.context.warn({code:"THIS_IS_UNDEFINED",message:"The 'this' keyword is equivalent to 'undefined' at the top level of an ES module, and has been rewritten",url:"https://rollupjs.org/guide/en/#error-this-is-undefined"},this.start)}render(e){null!==this.alias&&e.overwrite(this.start,this.end,this.alias,{contentOnly:!1,storeName:!0})}},ThrowStatement:class extends vt{hasEffects(){return!0}include(e,t){this.included=!0,this.argument.include(e,t),e.brokenFlow=2}render(e,t){this.argument.render(e,t,{preventASI:!0}),this.argument.start===this.start+5&&e.prependLeft(this.start+5," ")}},TryStatement:class extends vt{constructor(){super(...arguments),this.directlyIncluded=!1,this.includedLabelsAfterBlock=null}hasEffects(e){var t;return(this.context.options.treeshake.tryCatchDeoptimization?this.block.body.length>0:this.block.hasEffects(e))||!!(null===(t=this.finalizer)||void 0===t?void 0:t.hasEffects(e))}include(e,t){var i,s;const n=null===(i=this.context.options.treeshake)||void 0===i?void 0:i.tryCatchDeoptimization,{brokenFlow:r}=e;if(this.directlyIncluded&&n){if(this.includedLabelsAfterBlock)for(const t of this.includedLabelsAfterBlock)e.includedLabels.add(t)}else this.included=!0,this.directlyIncluded=!0,this.block.include(e,n?bt:t),e.includedLabels.size>0&&(this.includedLabelsAfterBlock=[...e.includedLabels]),e.brokenFlow=r;null!==this.handler&&(this.handler.include(e,t),e.brokenFlow=r),null===(s=this.finalizer)||void 0===s||s.include(e,t)}},UnaryExpression:class extends vt{getLiteralValueAtPath(e,t,i){if(e.length>0)return q;const s=this.argument.getLiteralValueAtPath(B,t,i);return"symbol"==typeof s?q:en[this.operator](s)}hasEffects(e){return this.deoptimized||this.applyDeoptimizations(),!("typeof"===this.operator&&this.argument instanceof fi)&&(this.argument.hasEffects(e)||"delete"===this.operator&&this.argument.hasEffectsOnInteractionAtPath(B,J,e))}hasEffectsOnInteractionAtPath(e,{type:t}){return 0!==t||e.length>("void"===this.operator?0:1)}applyDeoptimizations(){this.deoptimized=!0,"delete"===this.operator&&(this.argument.deoptimizePath(B),this.context.requestTreeshakingPass())}},UnknownNode:class extends vt{hasEffects(){return!0}include(e){super.include(e,!0)}},UpdateExpression:class extends vt{hasEffects(e){return this.deoptimized||this.applyDeoptimizations(),this.argument.hasEffectsAsAssignmentTarget(e,!0)}hasEffectsOnInteractionAtPath(e,{type:t}){return e.length>1||0!==t}include(e,t){this.deoptimized||this.applyDeoptimizations(),this.included=!0,this.argument.includeAsAssignmentTarget(e,t,!0)}initialise(){this.argument.setAssignedValue(Y)}render(e,t){const{exportNamesByVariable:i,format:s,snippets:{_:n}}=t;if(this.argument.render(e,t),"system"===s){const s=this.argument.variable,r=i.get(s);if(r)if(this.prefix)1===r.length?Oi(s,this.start,this.end,e,t):Ri(s,this.start,this.end,this.parent.type!==rt,e,t);else{const i=this.operator[0];!function(e,t,i,s,n,r,a){const{_:o}=r.snippets;n.prependRight(t,`${Ti([e],r,a)},${o}`),s&&(n.prependRight(t,"("),n.appendLeft(i,")"))}(s,this.start,this.end,this.parent.type!==rt,e,t,`${n}${i}${n}1`)}}}applyDeoptimizations(){this.deoptimized=!0,this.argument.deoptimizePath(B),this.argument instanceof fi&&(this.scope.findVariable(this.argument.name).isReassigned=!0),this.context.requestTreeshakingPass()}},VariableDeclaration:sn,VariableDeclarator:class extends vt{declareDeclarator(e){this.id.declare(e,this.init||Ve)}deoptimizePath(e){this.id.deoptimizePath(e)}hasEffects(e){var t;const i=null===(t=this.init)||void 0===t?void 0:t.hasEffects(e);return this.id.markDeclarationReached(),i||this.id.hasEffects(e)}include(e,t){var i;this.included=!0,null===(i=this.init)||void 0===i||i.include(e,t),this.id.markDeclarationReached(),(t||this.id.shouldBeIncluded(e))&&this.id.include(e,t)}render(e,t){const{exportNamesByVariable:i,snippets:{_:s}}=t,n=this.id.included;if(n)this.id.render(e,t);else{const t=Ei(e.original,"=",this.id.end);e.remove(this.start,vi(e.original,t+1))}this.init?this.init.render(e,t,n?se:{renderedSurroundingElement:rt}):this.id instanceof fi&&tn(this.id.variable,i)&&e.appendLeft(this.end,`${s}=${s}void 0`)}applyDeoptimizations(){}},WhileStatement:class extends vt{hasEffects(e){if(this.test.hasEffects(e))return!0;const{brokenFlow:t,ignore:{breaks:i,continues:s}}=e;return e.ignore.breaks=!0,e.ignore.continues=!0,!!this.body.hasEffects(e)||(e.ignore.breaks=i,e.ignore.continues=s,e.brokenFlow=t,!1)}include(e,t){this.included=!0,this.test.include(e,t);const{brokenFlow:i}=e;this.body.include(e,t,{asSingleStatement:!0}),e.brokenFlow=i}},YieldExpression:class extends vt{hasEffects(e){var t;return this.deoptimized||this.applyDeoptimizations(),!(e.ignore.returnYield&&!(null===(t=this.argument)||void 0===t?void 0:t.hasEffects(e)))}render(e,t){this.argument&&(this.argument.render(e,t,{preventASI:!0}),this.argument.start===this.start+5&&e.prependLeft(this.start+5," "))}}},rn="_missingExportShim";class an extends te{constructor(e){super(rn),this.module=e}include(){super.include(),this.module.needsExportShim=!0}}class on extends te{constructor(e){super(e.getModuleName()),this.memberVariables=null,this.mergedNamespaces=[],this.referencedEarly=!1,this.references=[],this.context=e,this.module=e.module}addReference(e){this.references.push(e),this.name=e.name}getMemberVariables(){if(this.memberVariables)return this.memberVariables;const e=Object.create(null);for(const t of this.context.getExports().concat(this.context.getReexports()))if("*"!==t[0]&&t!==this.module.info.syntheticNamedExports){const i=this.context.traceExport(t);i&&(e[t]=i)}return this.memberVariables=e}include(){this.included=!0,this.context.includeAllExports()}prepare(e){this.mergedNamespaces.length>0&&this.module.scope.addAccessedGlobals([ms],e)}renderBlock(e){const{exportNamesByVariable:t,format:i,freeze:s,indent:n,namespaceToStringTag:r,snippets:{_:a,cnst:o,getObject:l,getPropertyAccess:h,n:c,s:u}}=e,d=this.getMemberVariables(),p=Object.entries(d).map((([e,t])=>this.referencedEarly||t.isReassigned?[null,`get ${e}${a}()${a}{${a}return ${t.getName(h)}${u}${a}}`]:[e,t.getName(h)]));p.unshift([null,`__proto__:${a}null`]);let f=l(p,{lineBreakIndent:{base:"",t:n}});if(this.mergedNamespaces.length>0){const e=this.mergedNamespaces.map((e=>e.getName(h)));f=`/*#__PURE__*/_mergeNamespaces(${f},${a}[${e.join(`,${a}`)}])`}else r&&(f=`/*#__PURE__*/Object.defineProperty(${f},${a}Symbol.toStringTag,${a}${Rs(l)})`),s&&(f=`/*#__PURE__*/Object.freeze(${f})`);return f=`${o} ${this.getName(h)}${a}=${a}${f};`,"system"===i&&t.has(this)&&(f+=`${c}${Ti([this],e)};`),f}renderFirst(){return this.referencedEarly}setMergedNamespaces(e){this.mergedNamespaces=e;const t=this.context.getModuleExecIndex();for(const e of this.references)if(e.context.getModuleExecIndex()<=t){this.referencedEarly=!0;break}}}on.prototype.isNamespace=!0;class ln extends te{constructor(e,t,i){super(t),this.baseVariable=null,this.context=e,this.module=e.module,this.syntheticNamespace=i}getBaseVariable(){if(this.baseVariable)return this.baseVariable;let e=this.syntheticNamespace;for(;e instanceof Js||e instanceof ln;){if(e instanceof Js){const t=e.getOriginalVariable();if(t===e)break;e=t}e instanceof ln&&(e=e.syntheticNamespace)}return this.baseVariable=e}getBaseVariableName(){return this.syntheticNamespace.getBaseVariableName()}getName(e){return`${this.syntheticNamespace.getName(e)}${e(this.name)}`}include(){this.included=!0,this.context.includeVariableInModule(this.syntheticNamespace)}setRenderNames(e,t){super.setRenderNames(e,t)}}var hn;function cn(e){return e.id}!function(e){e[e.LOAD_AND_PARSE=0]="LOAD_AND_PARSE",e[e.ANALYSE=1]="ANALYSE",e[e.GENERATE=2]="GENERATE"}(hn||(hn={}));var un="performance"in("undefined"!=typeof globalThis?globalThis:"undefined"!=typeof window?window:{})?performance:{now:()=>0},dn={memoryUsage:()=>({heapUsed:0})};const pn=()=>{};let fn=new Map;function mn(e,t){switch(t){case 1:return`# ${e}`;case 2:return`## ${e}`;case 3:return e;default:return`${"  ".repeat(t-4)}- ${e}`}}function gn(e,t=3){e=mn(e,t);const i=dn.memoryUsage().heapUsed,s=un.now(),n=fn.get(e);void 0===n?fn.set(e,{memory:0,startMemory:i,startTime:s,time:0,totalMemory:0}):(n.startMemory=i,n.startTime=s)}function yn(e,t=3){e=mn(e,t);const i=fn.get(e);if(void 0!==i){const e=dn.memoryUsage().heapUsed;i.memory+=e-i.startMemory,i.time+=un.now()-i.startTime,i.totalMemory=Math.max(i.totalMemory,e)}}function xn(){const e={};for(const[t,{memory:i,time:s,totalMemory:n}]of fn)e[t]=[s,i,n];return e}let En=pn,bn=pn;const vn=["load","resolveDynamicImport","resolveId","transform"];function Sn(e,t){for(const i of vn)if(i in e){let s=`plugin ${t}`;e.name&&(s+=` (${e.name})`),s+=` - ${i}`;const n=e[i];e[i]=function(...e){En(s,4);const t=n.apply(this,e);return bn(s,4),t&&"function"==typeof t.then?(En(`${s} (async)`,4),t.then((e=>(bn(`${s} (async)`,4),e)))):t}}return e}function An(e){e.isExecuted=!0;const t=[e],i=new Set;for(const e of t)for(const s of[...e.dependencies,...e.implicitlyLoadedBefore])s instanceof Te||s.isExecuted||!s.info.moduleSideEffects&&!e.implicitlyLoadedBefore.has(s)||i.has(s.id)||(s.isExecuted=!0,i.add(s.id),t.push(s))}const In={identifier:null,localName:rn};function Pn(e,t,i,s,n=new Map){const r=n.get(t);if(r){if(r.has(e))return s?[null]:fe((a=t,o=e.id,{code:ge.CIRCULAR_REEXPORT,id:o,message:`"${a}" cannot be exported from ${ce(o)} as it is a reexport that references itself.`}));r.add(e)}else n.set(t,new Set([e]));var a,o;return e.getVariableForExportName(t,{importerForSideEffects:i,isExportAllSearch:s,searchedNamesAndModules:n})}class kn{constructor(e,t,i,s,n,r,a){this.graph=e,this.id=t,this.options=i,this.alternativeReexportModules=new Map,this.chunkFileNames=new Set,this.chunkNames=[],this.cycles=new Set,this.dependencies=new Set,this.dynamicDependencies=new Set,this.dynamicImporters=[],this.dynamicImports=[],this.execIndex=1/0,this.implicitlyLoadedAfter=new Set,this.implicitlyLoadedBefore=new Set,this.importDescriptions=new Map,this.importMetas=[],this.importedFromNotTreeshaken=!1,this.importers=[],this.includedDynamicImporters=[],this.includedImports=new Set,this.isExecuted=!1,this.isUserDefinedEntryPoint=!1,this.needsExportShim=!1,this.sideEffectDependenciesByVariable=new Map,this.sources=new Set,this.usesTopLevelAwait=!1,this.allExportNames=null,this.ast=null,this.exportAllModules=[],this.exportAllSources=new Set,this.exportNamesByVariable=null,this.exportShimVariable=new an(this),this.exports=new Map,this.namespaceReexportsByName=new Map,this.reexportDescriptions=new Map,this.relevantDependencies=null,this.syntheticExports=new Map,this.syntheticNamespace=null,this.transformDependencies=[],this.transitiveReexports=null,this.excludeFromSourcemap=/\0/.test(t),this.context=i.moduleContext(t),this.preserveSignature=this.options.preserveEntrySignatures;const o=this,{dynamicImports:l,dynamicImporters:h,implicitlyLoadedAfter:c,implicitlyLoadedBefore:u,importers:d,reexportDescriptions:p,sources:f}=this;this.info={ast:null,code:null,get dynamicallyImportedIdResolutions(){return l.map((({argument:e})=>"string"==typeof e&&o.resolvedIds[e])).filter(Boolean)},get dynamicallyImportedIds(){return l.map((({id:e})=>e)).filter((e=>null!=e))},get dynamicImporters(){return h.sort()},get hasDefaultExport(){return o.ast?o.exports.has("default")||p.has("default"):null},get hasModuleSideEffects(){return ke("Accessing ModuleInfo.hasModuleSideEffects from plugins is deprecated. Please use ModuleInfo.moduleSideEffects instead.",!1,i),this.moduleSideEffects},id:t,get implicitlyLoadedAfterOneOf(){return Array.from(c,cn).sort()},get implicitlyLoadedBefore(){return Array.from(u,cn).sort()},get importedIdResolutions(){return Array.from(f,(e=>o.resolvedIds[e])).filter(Boolean)},get importedIds(){return Array.from(f,(e=>{var t;return null===(t=o.resolvedIds[e])||void 0===t?void 0:t.id})).filter(Boolean)},get importers(){return d.sort()},isEntry:s,isExternal:!1,get isIncluded(){return e.phase!==hn.GENERATE?null:o.isIncluded()},meta:{...a},moduleSideEffects:n,syntheticNamedExports:r},Object.defineProperty(this.info,"hasModuleSideEffects",{enumerable:!1})}basename(){const e=_(this.id),t=T(this.id);return $e(t?e.slice(0,-t.length):e)}bindReferences(){this.ast.bind()}error(e,t){return this.addLocationToLogProps(e,t),fe(e)}getAllExportNames(){if(this.allExportNames)return this.allExportNames;this.allExportNames=new Set([...this.exports.keys(),...this.reexportDescriptions.keys()]);for(const e of this.exportAllModules)if(e instanceof Te)this.allExportNames.add(`*${e.id}`);else for(const t of e.getAllExportNames())"default"!==t&&this.allExportNames.add(t);return"string"==typeof this.info.syntheticNamedExports&&this.allExportNames.delete(this.info.syntheticNamedExports),this.allExportNames}getDependenciesToBeIncluded(){if(this.relevantDependencies)return this.relevantDependencies;this.relevantDependencies=new Set;const e=new Set,t=new Set,i=new Set(this.includedImports);if(this.info.isEntry||this.includedDynamicImporters.length>0||this.namespace.included||this.implicitlyLoadedAfter.size>0)for(const e of[...this.getReexports(),...this.getExports()]){const[t]=this.getVariableForExportName(e);t&&i.add(t)}for(let s of i){const i=this.sideEffectDependenciesByVariable.get(s);if(i)for(const e of i)t.add(e);s instanceof ln?s=s.getBaseVariable():s instanceof Js&&(s=s.getOriginalVariable()),e.add(s.module)}if(this.options.treeshake&&"no-treeshake"!==this.info.moduleSideEffects)this.addRelevantSideEffectDependencies(this.relevantDependencies,e,t);else for(const e of this.dependencies)this.relevantDependencies.add(e);for(const t of e)this.relevantDependencies.add(t);return this.relevantDependencies}getExportNamesByVariable(){if(this.exportNamesByVariable)return this.exportNamesByVariable;const e=new Map;for(const t of this.getAllExportNames()){let[i]=this.getVariableForExportName(t);if(i instanceof Js&&(i=i.getOriginalVariable()),!i||!(i.included||i instanceof ie))continue;const s=e.get(i);s?s.push(t):e.set(i,[t])}return this.exportNamesByVariable=e}getExports(){return Array.from(this.exports.keys())}getReexports(){if(this.transitiveReexports)return this.transitiveReexports;this.transitiveReexports=[];const e=new Set(this.reexportDescriptions.keys());for(const t of this.exportAllModules)if(t instanceof Te)e.add(`*${t.id}`);else for(const i of[...t.getReexports(),...t.getExports()])"default"!==i&&e.add(i);return this.transitiveReexports=[...e]}getRenderedExports(){const e=[],t=[];for(const i of this.exports.keys()){const[s]=this.getVariableForExportName(i);(s&&s.included?e:t).push(i)}return{removedExports:t,renderedExports:e}}getSyntheticNamespace(){return null===this.syntheticNamespace&&(this.syntheticNamespace=void 0,[this.syntheticNamespace]=this.getVariableForExportName("string"==typeof this.info.syntheticNamedExports?this.info.syntheticNamedExports:"default",{onlyExplicit:!0})),this.syntheticNamespace?this.syntheticNamespace:fe((e=this.id,t=this.info.syntheticNamedExports,{code:ge.SYNTHETIC_NAMED_EXPORTS_NEED_NAMESPACE_EXPORT,id:e,message:`Module "${ce(e)}" that is marked with 'syntheticNamedExports: ${JSON.stringify(t)}' needs ${"string"==typeof t&&"default"!==t?`an explicit export named "${t}"`:"a default export"} that does not reexport an unresolved named export of the same module.`}));var e,t}getVariableForExportName(e,{importerForSideEffects:t,isExportAllSearch:i,onlyExplicit:s,searchedNamesAndModules:n}=ne){var r;if("*"===e[0])return 1===e.length?[this.namespace]:this.graph.modulesById.get(e.slice(1)).getVariableForExportName("*");const a=this.reexportDescriptions.get(e);if(a){const[e]=Pn(a.module,a.localName,t,!1,n);return e?(t&&wn(e,t,this),[e]):this.error(be(a.localName,this.id,a.module.id),a.start)}const o=this.exports.get(e);if(o){if(o===In)return[this.exportShimVariable];const e=o.localName,i=this.traceVariable(e,{importerForSideEffects:t,searchedNamesAndModules:n});return t&&(M(t.sideEffectDependenciesByVariable,i,(()=>new Set)).add(this),wn(i,t,this)),[i]}if(s)return[null];if("default"!==e){const i=null!==(r=this.namespaceReexportsByName.get(e))&&void 0!==r?r:this.getVariableFromNamespaceReexports(e,t,n);if(this.namespaceReexportsByName.set(e,i),i[0])return i}return this.info.syntheticNamedExports?[M(this.syntheticExports,e,(()=>new ln(this.astContext,e,this.getSyntheticNamespace())))]:!i&&this.options.shimMissingExports?(this.shimMissingExport(e),[this.exportShimVariable]):[null]}hasEffects(){return"no-treeshake"===this.info.moduleSideEffects||this.ast.included&&this.ast.hasEffects(De())}include(){const e=Me();this.ast.shouldBeIncluded(e)&&this.ast.include(e,!1)}includeAllExports(e){this.isExecuted||(An(this),this.graph.needsTreeshakingPass=!0);for(const t of this.exports.keys())if(e||t!==this.info.syntheticNamedExports){const e=this.getVariableForExportName(t)[0];e.deoptimizePath(F),e.included||this.includeVariable(e)}for(const e of this.getReexports()){const[t]=this.getVariableForExportName(e);t&&(t.deoptimizePath(F),t.included||this.includeVariable(t),t instanceof ie&&(t.module.reexported=!0))}e&&this.namespace.setMergedNamespaces(this.includeAndGetAdditionalMergedNamespaces())}includeAllInBundle(){this.ast.include(Me(),!0),this.includeAllExports(!1)}isIncluded(){return this.ast.included||this.namespace.included||this.importedFromNotTreeshaken}linkImports(){this.addModulesToImportDescriptions(this.importDescriptions),this.addModulesToImportDescriptions(this.reexportDescriptions);const e=[];for(const t of this.exportAllSources){const i=this.graph.modulesById.get(this.resolvedIds[t].id);i instanceof Te?e.push(i):this.exportAllModules.push(i)}this.exportAllModules.push(...e)}render(e){const t=this.magicString.clone();return this.ast.render(t,e),this.usesTopLevelAwait=this.astContext.usesTopLevelAwait,t}setSource({ast:e,code:t,customTransformCache:i,originalCode:s,originalSourcemap:n,resolvedIds:r,sourcemapChain:a,transformDependencies:o,transformFiles:l,...h}){this.info.code=t,this.originalCode=s,this.originalSourcemap=n,this.sourcemapChain=a,l&&(this.transformFiles=l),this.transformDependencies=o,this.customTransformCache=i,this.updateOptions(h),En("generate ast",3),e||(e=this.tryParse()),bn("generate ast",3),this.resolvedIds=r||Object.create(null);const c=this.id;this.magicString=new E(t,{filename:this.excludeFromSourcemap?null:c,indentExclusionRanges:[]}),En("analyse ast",3),this.astContext={addDynamicImport:this.addDynamicImport.bind(this),addExport:this.addExport.bind(this),addImport:this.addImport.bind(this),addImportMeta:this.addImportMeta.bind(this),code:t,deoptimizationTracker:this.graph.deoptimizationTracker,error:this.error.bind(this),fileName:c,getExports:this.getExports.bind(this),getModuleExecIndex:()=>this.execIndex,getModuleName:this.basename.bind(this),getNodeConstructor:e=>nn[e]||nn.UnknownNode,getReexports:this.getReexports.bind(this),importDescriptions:this.importDescriptions,includeAllExports:()=>this.includeAllExports(!0),includeDynamicImport:this.includeDynamicImport.bind(this),includeVariableInModule:this.includeVariableInModule.bind(this),magicString:this.magicString,module:this,moduleContext:this.context,options:this.options,requestTreeshakingPass:()=>this.graph.needsTreeshakingPass=!0,traceExport:e=>this.getVariableForExportName(e)[0],traceVariable:this.traceVariable.bind(this),usesTopLevelAwait:!1,warn:this.warn.bind(this)},this.scope=new Zs(this.graph.scope,this.astContext),this.namespace=new on(this.astContext),this.ast=new Ks(e,{context:this.astContext,type:"Module"},this.scope),this.info.ast=e,bn("analyse ast",3)}toJSON(){return{ast:this.ast.esTreeNode,code:this.info.code,customTransformCache:this.customTransformCache,dependencies:Array.from(this.dependencies,cn),id:this.id,meta:this.info.meta,moduleSideEffects:this.info.moduleSideEffects,originalCode:this.originalCode,originalSourcemap:this.originalSourcemap,resolvedIds:this.resolvedIds,sourcemapChain:this.sourcemapChain,syntheticNamedExports:this.info.syntheticNamedExports,transformDependencies:this.transformDependencies,transformFiles:this.transformFiles}}traceVariable(e,{importerForSideEffects:t,isExportAllSearch:i,searchedNamesAndModules:s}=ne){const n=this.scope.variables.get(e);if(n)return n;const r=this.importDescriptions.get(e);if(r){const e=r.module;if(e instanceof kn&&"*"===r.name)return e.namespace;const[n]=Pn(e,r.name,t||this,i,s);return n||this.error(be(r.name,this.id,e.id),r.start)}return null}tryParse(){try{return this.graph.contextParse(this.info.code)}catch(e){let t=e.message.replace(/ \(\d+:\d+\)$/,"");return this.id.endsWith(".json")?t+=" (Note that you need @rollup/plugin-json to import JSON files)":this.id.endsWith(".js")||(t+=" (Note that you need plugins to import files that are not JavaScript)"),this.error({code:"PARSE_ERROR",message:t,parserError:e},e.pos)}}updateOptions({meta:e,moduleSideEffects:t,syntheticNamedExports:i}){null!=t&&(this.info.moduleSideEffects=t),null!=i&&(this.info.syntheticNamedExports=i),null!=e&&Object.assign(this.info.meta,e)}warn(e,t){this.addLocationToLogProps(e,t),this.options.onwarn(e)}addDynamicImport(e){let t=e.source;t instanceof Ys?1===t.quasis.length&&t.quasis[0].value.cooked&&(t=t.quasis[0].value.cooked):t instanceof ji&&"string"==typeof t.value&&(t=t.value),this.dynamicImports.push({argument:t,id:null,node:e,resolution:null})}addExport(e){if(e instanceof ns)this.exports.set("default",{identifier:e.variable.getAssignedVariableName(),localName:"default"});else if(e instanceof is){const t=e.source.value;if(this.sources.add(t),e.exported){const i=e.exported.name;this.reexportDescriptions.set(i,{localName:"*",module:null,source:t,start:e.start})}else this.exportAllSources.add(t)}else if(e.source instanceof ji){const t=e.source.value;this.sources.add(t);for(const i of e.specifiers){const e=i.exported.name;this.reexportDescriptions.set(e,{localName:i.local.name,module:null,source:t,start:i.start})}}else if(e.declaration){const t=e.declaration;if(t instanceof sn)for(const e of t.declarations)for(const t of Re(e.id))this.exports.set(t,{identifier:null,localName:t});else{const e=t.id.name;this.exports.set(e,{identifier:null,localName:e})}}else for(const t of e.specifiers){const e=t.local.name,i=t.exported.name;this.exports.set(i,{identifier:null,localName:e})}}addImport(e){const t=e.source.value;this.sources.add(t);for(const i of e.specifiers){const e="ImportDefaultSpecifier"===i.type,s="ImportNamespaceSpecifier"===i.type,n=e?"default":s?"*":i.imported.name;this.importDescriptions.set(i.local.name,{module:null,name:n,source:t,start:i.start})}}addImportMeta(e){this.importMetas.push(e)}addLocationToLogProps(e,t){e.id=this.id,e.pos=t;let i=this.info.code;const s=ae(i,t,{offsetLine:1});if(s){let{column:n,line:r}=s;try{({column:n,line:r}=function(e,t){const i=e.filter((e=>!!e.mappings));e:for(;i.length>0;){const e=i.pop().mappings[t.line-1];if(e){const i=e.filter((e=>e.length>1)),s=i[i.length-1];for(const e of i)if(e[0]>=t.column||e===s){t={column:e[3],line:e[2]+1};continue e}}throw new Error("Can't resolve original location of error.")}return t}(this.sourcemapChain,{column:n,line:r})),i=this.originalCode}catch(e){this.options.onwarn({code:"SOURCEMAP_ERROR",id:this.id,loc:{column:n,file:this.id,line:r},message:`Error when using sourcemap for reporting an error: ${e.message}`,pos:t})}me(e,{column:n,line:r},i,this.id)}}addModulesToImportDescriptions(e){for(const t of e.values()){const{id:e}=this.resolvedIds[t.source];t.module=this.graph.modulesById.get(e)}}addRelevantSideEffectDependencies(e,t,i){const s=new Set,n=r=>{for(const a of r)s.has(a)||(s.add(a),t.has(a)?e.add(a):(a.info.moduleSideEffects||i.has(a))&&(a instanceof Te||a.hasEffects()?e.add(a):n(a.dependencies)))};n(this.dependencies),n(i)}getVariableFromNamespaceReexports(e,t,i){let s=null;const n=new Map,r=new Set;for(const a of this.exportAllModules){if(a.info.syntheticNamedExports===e)continue;const[o,l]=Pn(a,e,t,!0,Cn(i));a instanceof Te||l?r.add(o):o instanceof ln?s||(s=o):o&&n.set(o,a)}if(n.size>0){const t=[...n],i=t[0][0];return 1===t.length?[i]:(this.options.onwarn(function(e,t,i){return{code:ge.NAMESPACE_CONFLICT,message:`Conflicting namespaces: "${ce(t)}" re-exports "${e}" from one of the modules ${le(i.map((e=>ce(e))))} (will be ignored)`,name:e,reexporter:t,sources:i}}(e,this.id,t.map((([,e])=>e.id)))),[null])}if(r.size>0){const t=[...r],i=t[0];return t.length>1&&this.options.onwarn(function(e,t,i,s){return{code:ge.AMBIGUOUS_EXTERNAL_NAMESPACES,message:`Ambiguous external namespace resolution: "${ce(t)}" re-exports "${e}" from one of the external modules ${le(s.map((e=>ce(e))))}, guessing "${ce(i)}".`,name:e,reexporter:t,sources:s}}(e,this.id,i.module.id,t.map((e=>e.module.id)))),[i,!0]}return s?[s]:[null]}includeAndGetAdditionalMergedNamespaces(){const e=new Set,t=new Set;for(const i of[this,...this.exportAllModules])if(i instanceof Te){const[t]=i.getVariableForExportName("*");t.include(),this.includedImports.add(t),e.add(t)}else if(i.info.syntheticNamedExports){const e=i.getSyntheticNamespace();e.include(),this.includedImports.add(e),t.add(e)}return[...t,...e]}includeDynamicImport(e){const t=this.dynamicImports.find((t=>t.node===e)).resolution;t instanceof kn&&(t.includedDynamicImporters.push(this),t.includeAllExports(!0))}includeVariable(e){if(!e.included){e.include(),this.graph.needsTreeshakingPass=!0;const t=e.module;if(t instanceof kn&&(t.isExecuted||An(t),t!==this)){const t=function(e,t){const i=M(t.sideEffectDependenciesByVariable,e,(()=>new Set));let s=e;const n=new Set([s]);for(;;){const e=s.module;if(s=s instanceof Js?s.getDirectOriginalVariable():s instanceof ln?s.syntheticNamespace:null,!s||n.has(s))break;n.add(s),i.add(e);const t=e.sideEffectDependenciesByVariable.get(s);if(t)for(const e of t)i.add(e)}return i}(e,this);for(const e of t)e.isExecuted||An(e)}}}includeVariableInModule(e){this.includeVariable(e);const t=e.module;t&&t!==this&&this.includedImports.add(e)}shimMissingExport(e){this.options.onwarn({code:"SHIMMED_EXPORT",exporter:ce(this.id),exportName:e,message:`Missing export "${e}" has been shimmed in module ${ce(this.id)}.`}),this.exports.set(e,In)}}function wn(e,t,i){if(e.module instanceof kn&&e.module!==i){const s=e.module.cycles;if(s.size>0){const n=i.cycles;for(const r of n)if(s.has(r)){t.alternativeReexportModules.set(e,i);break}}}}const Cn=e=>e&&new Map(Array.from(e,(([e,t])=>[e,new Set(t)])));function Nn(e){return e.endsWith(".js")?e.slice(0,-3):e}function _n(e,t){return e.autoId?`${e.basePath?e.basePath+"/":""}${Nn(t)}`:e.id||""}function $n(e,t,i,s,n,r,a,o="return "){const{_:l,cnst:h,getDirectReturnFunction:c,getFunctionIntro:u,getPropertyAccess:d,n:p,s:f}=n;if(!i)return`${p}${p}${o}${function(e,t,i,s,n){if(e.length>0)return e[0].local;for(const{defaultVariableName:e,id:r,isChunk:a,name:o,namedExportsMode:l,namespaceVariableName:h,reexports:c}of t)if(c)return Tn(o,c[0].imported,l,a,e,h,i,r,s,n)}(e,t,s,a,d)};`;let m="";for(const{defaultVariableName:e,id:n,isChunk:o,name:h,namedExportsMode:u,namespaceVariableName:f,reexports:g}of t)if(g&&i)for(const t of g)if("*"!==t.reexported){const i=Tn(h,t.imported,u,o,e,f,s,n,a,d);if(m&&(m+=p),"*"!==t.imported&&t.needsLiveBinding){const[e,s]=c([],{functionReturn:!0,lineBreakIndent:null,name:null});m+=`Object.defineProperty(exports,${l}'${t.reexported}',${l}{${p}${r}enumerable:${l}true,${p}${r}get:${l}${e}${i}${s}${p}});`}else m+=`exports${d(t.reexported)}${l}=${l}${i};`}for(const{exported:t,local:i}of e){const e=`exports${d(t)}`,s=i;e!==s&&(m&&(m+=p),m+=`${e}${l}=${l}${s};`)}for(const{name:e,reexports:s}of t)if(s&&i)for(const t of s)if("*"===t.reexported){m&&(m+=p);const i=`{${p}${r}if${l}(k${l}!==${l}'default'${l}&&${l}!exports.hasOwnProperty(k))${l}${Mn(e,t.needsLiveBinding,r,n)}${f}${p}}`;m+="var"===h&&t.needsLiveBinding?`Object.keys(${e}).forEach(${u(["k"],{isAsync:!1,name:null})}${i});`:`for${l}(${h} k in ${e})${l}${i}`}return m?`${p}${p}${m}`:""}function Tn(e,t,i,s,n,r,a,o,l,h){if("default"===t){if(!s){const t=String(a(o)),i=gs[t]?n:e;return ys(t,l)?`${i}${h("default")}`:i}return i?`${e}${h("default")}`:e}return"*"===t?(s?!i:xs[String(a(o))])?r:e:`${e}${h(t)}`}function On(e){return e([["value","true"]],{lineBreakIndent:null})}function Rn(e,t,i,{_:s,getObject:n}){if(e){if(t)return i?`Object.defineProperties(exports,${s}${n([["__esModule",On(n)],[null,`[Symbol.toStringTag]:${s}${Rs(n)}`]],{lineBreakIndent:null})});`:`Object.defineProperty(exports,${s}'__esModule',${s}${On(n)});`;if(i)return`Object.defineProperty(exports,${s}Symbol.toStringTag,${s}${Rs(n)});`}return""}const Mn=(e,t,i,{_:s,getDirectReturnFunction:n,n:r})=>{if(t){const[t,a]=n([],{functionReturn:!0,lineBreakIndent:null,name:null});return`Object.defineProperty(exports,${s}k,${s}{${r}${i}${i}enumerable:${s}true,${r}${i}${i}get:${s}${t}${e}[k]${a}${r}${i}})`}return`exports[k]${s}=${s}${e}[k]`};function Dn(e,t,i,s,n,r,a,o){const{_:l,cnst:h,n:c}=o,u=new Set,d=[],p=(e,t,i)=>{u.add(t),d.push(`${h} ${e}${l}=${l}/*#__PURE__*/${t}(${i});`)};for(const{defaultVariableName:i,imports:s,id:n,isChunk:r,name:a,namedExportsMode:o,namespaceVariableName:l,reexports:h}of e)if(r){for(const{imported:e,reexported:t}of[...s||[],...h||[]])if("*"===e&&"*"!==t){o||p(l,fs,a);break}}else{const e=String(t(n));let r=!1,o=!1;for(const{imported:t,reexported:n}of[...s||[],...h||[]]){let s,h;"default"===t?r||(r=!0,i!==l&&(h=i,s=gs[e])):"*"===t&&"*"!==n&&(o||(o=!0,s=xs[e],h=l)),s&&p(h,s,a)}}return`${bs(u,r,a,o,i,s,n)}${d.length>0?`${d.join(c)}${c}${c}`:""}`}function Ln(e,t){return"."!==e[0]?e:t?(i=e).endsWith(".js")?i:i+".js":Nn(e);var i}const Vn={assert:!0,buffer:!0,console:!0,constants:!0,domain:!0,events:!0,http:!0,https:!0,os:!0,path:!0,process:!0,punycode:!0,querystring:!0,stream:!0,string_decoder:!0,timers:!0,tty:!0,url:!0,util:!0,vm:!0,zlib:!0};function Bn(e,t){const i=t.map((({id:e})=>e)).filter((e=>e in Vn));i.length&&e({code:"MISSING_NODE_BUILTINS",message:`Creating a browser bundle that depends on Node.js built-in modules (${le(i)}). You might need to include https://github.com/FredKSchott/rollup-plugin-polyfill-node`,modules:i})}const Fn=(e,t)=>e.split(".").map(t).join("");function zn(e,t,i,s,{_:n,getPropertyAccess:r}){const a=e.split(".");a[0]=("function"==typeof i?i(a[0]):i[a[0]])||a[0];const o=a.pop();let l=t,h=a.map((e=>(l+=r(e),`${l}${n}=${n}${l}${n}||${n}{}`))).concat(`${l}${r(o)}`).join(`,${n}`)+`${n}=${n}${s}`;return a.length>0&&(h=`(${h})`),h}function jn(e){let t=e.length;for(;t--;){const{imports:i,reexports:s}=e[t];if(i||s)return e.slice(0,t+1)}return[]}const Un=({dependencies:e,exports:t})=>{const i=new Set(t.map((e=>e.exported)));i.add("default");for(const{reexports:t}of e)if(t)for(const e of t)"*"!==e.reexported&&i.add(e.reexported);return i},Gn=(e,t,{_:i,cnst:s,getObject:n,n:r})=>e?`${r}${t}${s} _starExcludes${i}=${i}${n([...e].map((e=>[e,"1"])),{lineBreakIndent:{base:t,t:t}})};`:"",Hn=(e,t,{_:i,n:s})=>e.length?`${s}${t}var ${e.join(`,${i}`)};`:"",Wn=(e,t,i)=>qn(e.filter((e=>e.hoisted)).map((e=>({name:e.exported,value:e.local}))),t,i);function qn(e,t,{_:i,n:s}){return 0===e.length?"":1===e.length?`exports('${e[0].name}',${i}${e[0].value});${s}${s}`:`exports({${s}`+e.map((({name:e,value:s})=>`${t}${e}:${i}${s}`)).join(`,${s}`)+`${s}});${s}${s}`}const Kn=(e,t,i)=>qn(e.filter((e=>e.expression)).map((e=>({name:e.exported,value:e.local}))),t,i),Xn=(e,t,i)=>qn(e.filter((e=>e.local===rn)).map((e=>({name:e.exported,value:rn}))),t,i);function Yn(e,t,i){return e?`${t}${Fn(e,i)}`:"null"}var Qn={amd:function(e,{accessedGlobals:t,dependencies:i,exports:s,hasExports:n,id:r,indent:a,intro:o,isEntryFacade:l,isModuleFacade:h,namedExportsMode:c,outro:u,snippets:d,warn:p},{amd:f,esModule:m,externalLiveBindings:g,freeze:y,interop:x,namespaceToStringTag:E,strict:b}){Bn(p,i);const v=i.map((e=>`'${Ln(e.id,f.forceJsExtensionForImports)}'`)),S=i.map((e=>e.name)),{n:A,getNonArrowFunctionIntro:I,_:P}=d;c&&n&&(S.unshift("exports"),v.unshift("'exports'")),t.has("require")&&(S.unshift("require"),v.unshift("'require'")),t.has("module")&&(S.unshift("module"),v.unshift("'module'"));const k=_n(f,r),w=(k?`'${k}',${P}`:"")+(v.length?`[${v.join(`,${P}`)}],${P}`:""),C=b?`${P}'use strict';`:"";e.prepend(`${o}${Dn(i,x,g,y,E,t,a,d)}`);const N=$n(s,i,c,x,d,a,g);let _=Rn(c&&n,l&&m,h&&E,d);return _&&(_=A+A+_),e.append(`${N}${_}${u}`),e.indent(a).prepend(`${f.define}(${w}(${I(S,{isAsync:!1,name:null})}{${C}${A}${A}`).append(`${A}${A}}));`)},cjs:function(e,{accessedGlobals:t,dependencies:i,exports:s,hasExports:n,indent:r,intro:a,isEntryFacade:o,isModuleFacade:l,namedExportsMode:h,outro:c,snippets:u},{compact:d,esModule:p,externalLiveBindings:f,freeze:m,interop:g,namespaceToStringTag:y,strict:x}){const{_:E,n:b}=u,v=x?`'use strict';${b}${b}`:"";let S=Rn(h&&n,o&&p,l&&y,u);S&&(S+=b+b);const A=function(e,{_:t,cnst:i,n:s},n){let r="",a=!1;for(const{id:o,name:l,reexports:h,imports:c}of e)h||c?(r+=n&&a?",":`${r?`;${s}`:""}${i} `,a=!0,r+=`${l}${t}=${t}require('${o}')`):(r&&(r+=n&&!a?",":`;${s}`),a=!1,r+=`require('${o}')`);return r?`${r};${s}${s}`:""}(i,u,d),I=Dn(i,g,f,m,y,t,r,u);e.prepend(`${v}${a}${S}${A}${I}`);const P=$n(s,i,h,g,u,r,f,`module.exports${E}=${E}`);return e.append(`${P}${c}`)},es:function(e,{accessedGlobals:t,indent:i,intro:s,outro:n,dependencies:r,exports:a,snippets:o},{externalLiveBindings:l,freeze:h,namespaceToStringTag:c}){const{_:u,n:d}=o,p=function(e,t){const i=[];for(const{id:s,reexports:n,imports:r,name:a}of e)if(n||r){if(r){let e=null,n=null;const a=[];for(const t of r)"default"===t.imported?e=t:"*"===t.imported?n=t:a.push(t);n&&i.push(`import${t}*${t}as ${n.local} from${t}'${s}';`),e&&0===a.length?i.push(`import ${e.local} from${t}'${s}';`):a.length>0&&i.push(`import ${e?`${e.local},${t}`:""}{${t}${a.map((e=>e.imported===e.local?e.imported:`${e.imported} as ${e.local}`)).join(`,${t}`)}${t}}${t}from${t}'${s}';`)}if(n){let e=null;const o=[],l=[];for(const t of n)"*"===t.reexported?e=t:"*"===t.imported?o.push(t):l.push(t);if(e&&i.push(`export${t}*${t}from${t}'${s}';`),o.length>0){r&&r.some((e=>"*"===e.imported&&e.local===a))||i.push(`import${t}*${t}as ${a} from${t}'${s}';`);for(const e of o)i.push(`export${t}{${t}${a===e.reexported?a:`${a} as ${e.reexported}`} };`)}l.length>0&&i.push(`export${t}{${t}${l.map((e=>e.imported===e.reexported?e.imported:`${e.imported} as ${e.reexported}`)).join(`,${t}`)}${t}}${t}from${t}'${s}';`)}}else i.push(`import${t}'${s}';`);return i}(r,u);p.length>0&&(s+=p.join(d)+d+d),(s+=bs(null,t,i,o,l,h,c))&&e.prepend(s);const f=function(e,{_:t,cnst:i}){const s=[],n=[];for(const r of e)r.expression&&s.push(`${i} ${r.local}${t}=${t}${r.expression};`),n.push(r.exported===r.local?r.local:`${r.local} as ${r.exported}`);return n.length&&s.push(`export${t}{${t}${n.join(`,${t}`)}${t}};`),s}(a,o);return f.length&&e.append(d+d+f.join(d).trim()),n&&e.append(n),e.trim()},iife:function(e,{accessedGlobals:t,dependencies:i,exports:s,hasExports:n,indent:r,intro:a,namedExportsMode:o,outro:l,snippets:h,warn:c},{compact:u,esModule:d,extend:p,freeze:f,externalLiveBindings:m,globals:g,interop:y,name:x,namespaceToStringTag:E,strict:b}){const{_:v,getNonArrowFunctionIntro:S,getPropertyAccess:A,n:I}=h,P=x&&x.includes("."),k=!p&&!P;if(x&&k&&(_e(w=x)||Ne.test(w)))return fe({code:"ILLEGAL_IDENTIFIER_AS_NAME",message:`Given name "${x}" is not a legal JS identifier. If you need this, you can try "output.extend: true".`});var w;Bn(c,i);const C=jn(i),N=C.map((e=>e.globalName||"null")),_=C.map((e=>e.name));n&&!x&&c({code:"MISSING_NAME_OPTION_FOR_IIFE_EXPORT",message:'If you do not supply "output.name", you may not be able to access the exports of an IIFE bundle.'}),o&&n&&(p?(N.unshift(`this${Fn(x,A)}${v}=${v}this${Fn(x,A)}${v}||${v}{}`),_.unshift("exports")):(N.unshift("{}"),_.unshift("exports")));const $=b?`${r}'use strict';${I}`:"",T=Dn(i,y,m,f,E,t,r,h);e.prepend(`${a}${T}`);let O=`(${S(_,{isAsync:!1,name:null})}{${I}${$}${I}`;n&&(!x||p&&o||(O=(k?`var ${x}`:`this${Fn(x,A)}`)+`${v}=${v}${O}`),P&&(O=function(e,t,i,{_:s,getPropertyAccess:n,s:r},a){const o=e.split(".");o[0]=("function"==typeof i?i(o[0]):i[o[0]])||o[0],o.pop();let l=t;return o.map((e=>(l+=n(e),`${l}${s}=${s}${l}${s}||${s}{}${r}`))).join(a?",":"\n")+(a&&o.length?";":"\n")}(x,"this",g,h,u)+O));let R=`${I}${I}})(${N.join(`,${v}`)});`;n&&!p&&o&&(R=`${I}${I}${r}return exports;${R}`);const M=$n(s,i,o,y,h,r,m);let D=Rn(o&&n,d,E,h);return D&&(D=I+I+D),e.append(`${M}${D}${l}`),e.indent(r).prepend(O).append(R)},system:function(e,{accessedGlobals:t,dependencies:i,exports:s,hasExports:n,indent:r,intro:a,snippets:o,outro:l,usesTopLevelAwait:h},{externalLiveBindings:c,freeze:u,name:d,namespaceToStringTag:p,strict:f,systemNullSetters:m}){const{_:g,getFunctionIntro:y,getNonArrowFunctionIntro:x,n:E,s:b}=o,{importBindings:v,setters:S,starExcludes:A}=function(e,t,i,{_:s,cnst:n,getObject:r,getPropertyAccess:a,n:o}){const l=[],h=[];let c=null;for(const{imports:u,reexports:d}of e){const p=[];if(u)for(const e of u)l.push(e.local),"*"===e.imported?p.push(`${e.local}${s}=${s}module;`):p.push(`${e.local}${s}=${s}module${a(e.imported)};`);if(d){const o=[];let l=!1;for(const{imported:e,reexported:t}of d)"*"===t?l=!0:o.push([t,"*"===e?"module":`module${a(e)}`]);if(o.length>1||l){const a=r(o,{lineBreakIndent:null});l?(c||(c=Un({dependencies:e,exports:t})),p.push(`${n} setter${s}=${s}${a};`,`for${s}(${n} name in module)${s}{`,`${i}if${s}(!_starExcludes[name])${s}setter[name]${s}=${s}module[name];`,"}","exports(setter);")):p.push(`exports(${a});`)}else{const[e,t]=o[0];p.push(`exports('${e}',${s}${t});`)}}h.push(p.join(`${o}${i}${i}${i}`))}return{importBindings:l,setters:h,starExcludes:c}}(i,s,r,o),I=d?`'${d}',${g}`:"",P=t.has("module")?["exports","module"]:n?["exports"]:[];let k=`System.register(${I}[`+i.map((({id:e})=>`'${e}'`)).join(`,${g}`)+`],${g}(${x(P,{isAsync:!1,name:null})}{${E}${r}${f?"'use strict';":""}`+Gn(A,r,o)+Hn(v,r,o)+`${E}${r}return${g}{${S.length?`${E}${r}${r}setters:${g}[${S.map((e=>e?`${y(["module"],{isAsync:!1,name:null})}{${E}${r}${r}${r}${e}${E}${r}${r}}`:m?"null":`${y([],{isAsync:!1,name:null})}{}`)).join(`,${g}`)}],`:""}${E}`;k+=`${r}${r}execute:${g}(${x([],{isAsync:h,name:null})}{${E}${E}`;const w=`${r}${r}})${E}${r}}${b}${E}}));`;return e.prepend(a+bs(null,t,r,o,c,u,p)+Wn(s,r,o)),e.append(`${l}${E}${E}`+Kn(s,r,o)+Xn(s,r,o)),e.indent(`${r}${r}${r}`).append(w).prepend(k)},umd:function(e,{accessedGlobals:t,dependencies:i,exports:s,hasExports:n,id:r,indent:a,intro:o,namedExportsMode:l,outro:h,snippets:c,warn:u},{amd:d,compact:p,esModule:f,extend:m,externalLiveBindings:g,freeze:y,interop:x,name:E,namespaceToStringTag:b,globals:v,noConflict:S,strict:A}){const{_:I,cnst:P,getFunctionIntro:k,getNonArrowFunctionIntro:w,getPropertyAccess:C,n:N,s:_}=c,$=p?"f":"factory",T=p?"g":"global";if(n&&!E)return fe({code:"MISSING_NAME_OPTION_FOR_IIFE_EXPORT",message:'You must supply "output.name" for UMD bundles that have exports so that the exports are accessible in environments without a module loader.'});Bn(u,i);const O=i.map((e=>`'${Ln(e.id,d.forceJsExtensionForImports)}'`)),R=i.map((e=>`require('${e.id}')`)),M=jn(i),D=M.map((e=>Yn(e.globalName,T,C))),L=M.map((e=>e.name));l&&(n||S)&&(O.unshift("'exports'"),R.unshift("exports"),D.unshift(zn(E,T,v,(m?`${Yn(E,T,C)}${I}||${I}`:"")+"{}",c)),L.unshift("exports"));const V=_n(d,r),B=(V?`'${V}',${I}`:"")+(O.length?`[${O.join(`,${I}`)}],${I}`:""),F=d.define,z=!l&&n?`module.exports${I}=${I}`:"",j=A?`${I}'use strict';${N}`:"";let U;if(S){const e=p?"e":"exports";let t;t=!l&&n?`${P} ${e}${I}=${I}${zn(E,T,v,`${$}(${D.join(`,${I}`)})`,c)};`:`${P} ${e}${I}=${I}${D.shift()};${N}${a}${a}${$}(${[e].concat(D).join(`,${I}`)});`,U=`(${k([],{isAsync:!1,name:null})}{${N}${a}${a}${P} current${I}=${I}${function(e,t,{_:i,getPropertyAccess:s}){let n=t;return e.split(".").map((e=>n+=s(e))).join(`${i}&&${i}`)}(E,T,c)};${N}${a}${a}${t}${N}${a}${a}${e}.noConflict${I}=${I}${k([],{isAsync:!1,name:null})}{${I}${Yn(E,T,C)}${I}=${I}current;${I}return ${e}${_}${I}};${N}${a}})()`}else U=`${$}(${D.join(`,${I}`)})`,!l&&n&&(U=zn(E,T,v,U,c));const G=n||S&&l||D.length>0,H=[$];G&&H.unshift(T);const W=G?`this,${I}`:"",q=G?`(${T}${I}=${I}typeof globalThis${I}!==${I}'undefined'${I}?${I}globalThis${I}:${I}${T}${I}||${I}self,${I}`:"",K=G?")":"",X=G?`${a}typeof exports${I}===${I}'object'${I}&&${I}typeof module${I}!==${I}'undefined'${I}?${I}${z}${$}(${R.join(`,${I}`)})${I}:${N}`:"",Y=`(${w(H,{isAsync:!1,name:null})}{${N}`+X+`${a}typeof ${F}${I}===${I}'function'${I}&&${I}${F}.amd${I}?${I}${F}(${B}${$})${I}:${N}`+`${a}${q}${U}${K};${N}`+`})(${W}(${w(L,{isAsync:!1,name:null})}{${j}${N}`,Q=N+N+"}));";e.prepend(`${o}${Dn(i,x,g,y,b,t,a,c)}`);const J=$n(s,i,l,x,c,a,g);let Z=Rn(l&&n,f,b,c);return Z&&(Z=N+N+Z),e.append(`${J}${Z}${h}`),e.trim().indent(a).append(Q).prepend(Y)}};class Jn{constructor(e,t){this.isOriginal=!0,this.filename=e,this.content=t}traceSegment(e,t,i){return{column:t,line:e,name:i,source:this}}}class Zn{constructor(e,t){this.sources=t,this.names=e.names,this.mappings=e.mappings}traceMappings(){const e=[],t=new Map,i=[],s=[],n=new Map,r=[];for(const a of this.mappings){const o=[];for(const r of a){if(1===r.length)continue;const a=this.sources[r[1]];if(!a)continue;const l=a.traceSegment(r[2],r[3],5===r.length?this.names[r[4]]:"");if(l){const{column:a,line:h,name:c,source:{content:u,filename:d}}=l;let p=t.get(d);if(void 0===p)p=e.length,e.push(d),t.set(d,p),i[p]=u;else if(null==i[p])i[p]=u;else if(null!=u&&i[p]!==u)return fe({message:`Multiple conflicting contents for sourcemap source ${d}`});const f=[r[0],p,h,a];if(c){let e=n.get(c);void 0===e&&(e=s.length,s.push(c),n.set(c,e)),f[4]=e}o.push(f)}}r.push(o)}return{mappings:r,names:s,sources:e,sourcesContent:i}}traceSegment(e,t,i){const s=this.mappings[e];if(!s)return null;let n=0,r=s.length-1;for(;n<=r;){const e=n+r>>1,a=s[e];if(a[0]===t||n===r){if(1==a.length)return null;const e=this.sources[a[1]];return e?e.traceSegment(a[2],a[3],5===a.length?this.names[a[4]]:i):null}a[0]>t?r=e-1:n=e+1}return null}}function er(e){return function(t,i){return i.mappings?new Zn(i,[t]):(e({code:"SOURCEMAP_BROKEN",message:`Sourcemap is likely to be incorrect: a plugin (${i.plugin}) was used to transform files, but didn't generate a sourcemap for the transformation. Consult the plugin documentation for help`,plugin:i.plugin,url:"https://rollupjs.org/guide/en/#warning-sourcemap-is-likely-to-be-incorrect"}),new Zn({mappings:[],names:[]},[t]))}}function tr(e,t,i,s,n){let r;if(i){const t=i.sources,s=i.sourcesContent||[],n=$(e)||".",a=i.sourceRoot||".",o=t.map(((e,t)=>new Jn(R(n,a,e),s[t])));r=new Zn(i,o)}else r=new Jn(e,t);return s.reduce(n,r)}var ir={},sr=nr;function nr(e,t){if(!e)throw new Error(t||"Assertion failed")}nr.equal=function(e,t,i){if(e!=t)throw new Error(i||"Assertion failed: "+e+" != "+t)};var rr={exports:{}};"function"==typeof Object.create?rr.exports=function(e,t){t&&(e.super_=t,e.prototype=Object.create(t.prototype,{constructor:{value:e,enumerable:!1,writable:!0,configurable:!0}}))}:rr.exports=function(e,t){if(t){e.super_=t;var i=function(){};i.prototype=t.prototype,e.prototype=new i,e.prototype.constructor=e}};var ar=sr,or=rr.exports;function lr(e,t){return 55296==(64512&e.charCodeAt(t))&&!(t<0||t+1>=e.length)&&56320==(64512&e.charCodeAt(t+1))}function hr(e){return(e>>>24|e>>>8&65280|e<<8&16711680|(255&e)<<24)>>>0}function cr(e){return 1===e.length?"0"+e:e}function ur(e){return 7===e.length?"0"+e:6===e.length?"00"+e:5===e.length?"000"+e:4===e.length?"0000"+e:3===e.length?"00000"+e:2===e.length?"000000"+e:1===e.length?"0000000"+e:e}ir.inherits=or,ir.toArray=function(e,t){if(Array.isArray(e))return e.slice();if(!e)return[];var i=[];if("string"==typeof e)if(t){if("hex"===t)for((e=e.replace(/[^a-z0-9]+/gi,"")).length%2!=0&&(e="0"+e),n=0;n<e.length;n+=2)i.push(parseInt(e[n]+e[n+1],16))}else for(var s=0,n=0;n<e.length;n++){var r=e.charCodeAt(n);r<128?i[s++]=r:r<2048?(i[s++]=r>>6|192,i[s++]=63&r|128):lr(e,n)?(r=65536+((1023&r)<<10)+(1023&e.charCodeAt(++n)),i[s++]=r>>18|240,i[s++]=r>>12&63|128,i[s++]=r>>6&63|128,i[s++]=63&r|128):(i[s++]=r>>12|224,i[s++]=r>>6&63|128,i[s++]=63&r|128)}else for(n=0;n<e.length;n++)i[n]=0|e[n];return i},ir.toHex=function(e){for(var t="",i=0;i<e.length;i++)t+=cr(e[i].toString(16));return t},ir.htonl=hr,ir.toHex32=function(e,t){for(var i="",s=0;s<e.length;s++){var n=e[s];"little"===t&&(n=hr(n)),i+=ur(n.toString(16))}return i},ir.zero2=cr,ir.zero8=ur,ir.join32=function(e,t,i,s){var n=i-t;ar(n%4==0);for(var r=new Array(n/4),a=0,o=t;a<r.length;a++,o+=4){var l;l="big"===s?e[o]<<24|e[o+1]<<16|e[o+2]<<8|e[o+3]:e[o+3]<<24|e[o+2]<<16|e[o+1]<<8|e[o],r[a]=l>>>0}return r},ir.split32=function(e,t){for(var i=new Array(4*e.length),s=0,n=0;s<e.length;s++,n+=4){var r=e[s];"big"===t?(i[n]=r>>>24,i[n+1]=r>>>16&255,i[n+2]=r>>>8&255,i[n+3]=255&r):(i[n+3]=r>>>24,i[n+2]=r>>>16&255,i[n+1]=r>>>8&255,i[n]=255&r)}return i},ir.rotr32=function(e,t){return e>>>t|e<<32-t},ir.rotl32=function(e,t){return e<<t|e>>>32-t},ir.sum32=function(e,t){return e+t>>>0},ir.sum32_3=function(e,t,i){return e+t+i>>>0},ir.sum32_4=function(e,t,i,s){return e+t+i+s>>>0},ir.sum32_5=function(e,t,i,s,n){return e+t+i+s+n>>>0},ir.sum64=function(e,t,i,s){var n=e[t],r=s+e[t+1]>>>0,a=(r<s?1:0)+i+n;e[t]=a>>>0,e[t+1]=r},ir.sum64_hi=function(e,t,i,s){return(t+s>>>0<t?1:0)+e+i>>>0},ir.sum64_lo=function(e,t,i,s){return t+s>>>0},ir.sum64_4_hi=function(e,t,i,s,n,r,a,o){var l=0,h=t;return l+=(h=h+s>>>0)<t?1:0,l+=(h=h+r>>>0)<r?1:0,e+i+n+a+(l+=(h=h+o>>>0)<o?1:0)>>>0},ir.sum64_4_lo=function(e,t,i,s,n,r,a,o){return t+s+r+o>>>0},ir.sum64_5_hi=function(e,t,i,s,n,r,a,o,l,h){var c=0,u=t;return c+=(u=u+s>>>0)<t?1:0,c+=(u=u+r>>>0)<r?1:0,c+=(u=u+o>>>0)<o?1:0,e+i+n+a+l+(c+=(u=u+h>>>0)<h?1:0)>>>0},ir.sum64_5_lo=function(e,t,i,s,n,r,a,o,l,h){return t+s+r+o+h>>>0},ir.rotr64_hi=function(e,t,i){return(t<<32-i|e>>>i)>>>0},ir.rotr64_lo=function(e,t,i){return(e<<32-i|t>>>i)>>>0},ir.shr64_hi=function(e,t,i){return e>>>i},ir.shr64_lo=function(e,t,i){return(e<<32-i|t>>>i)>>>0};var dr={},pr=ir,fr=sr;function mr(){this.pending=null,this.pendingTotal=0,this.blockSize=this.constructor.blockSize,this.outSize=this.constructor.outSize,this.hmacStrength=this.constructor.hmacStrength,this.padLength=this.constructor.padLength/8,this.endian="big",this._delta8=this.blockSize/8,this._delta32=this.blockSize/32}dr.BlockHash=mr,mr.prototype.update=function(e,t){if(e=pr.toArray(e,t),this.pending?this.pending=this.pending.concat(e):this.pending=e,this.pendingTotal+=e.length,this.pending.length>=this._delta8){var i=(e=this.pending).length%this._delta8;this.pending=e.slice(e.length-i,e.length),0===this.pending.length&&(this.pending=null),e=pr.join32(e,0,e.length-i,this.endian);for(var s=0;s<e.length;s+=this._delta32)this._update(e,s,s+this._delta32)}return this},mr.prototype.digest=function(e){return this.update(this._pad()),fr(null===this.pending),this._digest(e)},mr.prototype._pad=function(){var e=this.pendingTotal,t=this._delta8,i=t-(e+this.padLength)%t,s=new Array(i+this.padLength);s[0]=128;for(var n=1;n<i;n++)s[n]=0;if(e<<=3,"big"===this.endian){for(var r=8;r<this.padLength;r++)s[n++]=0;s[n++]=0,s[n++]=0,s[n++]=0,s[n++]=0,s[n++]=e>>>24&255,s[n++]=e>>>16&255,s[n++]=e>>>8&255,s[n++]=255&e}else for(s[n++]=255&e,s[n++]=e>>>8&255,s[n++]=e>>>16&255,s[n++]=e>>>24&255,s[n++]=0,s[n++]=0,s[n++]=0,s[n++]=0,r=8;r<this.padLength;r++)s[n++]=0;return s};var gr={},yr=ir.rotr32;function xr(e,t,i){return e&t^~e&i}function Er(e,t,i){return e&t^e&i^t&i}function br(e,t,i){return e^t^i}gr.ft_1=function(e,t,i,s){return 0===e?xr(t,i,s):1===e||3===e?br(t,i,s):2===e?Er(t,i,s):void 0},gr.ch32=xr,gr.maj32=Er,gr.p32=br,gr.s0_256=function(e){return yr(e,2)^yr(e,13)^yr(e,22)},gr.s1_256=function(e){return yr(e,6)^yr(e,11)^yr(e,25)},gr.g0_256=function(e){return yr(e,7)^yr(e,18)^e>>>3},gr.g1_256=function(e){return yr(e,17)^yr(e,19)^e>>>10};var vr=ir,Sr=dr,Ar=gr,Ir=sr,Pr=vr.sum32,kr=vr.sum32_4,wr=vr.sum32_5,Cr=Ar.ch32,Nr=Ar.maj32,_r=Ar.s0_256,$r=Ar.s1_256,Tr=Ar.g0_256,Or=Ar.g1_256,Rr=Sr.BlockHash,Mr=[1116352408,1899447441,3049323471,3921009573,961987163,1508970993,2453635748,2870763221,3624381080,310598401,607225278,1426881987,1925078388,2162078206,2614888103,3248222580,3835390401,4022224774,264347078,604807628,770255983,1249150122,1555081692,1996064986,2554220882,2821834349,2952996808,3210313671,3336571891,3584528711,113926993,338241895,666307205,773529912,1294757372,1396182291,1695183700,1986661051,2177026350,2456956037,2730485921,2820302411,3259730800,3345764771,3516065817,3600352804,4094571909,275423344,430227734,506948616,659060556,883997877,958139571,1322822218,1537002063,1747873779,1955562222,2024104815,2227730452,2361852424,2428436474,2756734187,3204031479,3329325298];function Dr(){if(!(this instanceof Dr))return new Dr;Rr.call(this),this.h=[1779033703,3144134277,1013904242,2773480762,1359893119,2600822924,528734635,1541459225],this.k=Mr,this.W=new Array(64)}vr.inherits(Dr,Rr);var Lr=Dr;Dr.blockSize=512,Dr.outSize=256,Dr.hmacStrength=192,Dr.padLength=64,Dr.prototype._update=function(e,t){for(var i=this.W,s=0;s<16;s++)i[s]=e[t+s];for(;s<i.length;s++)i[s]=kr(Or(i[s-2]),i[s-7],Tr(i[s-15]),i[s-16]);var n=this.h[0],r=this.h[1],a=this.h[2],o=this.h[3],l=this.h[4],h=this.h[5],c=this.h[6],u=this.h[7];for(Ir(this.k.length===i.length),s=0;s<i.length;s++){var d=wr(u,$r(l),Cr(l,h,c),this.k[s],i[s]),p=Pr(_r(n),Nr(n,r,a));u=c,c=h,h=l,l=Pr(o,d),o=a,a=r,r=n,n=Pr(d,p)}this.h[0]=Pr(this.h[0],n),this.h[1]=Pr(this.h[1],r),this.h[2]=Pr(this.h[2],a),this.h[3]=Pr(this.h[3],o),this.h[4]=Pr(this.h[4],l),this.h[5]=Pr(this.h[5],h),this.h[6]=Pr(this.h[6],c),this.h[7]=Pr(this.h[7],u)},Dr.prototype._digest=function(e){return"hex"===e?vr.toHex32(this.h,"big"):vr.split32(this.h,"big")};var Vr=Lr;const Br=()=>Vr(),Fr={amd:Ur,cjs:Ur,es:jr,iife:Ur,system:jr,umd:Ur};function zr(e,t,i,s,n,r,a,o,l,h,c,u,d){const p=e.slice().reverse();for(const e of p)e.scope.addUsedOutsideNames(s,n,c,u);!function(e,t,i){for(const s of t){for(const t of s.scope.variables.values())t.included&&!(t.renderBaseName||t instanceof Js&&t.getOriginalVariable()!==t)&&t.setRenderNames(null,Kt(t.name,e));if(i.has(s)){const t=s.namespace;t.setRenderNames(null,Kt(t.name,e))}}}(s,p,d),Fr[n](s,i,t,r,a,o,l,h);for(const e of p)e.scope.deconflict(n,c,u)}function jr(e,t,i,s,n,r,a,o){for(const t of i.dependencies)(n||t instanceof Te)&&(t.variableName=Kt(t.suggestedVariableName,e));for(const i of t){const t=i.module,s=i.name;i.isNamespace&&(n||t instanceof Te)?i.setRenderNames(null,(t instanceof Te?t:a.get(t)).variableName):t instanceof Te&&"default"===s?i.setRenderNames(null,Kt([...t.exportedVariables].some((([e,t])=>"*"===t&&e.included))?t.suggestedVariableName+"__default":t.suggestedVariableName,e)):i.setRenderNames(null,Kt(s,e))}for(const t of o)t.setRenderNames(null,Kt(t.name,e))}function Ur(e,t,{deconflictedDefault:i,deconflictedNamespace:s,dependencies:n},r,a,o,l){for(const t of n)t.variableName=Kt(t.suggestedVariableName,e);for(const t of s)t.namespaceVariableName=Kt(`${t.suggestedVariableName}__namespace`,e);for(const t of i)s.has(t)&&Es(String(r(t.id)),o)?t.defaultVariableName=t.namespaceVariableName:t.defaultVariableName=Kt(`${t.suggestedVariableName}__default`,e);for(const e of t){const t=e.module;if(t instanceof Te){const i=e.name;if("default"===i){const i=String(r(t.id)),s=gs[i]?t.defaultVariableName:t.variableName;ys(i,o)?e.setRenderNames(s,"default"):e.setRenderNames(null,s)}else"*"===i?e.setRenderNames(null,xs[String(r(t.id))]?t.namespaceVariableName:t.variableName):e.setRenderNames(t.variableName,null)}else{const i=l.get(t);a&&e.isNamespace?e.setRenderNames(null,"default"===i.exportMode?i.namespaceVariableName:i.variableName):"default"===i.exportMode?e.setRenderNames(null,i.variableName):e.setRenderNames(i.variableName,i.getVariableExportName(e))}}}const Gr=/[\\'\r\n\u2028\u2029]/,Hr=/(['\r\n\u2028\u2029])/g,Wr=/\\/g;function qr(e){return e.match(Gr)?e.replace(Wr,"\\\\").replace(Hr,"\\$1"):e}function Kr(e,{exports:t,name:i,format:s},n,r,a){const o=e.getExportNames();if("default"===t){if(1!==o.length||"default"!==o[0])return fe(xe("default",o,r))}else if("none"===t&&o.length)return fe(xe("none",o,r));return"auto"===t&&(0===o.length?t="none":1===o.length&&"default"===o[0]?("cjs"===s&&n.has("exports")&&a(function(e){const t=ce(e);return{code:ge.PREFER_NAMED_EXPORTS,id:e,message:`Entry module "${t}" is implicitly using "default" export mode, which means for CommonJS output that its default export is assigned to "module.exports". For many tools, such CommonJS output will not be interchangeable with the original ES module. If this is intended, explicitly set "output.exports" to either "auto" or "default", otherwise you might want to consider changing the signature of "${t}" to use named exports only.`,url:"https://rollupjs.org/guide/en/#outputexports"}}(r)),t="default"):("es"!==s&&"system"!==s&&o.includes("default")&&a(function(e,t){return{code:ge.MIXED_EXPORTS,id:e,message:`Entry module "${ce(e)}" is using named and default exports together. Consumers of your bundle will have to use \`${t||"chunk"}["default"]\` to access the default export, which may not be what you want. Use \`output.exports: "named"\` to disable this warning`,url:"https://rollupjs.org/guide/en/#outputexports"}}(r,i)),t="named")),t}function Xr(e){const t=e.split("\n"),i=t.filter((e=>/^\t+/.test(e))),s=t.filter((e=>/^ {2,}/.test(e)));if(0===i.length&&0===s.length)return null;if(i.length>=s.length)return"\t";const n=s.reduce(((e,t)=>{const i=/^ +/.exec(t)[0].length;return Math.min(i,e)}),1/0);return new Array(n+1).join(" ")}function Yr(e,t,i,s,n){const r=e.getDependenciesToBeIncluded();for(const e of r){if(e instanceof Te){t.push(e);continue}const r=n.get(e);r===s?i.has(e)||(i.add(e),Yr(e,t,i,s,n)):t.push(r)}}function Qr(e){if(!e)return null;if("string"==typeof e&&(e=JSON.parse(e)),""===e.mappings)return{mappings:[],names:[],sources:[],version:3};const t="string"==typeof e.mappings?function(e){for(var t=[],s=[],n=[0,0,0,0,0],a=0,o=0,l=0,h=0;o<e.length;o++){var c=e.charCodeAt(o);if(44===c)r(s,n,a),a=0;else if(59===c)r(s,n,a),a=0,t.push(s),s=[],n[0]=0;else{var u=i[c];if(void 0===u)throw new Error("Invalid character ("+String.fromCharCode(c)+")");var d=32&u;if(h+=(u&=31)<<l,d)l+=5;else{var p=1&h;h>>>=1,p&&(h=0===h?-2147483648:-h),n[a]+=h,a++,h=l=0}}}return r(s,n,a),t.push(s),t}(e.mappings):e.mappings;return{...e,mappings:t}}const Jr=Symbol("bundleKeys"),Zr={type:"placeholder"};function ea(e,t,i){return ue(e)?fe(Ie(`Invalid pattern "${e}" for "${t}", patterns can be neither absolute nor relative paths. If you want your files to be stored in a subdirectory, write its name without a leading slash like this: subdirectory/pattern.`)):e.replace(/\[(\w+)\]/g,((e,s)=>{if(!i.hasOwnProperty(s))return fe(Ie(`"[${s}]" is not a valid placeholder in "${t}" pattern.`));const n=i[s]();return ue(n)?fe(Ie(`Invalid substitution "${n}" for placeholder "[${s}]" in "${t}" pattern, can be neither absolute nor relative path.`)):n}))}function ta(e,{[Jr]:t}){if(!t.has(e.toLowerCase()))return e;const i=T(e);e=e.substring(0,e.length-i.length);let s,n=1;for(;t.has((s=e+ ++n+i).toLowerCase()););return s}const ia=[".js",".jsx",".ts",".tsx"];function sa(e,t,i,s){const n="function"==typeof t?t(e.id):t[e.id];return n||(i?(s({code:"MISSING_GLOBAL_NAME",guess:e.variableName,message:`No name was provided for external module '${e.id}' in output.globals – guessing '${e.variableName}'`,source:e.id}),e.variableName):void 0)}class na{constructor(e,t,i,s,n,r,a,o,l,h){this.orderedModules=e,this.inputOptions=t,this.outputOptions=i,this.unsetOptions=s,this.pluginDriver=n,this.modulesById=r,this.chunkByModule=a,this.facadeChunkByModule=o,this.includedNamespaces=l,this.manualChunkAlias=h,this.entryModules=[],this.exportMode="named",this.facadeModule=null,this.id=null,this.namespaceVariableName="",this.needsExportsShim=!1,this.variableName="",this.accessedGlobalsByScope=new Map,this.dependencies=new Set,this.dynamicDependencies=new Set,this.dynamicEntryModules=[],this.dynamicName=null,this.exportNamesByVariable=new Map,this.exports=new Set,this.exportsByName=new Map,this.fileName=null,this.implicitEntryModules=[],this.implicitlyLoadedBefore=new Set,this.imports=new Set,this.includedReexportsByModule=new Map,this.indentString=void 0,this.isEmpty=!0,this.name=null,this.renderedDependencies=null,this.renderedExports=null,this.renderedHash=void 0,this.renderedModuleSources=new Map,this.renderedModules=Object.create(null),this.renderedSource=null,this.sortedExportNames=null,this.strictFacade=!1,this.usedModules=void 0,this.execIndex=e.length>0?e[0].execIndex:1/0;const c=new Set(e);for(const t of e){t.namespace.included&&l.add(t),this.isEmpty&&t.isIncluded()&&(this.isEmpty=!1),(t.info.isEntry||i.preserveModules)&&this.entryModules.push(t);for(const e of t.includedDynamicImporters)c.has(e)||(this.dynamicEntryModules.push(t),t.info.syntheticNamedExports&&!i.preserveModules&&(l.add(t),this.exports.add(t.namespace)));t.implicitlyLoadedAfter.size>0&&this.implicitEntryModules.push(t)}this.suggestedVariableName=$e(this.generateVariableName())}static generateFacade(e,t,i,s,n,r,a,o,l,h){const c=new na([],e,t,i,s,n,r,a,o,null);c.assignFacadeName(h,l),a.has(l)||a.set(l,c);for(const e of l.getDependenciesToBeIncluded())c.dependencies.add(e instanceof kn?r.get(e):e);return!c.dependencies.has(r.get(l))&&l.info.moduleSideEffects&&l.hasEffects()&&c.dependencies.add(r.get(l)),c.ensureReexportsAreAvailableForModule(l),c.facadeModule=l,c.strictFacade=!0,c}canModuleBeFacade(e,t){const i=e.getExportNamesByVariable();for(const t of this.exports)if(!i.has(t))return 0===i.size&&e.isUserDefinedEntryPoint&&"strict"===e.preserveSignature&&this.unsetOptions.has("preserveEntrySignatures")&&this.inputOptions.onwarn({code:"EMPTY_FACADE",id:e.id,message:`To preserve the export signature of the entry module "${ce(e.id)}", an empty facade chunk was created. This often happens when creating a bundle for a web app where chunks are placed in script tags and exports are ignored. In this case it is recommended to set "preserveEntrySignatures: false" to avoid this and reduce the number of chunks. Otherwise if this is intentional, set "preserveEntrySignatures: 'strict'" explicitly to silence this warning.`,url:"https://rollupjs.org/guide/en/#preserveentrysignatures"}),!1;for(const s of t)if(!i.has(s)&&s.module!==e)return!1;return!0}generateExports(){this.sortedExportNames=null;const e=new Set(this.exports);if(null!==this.facadeModule&&(!1!==this.facadeModule.preserveSignature||this.strictFacade)){const t=this.facadeModule.getExportNamesByVariable();for(const[i,s]of t){this.exportNamesByVariable.set(i,[...s]);for(const e of s)this.exportsByName.set(e,i);e.delete(i)}}this.outputOptions.minifyInternalExports?function(e,t,i){let s=0;for(const n of e){let[e]=n.name;if(t.has(e))do{e=qt(++s),49===e.charCodeAt(0)&&(s+=9*64**(e.length-1),e=qt(s))}while(Ce.has(e)||t.has(e));t.set(e,n),i.set(n,[e])}}(e,this.exportsByName,this.exportNamesByVariable):function(e,t,i){for(const s of e){let e=0,n=s.name;for(;t.has(n);)n=s.name+"$"+ ++e;t.set(n,s),i.set(s,[n])}}(e,this.exportsByName,this.exportNamesByVariable),(this.outputOptions.preserveModules||this.facadeModule&&this.facadeModule.info.isEntry)&&(this.exportMode=Kr(this,this.outputOptions,this.unsetOptions,this.facadeModule.id,this.inputOptions.onwarn))}generateFacades(){var e;const t=[],i=new Set([...this.entryModules,...this.implicitEntryModules]),s=new Set(this.dynamicEntryModules.map((({namespace:e})=>e)));for(const e of i)if(e.preserveSignature)for(const t of e.getExportNamesByVariable().keys())s.add(t);for(const e of i){const i=Array.from(new Set(e.chunkNames.filter((({isUserDefined:e})=>e)).map((({name:e})=>e))),(e=>({name:e})));if(0===i.length&&e.isUserDefinedEntryPoint&&i.push({}),i.push(...Array.from(e.chunkFileNames,(e=>({fileName:e})))),0===i.length&&i.push({}),!this.facadeModule){const t="strict"===e.preserveSignature||"exports-only"===e.preserveSignature&&0!==e.getExportNamesByVariable().size;(!t||this.outputOptions.preserveModules||this.canModuleBeFacade(e,s))&&(this.facadeModule=e,this.facadeChunkByModule.set(e,this),e.preserveSignature&&(this.strictFacade=t),this.assignFacadeName(i.shift(),e))}for(const s of i)t.push(na.generateFacade(this.inputOptions,this.outputOptions,this.unsetOptions,this.pluginDriver,this.modulesById,this.chunkByModule,this.facadeChunkByModule,this.includedNamespaces,e,s))}for(const t of this.dynamicEntryModules)t.info.syntheticNamedExports||(!this.facadeModule&&this.canModuleBeFacade(t,s)?(this.facadeModule=t,this.facadeChunkByModule.set(t,this),this.strictFacade=!0,this.dynamicName=ra(t)):this.facadeModule===t&&!this.strictFacade&&this.canModuleBeFacade(t,s)?this.strictFacade=!0:(null===(e=this.facadeChunkByModule.get(t))||void 0===e?void 0:e.strictFacade)||(this.includedNamespaces.add(t),this.exports.add(t.namespace)));return this.outputOptions.preserveModules||this.addNecessaryImportsForFacades(),t}generateId(e,t,i,s){if(null!==this.fileName)return this.fileName;const[n,r]=this.facadeModule&&this.facadeModule.isUserDefinedEntryPoint?[t.entryFileNames,"output.entryFileNames"]:[t.chunkFileNames,"output.chunkFileNames"];return ta(ea("function"==typeof n?n(this.getChunkInfo()):n,r,{format:()=>t.format,hash:()=>s?this.computeContentHashWithDependencies(e,t,i):"[hash]",name:()=>this.getChunkName()}),i)}generateIdPreserveModules(e,t,i,s){const[{id:n}]=this.orderedModules,r=this.outputOptions.sanitizeFileName(n.split(aa,1)[0]);let a;const o=s.has("entryFileNames")?"[name][assetExtname].js":t.entryFileNames,l="function"==typeof o?o(this.getChunkInfo()):o;if(w(r)){const i=$(r),s=T(r),n=`${i}/${ea(l,"output.entryFileNames",{assetExtname:()=>ia.includes(s)?"":s,ext:()=>s.substring(1),extname:()=>s,format:()=>t.format,name:()=>this.getChunkName()})}`,{preserveModulesRoot:o}=t;a=o&&R(n).startsWith(o)?n.slice(o.length).replace(/^[\\/]/,""):O(e,n)}else{const e=T(r);a=`_virtual/${ea(l,"output.entryFileNames",{assetExtname:()=>ia.includes(e)?"":e,ext:()=>e.substring(1),extname:()=>e,format:()=>t.format,name:()=>he(r)})}`}return ta(N(a),i)}getChunkInfo(){const e=this.facadeModule,t=this.getChunkName.bind(this);return{exports:this.getExportNames(),facadeModuleId:e&&e.id,isDynamicEntry:this.dynamicEntryModules.length>0,isEntry:null!==e&&e.info.isEntry,isImplicitEntry:this.implicitEntryModules.length>0,modules:this.renderedModules,get name(){return t()},type:"chunk"}}getChunkInfoWithFileNames(){return Object.assign(this.getChunkInfo(),{code:void 0,dynamicImports:Array.from(this.dynamicDependencies,cn),fileName:this.id,implicitlyLoadedBefore:Array.from(this.implicitlyLoadedBefore,cn),importedBindings:this.getImportedBindingsPerDependency(),imports:Array.from(this.dependencies,cn),map:void 0,referencedFiles:this.getReferencedFiles()})}getChunkName(){var e;return null!==(e=this.name)&&void 0!==e?e:this.name=this.outputOptions.sanitizeFileName(this.getFallbackChunkName())}getExportNames(){var e;return null!==(e=this.sortedExportNames)&&void 0!==e?e:this.sortedExportNames=Array.from(this.exportsByName.keys()).sort()}getRenderedHash(){if(this.renderedHash)return this.renderedHash;const e=Br(),t=this.pluginDriver.hookReduceValueSync("augmentChunkHash","",[this.getChunkInfo()],((e,t)=>(t&&(e+=t),e)));return e.update(t),e.update(this.renderedSource.toString()),e.update(this.getExportNames().map((e=>{const t=this.exportsByName.get(e);return`${ce(t.module.id).replace(/\\/g,"/")}:${t.name}:${e}`})).join(",")),this.renderedHash=e.digest("hex")}getVariableExportName(e){return this.outputOptions.preserveModules&&e instanceof on?"*":this.exportNamesByVariable.get(e)[0]}link(){this.dependencies=function(e,t,i){const s=[],n=new Set;for(let r=t.length-1;r>=0;r--){const a=t[r];if(!n.has(a)){const t=[];Yr(a,t,n,e,i),s.unshift(t)}}const r=new Set;for(const e of s)for(const t of e)r.add(t);return r}(this,this.orderedModules,this.chunkByModule);for(const e of this.orderedModules)this.addDependenciesToChunk(e.dynamicDependencies,this.dynamicDependencies),this.addDependenciesToChunk(e.implicitlyLoadedBefore,this.implicitlyLoadedBefore),this.setUpChunkImportsAndExportsForModule(e)}preRender(e,t,i){const{_:s,getPropertyAccess:n,n:r}=i,a=new v({separator:`${r}${r}`});this.usedModules=[],this.indentString=function(e,t){if(!0!==t.indent)return t.indent;for(const t of e){const e=Xr(t.originalCode);if(null!==e)return e}return"\t"}(this.orderedModules,e);const o={dynamicImportFunction:e.dynamicImportFunction,exportNamesByVariable:this.exportNamesByVariable,format:e.format,freeze:e.freeze,indent:this.indentString,namespaceToStringTag:e.namespaceToStringTag,outputPluginDriver:this.pluginDriver,snippets:i};if(e.hoistTransitiveImports&&!this.outputOptions.preserveModules&&null!==this.facadeModule)for(const e of this.dependencies)e instanceof na&&this.inlineChunkDependencies(e);this.prepareModulesForRendering(i),this.setIdentifierRenderResolutions(e);let l="";const h=this.renderedModules;for(const t of this.orderedModules){let i=0;if(t.isIncluded()||this.includedNamespaces.has(t)){const s=t.render(o).trim();i=s.length(),i&&(e.compact&&s.lastLine().includes("//")&&s.append("\n"),this.renderedModuleSources.set(t,s),a.addSource(s),this.usedModules.push(t));const n=t.namespace;if(this.includedNamespaces.has(t)&&!this.outputOptions.preserveModules){const e=n.renderBlock(o);n.renderFirst()?l+=r+e:a.addSource(new E(e))}}const{renderedExports:s,removedExports:n}=t.getRenderedExports(),{renderedModuleSources:c}=this;h[t.id]={get code(){var e,i;return null!==(i=null===(e=c.get(t))||void 0===e?void 0:e.toString())&&void 0!==i?i:null},originalLength:t.originalCode.length,removedExports:n,renderedExports:s,renderedLength:i}}if(l&&a.prepend(l+r+r),this.needsExportsShim&&a.prepend(`${r}${i.cnst} _missingExportShim${s}=${s}void 0;${r}${r}`),e.compact?this.renderedSource=a:this.renderedSource=a.trim(),this.renderedHash=void 0,this.isEmpty&&0===this.getExportNames().length&&0===this.dependencies.size){const e=this.getChunkName();this.inputOptions.onwarn({chunkName:e,code:"EMPTY_BUNDLE",message:`Generated an empty chunk: "${e}"`})}this.setExternalRenderPaths(e,t),this.renderedDependencies=this.getChunkDependencyDeclarations(e,n),this.renderedExports="none"===this.exportMode?[]:this.getChunkExportDeclarations(e.format,n)}async render(e,t,i,s){En("render format",2);const n=e.format,r=Qn[n];e.dynamicImportFunction&&"es"!==n&&this.inputOptions.onwarn(Ee("output.dynamicImportFunction","outputdynamicImportFunction",'this option is ignored for formats other than "es"'));for(const e of this.dependencies){const t=this.renderedDependencies.get(e);if(e instanceof Te){const i=e.renderPath;t.id=qr(e.renormalizeRenderPath?pe(this.id,i,!1,!1):i)}else t.namedExportsMode="default"!==e.exportMode,t.id=qr(pe(this.id,e.id,!1,!0))}this.finaliseDynamicImports(e,s),this.finaliseImportMetas(n,s);const a=0!==this.renderedExports.length||[...this.renderedDependencies.values()].some((e=>e.reexports&&0!==e.reexports.length));let o=null;const l=new Set;for(const e of this.orderedModules){e.usesTopLevelAwait&&(o=e.id);const t=this.accessedGlobalsByScope.get(e.scope);if(t)for(const e of t)l.add(e)}if(null!==o&&"es"!==n&&"system"!==n)return fe({code:"INVALID_TLA_FORMAT",id:o,message:`Module format ${n} does not support top-level await. Use the "es" or "system" output formats rather.`});if(!this.id)throw new Error("Internal Error: expecting chunk id");const h=r(this.renderedSource,{accessedGlobals:l,dependencies:[...this.renderedDependencies.values()],exports:this.renderedExports,hasExports:a,id:this.id,indent:this.indentString,intro:t.intro,isEntryFacade:this.outputOptions.preserveModules||null!==this.facadeModule&&this.facadeModule.info.isEntry,isModuleFacade:null!==this.facadeModule,namedExportsMode:"default"!==this.exportMode,outro:t.outro,snippets:s,usesTopLevelAwait:null!==o,warn:this.inputOptions.onwarn},e);t.banner&&h.prepend(t.banner),t.footer&&h.append(t.footer);const u=h.toString();bn("render format",2);let d=null;const p=[];let f=await function({code:e,options:t,outputPluginDriver:i,renderChunk:s,sourcemapChain:n}){return i.hookReduceArg0("renderChunk",[e,s,t],((e,t,i)=>{if(null==t)return e;if("string"==typeof t&&(t={code:t,map:void 0}),null!==t.map){const e=Qr(t.map);n.push(e||{missing:!0,plugin:i.name})}return t.code}))}({code:u,options:e,outputPluginDriver:this.pluginDriver,renderChunk:i,sourcemapChain:p});if(e.sourcemap){let t;En("sourcemap",2),t=e.file?R(e.sourcemapFile||e.file):e.dir?R(e.dir,this.id):R(this.id);const i=h.generateDecodedMap({});d=function(e,t,i,s,n,r){const a=er(r),o=i.filter((e=>!e.excludeFromSourcemap)).map((e=>tr(e.id,e.originalCode,e.originalSourcemap,e.sourcemapChain,a))),l=new Zn(t,o),h=s.reduce(a,l);let{sources:u,sourcesContent:d,names:p,mappings:f}=h.traceMappings();if(e){const t=$(e);u=u.map((e=>O(t,e))),e=_(e)}return d=n?null:d,new c({file:e,mappings:f,names:p,sources:u,sourcesContent:d})}(t,i,this.usedModules,p,e.sourcemapExcludeSources,this.inputOptions.onwarn),d.sources=d.sources.map((i=>{const{sourcemapPathTransform:s}=e;if(s){const e=s(i,`${t}.map`);return"string"!=typeof e&&fe(Ie("sourcemapPathTransform function must return a string.")),e}return i})).map(N),bn("sourcemap",2)}return e.compact||"\n"===f[f.length-1]||(f+="\n"),{code:f,map:d}}addDependenciesToChunk(e,t){for(const i of e)if(i instanceof kn){const e=this.chunkByModule.get(i);e&&e!==this&&t.add(e)}else t.add(i)}addNecessaryImportsForFacades(){for(const[e,t]of this.includedReexportsByModule)if(this.includedNamespaces.has(e))for(const e of t)this.imports.add(e)}assignFacadeName({fileName:e,name:t},i){e?this.fileName=e:this.name=this.outputOptions.sanitizeFileName(t||ra(i))}checkCircularDependencyImport(e,t){const i=e.module;if(i instanceof kn){const o=this.chunkByModule.get(i);let l;do{if(l=t.alternativeReexportModules.get(e),l){const h=this.chunkByModule.get(l);h&&h!==o&&this.inputOptions.onwarn((s=i.getExportNamesByVariable().get(e)[0],n=i.id,r=l.id,a=t.id,{code:ge.CYCLIC_CROSS_CHUNK_REEXPORT,exporter:n,importer:a,message:`Export "${s}" of module ${ce(n)} was reexported through module ${ce(r)} while both modules are dependencies of each other and will end up in different chunks by current Rollup settings. This scenario is not well supported at the moment as it will produce a circular dependency between chunks and will likely lead to broken execution order.\nEither change the import in ${ce(a)} to point directly to the exporting module or do not use "preserveModules" to ensure these modules end up in the same chunk.`,reexporter:r})),t=l}}while(l)}var s,n,r,a}computeContentHashWithDependencies(e,t,i){const s=Br();s.update([e.intro,e.outro,e.banner,e.footer].join(":")),s.update(t.format);const n=new Set([this]);for(const r of n)if(r instanceof Te?s.update(`:${r.renderPath}`):(s.update(r.getRenderedHash()),s.update(r.generateId(e,t,i,!1))),!(r instanceof Te))for(const e of[...r.dependencies,...r.dynamicDependencies])n.add(e);return s.digest("hex").substr(0,8)}ensureReexportsAreAvailableForModule(e){const t=[],i=e.getExportNamesByVariable();for(const s of i.keys()){const i=s instanceof ln,n=i?s.getBaseVariable():s;if(!(n instanceof on&&this.outputOptions.preserveModules)){this.checkCircularDependencyImport(n,e);const s=n.module;if(s instanceof kn){const e=this.chunkByModule.get(s);e&&e!==this&&(e.exports.add(n),t.push(n),i&&this.imports.add(n))}}}t.length&&this.includedReexportsByModule.set(e,t)}finaliseDynamicImports(e,t){const i="amd"===e.format&&!e.amd.forceJsExtensionForImports;for(const[e,s]of this.renderedModuleSources)for(const{node:n,resolution:r}of e.dynamicImports){const e=this.chunkByModule.get(r),a=this.facadeChunkByModule.get(r);if(!r||!n.included||e===this)continue;const o=r instanceof kn?`'${qr(pe(this.id,(a||e).id,i,!0))}'`:r instanceof Te?`'${qr(r.renormalizeRenderPath?pe(this.id,r.renderPath,i,!1):r.renderPath)}'`:r;n.renderFinalResolution(s,o,r instanceof kn&&!(null==a?void 0:a.strictFacade)&&e.exportNamesByVariable.get(r.namespace)[0],t)}}finaliseImportMetas(e,t){for(const[i,s]of this.renderedModuleSources)for(const n of i.importMetas)n.renderFinalMechanism(s,this.id,e,t,this.pluginDriver)}generateVariableName(){if(this.manualChunkAlias)return this.manualChunkAlias;const e=this.entryModules[0]||this.implicitEntryModules[0]||this.dynamicEntryModules[0]||this.orderedModules[this.orderedModules.length-1];return e?ra(e):"chunk"}getChunkDependencyDeclarations(e,t){const i=this.getImportSpecifiers(t),s=this.getReexportSpecifiers(),n=new Map;for(const t of this.dependencies){const r=i.get(t)||null,a=s.get(t)||null,o=t instanceof Te||"default"!==t.exportMode;n.set(t,{defaultVariableName:t.defaultVariableName,globalName:t instanceof Te&&("umd"===e.format||"iife"===e.format)&&sa(t,e.globals,null!==(r||a),this.inputOptions.onwarn),id:void 0,imports:r,isChunk:t instanceof na,name:t.variableName,namedExportsMode:o,namespaceVariableName:t.namespaceVariableName,reexports:a})}return n}getChunkExportDeclarations(e,t){const i=[];for(const s of this.getExportNames()){if("*"===s[0])continue;const n=this.exportsByName.get(s);if(!(n instanceof ln)){const e=n.module;if(e&&this.chunkByModule.get(e)!==this)continue}let r=null,a=!1,o=n.getName(t);if(n instanceof Wt){for(const e of n.declarations)if(e.parent instanceof ss||e instanceof ns&&e.declaration instanceof ss){a=!0;break}}else n instanceof ln&&(r=o,"es"===e&&(o=n.renderName));i.push({exported:s,expression:r,hoisted:a,local:o})}return i}getDependenciesToBeDeconflicted(e,t,i){const s=new Set,n=new Set,r=new Set;for(const t of[...this.exportNamesByVariable.keys(),...this.imports])if(e||t.isNamespace){const a=t.module;if(a instanceof Te)s.add(a),e&&("default"===t.name?gs[String(i(a.id))]&&n.add(a):"*"===t.name&&xs[String(i(a.id))]&&r.add(a));else{const i=this.chunkByModule.get(a);i!==this&&(s.add(i),e&&"default"===i.exportMode&&t.isNamespace&&r.add(i))}}if(t)for(const e of this.dependencies)s.add(e);return{deconflictedDefault:n,deconflictedNamespace:r,dependencies:s}}getFallbackChunkName(){return this.manualChunkAlias?this.manualChunkAlias:this.dynamicName?this.dynamicName:this.fileName?he(this.fileName):he(this.orderedModules[this.orderedModules.length-1].id)}getImportSpecifiers(e){const{interop:t}=this.outputOptions,i=new Map;for(const s of this.imports){const n=s.module;let r,a;if(n instanceof Te){if(r=n,a=s.name,"default"!==a&&"*"!==a&&"defaultOnly"===t(n.id))return fe(Se(n.id,a,!1))}else r=this.chunkByModule.get(n),a=r.getVariableExportName(s);M(i,r,(()=>[])).push({imported:a,local:s.getName(e)})}return i}getImportedBindingsPerDependency(){const e={};for(const[t,i]of this.renderedDependencies){const s=new Set;if(i.imports)for(const{imported:e}of i.imports)s.add(e);if(i.reexports)for(const{imported:e}of i.reexports)s.add(e);e[t.id]=[...s]}return e}getReexportSpecifiers(){const{externalLiveBindings:e,interop:t}=this.outputOptions,i=new Map;for(let s of this.getExportNames()){let n,r,a=!1;if("*"===s[0]){const i=s.substring(1);"defaultOnly"===t(i)&&this.inputOptions.onwarn(Ae(i)),a=e,n=this.modulesById.get(i),r=s="*"}else{const i=this.exportsByName.get(s);if(i instanceof ln)continue;const o=i.module;if(o instanceof kn){if(n=this.chunkByModule.get(o),n===this)continue;r=n.getVariableExportName(i),a=i.isReassigned}else{if(n=o,r=i.name,"default"!==r&&"*"!==r&&"defaultOnly"===t(o.id))return fe(Se(o.id,r,!0));a=e&&("default"!==r||ys(String(t(o.id)),!0))}}M(i,n,(()=>[])).push({imported:r,needsLiveBinding:a,reexported:s})}return i}getReferencedFiles(){const e=[];for(const t of this.orderedModules)for(const i of t.importMetas){const t=i.getReferencedFileName(this.pluginDriver);t&&e.push(t)}return e}inlineChunkDependencies(e){for(const t of e.dependencies)this.dependencies.has(t)||(this.dependencies.add(t),t instanceof na&&this.inlineChunkDependencies(t))}prepareModulesForRendering(e){var t;const i=this.accessedGlobalsByScope;for(const s of this.orderedModules){for(const{node:n,resolution:r}of s.dynamicImports)if(n.included)if(r instanceof kn){const s=this.chunkByModule.get(r);s===this?n.setInternalResolution(r.namespace):n.setExternalResolution((null===(t=this.facadeChunkByModule.get(r))||void 0===t?void 0:t.exportMode)||s.exportMode,r,this.outputOptions,e,this.pluginDriver,i)}else n.setExternalResolution("external",r,this.outputOptions,e,this.pluginDriver,i);for(const e of s.importMetas)e.addAccessedGlobals(this.outputOptions.format,i);this.includedNamespaces.has(s)&&!this.outputOptions.preserveModules&&s.namespace.prepare(i)}}setExternalRenderPaths(e,t){for(const i of[...this.dependencies,...this.dynamicDependencies])i instanceof Te&&i.setRenderPath(e,t)}setIdentifierRenderResolutions({format:e,interop:t,namespaceToStringTag:i}){const s=new Set;for(const t of this.getExportNames()){const i=this.exportsByName.get(t);"es"!==e&&"system"!==e&&i.isReassigned&&!i.isId?i.setRenderNames("exports",t):i instanceof ln?s.add(i):i.setRenderNames(null,null)}for(const e of this.orderedModules)if(e.needsExportShim){this.needsExportsShim=!0;break}const n=new Set(["Object","Promise"]);switch(this.needsExportsShim&&n.add(rn),i&&n.add("Symbol"),e){case"system":n.add("module").add("exports");break;case"es":break;case"cjs":n.add("module").add("require").add("__filename").add("__dirname");default:n.add("exports");for(const e of Os)n.add(e)}zr(this.orderedModules,this.getDependenciesToBeDeconflicted("es"!==e&&"system"!==e,"amd"===e||"umd"===e||"iife"===e,t),this.imports,n,e,t,this.outputOptions.preserveModules,this.outputOptions.externalLiveBindings,this.chunkByModule,s,this.exportNamesByVariable,this.accessedGlobalsByScope,this.includedNamespaces)}setUpChunkImportsAndExportsForModule(e){const t=new Set(e.includedImports);if(!this.outputOptions.preserveModules&&this.includedNamespaces.has(e)){const i=e.namespace.getMemberVariables();for(const e of Object.values(i))t.add(e)}for(let i of t){i instanceof Js&&(i=i.getOriginalVariable()),i instanceof ln&&(i=i.getBaseVariable());const t=this.chunkByModule.get(i.module);t!==this&&(this.imports.add(i),!(i instanceof on&&this.outputOptions.preserveModules)&&i.module instanceof kn&&(t.exports.add(i),this.checkCircularDependencyImport(i,e)))}(this.includedNamespaces.has(e)||e.info.isEntry&&!1!==e.preserveSignature||e.includedDynamicImporters.some((e=>this.chunkByModule.get(e)!==this)))&&this.ensureReexportsAreAvailableForModule(e);for(const{node:t,resolution:i}of e.dynamicImports)t.included&&i instanceof kn&&this.chunkByModule.get(i)===this&&!this.includedNamespaces.has(i)&&(this.includedNamespaces.add(i),this.ensureReexportsAreAvailableForModule(i))}}function ra(e){var t,i,s,n;return null!==(n=null!==(i=null===(t=e.chunkNames.find((({isUserDefined:e})=>e)))||void 0===t?void 0:t.name)&&void 0!==i?i:null===(s=e.chunkNames[0])||void 0===s?void 0:s.name)&&void 0!==n?n:he(e.id)}const aa=/[?#]/,oa=(e,t)=>t?`${e}\n${t}`:e,la=(e,t)=>t?`${e}\n\n${t}`:e;function ha(e,t){const i=[],s=new Set(t.keys()),n=Object.create(null);for(const[e,i]of t)ca(e,n[i]=n[i]||[],s);for(const[e,t]of Object.entries(n))i.push({alias:e,modules:t});const r=new Map,{dependentEntryPointsByModule:a,dynamicEntryModules:o}=function(e){const t=new Set,i=new Map,s=new Set(e);for(const e of s){const n=new Set([e]);for(const r of n){M(i,r,(()=>new Set)).add(e);for(const e of r.getDependenciesToBeIncluded())e instanceof Te||n.add(e);for(const{resolution:e}of r.dynamicImports)e instanceof kn&&e.includedDynamicImporters.length>0&&(t.add(e),s.add(e));for(const e of r.implicitlyLoadedBefore)t.add(e),s.add(e)}}return{dependentEntryPointsByModule:i,dynamicEntryModules:t}}(e),l=function(e,t){const i=new Map;for(const s of t){const t=M(i,s,(()=>new Set));for(const i of[...s.includedDynamicImporters,...s.implicitlyLoadedAfter])for(const s of e.get(i))t.add(s)}return i}(a,o),h=new Set(e);function c(e,t){const i=new Set([e]);for(const n of i){const o=M(r,n,(()=>new Set));if(!t||!u(t,a.get(n))){o.add(e);for(const e of n.getDependenciesToBeIncluded())e instanceof Te||s.has(e)||i.add(e)}}}function u(e,t){const i=new Set(e);for(const e of i)if(!t.has(e)){if(h.has(e))return!1;const t=l.get(e);for(const e of t)i.add(e)}return!0}for(const t of e)s.has(t)||c(t,null);for(const e of o)s.has(e)||c(e,l.get(e));return i.push(...function(e,t){const i=Object.create(null);for(const[s,n]of t){let t="";for(const i of e)t+=n.has(i)?"X":"_";const r=i[t];r?r.push(s):i[t]=[s]}return Object.values(i).map((e=>({alias:null,modules:e})))}([...e,...o],r)),i}function ca(e,t,i){const s=new Set([e]);for(const e of s){i.add(e),t.push(e);for(const t of e.dependencies)t instanceof Te||i.has(t)||s.add(t)}}const ua=(e,t)=>e.execIndex>t.execIndex?1:-1;function da(e,t,i){const s=Symbol(e.id),n=[ce(e.id)];let r=t;for(e.cycles.add(s);r!==e;)r.cycles.add(s),n.push(ce(r.id)),r=i.get(r);return n.push(n[0]),n.reverse(),n}const pa=(e,t)=>t?`(${e})`:e,fa=/^(?!\d)[\w$]+$/;class ma{constructor(e,t,i,s,n){this.outputOptions=e,this.unsetOptions=t,this.inputOptions=i,this.pluginDriver=s,this.graph=n,this.facadeChunkByModule=new Map,this.includedNamespaces=new Set}async generate(e){En("GENERATE",1);const t=Object.create(null),i=(e=>{const t=new Set;return new Proxy(e,{deleteProperty:(e,i)=>("string"==typeof i&&t.delete(i.toLowerCase()),Reflect.deleteProperty(e,i)),get:(e,i)=>i===Jr?t:Reflect.get(e,i),set:(e,i,s)=>("string"==typeof i&&t.add(i.toLowerCase()),Reflect.set(e,i,s))})})(t);this.pluginDriver.setOutputBundle(i,this.outputOptions,this.facadeChunkByModule);try{await this.pluginDriver.hookParallel("renderStart",[this.outputOptions,this.inputOptions]),En("generate chunks",2);const e=await this.generateChunks();e.length>1&&(s=this.outputOptions,n=this.inputOptions.onwarn,"umd"===s.format||"iife"===s.format?fe(Ee("output.format","outputformat","UMD and IIFE output formats are not supported for code-splitting builds",s.format)):"string"==typeof s.file?fe(Ee("output.file","outputdir",'when building multiple chunks, the "output.dir" option must be used, not "output.file". To inline dynamic imports, set the "inlineDynamicImports" option')):s.sourcemapFile?fe(Ee("output.sourcemapFile","outputsourcemapfile",'"output.sourcemapFile" is only supported for single-file builds')):!s.amd.autoId&&s.amd.id&&n(Ee("output.amd.id","outputamd",'this option is only properly supported for single-file builds. Use "output.amd.autoId" and "output.amd.basePath" instead')));const t=function(e){if(0===e.length)return"/";if(1===e.length)return $(e[0]);const t=e.slice(1).reduce(((e,t)=>{const i=t.split(/\/+|\\+/);let s;for(s=0;e[s]===i[s]&&s<Math.min(e.length,i.length);s++);return e.slice(0,s)}),e[0].split(/\/+|\\+/));return t.length>1?t.join("/"):"/"}(function(e){const t=[];for(const i of e)for(const e of i.entryModules)w(e.id)&&t.push(e.id);return t}(e));bn("generate chunks",2),En("render modules",2);const r=await async function(e,t){try{let[i,s,n,r]=await Promise.all([t.hookReduceValue("banner",e.banner(),[],oa),t.hookReduceValue("footer",e.footer(),[],oa),t.hookReduceValue("intro",e.intro(),[],la),t.hookReduceValue("outro",e.outro(),[],la)]);return n&&(n+="\n\n"),r&&(r=`\n\n${r}`),i.length&&(i+="\n"),s.length&&(s="\n"+s),{banner:i,footer:s,intro:n,outro:r}}catch(e){return fe({code:"ADDON_ERROR",message:`Could not retrieve ${e.hook}. Check configuration of plugin ${e.plugin}.\n\tError Message: ${e.message}`})}}(this.outputOptions,this.pluginDriver),a=function({compact:e,generatedCode:{arrowFunctions:t,constBindings:i,objectShorthand:s,reservedNamesAsProps:n}}){const{_:r,n:a,s:o}=e?{_:"",n:"",s:""}:{_:" ",n:"\n",s:";"},l=i?"const":"var",h=(e,{isAsync:t,name:i})=>`${t?"async ":""}function${i?` ${i}`:""}${r}(${e.join(`,${r}`)})${r}`,c=t?(e,{isAsync:t,name:i})=>{const s=1===e.length;return`${i?`${l} ${i}${r}=${r}`:""}${t?`async${s?" ":r}`:""}${s?e[0]:`(${e.join(`,${r}`)})`}${r}=>${r}`}:h,u=(e,{functionReturn:i,lineBreakIndent:s,name:n})=>[`${c(e,{isAsync:!1,name:n})}${t?s?`${a}${s.base}${s.t}`:"":`{${s?`${a}${s.base}${s.t}`:r}${i?"return ":""}`}`,t?`${n?";":""}${s?`${a}${s.base}`:""}`:`${o}${s?`${a}${s.base}`:r}}`],d=n?e=>fa.test(e):e=>!Ce.has(e)&&fa.test(e);return{_:r,cnst:l,getDirectReturnFunction:u,getDirectReturnIifeLeft:(e,i,{needsArrowReturnParens:s,needsWrappedFunction:n})=>{const[r,a]=u(e,{functionReturn:!0,lineBreakIndent:null,name:null});return`${pa(`${r}${pa(i,t&&s)}${a}`,t||n)}(`},getFunctionIntro:c,getNonArrowFunctionIntro:h,getObject(e,{lineBreakIndent:t}){const i=t?`${a}${t.base}${t.t}`:r;return`{${e.map((([e,t])=>{if(null===e)return`${i}${t}`;const n=!d(e);return e===t&&s&&!n?i+e:`${i}${n?`'${e}'`:e}:${r}${t}`})).join(",")}${0===e.length?"":t?`${a}${t.base}`:r}}`},getPropertyAccess:e=>d(e)?`.${e}`:`[${JSON.stringify(e)}]`,n:a,s:o}}(this.outputOptions);this.prerenderChunks(e,t,a),bn("render modules",2),await this.addFinalizedChunksToBundle(e,t,r,i,a)}catch(e){throw await this.pluginDriver.hookParallel("renderError",[e]),e}var s,n;return await this.pluginDriver.hookSeq("generateBundle",[this.outputOptions,i,e]),this.finaliseAssets(i),bn("GENERATE",1),t}async addFinalizedChunksToBundle(e,t,i,s,n){this.assignChunkIds(e,t,i,s);for(const t of e)s[t.id]=t.getChunkInfoWithFileNames();await Promise.all(e.map((async e=>{const t=s[e.id];Object.assign(t,await e.render(this.outputOptions,i,t,n))})))}async addManualChunks(e){const t=new Map,i=await Promise.all(Object.entries(e).map((async([e,t])=>({alias:e,entries:await this.graph.moduleLoader.addAdditionalModules(t)}))));for(const{alias:e,entries:s}of i)for(const i of s)ya(e,i,t);return t}assignChunkIds(e,t,i,s){const n=[],r=[];for(const t of e)(t.facadeModule&&t.facadeModule.isUserDefinedEntryPoint?n:r).push(t);const a=n.concat(r);for(const e of a)this.outputOptions.file?e.id=_(this.outputOptions.file):this.outputOptions.preserveModules?e.id=e.generateIdPreserveModules(t,this.outputOptions,s,this.unsetOptions):e.id=e.generateId(i,this.outputOptions,s,!0),s[e.id]=Zr}assignManualChunks(e){const t=[],i={getModuleIds:()=>this.graph.modulesById.keys(),getModuleInfo:this.graph.getModuleInfo};for(const s of this.graph.modulesById.values())if(s instanceof kn){const n=e(s.id,i);"string"==typeof n&&t.push([n,s])}t.sort((([e],[t])=>e>t?1:e<t?-1:0));const s=new Map;for(const[e,i]of t)ya(e,i,s);return s}finaliseAssets(e){for(const t of Object.values(e))if(t.type||(ke('A plugin is directly adding properties to the bundle object in the "generateBundle" hook. This is deprecated and will be removed in a future Rollup version, please use "this.emitFile" instead.',!0,this.inputOptions),t.type="asset"),this.outputOptions.validate&&"code"in t)try{this.graph.contextParse(t.code,{allowHashBang:!0,ecmaVersion:"latest"})}catch(e){this.inputOptions.onwarn(ye(t,e))}this.pluginDriver.finaliseAssets()}async generateChunks(){const{manualChunks:e}=this.outputOptions,t="object"==typeof e?await this.addManualChunks(e):this.assignManualChunks(e),i=[],s=new Map;for(const{alias:e,modules:n}of this.outputOptions.inlineDynamicImports?[{alias:null,modules:ga(this.graph.modulesById)}]:this.outputOptions.preserveModules?ga(this.graph.modulesById).map((e=>({alias:null,modules:[e]}))):ha(this.graph.entryModules,t)){n.sort(ua);const t=new na(n,this.inputOptions,this.outputOptions,this.unsetOptions,this.pluginDriver,this.graph.modulesById,s,this.facadeChunkByModule,this.includedNamespaces,e);i.push(t);for(const e of n)s.set(e,t)}for(const e of i)e.link();const n=[];for(const e of i)n.push(...e.generateFacades());return[...i,...n]}prerenderChunks(e,t,i){for(const t of e)t.generateExports();for(const s of e)s.preRender(this.outputOptions,t,i)}}function ga(e){return[...e.values()].filter((e=>e instanceof kn&&(e.isIncluded()||e.info.isEntry||e.includedDynamicImporters.length>0)))}function ya(e,t,i){const s=i.get(t);if("string"==typeof s&&s!==e)return fe((n=t.id,r=e,a=s,{code:ge.INVALID_CHUNK,message:`Cannot assign ${ce(n)} to the "${r}" chunk as it is already in the "${a}" chunk.`}));var n,r,a;i.set(t,e)}var xa=[509,0,227,0,150,4,294,9,1368,2,2,1,6,3,41,2,5,0,166,1,574,3,9,9,370,1,154,10,50,3,123,2,54,14,32,10,3,1,11,3,46,10,8,0,46,9,7,2,37,13,2,9,6,1,45,0,13,2,49,13,9,3,2,11,83,11,7,0,161,11,6,9,7,3,56,1,2,6,3,1,3,2,10,0,11,1,3,6,4,4,193,17,10,9,5,0,82,19,13,9,214,6,3,8,28,1,83,16,16,9,82,12,9,9,84,14,5,9,243,14,166,9,71,5,2,1,3,3,2,0,2,1,13,9,120,6,3,6,4,0,29,9,41,6,2,3,9,0,10,10,47,15,406,7,2,7,17,9,57,21,2,13,123,5,4,0,2,1,2,6,2,0,9,9,49,4,2,1,2,4,9,9,330,3,19306,9,87,9,39,4,60,6,26,9,1014,0,2,54,8,3,82,0,12,1,19628,1,4706,45,3,22,543,4,4,5,9,7,3,6,31,3,149,2,1418,49,513,54,5,49,9,0,15,0,23,4,2,14,1361,6,2,16,3,6,2,1,2,4,262,6,10,9,357,0,62,13,1495,6,110,6,6,9,4759,9,787719,239],Ea=[0,11,2,25,2,18,2,1,2,14,3,13,35,122,70,52,268,28,4,48,48,31,14,29,6,37,11,29,3,35,5,7,2,4,43,157,19,35,5,35,5,39,9,51,13,10,2,14,2,6,2,1,2,10,2,14,2,6,2,1,68,310,10,21,11,7,25,5,2,41,2,8,70,5,3,0,2,43,2,1,4,0,3,22,11,22,10,30,66,18,2,1,11,21,11,25,71,55,7,1,65,0,16,3,2,2,2,28,43,28,4,28,36,7,2,27,28,53,11,21,11,18,14,17,111,72,56,50,14,50,14,35,349,41,7,1,79,28,11,0,9,21,43,17,47,20,28,22,13,52,58,1,3,0,14,44,33,24,27,35,30,0,3,0,9,34,4,0,13,47,15,3,22,0,2,0,36,17,2,24,85,6,2,0,2,3,2,14,2,9,8,46,39,7,3,1,3,21,2,6,2,1,2,4,4,0,19,0,13,4,159,52,19,3,21,2,31,47,21,1,2,0,185,46,42,3,37,47,21,0,60,42,14,0,72,26,38,6,186,43,117,63,32,7,3,0,3,7,2,1,2,23,16,0,2,0,95,7,3,38,17,0,2,0,29,0,11,39,8,0,22,0,12,45,20,0,19,72,264,8,2,36,18,0,50,29,113,6,2,1,2,37,22,0,26,5,2,1,2,31,15,0,328,18,190,0,80,921,103,110,18,195,2637,96,16,1070,4050,582,8634,568,8,30,18,78,18,29,19,47,17,3,32,20,6,18,689,63,129,74,6,0,67,12,65,1,2,0,29,6135,9,1237,43,8,8936,3,2,6,2,1,2,290,46,2,18,3,9,395,2309,106,6,12,4,8,8,9,5991,84,2,70,2,1,3,0,3,1,3,3,2,11,2,0,2,6,2,64,2,3,3,7,2,6,2,27,2,3,2,4,2,0,4,6,2,339,3,24,2,24,2,30,2,24,2,30,2,24,2,30,2,24,2,30,2,24,2,7,1845,30,482,44,11,6,17,0,322,29,19,43,1269,6,2,3,2,1,2,14,2,196,60,67,8,0,1205,3,2,26,2,1,2,0,3,0,2,9,2,3,2,0,2,0,7,0,5,0,2,0,2,0,2,2,2,1,2,0,3,0,2,0,2,0,2,0,2,0,2,1,2,0,3,3,2,6,2,3,2,3,2,0,2,9,2,16,6,2,2,4,2,16,4421,42719,33,4152,8,221,3,5761,15,7472,3104,541,1507,4938],ba="ªµºÀ-ÖØ-öø-ˁˆ-ˑˠ-ˤˬˮͰ-ʹͶͷͺ-ͽͿΆΈ-ΊΌΎ-ΡΣ-ϵϷ-ҁҊ-ԯԱ-Ֆՙՠ-ֈא-תׯ-ײؠ-يٮٯٱ-ۓەۥۦۮۯۺ-ۼۿܐܒ-ܯݍ-ޥޱߊ-ߪߴߵߺࠀ-ࠕࠚࠤࠨࡀ-ࡘࡠ-ࡪࡰ-ࢇࢉ-ࢎࢠ-ࣉऄ-हऽॐक़-ॡॱ-ঀঅ-ঌএঐও-নপ-রলশ-হঽৎড়ঢ়য়-ৡৰৱৼਅ-ਊਏਐਓ-ਨਪ-ਰਲਲ਼ਵਸ਼ਸਹਖ਼-ੜਫ਼ੲ-ੴઅ-ઍએ-ઑઓ-નપ-રલળવ-હઽૐૠૡૹଅ-ଌଏଐଓ-ନପ-ରଲଳଵ-ହଽଡ଼ଢ଼ୟ-ୡୱஃஅ-ஊஎ-ஐஒ-கஙசஜஞடணதந-பம-ஹௐఅ-ఌఎ-ఐఒ-నప-హఽౘ-ౚౝౠౡಀಅ-ಌಎ-ಐಒ-ನಪ-ಳವ-ಹಽೝೞೠೡೱೲഄ-ഌഎ-ഐഒ-ഺഽൎൔ-ൖൟ-ൡൺ-ൿඅ-ඖක-නඳ-රලව-ෆก-ะาำเ-ๆກຂຄຆ-ຊຌ-ຣລວ-ະາຳຽເ-ໄໆໜ-ໟༀཀ-ཇཉ-ཬྈ-ྌက-ဪဿၐ-ၕၚ-ၝၡၥၦၮ-ၰၵ-ႁႎႠ-ჅჇჍა-ჺჼ-ቈቊ-ቍቐ-ቖቘቚ-ቝበ-ኈኊ-ኍነ-ኰኲ-ኵኸ-ኾዀዂ-ዅወ-ዖዘ-ጐጒ-ጕጘ-ፚᎀ-ᎏᎠ-Ᏽᏸ-ᏽᐁ-ᙬᙯ-ᙿᚁ-ᚚᚠ-ᛪᛮ-ᛸᜀ-ᜑᜟ-ᜱᝀ-ᝑᝠ-ᝬᝮ-ᝰក-ឳៗៜᠠ-ᡸᢀ-ᢨᢪᢰ-ᣵᤀ-ᤞᥐ-ᥭᥰ-ᥴᦀ-ᦫᦰ-ᧉᨀ-ᨖᨠ-ᩔᪧᬅ-ᬳᭅ-ᭌᮃ-ᮠᮮᮯᮺ-ᯥᰀ-ᰣᱍ-ᱏᱚ-ᱽᲀ-ᲈᲐ-ᲺᲽ-Ჿᳩ-ᳬᳮ-ᳳᳵᳶᳺᴀ-ᶿḀ-ἕἘ-Ἕἠ-ὅὈ-Ὅὐ-ὗὙὛὝὟ-ώᾀ-ᾴᾶ-ᾼιῂ-ῄῆ-ῌῐ-ΐῖ-Ίῠ-Ῥῲ-ῴῶ-ῼⁱⁿₐ-ₜℂℇℊ-ℓℕ℘-ℝℤΩℨK-ℹℼ-ℿⅅ-ⅉⅎⅠ-ↈⰀ-ⳤⳫ-ⳮⳲⳳⴀ-ⴥⴧⴭⴰ-ⵧⵯⶀ-ⶖⶠ-ⶦⶨ-ⶮⶰ-ⶶⶸ-ⶾⷀ-ⷆⷈ-ⷎⷐ-ⷖⷘ-ⷞ々-〇〡-〩〱-〵〸-〼ぁ-ゖ゛-ゟァ-ヺー-ヿㄅ-ㄯㄱ-ㆎㆠ-ㆿㇰ-ㇿ㐀-䶿一-ꒌꓐ-ꓽꔀ-ꘌꘐ-ꘟꘪꘫꙀ-ꙮꙿ-ꚝꚠ-ꛯꜗ-ꜟꜢ-ꞈꞋ-ꟊꟐꟑꟓꟕ-ꟙꟲ-ꠁꠃ-ꠅꠇ-ꠊꠌ-ꠢꡀ-ꡳꢂ-ꢳꣲ-ꣷꣻꣽꣾꤊ-ꤥꤰ-ꥆꥠ-ꥼꦄ-ꦲꧏꧠ-ꧤꧦ-ꧯꧺ-ꧾꨀ-ꨨꩀ-ꩂꩄ-ꩋꩠ-ꩶꩺꩾ-ꪯꪱꪵꪶꪹ-ꪽꫀꫂꫛ-ꫝꫠ-ꫪꫲ-ꫴꬁ-ꬆꬉ-ꬎꬑ-ꬖꬠ-ꬦꬨ-ꬮꬰ-ꭚꭜ-ꭩꭰ-ꯢ가-힣ힰ-ퟆퟋ-ퟻ豈-舘並-龎ﬀ-ﬆﬓ-ﬗיִײַ-ﬨשׁ-זּטּ-לּמּנּסּףּפּצּ-ﮱﯓ-ﴽﵐ-ﶏﶒ-ﷇﷰ-ﷻﹰ-ﹴﹶ-ﻼＡ-Ｚａ-ｚｦ-ﾾￂ-ￇￊ-ￏￒ-ￗￚ-ￜ",va={3:"abstract boolean byte char class double enum export extends final float goto implements import int interface long native package private protected public short static super synchronized throws transient volatile",5:"class enum extends super const export import",6:"enum",strict:"implements interface let package private protected public static yield",strictBind:"eval arguments"},Sa="break case catch continue debugger default do else finally for function if return switch throw try var while with null true false instanceof typeof void delete new in this",Aa={5:Sa,"5module":Sa+" export import",6:Sa+" const class extends export import super"},Ia=/^in(stanceof)?$/,Pa=new RegExp("["+ba+"]"),ka=new RegExp("["+ba+"‌‍·̀-ͯ·҃-֑҇-ׇֽֿׁׂׅׄؐ-ًؚ-٩ٰۖ-ۜ۟-۪ۤۧۨ-ۭ۰-۹ܑܰ-݊ަ-ް߀-߉߫-߽߳ࠖ-࠙ࠛ-ࠣࠥ-ࠧࠩ-࡙࠭-࡛࢘-࢟࣊-ࣣ࣡-ःऺ-़ा-ॏ॑-ॗॢॣ०-९ঁ-ঃ়া-ৄেৈো-্ৗৢৣ০-৯৾ਁ-ਃ਼ਾ-ੂੇੈੋ-੍ੑ੦-ੱੵઁ-ઃ઼ા-ૅે-ૉો-્ૢૣ૦-૯ૺ-૿ଁ-ଃ଼ା-ୄେୈୋ-୍୕-ୗୢୣ୦-୯ஂா-ூெ-ைொ-்ௗ௦-௯ఀ-ఄ఼ా-ౄె-ైొ-్ౕౖౢౣ౦-౯ಁ-ಃ಼ಾ-ೄೆ-ೈೊ-್ೕೖೢೣ೦-೯ഀ-ഃ഻഼ാ-ൄെ-ൈൊ-്ൗൢൣ൦-൯ඁ-ඃ්ා-ුූෘ-ෟ෦-෯ෲෳัิ-ฺ็-๎๐-๙ັິ-ຼ່-ໍ໐-໙༘༙༠-༩༹༵༷༾༿ཱ-྄྆྇ྍ-ྗྙ-ྼ࿆ါ-ှ၀-၉ၖ-ၙၞ-ၠၢ-ၤၧ-ၭၱ-ၴႂ-ႍႏ-ႝ፝-፟፩-፱ᜒ-᜕ᜲ-᜴ᝒᝓᝲᝳ឴-៓៝០-៩᠋-᠍᠏-᠙ᢩᤠ-ᤫᤰ-᤻᥆-᥏᧐-᧚ᨗ-ᨛᩕ-ᩞ᩠-᩿᩼-᪉᪐-᪙᪰-᪽ᪿ-ᫎᬀ-ᬄ᬴-᭄᭐-᭙᭫-᭳ᮀ-ᮂᮡ-ᮭ᮰-᮹᯦-᯳ᰤ-᰷᱀-᱉᱐-᱙᳐-᳔᳒-᳨᳭᳴᳷-᳹᷀-᷿‿⁀⁔⃐-⃥⃜⃡-⃰⳯-⵿⳱ⷠ-〪ⷿ-゙゚〯꘠-꘩꙯ꙴ-꙽ꚞꚟ꛰꛱ꠂ꠆ꠋꠣ-ꠧ꠬ꢀꢁꢴ-ꣅ꣐-꣙꣠-꣱ꣿ-꤉ꤦ-꤭ꥇ-꥓ꦀ-ꦃ꦳-꧀꧐-꧙ꧥ꧰-꧹ꨩ-ꨶꩃꩌꩍ꩐-꩙ꩻ-ꩽꪰꪲ-ꪴꪷꪸꪾ꪿꫁ꫫ-ꫯꫵ꫶ꯣ-ꯪ꯬꯭꯰-꯹ﬞ︀-️︠-︯︳︴﹍-﹏０-９＿]");function wa(e,t){for(var i=65536,s=0;s<t.length;s+=2){if((i+=t[s])>e)return!1;if((i+=t[s+1])>=e)return!0}}function Ca(e,t){return e<65?36===e:e<91||(e<97?95===e:e<123||(e<=65535?e>=170&&Pa.test(String.fromCharCode(e)):!1!==t&&wa(e,Ea)))}function Na(e,t){return e<48?36===e:e<58||!(e<65)&&(e<91||(e<97?95===e:e<123||(e<=65535?e>=170&&ka.test(String.fromCharCode(e)):!1!==t&&(wa(e,Ea)||wa(e,xa)))))}var _a=function(e,t){void 0===t&&(t={}),this.label=e,this.keyword=t.keyword,this.beforeExpr=!!t.beforeExpr,this.startsExpr=!!t.startsExpr,this.isLoop=!!t.isLoop,this.isAssign=!!t.isAssign,this.prefix=!!t.prefix,this.postfix=!!t.postfix,this.binop=t.binop||null,this.updateContext=null};function $a(e,t){return new _a(e,{beforeExpr:!0,binop:t})}var Ta={beforeExpr:!0},Oa={startsExpr:!0},Ra={};function Ma(e,t){return void 0===t&&(t={}),t.keyword=e,Ra[e]=new _a(e,t)}var Da={num:new _a("num",Oa),regexp:new _a("regexp",Oa),string:new _a("string",Oa),name:new _a("name",Oa),privateId:new _a("privateId",Oa),eof:new _a("eof"),bracketL:new _a("[",{beforeExpr:!0,startsExpr:!0}),bracketR:new _a("]"),braceL:new _a("{",{beforeExpr:!0,startsExpr:!0}),braceR:new _a("}"),parenL:new _a("(",{beforeExpr:!0,startsExpr:!0}),parenR:new _a(")"),comma:new _a(",",Ta),semi:new _a(";",Ta),colon:new _a(":",Ta),dot:new _a("."),question:new _a("?",Ta),questionDot:new _a("?."),arrow:new _a("=>",Ta),template:new _a("template"),invalidTemplate:new _a("invalidTemplate"),ellipsis:new _a("...",Ta),backQuote:new _a("`",Oa),dollarBraceL:new _a("${",{beforeExpr:!0,startsExpr:!0}),eq:new _a("=",{beforeExpr:!0,isAssign:!0}),assign:new _a("_=",{beforeExpr:!0,isAssign:!0}),incDec:new _a("++/--",{prefix:!0,postfix:!0,startsExpr:!0}),prefix:new _a("!/~",{beforeExpr:!0,prefix:!0,startsExpr:!0}),logicalOR:$a("||",1),logicalAND:$a("&&",2),bitwiseOR:$a("|",3),bitwiseXOR:$a("^",4),bitwiseAND:$a("&",5),equality:$a("==/!=/===/!==",6),relational:$a("</>/<=/>=",7),bitShift:$a("<</>>/>>>",8),plusMin:new _a("+/-",{beforeExpr:!0,binop:9,prefix:!0,startsExpr:!0}),modulo:$a("%",10),star:$a("*",10),slash:$a("/",10),starstar:new _a("**",{beforeExpr:!0}),coalesce:$a("??",1),_break:Ma("break"),_case:Ma("case",Ta),_catch:Ma("catch"),_continue:Ma("continue"),_debugger:Ma("debugger"),_default:Ma("default",Ta),_do:Ma("do",{isLoop:!0,beforeExpr:!0}),_else:Ma("else",Ta),_finally:Ma("finally"),_for:Ma("for",{isLoop:!0}),_function:Ma("function",Oa),_if:Ma("if"),_return:Ma("return",Ta),_switch:Ma("switch"),_throw:Ma("throw",Ta),_try:Ma("try"),_var:Ma("var"),_const:Ma("const"),_while:Ma("while",{isLoop:!0}),_with:Ma("with"),_new:Ma("new",{beforeExpr:!0,startsExpr:!0}),_this:Ma("this",Oa),_super:Ma("super",Oa),_class:Ma("class",Oa),_extends:Ma("extends",Ta),_export:Ma("export"),_import:Ma("import",Oa),_null:Ma("null",Oa),_true:Ma("true",Oa),_false:Ma("false",Oa),_in:Ma("in",{beforeExpr:!0,binop:7}),_instanceof:Ma("instanceof",{beforeExpr:!0,binop:7}),_typeof:Ma("typeof",{beforeExpr:!0,prefix:!0,startsExpr:!0}),_void:Ma("void",{beforeExpr:!0,prefix:!0,startsExpr:!0}),_delete:Ma("delete",{beforeExpr:!0,prefix:!0,startsExpr:!0})},La=/\r\n?|\n|\u2028|\u2029/,Va=new RegExp(La.source,"g");function Ba(e){return 10===e||13===e||8232===e||8233===e}function Fa(e,t,i){void 0===i&&(i=e.length);for(var s=t;s<i;s++){var n=e.charCodeAt(s);if(Ba(n))return s<i-1&&13===n&&10===e.charCodeAt(s+1)?s+2:s+1}return-1}var za=/[\u1680\u2000-\u200a\u202f\u205f\u3000\ufeff]/,ja=/(?:\s|\/\/.*|\/\*[^]*?\*\/)*/g,Ua=Object.prototype,Ga=Ua.hasOwnProperty,Ha=Ua.toString,Wa=Object.hasOwn||function(e,t){return Ga.call(e,t)},qa=Array.isArray||function(e){return"[object Array]"===Ha.call(e)};function Ka(e){return new RegExp("^(?:"+e.replace(/ /g,"|")+")$")}function Xa(e){return e<=65535?String.fromCharCode(e):(e-=65536,String.fromCharCode(55296+(e>>10),56320+(1023&e)))}var Ya=/(?:[\uD800-\uDBFF](?![\uDC00-\uDFFF])|(?:[^\uD800-\uDBFF]|^)[\uDC00-\uDFFF])/,Qa=function(e,t){this.line=e,this.column=t};Qa.prototype.offset=function(e){return new Qa(this.line,this.column+e)};var Ja=function(e,t,i){this.start=t,this.end=i,null!==e.sourceFile&&(this.source=e.sourceFile)};function Za(e,t){for(var i=1,s=0;;){var n=Fa(e,s,t);if(n<0)return new Qa(i,t-s);++i,s=n}}var eo={ecmaVersion:null,sourceType:"script",onInsertedSemicolon:null,onTrailingComma:null,allowReserved:null,allowReturnOutsideFunction:!1,allowImportExportEverywhere:!1,allowAwaitOutsideFunction:null,allowSuperOutsideMethod:null,allowHashBang:!1,locations:!1,onToken:null,onComment:null,ranges:!1,program:null,sourceFile:null,directSourceFile:null,preserveParens:!1},to=!1;function io(e){var t={};for(var i in eo)t[i]=e&&Wa(e,i)?e[i]:eo[i];if("latest"===t.ecmaVersion?t.ecmaVersion=1e8:null==t.ecmaVersion?(!to&&"object"==typeof console&&console.warn&&(to=!0,console.warn("Since Acorn 8.0.0, options.ecmaVersion is required.\nDefaulting to 2020, but this will stop working in the future.")),t.ecmaVersion=11):t.ecmaVersion>=2015&&(t.ecmaVersion-=2009),null==t.allowReserved&&(t.allowReserved=t.ecmaVersion<5),qa(t.onToken)){var s=t.onToken;t.onToken=function(e){return s.push(e)}}return qa(t.onComment)&&(t.onComment=function(e,t){return function(i,s,n,r,a,o){var l={type:i?"Block":"Line",value:s,start:n,end:r};e.locations&&(l.loc=new Ja(this,a,o)),e.ranges&&(l.range=[n,r]),t.push(l)}}(t,t.onComment)),t}var so=256;function no(e,t){return 2|(e?4:0)|(t?8:0)}var ro=function(e,t,i){this.options=e=io(e),this.sourceFile=e.sourceFile,this.keywords=Ka(Aa[e.ecmaVersion>=6?6:"module"===e.sourceType?"5module":5]);var s="";!0!==e.allowReserved&&(s=va[e.ecmaVersion>=6?6:5===e.ecmaVersion?5:3],"module"===e.sourceType&&(s+=" await")),this.reservedWords=Ka(s);var n=(s?s+" ":"")+va.strict;this.reservedWordsStrict=Ka(n),this.reservedWordsStrictBind=Ka(n+" "+va.strictBind),this.input=String(t),this.containsEsc=!1,i?(this.pos=i,this.lineStart=this.input.lastIndexOf("\n",i-1)+1,this.curLine=this.input.slice(0,this.lineStart).split(La).length):(this.pos=this.lineStart=0,this.curLine=1),this.type=Da.eof,this.value=null,this.start=this.end=this.pos,this.startLoc=this.endLoc=this.curPosition(),this.lastTokEndLoc=this.lastTokStartLoc=null,this.lastTokStart=this.lastTokEnd=this.pos,this.context=this.initialContext(),this.exprAllowed=!0,this.inModule="module"===e.sourceType,this.strict=this.inModule||this.strictDirective(this.pos),this.potentialArrowAt=-1,this.potentialArrowInForAwait=!1,this.yieldPos=this.awaitPos=this.awaitIdentPos=0,this.labels=[],this.undefinedExports=Object.create(null),0===this.pos&&e.allowHashBang&&"#!"===this.input.slice(0,2)&&this.skipLineComment(2),this.scopeStack=[],this.enterScope(1),this.regexpState=null,this.privateNameStack=[]},ao={inFunction:{configurable:!0},inGenerator:{configurable:!0},inAsync:{configurable:!0},canAwait:{configurable:!0},allowSuper:{configurable:!0},allowDirectSuper:{configurable:!0},treatFunctionsAsVar:{configurable:!0},allowNewDotTarget:{configurable:!0},inClassStaticBlock:{configurable:!0}};ro.prototype.parse=function(){var e=this.options.program||this.startNode();return this.nextToken(),this.parseTopLevel(e)},ao.inFunction.get=function(){return(2&this.currentVarScope().flags)>0},ao.inGenerator.get=function(){return(8&this.currentVarScope().flags)>0&&!this.currentVarScope().inClassFieldInit},ao.inAsync.get=function(){return(4&this.currentVarScope().flags)>0&&!this.currentVarScope().inClassFieldInit},ao.canAwait.get=function(){for(var e=this.scopeStack.length-1;e>=0;e--){var t=this.scopeStack[e];if(t.inClassFieldInit||t.flags&so)return!1;if(2&t.flags)return(4&t.flags)>0}return this.inModule&&this.options.ecmaVersion>=13||this.options.allowAwaitOutsideFunction},ao.allowSuper.get=function(){var e=this.currentThisScope(),t=e.flags,i=e.inClassFieldInit;return(64&t)>0||i||this.options.allowSuperOutsideMethod},ao.allowDirectSuper.get=function(){return(128&this.currentThisScope().flags)>0},ao.treatFunctionsAsVar.get=function(){return this.treatFunctionsAsVarInScope(this.currentScope())},ao.allowNewDotTarget.get=function(){var e=this.currentThisScope(),t=e.flags,i=e.inClassFieldInit;return(258&t)>0||i},ao.inClassStaticBlock.get=function(){return(this.currentVarScope().flags&so)>0},ro.extend=function(){for(var e=[],t=arguments.length;t--;)e[t]=arguments[t];for(var i=this,s=0;s<e.length;s++)i=e[s](i);return i},ro.parse=function(e,t){return new this(t,e).parse()},ro.parseExpressionAt=function(e,t,i){var s=new this(i,e,t);return s.nextToken(),s.parseExpression()},ro.tokenizer=function(e,t){return new this(t,e)},Object.defineProperties(ro.prototype,ao);var oo=ro.prototype,lo=/^(?:'((?:\\.|[^'\\])*?)'|"((?:\\.|[^"\\])*?)")/;oo.strictDirective=function(e){if(this.options.ecmaVersion<5)return!1;for(;;){ja.lastIndex=e,e+=ja.exec(this.input)[0].length;var t=lo.exec(this.input.slice(e));if(!t)return!1;if("use strict"===(t[1]||t[2])){ja.lastIndex=e+t[0].length;var i=ja.exec(this.input),s=i.index+i[0].length,n=this.input.charAt(s);return";"===n||"}"===n||La.test(i[0])&&!(/[(`.[+\-/*%<>=,?^&]/.test(n)||"!"===n&&"="===this.input.charAt(s+1))}e+=t[0].length,ja.lastIndex=e,e+=ja.exec(this.input)[0].length,";"===this.input[e]&&e++}},oo.eat=function(e){return this.type===e&&(this.next(),!0)},oo.isContextual=function(e){return this.type===Da.name&&this.value===e&&!this.containsEsc},oo.eatContextual=function(e){return!!this.isContextual(e)&&(this.next(),!0)},oo.expectContextual=function(e){this.eatContextual(e)||this.unexpected()},oo.canInsertSemicolon=function(){return this.type===Da.eof||this.type===Da.braceR||La.test(this.input.slice(this.lastTokEnd,this.start))},oo.insertSemicolon=function(){if(this.canInsertSemicolon())return this.options.onInsertedSemicolon&&this.options.onInsertedSemicolon(this.lastTokEnd,this.lastTokEndLoc),!0},oo.semicolon=function(){this.eat(Da.semi)||this.insertSemicolon()||this.unexpected()},oo.afterTrailingComma=function(e,t){if(this.type===e)return this.options.onTrailingComma&&this.options.onTrailingComma(this.lastTokStart,this.lastTokStartLoc),t||this.next(),!0},oo.expect=function(e){this.eat(e)||this.unexpected()},oo.unexpected=function(e){this.raise(null!=e?e:this.start,"Unexpected token")};var ho=function(){this.shorthandAssign=this.trailingComma=this.parenthesizedAssign=this.parenthesizedBind=this.doubleProto=-1};oo.checkPatternErrors=function(e,t){if(e){e.trailingComma>-1&&this.raiseRecoverable(e.trailingComma,"Comma is not permitted after the rest element");var i=t?e.parenthesizedAssign:e.parenthesizedBind;i>-1&&this.raiseRecoverable(i,"Parenthesized pattern")}},oo.checkExpressionErrors=function(e,t){if(!e)return!1;var i=e.shorthandAssign,s=e.doubleProto;if(!t)return i>=0||s>=0;i>=0&&this.raise(i,"Shorthand property assignments are valid only in destructuring patterns"),s>=0&&this.raiseRecoverable(s,"Redefinition of __proto__ property")},oo.checkYieldAwaitInDefaultParams=function(){this.yieldPos&&(!this.awaitPos||this.yieldPos<this.awaitPos)&&this.raise(this.yieldPos,"Yield expression cannot be a default value"),this.awaitPos&&this.raise(this.awaitPos,"Await expression cannot be a default value")},oo.isSimpleAssignTarget=function(e){return"ParenthesizedExpression"===e.type?this.isSimpleAssignTarget(e.expression):"Identifier"===e.type||"MemberExpression"===e.type};var co=ro.prototype;co.parseTopLevel=function(e){var t=Object.create(null);for(e.body||(e.body=[]);this.type!==Da.eof;){var i=this.parseStatement(null,!0,t);e.body.push(i)}if(this.inModule)for(var s=0,n=Object.keys(this.undefinedExports);s<n.length;s+=1){var r=n[s];this.raiseRecoverable(this.undefinedExports[r].start,"Export '"+r+"' is not defined")}return this.adaptDirectivePrologue(e.body),this.next(),e.sourceType=this.options.sourceType,this.finishNode(e,"Program")};var uo={kind:"loop"},po={kind:"switch"};co.isLet=function(e){if(this.options.ecmaVersion<6||!this.isContextual("let"))return!1;ja.lastIndex=this.pos;var t=ja.exec(this.input),i=this.pos+t[0].length,s=this.input.charCodeAt(i);if(91===s||92===s||s>55295&&s<56320)return!0;if(e)return!1;if(123===s)return!0;if(Ca(s,!0)){for(var n=i+1;Na(s=this.input.charCodeAt(n),!0);)++n;if(92===s||s>55295&&s<56320)return!0;var r=this.input.slice(i,n);if(!Ia.test(r))return!0}return!1},co.isAsyncFunction=function(){if(this.options.ecmaVersion<8||!this.isContextual("async"))return!1;ja.lastIndex=this.pos;var e,t=ja.exec(this.input),i=this.pos+t[0].length;return!(La.test(this.input.slice(this.pos,i))||"function"!==this.input.slice(i,i+8)||i+8!==this.input.length&&(Na(e=this.input.charCodeAt(i+8))||e>55295&&e<56320))},co.parseStatement=function(e,t,i){var s,n=this.type,r=this.startNode();switch(this.isLet(e)&&(n=Da._var,s="let"),n){case Da._break:case Da._continue:return this.parseBreakContinueStatement(r,n.keyword);case Da._debugger:return this.parseDebuggerStatement(r);case Da._do:return this.parseDoStatement(r);case Da._for:return this.parseForStatement(r);case Da._function:return e&&(this.strict||"if"!==e&&"label"!==e)&&this.options.ecmaVersion>=6&&this.unexpected(),this.parseFunctionStatement(r,!1,!e);case Da._class:return e&&this.unexpected(),this.parseClass(r,!0);case Da._if:return this.parseIfStatement(r);case Da._return:return this.parseReturnStatement(r);case Da._switch:return this.parseSwitchStatement(r);case Da._throw:return this.parseThrowStatement(r);case Da._try:return this.parseTryStatement(r);case Da._const:case Da._var:return s=s||this.value,e&&"var"!==s&&this.unexpected(),this.parseVarStatement(r,s);case Da._while:return this.parseWhileStatement(r);case Da._with:return this.parseWithStatement(r);case Da.braceL:return this.parseBlock(!0,r);case Da.semi:return this.parseEmptyStatement(r);case Da._export:case Da._import:if(this.options.ecmaVersion>10&&n===Da._import){ja.lastIndex=this.pos;var a=ja.exec(this.input),o=this.pos+a[0].length,l=this.input.charCodeAt(o);if(40===l||46===l)return this.parseExpressionStatement(r,this.parseExpression())}return this.options.allowImportExportEverywhere||(t||this.raise(this.start,"'import' and 'export' may only appear at the top level"),this.inModule||this.raise(this.start,"'import' and 'export' may appear only with 'sourceType: module'")),n===Da._import?this.parseImport(r):this.parseExport(r,i);default:if(this.isAsyncFunction())return e&&this.unexpected(),this.next(),this.parseFunctionStatement(r,!0,!e);var h=this.value,c=this.parseExpression();return n===Da.name&&"Identifier"===c.type&&this.eat(Da.colon)?this.parseLabeledStatement(r,h,c,e):this.parseExpressionStatement(r,c)}},co.parseBreakContinueStatement=function(e,t){var i="break"===t;this.next(),this.eat(Da.semi)||this.insertSemicolon()?e.label=null:this.type!==Da.name?this.unexpected():(e.label=this.parseIdent(),this.semicolon());for(var s=0;s<this.labels.length;++s){var n=this.labels[s];if(null==e.label||n.name===e.label.name){if(null!=n.kind&&(i||"loop"===n.kind))break;if(e.label&&i)break}}return s===this.labels.length&&this.raise(e.start,"Unsyntactic "+t),this.finishNode(e,i?"BreakStatement":"ContinueStatement")},co.parseDebuggerStatement=function(e){return this.next(),this.semicolon(),this.finishNode(e,"DebuggerStatement")},co.parseDoStatement=function(e){return this.next(),this.labels.push(uo),e.body=this.parseStatement("do"),this.labels.pop(),this.expect(Da._while),e.test=this.parseParenExpression(),this.options.ecmaVersion>=6?this.eat(Da.semi):this.semicolon(),this.finishNode(e,"DoWhileStatement")},co.parseForStatement=function(e){this.next();var t=this.options.ecmaVersion>=9&&this.canAwait&&this.eatContextual("await")?this.lastTokStart:-1;if(this.labels.push(uo),this.enterScope(0),this.expect(Da.parenL),this.type===Da.semi)return t>-1&&this.unexpected(t),this.parseFor(e,null);var i=this.isLet();if(this.type===Da._var||this.type===Da._const||i){var s=this.startNode(),n=i?"let":this.value;return this.next(),this.parseVar(s,!0,n),this.finishNode(s,"VariableDeclaration"),(this.type===Da._in||this.options.ecmaVersion>=6&&this.isContextual("of"))&&1===s.declarations.length?(this.options.ecmaVersion>=9&&(this.type===Da._in?t>-1&&this.unexpected(t):e.await=t>-1),this.parseForIn(e,s)):(t>-1&&this.unexpected(t),this.parseFor(e,s))}var r=this.isContextual("let"),a=!1,o=new ho,l=this.parseExpression(!(t>-1)||"await",o);return this.type===Da._in||(a=this.options.ecmaVersion>=6&&this.isContextual("of"))?(this.options.ecmaVersion>=9&&(this.type===Da._in?t>-1&&this.unexpected(t):e.await=t>-1),r&&a&&this.raise(l.start,"The left-hand side of a for-of loop may not start with 'let'."),this.toAssignable(l,!1,o),this.checkLValPattern(l),this.parseForIn(e,l)):(this.checkExpressionErrors(o,!0),t>-1&&this.unexpected(t),this.parseFor(e,l))},co.parseFunctionStatement=function(e,t,i){return this.next(),this.parseFunction(e,mo|(i?0:go),!1,t)},co.parseIfStatement=function(e){return this.next(),e.test=this.parseParenExpression(),e.consequent=this.parseStatement("if"),e.alternate=this.eat(Da._else)?this.parseStatement("if"):null,this.finishNode(e,"IfStatement")},co.parseReturnStatement=function(e){return this.inFunction||this.options.allowReturnOutsideFunction||this.raise(this.start,"'return' outside of function"),this.next(),this.eat(Da.semi)||this.insertSemicolon()?e.argument=null:(e.argument=this.parseExpression(),this.semicolon()),this.finishNode(e,"ReturnStatement")},co.parseSwitchStatement=function(e){var t;this.next(),e.discriminant=this.parseParenExpression(),e.cases=[],this.expect(Da.braceL),this.labels.push(po),this.enterScope(0);for(var i=!1;this.type!==Da.braceR;)if(this.type===Da._case||this.type===Da._default){var s=this.type===Da._case;t&&this.finishNode(t,"SwitchCase"),e.cases.push(t=this.startNode()),t.consequent=[],this.next(),s?t.test=this.parseExpression():(i&&this.raiseRecoverable(this.lastTokStart,"Multiple default clauses"),i=!0,t.test=null),this.expect(Da.colon)}else t||this.unexpected(),t.consequent.push(this.parseStatement(null));return this.exitScope(),t&&this.finishNode(t,"SwitchCase"),this.next(),this.labels.pop(),this.finishNode(e,"SwitchStatement")},co.parseThrowStatement=function(e){return this.next(),La.test(this.input.slice(this.lastTokEnd,this.start))&&this.raise(this.lastTokEnd,"Illegal newline after throw"),e.argument=this.parseExpression(),this.semicolon(),this.finishNode(e,"ThrowStatement")};var fo=[];co.parseTryStatement=function(e){if(this.next(),e.block=this.parseBlock(),e.handler=null,this.type===Da._catch){var t=this.startNode();if(this.next(),this.eat(Da.parenL)){t.param=this.parseBindingAtom();var i="Identifier"===t.param.type;this.enterScope(i?32:0),this.checkLValPattern(t.param,i?4:2),this.expect(Da.parenR)}else this.options.ecmaVersion<10&&this.unexpected(),t.param=null,this.enterScope(0);t.body=this.parseBlock(!1),this.exitScope(),e.handler=this.finishNode(t,"CatchClause")}return e.finalizer=this.eat(Da._finally)?this.parseBlock():null,e.handler||e.finalizer||this.raise(e.start,"Missing catch or finally clause"),this.finishNode(e,"TryStatement")},co.parseVarStatement=function(e,t){return this.next(),this.parseVar(e,!1,t),this.semicolon(),this.finishNode(e,"VariableDeclaration")},co.parseWhileStatement=function(e){return this.next(),e.test=this.parseParenExpression(),this.labels.push(uo),e.body=this.parseStatement("while"),this.labels.pop(),this.finishNode(e,"WhileStatement")},co.parseWithStatement=function(e){return this.strict&&this.raise(this.start,"'with' in strict mode"),this.next(),e.object=this.parseParenExpression(),e.body=this.parseStatement("with"),this.finishNode(e,"WithStatement")},co.parseEmptyStatement=function(e){return this.next(),this.finishNode(e,"EmptyStatement")},co.parseLabeledStatement=function(e,t,i,s){for(var n=0,r=this.labels;n<r.length;n+=1)r[n].name===t&&this.raise(i.start,"Label '"+t+"' is already declared");for(var a=this.type.isLoop?"loop":this.type===Da._switch?"switch":null,o=this.labels.length-1;o>=0;o--){var l=this.labels[o];if(l.statementStart!==e.start)break;l.statementStart=this.start,l.kind=a}return this.labels.push({name:t,kind:a,statementStart:this.start}),e.body=this.parseStatement(s?-1===s.indexOf("label")?s+"label":s:"label"),this.labels.pop(),e.label=i,this.finishNode(e,"LabeledStatement")},co.parseExpressionStatement=function(e,t){return e.expression=t,this.semicolon(),this.finishNode(e,"ExpressionStatement")},co.parseBlock=function(e,t,i){for(void 0===e&&(e=!0),void 0===t&&(t=this.startNode()),t.body=[],this.expect(Da.braceL),e&&this.enterScope(0);this.type!==Da.braceR;){var s=this.parseStatement(null);t.body.push(s)}return i&&(this.strict=!1),this.next(),e&&this.exitScope(),this.finishNode(t,"BlockStatement")},co.parseFor=function(e,t){return e.init=t,this.expect(Da.semi),e.test=this.type===Da.semi?null:this.parseExpression(),this.expect(Da.semi),e.update=this.type===Da.parenR?null:this.parseExpression(),this.expect(Da.parenR),e.body=this.parseStatement("for"),this.exitScope(),this.labels.pop(),this.finishNode(e,"ForStatement")},co.parseForIn=function(e,t){var i=this.type===Da._in;return this.next(),"VariableDeclaration"===t.type&&null!=t.declarations[0].init&&(!i||this.options.ecmaVersion<8||this.strict||"var"!==t.kind||"Identifier"!==t.declarations[0].id.type)&&this.raise(t.start,(i?"for-in":"for-of")+" loop variable declaration may not have an initializer"),e.left=t,e.right=i?this.parseExpression():this.parseMaybeAssign(),this.expect(Da.parenR),e.body=this.parseStatement("for"),this.exitScope(),this.labels.pop(),this.finishNode(e,i?"ForInStatement":"ForOfStatement")},co.parseVar=function(e,t,i){for(e.declarations=[],e.kind=i;;){var s=this.startNode();if(this.parseVarId(s,i),this.eat(Da.eq)?s.init=this.parseMaybeAssign(t):"const"!==i||this.type===Da._in||this.options.ecmaVersion>=6&&this.isContextual("of")?"Identifier"===s.id.type||t&&(this.type===Da._in||this.isContextual("of"))?s.init=null:this.raise(this.lastTokEnd,"Complex binding patterns require an initialization value"):this.unexpected(),e.declarations.push(this.finishNode(s,"VariableDeclarator")),!this.eat(Da.comma))break}return e},co.parseVarId=function(e,t){e.id=this.parseBindingAtom(),this.checkLValPattern(e.id,"var"===t?1:2,!1)};var mo=1,go=2;function yo(e,t){var i=t.key.name,s=e[i],n="true";return"MethodDefinition"!==t.type||"get"!==t.kind&&"set"!==t.kind||(n=(t.static?"s":"i")+t.kind),"iget"===s&&"iset"===n||"iset"===s&&"iget"===n||"sget"===s&&"sset"===n||"sset"===s&&"sget"===n?(e[i]="true",!1):!!s||(e[i]=n,!1)}function xo(e,t){var i=e.computed,s=e.key;return!i&&("Identifier"===s.type&&s.name===t||"Literal"===s.type&&s.value===t)}co.parseFunction=function(e,t,i,s,n){this.initFunction(e),(this.options.ecmaVersion>=9||this.options.ecmaVersion>=6&&!s)&&(this.type===Da.star&&t&go&&this.unexpected(),e.generator=this.eat(Da.star)),this.options.ecmaVersion>=8&&(e.async=!!s),t&mo&&(e.id=4&t&&this.type!==Da.name?null:this.parseIdent(),!e.id||t&go||this.checkLValSimple(e.id,this.strict||e.generator||e.async?this.treatFunctionsAsVar?1:2:3));var r=this.yieldPos,a=this.awaitPos,o=this.awaitIdentPos;return this.yieldPos=0,this.awaitPos=0,this.awaitIdentPos=0,this.enterScope(no(e.async,e.generator)),t&mo||(e.id=this.type===Da.name?this.parseIdent():null),this.parseFunctionParams(e),this.parseFunctionBody(e,i,!1,n),this.yieldPos=r,this.awaitPos=a,this.awaitIdentPos=o,this.finishNode(e,t&mo?"FunctionDeclaration":"FunctionExpression")},co.parseFunctionParams=function(e){this.expect(Da.parenL),e.params=this.parseBindingList(Da.parenR,!1,this.options.ecmaVersion>=8),this.checkYieldAwaitInDefaultParams()},co.parseClass=function(e,t){this.next();var i=this.strict;this.strict=!0,this.parseClassId(e,t),this.parseClassSuper(e);var s=this.enterClassBody(),n=this.startNode(),r=!1;for(n.body=[],this.expect(Da.braceL);this.type!==Da.braceR;){var a=this.parseClassElement(null!==e.superClass);a&&(n.body.push(a),"MethodDefinition"===a.type&&"constructor"===a.kind?(r&&this.raise(a.start,"Duplicate constructor in the same class"),r=!0):a.key&&"PrivateIdentifier"===a.key.type&&yo(s,a)&&this.raiseRecoverable(a.key.start,"Identifier '#"+a.key.name+"' has already been declared"))}return this.strict=i,this.next(),e.body=this.finishNode(n,"ClassBody"),this.exitClassBody(),this.finishNode(e,t?"ClassDeclaration":"ClassExpression")},co.parseClassElement=function(e){if(this.eat(Da.semi))return null;var t=this.options.ecmaVersion,i=this.startNode(),s="",n=!1,r=!1,a="method",o=!1;if(this.eatContextual("static")){if(t>=13&&this.eat(Da.braceL))return this.parseClassStaticBlock(i),i;this.isClassElementNameStart()||this.type===Da.star?o=!0:s="static"}if(i.static=o,!s&&t>=8&&this.eatContextual("async")&&(!this.isClassElementNameStart()&&this.type!==Da.star||this.canInsertSemicolon()?s="async":r=!0),!s&&(t>=9||!r)&&this.eat(Da.star)&&(n=!0),!s&&!r&&!n){var l=this.value;(this.eatContextual("get")||this.eatContextual("set"))&&(this.isClassElementNameStart()?a=l:s=l)}if(s?(i.computed=!1,i.key=this.startNodeAt(this.lastTokStart,this.lastTokStartLoc),i.key.name=s,this.finishNode(i.key,"Identifier")):this.parseClassElementName(i),t<13||this.type===Da.parenL||"method"!==a||n||r){var h=!i.static&&xo(i,"constructor"),c=h&&e;h&&"method"!==a&&this.raise(i.key.start,"Constructor can't have get/set modifier"),i.kind=h?"constructor":a,this.parseClassMethod(i,n,r,c)}else this.parseClassField(i);return i},co.isClassElementNameStart=function(){return this.type===Da.name||this.type===Da.privateId||this.type===Da.num||this.type===Da.string||this.type===Da.bracketL||this.type.keyword},co.parseClassElementName=function(e){this.type===Da.privateId?("constructor"===this.value&&this.raise(this.start,"Classes can't have an element named '#constructor'"),e.computed=!1,e.key=this.parsePrivateIdent()):this.parsePropertyName(e)},co.parseClassMethod=function(e,t,i,s){var n=e.key;"constructor"===e.kind?(t&&this.raise(n.start,"Constructor can't be a generator"),i&&this.raise(n.start,"Constructor can't be an async method")):e.static&&xo(e,"prototype")&&this.raise(n.start,"Classes may not have a static property named prototype");var r=e.value=this.parseMethod(t,i,s);return"get"===e.kind&&0!==r.params.length&&this.raiseRecoverable(r.start,"getter should have no params"),"set"===e.kind&&1!==r.params.length&&this.raiseRecoverable(r.start,"setter should have exactly one param"),"set"===e.kind&&"RestElement"===r.params[0].type&&this.raiseRecoverable(r.params[0].start,"Setter cannot use rest params"),this.finishNode(e,"MethodDefinition")},co.parseClassField=function(e){if(xo(e,"constructor")?this.raise(e.key.start,"Classes can't have a field named 'constructor'"):e.static&&xo(e,"prototype")&&this.raise(e.key.start,"Classes can't have a static field named 'prototype'"),this.eat(Da.eq)){var t=this.currentThisScope(),i=t.inClassFieldInit;t.inClassFieldInit=!0,e.value=this.parseMaybeAssign(),t.inClassFieldInit=i}else e.value=null;return this.semicolon(),this.finishNode(e,"PropertyDefinition")},co.parseClassStaticBlock=function(e){e.body=[];var t=this.labels;for(this.labels=[],this.enterScope(320);this.type!==Da.braceR;){var i=this.parseStatement(null);e.body.push(i)}return this.next(),this.exitScope(),this.labels=t,this.finishNode(e,"StaticBlock")},co.parseClassId=function(e,t){this.type===Da.name?(e.id=this.parseIdent(),t&&this.checkLValSimple(e.id,2,!1)):(!0===t&&this.unexpected(),e.id=null)},co.parseClassSuper=function(e){e.superClass=this.eat(Da._extends)?this.parseExprSubscripts(!1):null},co.enterClassBody=function(){var e={declared:Object.create(null),used:[]};return this.privateNameStack.push(e),e.declared},co.exitClassBody=function(){for(var e=this.privateNameStack.pop(),t=e.declared,i=e.used,s=this.privateNameStack.length,n=0===s?null:this.privateNameStack[s-1],r=0;r<i.length;++r){var a=i[r];Wa(t,a.name)||(n?n.used.push(a):this.raiseRecoverable(a.start,"Private field '#"+a.name+"' must be declared in an enclosing class"))}},co.parseExport=function(e,t){if(this.next(),this.eat(Da.star))return this.options.ecmaVersion>=11&&(this.eatContextual("as")?(e.exported=this.parseModuleExportName(),this.checkExport(t,e.exported,this.lastTokStart)):e.exported=null),this.expectContextual("from"),this.type!==Da.string&&this.unexpected(),e.source=this.parseExprAtom(),this.semicolon(),this.finishNode(e,"ExportAllDeclaration");if(this.eat(Da._default)){var i;if(this.checkExport(t,"default",this.lastTokStart),this.type===Da._function||(i=this.isAsyncFunction())){var s=this.startNode();this.next(),i&&this.next(),e.declaration=this.parseFunction(s,4|mo,!1,i)}else if(this.type===Da._class){var n=this.startNode();e.declaration=this.parseClass(n,"nullableID")}else e.declaration=this.parseMaybeAssign(),this.semicolon();return this.finishNode(e,"ExportDefaultDeclaration")}if(this.shouldParseExportStatement())e.declaration=this.parseStatement(null),"VariableDeclaration"===e.declaration.type?this.checkVariableExport(t,e.declaration.declarations):this.checkExport(t,e.declaration.id,e.declaration.id.start),e.specifiers=[],e.source=null;else{if(e.declaration=null,e.specifiers=this.parseExportSpecifiers(t),this.eatContextual("from"))this.type!==Da.string&&this.unexpected(),e.source=this.parseExprAtom();else{for(var r=0,a=e.specifiers;r<a.length;r+=1){var o=a[r];this.checkUnreserved(o.local),this.checkLocalExport(o.local),"Literal"===o.local.type&&this.raise(o.local.start,"A string literal cannot be used as an exported binding without `from`.")}e.source=null}this.semicolon()}return this.finishNode(e,"ExportNamedDeclaration")},co.checkExport=function(e,t,i){e&&("string"!=typeof t&&(t="Identifier"===t.type?t.name:t.value),Wa(e,t)&&this.raiseRecoverable(i,"Duplicate export '"+t+"'"),e[t]=!0)},co.checkPatternExport=function(e,t){var i=t.type;if("Identifier"===i)this.checkExport(e,t,t.start);else if("ObjectPattern"===i)for(var s=0,n=t.properties;s<n.length;s+=1){var r=n[s];this.checkPatternExport(e,r)}else if("ArrayPattern"===i)for(var a=0,o=t.elements;a<o.length;a+=1){var l=o[a];l&&this.checkPatternExport(e,l)}else"Property"===i?this.checkPatternExport(e,t.value):"AssignmentPattern"===i?this.checkPatternExport(e,t.left):"RestElement"===i?this.checkPatternExport(e,t.argument):"ParenthesizedExpression"===i&&this.checkPatternExport(e,t.expression)},co.checkVariableExport=function(e,t){if(e)for(var i=0,s=t;i<s.length;i+=1){var n=s[i];this.checkPatternExport(e,n.id)}},co.shouldParseExportStatement=function(){return"var"===this.type.keyword||"const"===this.type.keyword||"class"===this.type.keyword||"function"===this.type.keyword||this.isLet()||this.isAsyncFunction()},co.parseExportSpecifiers=function(e){var t=[],i=!0;for(this.expect(Da.braceL);!this.eat(Da.braceR);){if(i)i=!1;else if(this.expect(Da.comma),this.afterTrailingComma(Da.braceR))break;var s=this.startNode();s.local=this.parseModuleExportName(),s.exported=this.eatContextual("as")?this.parseModuleExportName():s.local,this.checkExport(e,s.exported,s.exported.start),t.push(this.finishNode(s,"ExportSpecifier"))}return t},co.parseImport=function(e){return this.next(),this.type===Da.string?(e.specifiers=fo,e.source=this.parseExprAtom()):(e.specifiers=this.parseImportSpecifiers(),this.expectContextual("from"),e.source=this.type===Da.string?this.parseExprAtom():this.unexpected()),this.semicolon(),this.finishNode(e,"ImportDeclaration")},co.parseImportSpecifiers=function(){var e=[],t=!0;if(this.type===Da.name){var i=this.startNode();if(i.local=this.parseIdent(),this.checkLValSimple(i.local,2),e.push(this.finishNode(i,"ImportDefaultSpecifier")),!this.eat(Da.comma))return e}if(this.type===Da.star){var s=this.startNode();return this.next(),this.expectContextual("as"),s.local=this.parseIdent(),this.checkLValSimple(s.local,2),e.push(this.finishNode(s,"ImportNamespaceSpecifier")),e}for(this.expect(Da.braceL);!this.eat(Da.braceR);){if(t)t=!1;else if(this.expect(Da.comma),this.afterTrailingComma(Da.braceR))break;var n=this.startNode();n.imported=this.parseModuleExportName(),this.eatContextual("as")?n.local=this.parseIdent():(this.checkUnreserved(n.imported),n.local=n.imported),this.checkLValSimple(n.local,2),e.push(this.finishNode(n,"ImportSpecifier"))}return e},co.parseModuleExportName=function(){if(this.options.ecmaVersion>=13&&this.type===Da.string){var e=this.parseLiteral(this.value);return Ya.test(e.value)&&this.raise(e.start,"An export name cannot include a lone surrogate."),e}return this.parseIdent(!0)},co.adaptDirectivePrologue=function(e){for(var t=0;t<e.length&&this.isDirectiveCandidate(e[t]);++t)e[t].directive=e[t].expression.raw.slice(1,-1)},co.isDirectiveCandidate=function(e){return"ExpressionStatement"===e.type&&"Literal"===e.expression.type&&"string"==typeof e.expression.value&&('"'===this.input[e.start]||"'"===this.input[e.start])};var Eo=ro.prototype;Eo.toAssignable=function(e,t,i){if(this.options.ecmaVersion>=6&&e)switch(e.type){case"Identifier":this.inAsync&&"await"===e.name&&this.raise(e.start,"Cannot use 'await' as identifier inside an async function");break;case"ObjectPattern":case"ArrayPattern":case"AssignmentPattern":case"RestElement":break;case"ObjectExpression":e.type="ObjectPattern",i&&this.checkPatternErrors(i,!0);for(var s=0,n=e.properties;s<n.length;s+=1){var r=n[s];this.toAssignable(r,t),"RestElement"!==r.type||"ArrayPattern"!==r.argument.type&&"ObjectPattern"!==r.argument.type||this.raise(r.argument.start,"Unexpected token")}break;case"Property":"init"!==e.kind&&this.raise(e.key.start,"Object pattern can't contain getter or setter"),this.toAssignable(e.value,t);break;case"ArrayExpression":e.type="ArrayPattern",i&&this.checkPatternErrors(i,!0),this.toAssignableList(e.elements,t);break;case"SpreadElement":e.type="RestElement",this.toAssignable(e.argument,t),"AssignmentPattern"===e.argument.type&&this.raise(e.argument.start,"Rest elements cannot have a default value");break;case"AssignmentExpression":"="!==e.operator&&this.raise(e.left.end,"Only '=' operator can be used for specifying default value."),e.type="AssignmentPattern",delete e.operator,this.toAssignable(e.left,t);break;case"ParenthesizedExpression":this.toAssignable(e.expression,t,i);break;case"ChainExpression":this.raiseRecoverable(e.start,"Optional chaining cannot appear in left-hand side");break;case"MemberExpression":if(!t)break;default:this.raise(e.start,"Assigning to rvalue")}else i&&this.checkPatternErrors(i,!0);return e},Eo.toAssignableList=function(e,t){for(var i=e.length,s=0;s<i;s++){var n=e[s];n&&this.toAssignable(n,t)}if(i){var r=e[i-1];6===this.options.ecmaVersion&&t&&r&&"RestElement"===r.type&&"Identifier"!==r.argument.type&&this.unexpected(r.argument.start)}return e},Eo.parseSpread=function(e){var t=this.startNode();return this.next(),t.argument=this.parseMaybeAssign(!1,e),this.finishNode(t,"SpreadElement")},Eo.parseRestBinding=function(){var e=this.startNode();return this.next(),6===this.options.ecmaVersion&&this.type!==Da.name&&this.unexpected(),e.argument=this.parseBindingAtom(),this.finishNode(e,"RestElement")},Eo.parseBindingAtom=function(){if(this.options.ecmaVersion>=6)switch(this.type){case Da.bracketL:var e=this.startNode();return this.next(),e.elements=this.parseBindingList(Da.bracketR,!0,!0),this.finishNode(e,"ArrayPattern");case Da.braceL:return this.parseObj(!0)}return this.parseIdent()},Eo.parseBindingList=function(e,t,i){for(var s=[],n=!0;!this.eat(e);)if(n?n=!1:this.expect(Da.comma),t&&this.type===Da.comma)s.push(null);else{if(i&&this.afterTrailingComma(e))break;if(this.type===Da.ellipsis){var r=this.parseRestBinding();this.parseBindingListItem(r),s.push(r),this.type===Da.comma&&this.raise(this.start,"Comma is not permitted after the rest element"),this.expect(e);break}var a=this.parseMaybeDefault(this.start,this.startLoc);this.parseBindingListItem(a),s.push(a)}return s},Eo.parseBindingListItem=function(e){return e},Eo.parseMaybeDefault=function(e,t,i){if(i=i||this.parseBindingAtom(),this.options.ecmaVersion<6||!this.eat(Da.eq))return i;var s=this.startNodeAt(e,t);return s.left=i,s.right=this.parseMaybeAssign(),this.finishNode(s,"AssignmentPattern")},Eo.checkLValSimple=function(e,t,i){void 0===t&&(t=0);var s=0!==t;switch(e.type){case"Identifier":this.strict&&this.reservedWordsStrictBind.test(e.name)&&this.raiseRecoverable(e.start,(s?"Binding ":"Assigning to ")+e.name+" in strict mode"),s&&(2===t&&"let"===e.name&&this.raiseRecoverable(e.start,"let is disallowed as a lexically bound name"),i&&(Wa(i,e.name)&&this.raiseRecoverable(e.start,"Argument name clash"),i[e.name]=!0),5!==t&&this.declareName(e.name,t,e.start));break;case"ChainExpression":this.raiseRecoverable(e.start,"Optional chaining cannot appear in left-hand side");break;case"MemberExpression":s&&this.raiseRecoverable(e.start,"Binding member expression");break;case"ParenthesizedExpression":return s&&this.raiseRecoverable(e.start,"Binding parenthesized expression"),this.checkLValSimple(e.expression,t,i);default:this.raise(e.start,(s?"Binding":"Assigning to")+" rvalue")}},Eo.checkLValPattern=function(e,t,i){switch(void 0===t&&(t=0),e.type){case"ObjectPattern":for(var s=0,n=e.properties;s<n.length;s+=1){var r=n[s];this.checkLValInnerPattern(r,t,i)}break;case"ArrayPattern":for(var a=0,o=e.elements;a<o.length;a+=1){var l=o[a];l&&this.checkLValInnerPattern(l,t,i)}break;default:this.checkLValSimple(e,t,i)}},Eo.checkLValInnerPattern=function(e,t,i){switch(void 0===t&&(t=0),e.type){case"Property":this.checkLValInnerPattern(e.value,t,i);break;case"AssignmentPattern":this.checkLValPattern(e.left,t,i);break;case"RestElement":this.checkLValPattern(e.argument,t,i);break;default:this.checkLValPattern(e,t,i)}};var bo=function(e,t,i,s,n){this.token=e,this.isExpr=!!t,this.preserveSpace=!!i,this.override=s,this.generator=!!n},vo={b_stat:new bo("{",!1),b_expr:new bo("{",!0),b_tmpl:new bo("${",!1),p_stat:new bo("(",!1),p_expr:new bo("(",!0),q_tmpl:new bo("`",!0,!0,(function(e){return e.tryReadTemplateToken()})),f_stat:new bo("function",!1),f_expr:new bo("function",!0),f_expr_gen:new bo("function",!0,!1,null,!0),f_gen:new bo("function",!1,!1,null,!0)},So=ro.prototype;So.initialContext=function(){return[vo.b_stat]},So.curContext=function(){return this.context[this.context.length-1]},So.braceIsBlock=function(e){var t=this.curContext();return t===vo.f_expr||t===vo.f_stat||(e!==Da.colon||t!==vo.b_stat&&t!==vo.b_expr?e===Da._return||e===Da.name&&this.exprAllowed?La.test(this.input.slice(this.lastTokEnd,this.start)):e===Da._else||e===Da.semi||e===Da.eof||e===Da.parenR||e===Da.arrow||(e===Da.braceL?t===vo.b_stat:e!==Da._var&&e!==Da._const&&e!==Da.name&&!this.exprAllowed):!t.isExpr)},So.inGeneratorContext=function(){for(var e=this.context.length-1;e>=1;e--){var t=this.context[e];if("function"===t.token)return t.generator}return!1},So.updateContext=function(e){var t,i=this.type;i.keyword&&e===Da.dot?this.exprAllowed=!1:(t=i.updateContext)?t.call(this,e):this.exprAllowed=i.beforeExpr},So.overrideContext=function(e){this.curContext()!==e&&(this.context[this.context.length-1]=e)},Da.parenR.updateContext=Da.braceR.updateContext=function(){if(1!==this.context.length){var e=this.context.pop();e===vo.b_stat&&"function"===this.curContext().token&&(e=this.context.pop()),this.exprAllowed=!e.isExpr}else this.exprAllowed=!0},Da.braceL.updateContext=function(e){this.context.push(this.braceIsBlock(e)?vo.b_stat:vo.b_expr),this.exprAllowed=!0},Da.dollarBraceL.updateContext=function(){this.context.push(vo.b_tmpl),this.exprAllowed=!0},Da.parenL.updateContext=function(e){var t=e===Da._if||e===Da._for||e===Da._with||e===Da._while;this.context.push(t?vo.p_stat:vo.p_expr),this.exprAllowed=!0},Da.incDec.updateContext=function(){},Da._function.updateContext=Da._class.updateContext=function(e){!e.beforeExpr||e===Da._else||e===Da.semi&&this.curContext()!==vo.p_stat||e===Da._return&&La.test(this.input.slice(this.lastTokEnd,this.start))||(e===Da.colon||e===Da.braceL)&&this.curContext()===vo.b_stat?this.context.push(vo.f_stat):this.context.push(vo.f_expr),this.exprAllowed=!1},Da.backQuote.updateContext=function(){this.curContext()===vo.q_tmpl?this.context.pop():this.context.push(vo.q_tmpl),this.exprAllowed=!1},Da.star.updateContext=function(e){if(e===Da._function){var t=this.context.length-1;this.context[t]===vo.f_expr?this.context[t]=vo.f_expr_gen:this.context[t]=vo.f_gen}this.exprAllowed=!0},Da.name.updateContext=function(e){var t=!1;this.options.ecmaVersion>=6&&e!==Da.dot&&("of"===this.value&&!this.exprAllowed||"yield"===this.value&&this.inGeneratorContext())&&(t=!0),this.exprAllowed=t};var Ao=ro.prototype;function Io(e){return"MemberExpression"===e.type&&"PrivateIdentifier"===e.property.type||"ChainExpression"===e.type&&Io(e.expression)}Ao.checkPropClash=function(e,t,i){if(!(this.options.ecmaVersion>=9&&"SpreadElement"===e.type||this.options.ecmaVersion>=6&&(e.computed||e.method||e.shorthand))){var s,n=e.key;switch(n.type){case"Identifier":s=n.name;break;case"Literal":s=String(n.value);break;default:return}var r=e.kind;if(this.options.ecmaVersion>=6)"__proto__"===s&&"init"===r&&(t.proto&&(i?i.doubleProto<0&&(i.doubleProto=n.start):this.raiseRecoverable(n.start,"Redefinition of __proto__ property")),t.proto=!0);else{var a=t[s="$"+s];a?("init"===r?this.strict&&a.init||a.get||a.set:a.init||a[r])&&this.raiseRecoverable(n.start,"Redefinition of property"):a=t[s]={init:!1,get:!1,set:!1},a[r]=!0}}},Ao.parseExpression=function(e,t){var i=this.start,s=this.startLoc,n=this.parseMaybeAssign(e,t);if(this.type===Da.comma){var r=this.startNodeAt(i,s);for(r.expressions=[n];this.eat(Da.comma);)r.expressions.push(this.parseMaybeAssign(e,t));return this.finishNode(r,"SequenceExpression")}return n},Ao.parseMaybeAssign=function(e,t,i){if(this.isContextual("yield")){if(this.inGenerator)return this.parseYield(e);this.exprAllowed=!1}var s=!1,n=-1,r=-1,a=-1;t?(n=t.parenthesizedAssign,r=t.trailingComma,a=t.doubleProto,t.parenthesizedAssign=t.trailingComma=-1):(t=new ho,s=!0);var o=this.start,l=this.startLoc;this.type!==Da.parenL&&this.type!==Da.name||(this.potentialArrowAt=this.start,this.potentialArrowInForAwait="await"===e);var h=this.parseMaybeConditional(e,t);if(i&&(h=i.call(this,h,o,l)),this.type.isAssign){var c=this.startNodeAt(o,l);return c.operator=this.value,this.type===Da.eq&&(h=this.toAssignable(h,!1,t)),s||(t.parenthesizedAssign=t.trailingComma=t.doubleProto=-1),t.shorthandAssign>=h.start&&(t.shorthandAssign=-1),this.type===Da.eq?this.checkLValPattern(h):this.checkLValSimple(h),c.left=h,this.next(),c.right=this.parseMaybeAssign(e),a>-1&&(t.doubleProto=a),this.finishNode(c,"AssignmentExpression")}return s&&this.checkExpressionErrors(t,!0),n>-1&&(t.parenthesizedAssign=n),r>-1&&(t.trailingComma=r),h},Ao.parseMaybeConditional=function(e,t){var i=this.start,s=this.startLoc,n=this.parseExprOps(e,t);if(this.checkExpressionErrors(t))return n;if(this.eat(Da.question)){var r=this.startNodeAt(i,s);return r.test=n,r.consequent=this.parseMaybeAssign(),this.expect(Da.colon),r.alternate=this.parseMaybeAssign(e),this.finishNode(r,"ConditionalExpression")}return n},Ao.parseExprOps=function(e,t){var i=this.start,s=this.startLoc,n=this.parseMaybeUnary(t,!1,!1,e);return this.checkExpressionErrors(t)||n.start===i&&"ArrowFunctionExpression"===n.type?n:this.parseExprOp(n,i,s,-1,e)},Ao.parseExprOp=function(e,t,i,s,n){var r=this.type.binop;if(null!=r&&(!n||this.type!==Da._in)&&r>s){var a=this.type===Da.logicalOR||this.type===Da.logicalAND,o=this.type===Da.coalesce;o&&(r=Da.logicalAND.binop);var l=this.value;this.next();var h=this.start,c=this.startLoc,u=this.parseExprOp(this.parseMaybeUnary(null,!1,!1,n),h,c,r,n),d=this.buildBinary(t,i,e,u,l,a||o);return(a&&this.type===Da.coalesce||o&&(this.type===Da.logicalOR||this.type===Da.logicalAND))&&this.raiseRecoverable(this.start,"Logical expressions and coalesce expressions cannot be mixed. Wrap either by parentheses"),this.parseExprOp(d,t,i,s,n)}return e},Ao.buildBinary=function(e,t,i,s,n,r){"PrivateIdentifier"===s.type&&this.raise(s.start,"Private identifier can only be left side of binary expression");var a=this.startNodeAt(e,t);return a.left=i,a.operator=n,a.right=s,this.finishNode(a,r?"LogicalExpression":"BinaryExpression")},Ao.parseMaybeUnary=function(e,t,i,s){var n,r=this.start,a=this.startLoc;if(this.isContextual("await")&&this.canAwait)n=this.parseAwait(s),t=!0;else if(this.type.prefix){var o=this.startNode(),l=this.type===Da.incDec;o.operator=this.value,o.prefix=!0,this.next(),o.argument=this.parseMaybeUnary(null,!0,l,s),this.checkExpressionErrors(e,!0),l?this.checkLValSimple(o.argument):this.strict&&"delete"===o.operator&&"Identifier"===o.argument.type?this.raiseRecoverable(o.start,"Deleting local variable in strict mode"):"delete"===o.operator&&Io(o.argument)?this.raiseRecoverable(o.start,"Private fields can not be deleted"):t=!0,n=this.finishNode(o,l?"UpdateExpression":"UnaryExpression")}else if(t||this.type!==Da.privateId){if(n=this.parseExprSubscripts(e,s),this.checkExpressionErrors(e))return n;for(;this.type.postfix&&!this.canInsertSemicolon();){var h=this.startNodeAt(r,a);h.operator=this.value,h.prefix=!1,h.argument=n,this.checkLValSimple(n),this.next(),n=this.finishNode(h,"UpdateExpression")}}else(s||0===this.privateNameStack.length)&&this.unexpected(),n=this.parsePrivateIdent(),this.type!==Da._in&&this.unexpected();return i||!this.eat(Da.starstar)?n:t?void this.unexpected(this.lastTokStart):this.buildBinary(r,a,n,this.parseMaybeUnary(null,!1,!1,s),"**",!1)},Ao.parseExprSubscripts=function(e,t){var i=this.start,s=this.startLoc,n=this.parseExprAtom(e,t);if("ArrowFunctionExpression"===n.type&&")"!==this.input.slice(this.lastTokStart,this.lastTokEnd))return n;var r=this.parseSubscripts(n,i,s,!1,t);return e&&"MemberExpression"===r.type&&(e.parenthesizedAssign>=r.start&&(e.parenthesizedAssign=-1),e.parenthesizedBind>=r.start&&(e.parenthesizedBind=-1),e.trailingComma>=r.start&&(e.trailingComma=-1)),r},Ao.parseSubscripts=function(e,t,i,s,n){for(var r=this.options.ecmaVersion>=8&&"Identifier"===e.type&&"async"===e.name&&this.lastTokEnd===e.end&&!this.canInsertSemicolon()&&e.end-e.start==5&&this.potentialArrowAt===e.start,a=!1;;){var o=this.parseSubscript(e,t,i,s,r,a,n);if(o.optional&&(a=!0),o===e||"ArrowFunctionExpression"===o.type){if(a){var l=this.startNodeAt(t,i);l.expression=o,o=this.finishNode(l,"ChainExpression")}return o}e=o}},Ao.parseSubscript=function(e,t,i,s,n,r,a){var o=this.options.ecmaVersion>=11,l=o&&this.eat(Da.questionDot);s&&l&&this.raise(this.lastTokStart,"Optional chaining cannot appear in the callee of new expressions");var h=this.eat(Da.bracketL);if(h||l&&this.type!==Da.parenL&&this.type!==Da.backQuote||this.eat(Da.dot)){var c=this.startNodeAt(t,i);c.object=e,h?(c.property=this.parseExpression(),this.expect(Da.bracketR)):this.type===Da.privateId&&"Super"!==e.type?c.property=this.parsePrivateIdent():c.property=this.parseIdent("never"!==this.options.allowReserved),c.computed=!!h,o&&(c.optional=l),e=this.finishNode(c,"MemberExpression")}else if(!s&&this.eat(Da.parenL)){var u=new ho,d=this.yieldPos,p=this.awaitPos,f=this.awaitIdentPos;this.yieldPos=0,this.awaitPos=0,this.awaitIdentPos=0;var m=this.parseExprList(Da.parenR,this.options.ecmaVersion>=8,!1,u);if(n&&!l&&!this.canInsertSemicolon()&&this.eat(Da.arrow))return this.checkPatternErrors(u,!1),this.checkYieldAwaitInDefaultParams(),this.awaitIdentPos>0&&this.raise(this.awaitIdentPos,"Cannot use 'await' as identifier inside an async function"),this.yieldPos=d,this.awaitPos=p,this.awaitIdentPos=f,this.parseArrowExpression(this.startNodeAt(t,i),m,!0,a);this.checkExpressionErrors(u,!0),this.yieldPos=d||this.yieldPos,this.awaitPos=p||this.awaitPos,this.awaitIdentPos=f||this.awaitIdentPos;var g=this.startNodeAt(t,i);g.callee=e,g.arguments=m,o&&(g.optional=l),e=this.finishNode(g,"CallExpression")}else if(this.type===Da.backQuote){(l||r)&&this.raise(this.start,"Optional chaining cannot appear in the tag of tagged template expressions");var y=this.startNodeAt(t,i);y.tag=e,y.quasi=this.parseTemplate({isTagged:!0}),e=this.finishNode(y,"TaggedTemplateExpression")}return e},Ao.parseExprAtom=function(e,t){this.type===Da.slash&&this.readRegexp();var i,s=this.potentialArrowAt===this.start;switch(this.type){case Da._super:return this.allowSuper||this.raise(this.start,"'super' keyword outside a method"),i=this.startNode(),this.next(),this.type!==Da.parenL||this.allowDirectSuper||this.raise(i.start,"super() call outside constructor of a subclass"),this.type!==Da.dot&&this.type!==Da.bracketL&&this.type!==Da.parenL&&this.unexpected(),this.finishNode(i,"Super");case Da._this:return i=this.startNode(),this.next(),this.finishNode(i,"ThisExpression");case Da.name:var n=this.start,r=this.startLoc,a=this.containsEsc,o=this.parseIdent(!1);if(this.options.ecmaVersion>=8&&!a&&"async"===o.name&&!this.canInsertSemicolon()&&this.eat(Da._function))return this.overrideContext(vo.f_expr),this.parseFunction(this.startNodeAt(n,r),0,!1,!0,t);if(s&&!this.canInsertSemicolon()){if(this.eat(Da.arrow))return this.parseArrowExpression(this.startNodeAt(n,r),[o],!1,t);if(this.options.ecmaVersion>=8&&"async"===o.name&&this.type===Da.name&&!a&&(!this.potentialArrowInForAwait||"of"!==this.value||this.containsEsc))return o=this.parseIdent(!1),!this.canInsertSemicolon()&&this.eat(Da.arrow)||this.unexpected(),this.parseArrowExpression(this.startNodeAt(n,r),[o],!0,t)}return o;case Da.regexp:var l=this.value;return(i=this.parseLiteral(l.value)).regex={pattern:l.pattern,flags:l.flags},i;case Da.num:case Da.string:return this.parseLiteral(this.value);case Da._null:case Da._true:case Da._false:return(i=this.startNode()).value=this.type===Da._null?null:this.type===Da._true,i.raw=this.type.keyword,this.next(),this.finishNode(i,"Literal");case Da.parenL:var h=this.start,c=this.parseParenAndDistinguishExpression(s,t);return e&&(e.parenthesizedAssign<0&&!this.isSimpleAssignTarget(c)&&(e.parenthesizedAssign=h),e.parenthesizedBind<0&&(e.parenthesizedBind=h)),c;case Da.bracketL:return i=this.startNode(),this.next(),i.elements=this.parseExprList(Da.bracketR,!0,!0,e),this.finishNode(i,"ArrayExpression");case Da.braceL:return this.overrideContext(vo.b_expr),this.parseObj(!1,e);case Da._function:return i=this.startNode(),this.next(),this.parseFunction(i,0);case Da._class:return this.parseClass(this.startNode(),!1);case Da._new:return this.parseNew();case Da.backQuote:return this.parseTemplate();case Da._import:return this.options.ecmaVersion>=11?this.parseExprImport():this.unexpected();default:this.unexpected()}},Ao.parseExprImport=function(){var e=this.startNode();this.containsEsc&&this.raiseRecoverable(this.start,"Escape sequence in keyword import");var t=this.parseIdent(!0);switch(this.type){case Da.parenL:return this.parseDynamicImport(e);case Da.dot:return e.meta=t,this.parseImportMeta(e);default:this.unexpected()}},Ao.parseDynamicImport=function(e){if(this.next(),e.source=this.parseMaybeAssign(),!this.eat(Da.parenR)){var t=this.start;this.eat(Da.comma)&&this.eat(Da.parenR)?this.raiseRecoverable(t,"Trailing comma is not allowed in import()"):this.unexpected(t)}return this.finishNode(e,"ImportExpression")},Ao.parseImportMeta=function(e){this.next();var t=this.containsEsc;return e.property=this.parseIdent(!0),"meta"!==e.property.name&&this.raiseRecoverable(e.property.start,"The only valid meta property for import is 'import.meta'"),t&&this.raiseRecoverable(e.start,"'import.meta' must not contain escaped characters"),"module"===this.options.sourceType||this.options.allowImportExportEverywhere||this.raiseRecoverable(e.start,"Cannot use 'import.meta' outside a module"),this.finishNode(e,"MetaProperty")},Ao.parseLiteral=function(e){var t=this.startNode();return t.value=e,t.raw=this.input.slice(this.start,this.end),110===t.raw.charCodeAt(t.raw.length-1)&&(t.bigint=t.raw.slice(0,-1).replace(/_/g,"")),this.next(),this.finishNode(t,"Literal")},Ao.parseParenExpression=function(){this.expect(Da.parenL);var e=this.parseExpression();return this.expect(Da.parenR),e},Ao.parseParenAndDistinguishExpression=function(e,t){var i,s=this.start,n=this.startLoc,r=this.options.ecmaVersion>=8;if(this.options.ecmaVersion>=6){this.next();var a,o=this.start,l=this.startLoc,h=[],c=!0,u=!1,d=new ho,p=this.yieldPos,f=this.awaitPos;for(this.yieldPos=0,this.awaitPos=0;this.type!==Da.parenR;){if(c?c=!1:this.expect(Da.comma),r&&this.afterTrailingComma(Da.parenR,!0)){u=!0;break}if(this.type===Da.ellipsis){a=this.start,h.push(this.parseParenItem(this.parseRestBinding())),this.type===Da.comma&&this.raise(this.start,"Comma is not permitted after the rest element");break}h.push(this.parseMaybeAssign(!1,d,this.parseParenItem))}var m=this.lastTokEnd,g=this.lastTokEndLoc;if(this.expect(Da.parenR),e&&!this.canInsertSemicolon()&&this.eat(Da.arrow))return this.checkPatternErrors(d,!1),this.checkYieldAwaitInDefaultParams(),this.yieldPos=p,this.awaitPos=f,this.parseParenArrowList(s,n,h,t);h.length&&!u||this.unexpected(this.lastTokStart),a&&this.unexpected(a),this.checkExpressionErrors(d,!0),this.yieldPos=p||this.yieldPos,this.awaitPos=f||this.awaitPos,h.length>1?((i=this.startNodeAt(o,l)).expressions=h,this.finishNodeAt(i,"SequenceExpression",m,g)):i=h[0]}else i=this.parseParenExpression();if(this.options.preserveParens){var y=this.startNodeAt(s,n);return y.expression=i,this.finishNode(y,"ParenthesizedExpression")}return i},Ao.parseParenItem=function(e){return e},Ao.parseParenArrowList=function(e,t,i,s){return this.parseArrowExpression(this.startNodeAt(e,t),i,!1,s)};var Po=[];Ao.parseNew=function(){this.containsEsc&&this.raiseRecoverable(this.start,"Escape sequence in keyword new");var e=this.startNode(),t=this.parseIdent(!0);if(this.options.ecmaVersion>=6&&this.eat(Da.dot)){e.meta=t;var i=this.containsEsc;return e.property=this.parseIdent(!0),"target"!==e.property.name&&this.raiseRecoverable(e.property.start,"The only valid meta property for new is 'new.target'"),i&&this.raiseRecoverable(e.start,"'new.target' must not contain escaped characters"),this.allowNewDotTarget||this.raiseRecoverable(e.start,"'new.target' can only be used in functions and class static block"),this.finishNode(e,"MetaProperty")}var s=this.start,n=this.startLoc,r=this.type===Da._import;return e.callee=this.parseSubscripts(this.parseExprAtom(),s,n,!0,!1),r&&"ImportExpression"===e.callee.type&&this.raise(s,"Cannot use new with import()"),this.eat(Da.parenL)?e.arguments=this.parseExprList(Da.parenR,this.options.ecmaVersion>=8,!1):e.arguments=Po,this.finishNode(e,"NewExpression")},Ao.parseTemplateElement=function(e){var t=e.isTagged,i=this.startNode();return this.type===Da.invalidTemplate?(t||this.raiseRecoverable(this.start,"Bad escape sequence in untagged template literal"),i.value={raw:this.value,cooked:null}):i.value={raw:this.input.slice(this.start,this.end).replace(/\r\n?/g,"\n"),cooked:this.value},this.next(),i.tail=this.type===Da.backQuote,this.finishNode(i,"TemplateElement")},Ao.parseTemplate=function(e){void 0===e&&(e={});var t=e.isTagged;void 0===t&&(t=!1);var i=this.startNode();this.next(),i.expressions=[];var s=this.parseTemplateElement({isTagged:t});for(i.quasis=[s];!s.tail;)this.type===Da.eof&&this.raise(this.pos,"Unterminated template literal"),this.expect(Da.dollarBraceL),i.expressions.push(this.parseExpression()),this.expect(Da.braceR),i.quasis.push(s=this.parseTemplateElement({isTagged:t}));return this.next(),this.finishNode(i,"TemplateLiteral")},Ao.isAsyncProp=function(e){return!e.computed&&"Identifier"===e.key.type&&"async"===e.key.name&&(this.type===Da.name||this.type===Da.num||this.type===Da.string||this.type===Da.bracketL||this.type.keyword||this.options.ecmaVersion>=9&&this.type===Da.star)&&!La.test(this.input.slice(this.lastTokEnd,this.start))},Ao.parseObj=function(e,t){var i=this.startNode(),s=!0,n={};for(i.properties=[],this.next();!this.eat(Da.braceR);){if(s)s=!1;else if(this.expect(Da.comma),this.options.ecmaVersion>=5&&this.afterTrailingComma(Da.braceR))break;var r=this.parseProperty(e,t);e||this.checkPropClash(r,n,t),i.properties.push(r)}return this.finishNode(i,e?"ObjectPattern":"ObjectExpression")},Ao.parseProperty=function(e,t){var i,s,n,r,a=this.startNode();if(this.options.ecmaVersion>=9&&this.eat(Da.ellipsis))return e?(a.argument=this.parseIdent(!1),this.type===Da.comma&&this.raise(this.start,"Comma is not permitted after the rest element"),this.finishNode(a,"RestElement")):(this.type===Da.parenL&&t&&(t.parenthesizedAssign<0&&(t.parenthesizedAssign=this.start),t.parenthesizedBind<0&&(t.parenthesizedBind=this.start)),a.argument=this.parseMaybeAssign(!1,t),this.type===Da.comma&&t&&t.trailingComma<0&&(t.trailingComma=this.start),this.finishNode(a,"SpreadElement"));this.options.ecmaVersion>=6&&(a.method=!1,a.shorthand=!1,(e||t)&&(n=this.start,r=this.startLoc),e||(i=this.eat(Da.star)));var o=this.containsEsc;return this.parsePropertyName(a),!e&&!o&&this.options.ecmaVersion>=8&&!i&&this.isAsyncProp(a)?(s=!0,i=this.options.ecmaVersion>=9&&this.eat(Da.star),this.parsePropertyName(a,t)):s=!1,this.parsePropertyValue(a,e,i,s,n,r,t,o),this.finishNode(a,"Property")},Ao.parsePropertyValue=function(e,t,i,s,n,r,a,o){if((i||s)&&this.type===Da.colon&&this.unexpected(),this.eat(Da.colon))e.value=t?this.parseMaybeDefault(this.start,this.startLoc):this.parseMaybeAssign(!1,a),e.kind="init";else if(this.options.ecmaVersion>=6&&this.type===Da.parenL)t&&this.unexpected(),e.kind="init",e.method=!0,e.value=this.parseMethod(i,s);else if(t||o||!(this.options.ecmaVersion>=5)||e.computed||"Identifier"!==e.key.type||"get"!==e.key.name&&"set"!==e.key.name||this.type===Da.comma||this.type===Da.braceR||this.type===Da.eq)this.options.ecmaVersion>=6&&!e.computed&&"Identifier"===e.key.type?((i||s)&&this.unexpected(),this.checkUnreserved(e.key),"await"!==e.key.name||this.awaitIdentPos||(this.awaitIdentPos=n),e.kind="init",t?e.value=this.parseMaybeDefault(n,r,this.copyNode(e.key)):this.type===Da.eq&&a?(a.shorthandAssign<0&&(a.shorthandAssign=this.start),e.value=this.parseMaybeDefault(n,r,this.copyNode(e.key))):e.value=this.copyNode(e.key),e.shorthand=!0):this.unexpected();else{(i||s)&&this.unexpected(),e.kind=e.key.name,this.parsePropertyName(e),e.value=this.parseMethod(!1);var l="get"===e.kind?0:1;if(e.value.params.length!==l){var h=e.value.start;"get"===e.kind?this.raiseRecoverable(h,"getter should have no params"):this.raiseRecoverable(h,"setter should have exactly one param")}else"set"===e.kind&&"RestElement"===e.value.params[0].type&&this.raiseRecoverable(e.value.params[0].start,"Setter cannot use rest params")}},Ao.parsePropertyName=function(e){if(this.options.ecmaVersion>=6){if(this.eat(Da.bracketL))return e.computed=!0,e.key=this.parseMaybeAssign(),this.expect(Da.bracketR),e.key;e.computed=!1}return e.key=this.type===Da.num||this.type===Da.string?this.parseExprAtom():this.parseIdent("never"!==this.options.allowReserved)},Ao.initFunction=function(e){e.id=null,this.options.ecmaVersion>=6&&(e.generator=e.expression=!1),this.options.ecmaVersion>=8&&(e.async=!1)},Ao.parseMethod=function(e,t,i){var s=this.startNode(),n=this.yieldPos,r=this.awaitPos,a=this.awaitIdentPos;return this.initFunction(s),this.options.ecmaVersion>=6&&(s.generator=e),this.options.ecmaVersion>=8&&(s.async=!!t),this.yieldPos=0,this.awaitPos=0,this.awaitIdentPos=0,this.enterScope(64|no(t,s.generator)|(i?128:0)),this.expect(Da.parenL),s.params=this.parseBindingList(Da.parenR,!1,this.options.ecmaVersion>=8),this.checkYieldAwaitInDefaultParams(),this.parseFunctionBody(s,!1,!0,!1),this.yieldPos=n,this.awaitPos=r,this.awaitIdentPos=a,this.finishNode(s,"FunctionExpression")},Ao.parseArrowExpression=function(e,t,i,s){var n=this.yieldPos,r=this.awaitPos,a=this.awaitIdentPos;return this.enterScope(16|no(i,!1)),this.initFunction(e),this.options.ecmaVersion>=8&&(e.async=!!i),this.yieldPos=0,this.awaitPos=0,this.awaitIdentPos=0,e.params=this.toAssignableList(t,!0),this.parseFunctionBody(e,!0,!1,s),this.yieldPos=n,this.awaitPos=r,this.awaitIdentPos=a,this.finishNode(e,"ArrowFunctionExpression")},Ao.parseFunctionBody=function(e,t,i,s){var n=t&&this.type!==Da.braceL,r=this.strict,a=!1;if(n)e.body=this.parseMaybeAssign(s),e.expression=!0,this.checkParams(e,!1);else{var o=this.options.ecmaVersion>=7&&!this.isSimpleParamList(e.params);r&&!o||(a=this.strictDirective(this.end))&&o&&this.raiseRecoverable(e.start,"Illegal 'use strict' directive in function with non-simple parameter list");var l=this.labels;this.labels=[],a&&(this.strict=!0),this.checkParams(e,!r&&!a&&!t&&!i&&this.isSimpleParamList(e.params)),this.strict&&e.id&&this.checkLValSimple(e.id,5),e.body=this.parseBlock(!1,void 0,a&&!r),e.expression=!1,this.adaptDirectivePrologue(e.body.body),this.labels=l}this.exitScope()},Ao.isSimpleParamList=function(e){for(var t=0,i=e;t<i.length;t+=1)if("Identifier"!==i[t].type)return!1;return!0},Ao.checkParams=function(e,t){for(var i=Object.create(null),s=0,n=e.params;s<n.length;s+=1){var r=n[s];this.checkLValInnerPattern(r,1,t?null:i)}},Ao.parseExprList=function(e,t,i,s){for(var n=[],r=!0;!this.eat(e);){if(r)r=!1;else if(this.expect(Da.comma),t&&this.afterTrailingComma(e))break;var a=void 0;i&&this.type===Da.comma?a=null:this.type===Da.ellipsis?(a=this.parseSpread(s),s&&this.type===Da.comma&&s.trailingComma<0&&(s.trailingComma=this.start)):a=this.parseMaybeAssign(!1,s),n.push(a)}return n},Ao.checkUnreserved=function(e){var t=e.start,i=e.end,s=e.name;this.inGenerator&&"yield"===s&&this.raiseRecoverable(t,"Cannot use 'yield' as identifier inside a generator"),this.inAsync&&"await"===s&&this.raiseRecoverable(t,"Cannot use 'await' as identifier inside an async function"),this.currentThisScope().inClassFieldInit&&"arguments"===s&&this.raiseRecoverable(t,"Cannot use 'arguments' in class field initializer"),!this.inClassStaticBlock||"arguments"!==s&&"await"!==s||this.raise(t,"Cannot use "+s+" in class static initialization block"),this.keywords.test(s)&&this.raise(t,"Unexpected keyword '"+s+"'"),this.options.ecmaVersion<6&&-1!==this.input.slice(t,i).indexOf("\\")||(this.strict?this.reservedWordsStrict:this.reservedWords).test(s)&&(this.inAsync||"await"!==s||this.raiseRecoverable(t,"Cannot use keyword 'await' outside an async function"),this.raiseRecoverable(t,"The keyword '"+s+"' is reserved"))},Ao.parseIdent=function(e,t){var i=this.startNode();return this.type===Da.name?i.name=this.value:this.type.keyword?(i.name=this.type.keyword,"class"!==i.name&&"function"!==i.name||this.lastTokEnd===this.lastTokStart+1&&46===this.input.charCodeAt(this.lastTokStart)||this.context.pop()):this.unexpected(),this.next(!!e),this.finishNode(i,"Identifier"),e||(this.checkUnreserved(i),"await"!==i.name||this.awaitIdentPos||(this.awaitIdentPos=i.start)),i},Ao.parsePrivateIdent=function(){var e=this.startNode();return this.type===Da.privateId?e.name=this.value:this.unexpected(),this.next(),this.finishNode(e,"PrivateIdentifier"),0===this.privateNameStack.length?this.raise(e.start,"Private field '#"+e.name+"' must be declared in an enclosing class"):this.privateNameStack[this.privateNameStack.length-1].used.push(e),e},Ao.parseYield=function(e){this.yieldPos||(this.yieldPos=this.start);var t=this.startNode();return this.next(),this.type===Da.semi||this.canInsertSemicolon()||this.type!==Da.star&&!this.type.startsExpr?(t.delegate=!1,t.argument=null):(t.delegate=this.eat(Da.star),t.argument=this.parseMaybeAssign(e)),this.finishNode(t,"YieldExpression")},Ao.parseAwait=function(e){this.awaitPos||(this.awaitPos=this.start);var t=this.startNode();return this.next(),t.argument=this.parseMaybeUnary(null,!0,!1,e),this.finishNode(t,"AwaitExpression")};var ko=ro.prototype;ko.raise=function(e,t){var i=Za(this.input,e);t+=" ("+i.line+":"+i.column+")";var s=new SyntaxError(t);throw s.pos=e,s.loc=i,s.raisedAt=this.pos,s},ko.raiseRecoverable=ko.raise,ko.curPosition=function(){if(this.options.locations)return new Qa(this.curLine,this.pos-this.lineStart)};var wo=ro.prototype,Co=function(e){this.flags=e,this.var=[],this.lexical=[],this.functions=[],this.inClassFieldInit=!1};wo.enterScope=function(e){this.scopeStack.push(new Co(e))},wo.exitScope=function(){this.scopeStack.pop()},wo.treatFunctionsAsVarInScope=function(e){return 2&e.flags||!this.inModule&&1&e.flags},wo.declareName=function(e,t,i){var s=!1;if(2===t){var n=this.currentScope();s=n.lexical.indexOf(e)>-1||n.functions.indexOf(e)>-1||n.var.indexOf(e)>-1,n.lexical.push(e),this.inModule&&1&n.flags&&delete this.undefinedExports[e]}else if(4===t)this.currentScope().lexical.push(e);else if(3===t){var r=this.currentScope();s=this.treatFunctionsAsVar?r.lexical.indexOf(e)>-1:r.lexical.indexOf(e)>-1||r.var.indexOf(e)>-1,r.functions.push(e)}else for(var a=this.scopeStack.length-1;a>=0;--a){var o=this.scopeStack[a];if(o.lexical.indexOf(e)>-1&&!(32&o.flags&&o.lexical[0]===e)||!this.treatFunctionsAsVarInScope(o)&&o.functions.indexOf(e)>-1){s=!0;break}if(o.var.push(e),this.inModule&&1&o.flags&&delete this.undefinedExports[e],259&o.flags)break}s&&this.raiseRecoverable(i,"Identifier '"+e+"' has already been declared")},wo.checkLocalExport=function(e){-1===this.scopeStack[0].lexical.indexOf(e.name)&&-1===this.scopeStack[0].var.indexOf(e.name)&&(this.undefinedExports[e.name]=e)},wo.currentScope=function(){return this.scopeStack[this.scopeStack.length-1]},wo.currentVarScope=function(){for(var e=this.scopeStack.length-1;;e--){var t=this.scopeStack[e];if(259&t.flags)return t}},wo.currentThisScope=function(){for(var e=this.scopeStack.length-1;;e--){var t=this.scopeStack[e];if(259&t.flags&&!(16&t.flags))return t}};var No=function(e,t,i){this.type="",this.start=t,this.end=0,e.options.locations&&(this.loc=new Ja(e,i)),e.options.directSourceFile&&(this.sourceFile=e.options.directSourceFile),e.options.ranges&&(this.range=[t,0])},_o=ro.prototype;function $o(e,t,i,s){return e.type=t,e.end=i,this.options.locations&&(e.loc.end=s),this.options.ranges&&(e.range[1]=i),e}_o.startNode=function(){return new No(this,this.start,this.startLoc)},_o.startNodeAt=function(e,t){return new No(this,e,t)},_o.finishNode=function(e,t){return $o.call(this,e,t,this.lastTokEnd,this.lastTokEndLoc)},_o.finishNodeAt=function(e,t,i,s){return $o.call(this,e,t,i,s)},_o.copyNode=function(e){var t=new No(this,e.start,this.startLoc);for(var i in e)t[i]=e[i];return t};var To="ASCII ASCII_Hex_Digit AHex Alphabetic Alpha Any Assigned Bidi_Control Bidi_C Bidi_Mirrored Bidi_M Case_Ignorable CI Cased Changes_When_Casefolded CWCF Changes_When_Casemapped CWCM Changes_When_Lowercased CWL Changes_When_NFKC_Casefolded CWKCF Changes_When_Titlecased CWT Changes_When_Uppercased CWU Dash Default_Ignorable_Code_Point DI Deprecated Dep Diacritic Dia Emoji Emoji_Component Emoji_Modifier Emoji_Modifier_Base Emoji_Presentation Extender Ext Grapheme_Base Gr_Base Grapheme_Extend Gr_Ext Hex_Digit Hex IDS_Binary_Operator IDSB IDS_Trinary_Operator IDST ID_Continue IDC ID_Start IDS Ideographic Ideo Join_Control Join_C Logical_Order_Exception LOE Lowercase Lower Math Noncharacter_Code_Point NChar Pattern_Syntax Pat_Syn Pattern_White_Space Pat_WS Quotation_Mark QMark Radical Regional_Indicator RI Sentence_Terminal STerm Soft_Dotted SD Terminal_Punctuation Term Unified_Ideograph UIdeo Uppercase Upper Variation_Selector VS White_Space space XID_Continue XIDC XID_Start XIDS",Oo=To+" Extended_Pictographic",Ro=Oo+" EBase EComp EMod EPres ExtPict",Mo={9:To,10:Oo,11:Oo,12:Ro,13:"ASCII ASCII_Hex_Digit AHex Alphabetic Alpha Any Assigned Bidi_Control Bidi_C Bidi_Mirrored Bidi_M Case_Ignorable CI Cased Changes_When_Casefolded CWCF Changes_When_Casemapped CWCM Changes_When_Lowercased CWL Changes_When_NFKC_Casefolded CWKCF Changes_When_Titlecased CWT Changes_When_Uppercased CWU Dash Default_Ignorable_Code_Point DI Deprecated Dep Diacritic Dia Emoji Emoji_Component Emoji_Modifier Emoji_Modifier_Base Emoji_Presentation Extender Ext Grapheme_Base Gr_Base Grapheme_Extend Gr_Ext Hex_Digit Hex IDS_Binary_Operator IDSB IDS_Trinary_Operator IDST ID_Continue IDC ID_Start IDS Ideographic Ideo Join_Control Join_C Logical_Order_Exception LOE Lowercase Lower Math Noncharacter_Code_Point NChar Pattern_Syntax Pat_Syn Pattern_White_Space Pat_WS Quotation_Mark QMark Radical Regional_Indicator RI Sentence_Terminal STerm Soft_Dotted SD Terminal_Punctuation Term Unified_Ideograph UIdeo Uppercase Upper Variation_Selector VS White_Space space XID_Continue XIDC XID_Start XIDS Extended_Pictographic EBase EComp EMod EPres ExtPict"},Do="Cased_Letter LC Close_Punctuation Pe Connector_Punctuation Pc Control Cc cntrl Currency_Symbol Sc Dash_Punctuation Pd Decimal_Number Nd digit Enclosing_Mark Me Final_Punctuation Pf Format Cf Initial_Punctuation Pi Letter L Letter_Number Nl Line_Separator Zl Lowercase_Letter Ll Mark M Combining_Mark Math_Symbol Sm Modifier_Letter Lm Modifier_Symbol Sk Nonspacing_Mark Mn Number N Open_Punctuation Ps Other C Other_Letter Lo Other_Number No Other_Punctuation Po Other_Symbol So Paragraph_Separator Zp Private_Use Co Punctuation P punct Separator Z Space_Separator Zs Spacing_Mark Mc Surrogate Cs Symbol S Titlecase_Letter Lt Unassigned Cn Uppercase_Letter Lu",Lo="Adlam Adlm Ahom Anatolian_Hieroglyphs Hluw Arabic Arab Armenian Armn Avestan Avst Balinese Bali Bamum Bamu Bassa_Vah Bass Batak Batk Bengali Beng Bhaiksuki Bhks Bopomofo Bopo Brahmi Brah Braille Brai Buginese Bugi Buhid Buhd Canadian_Aboriginal Cans Carian Cari Caucasian_Albanian Aghb Chakma Cakm Cham Cham Cherokee Cher Common Zyyy Coptic Copt Qaac Cuneiform Xsux Cypriot Cprt Cyrillic Cyrl Deseret Dsrt Devanagari Deva Duployan Dupl Egyptian_Hieroglyphs Egyp Elbasan Elba Ethiopic Ethi Georgian Geor Glagolitic Glag Gothic Goth Grantha Gran Greek Grek Gujarati Gujr Gurmukhi Guru Han Hani Hangul Hang Hanunoo Hano Hatran Hatr Hebrew Hebr Hiragana Hira Imperial_Aramaic Armi Inherited Zinh Qaai Inscriptional_Pahlavi Phli Inscriptional_Parthian Prti Javanese Java Kaithi Kthi Kannada Knda Katakana Kana Kayah_Li Kali Kharoshthi Khar Khmer Khmr Khojki Khoj Khudawadi Sind Lao Laoo Latin Latn Lepcha Lepc Limbu Limb Linear_A Lina Linear_B Linb Lisu Lisu Lycian Lyci Lydian Lydi Mahajani Mahj Malayalam Mlym Mandaic Mand Manichaean Mani Marchen Marc Masaram_Gondi Gonm Meetei_Mayek Mtei Mende_Kikakui Mend Meroitic_Cursive Merc Meroitic_Hieroglyphs Mero Miao Plrd Modi Mongolian Mong Mro Mroo Multani Mult Myanmar Mymr Nabataean Nbat New_Tai_Lue Talu Newa Newa Nko Nkoo Nushu Nshu Ogham Ogam Ol_Chiki Olck Old_Hungarian Hung Old_Italic Ital Old_North_Arabian Narb Old_Permic Perm Old_Persian Xpeo Old_South_Arabian Sarb Old_Turkic Orkh Oriya Orya Osage Osge Osmanya Osma Pahawh_Hmong Hmng Palmyrene Palm Pau_Cin_Hau Pauc Phags_Pa Phag Phoenician Phnx Psalter_Pahlavi Phlp Rejang Rjng Runic Runr Samaritan Samr Saurashtra Saur Sharada Shrd Shavian Shaw Siddham Sidd SignWriting Sgnw Sinhala Sinh Sora_Sompeng Sora Soyombo Soyo Sundanese Sund Syloti_Nagri Sylo Syriac Syrc Tagalog Tglg Tagbanwa Tagb Tai_Le Tale Tai_Tham Lana Tai_Viet Tavt Takri Takr Tamil Taml Tangut Tang Telugu Telu Thaana Thaa Thai Thai Tibetan Tibt Tifinagh Tfng Tirhuta Tirh Ugaritic Ugar Vai Vaii Warang_Citi Wara Yi Yiii Zanabazar_Square Zanb",Vo=Lo+" Dogra Dogr Gunjala_Gondi Gong Hanifi_Rohingya Rohg Makasar Maka Medefaidrin Medf Old_Sogdian Sogo Sogdian Sogd",Bo=Vo+" Elymaic Elym Nandinagari Nand Nyiakeng_Puachue_Hmong Hmnp Wancho Wcho",Fo=Bo+" Chorasmian Chrs Diak Dives_Akuru Khitan_Small_Script Kits Yezi Yezidi",zo={9:Lo,10:Vo,11:Bo,12:Fo,13:"Adlam Adlm Ahom Anatolian_Hieroglyphs Hluw Arabic Arab Armenian Armn Avestan Avst Balinese Bali Bamum Bamu Bassa_Vah Bass Batak Batk Bengali Beng Bhaiksuki Bhks Bopomofo Bopo Brahmi Brah Braille Brai Buginese Bugi Buhid Buhd Canadian_Aboriginal Cans Carian Cari Caucasian_Albanian Aghb Chakma Cakm Cham Cham Cherokee Cher Common Zyyy Coptic Copt Qaac Cuneiform Xsux Cypriot Cprt Cyrillic Cyrl Deseret Dsrt Devanagari Deva Duployan Dupl Egyptian_Hieroglyphs Egyp Elbasan Elba Ethiopic Ethi Georgian Geor Glagolitic Glag Gothic Goth Grantha Gran Greek Grek Gujarati Gujr Gurmukhi Guru Han Hani Hangul Hang Hanunoo Hano Hatran Hatr Hebrew Hebr Hiragana Hira Imperial_Aramaic Armi Inherited Zinh Qaai Inscriptional_Pahlavi Phli Inscriptional_Parthian Prti Javanese Java Kaithi Kthi Kannada Knda Katakana Kana Kayah_Li Kali Kharoshthi Khar Khmer Khmr Khojki Khoj Khudawadi Sind Lao Laoo Latin Latn Lepcha Lepc Limbu Limb Linear_A Lina Linear_B Linb Lisu Lisu Lycian Lyci Lydian Lydi Mahajani Mahj Malayalam Mlym Mandaic Mand Manichaean Mani Marchen Marc Masaram_Gondi Gonm Meetei_Mayek Mtei Mende_Kikakui Mend Meroitic_Cursive Merc Meroitic_Hieroglyphs Mero Miao Plrd Modi Mongolian Mong Mro Mroo Multani Mult Myanmar Mymr Nabataean Nbat New_Tai_Lue Talu Newa Newa Nko Nkoo Nushu Nshu Ogham Ogam Ol_Chiki Olck Old_Hungarian Hung Old_Italic Ital Old_North_Arabian Narb Old_Permic Perm Old_Persian Xpeo Old_South_Arabian Sarb Old_Turkic Orkh Oriya Orya Osage Osge Osmanya Osma Pahawh_Hmong Hmng Palmyrene Palm Pau_Cin_Hau Pauc Phags_Pa Phag Phoenician Phnx Psalter_Pahlavi Phlp Rejang Rjng Runic Runr Samaritan Samr Saurashtra Saur Sharada Shrd Shavian Shaw Siddham Sidd SignWriting Sgnw Sinhala Sinh Sora_Sompeng Sora Soyombo Soyo Sundanese Sund Syloti_Nagri Sylo Syriac Syrc Tagalog Tglg Tagbanwa Tagb Tai_Le Tale Tai_Tham Lana Tai_Viet Tavt Takri Takr Tamil Taml Tangut Tang Telugu Telu Thaana Thaa Thai Thai Tibetan Tibt Tifinagh Tfng Tirhuta Tirh Ugaritic Ugar Vai Vaii Warang_Citi Wara Yi Yiii Zanabazar_Square Zanb Dogra Dogr Gunjala_Gondi Gong Hanifi_Rohingya Rohg Makasar Maka Medefaidrin Medf Old_Sogdian Sogo Sogdian Sogd Elymaic Elym Nandinagari Nand Nyiakeng_Puachue_Hmong Hmnp Wancho Wcho Chorasmian Chrs Diak Dives_Akuru Khitan_Small_Script Kits Yezi Yezidi Cypro_Minoan Cpmn Old_Uyghur Ougr Tangsa Tnsa Toto Vithkuqi Vith"},jo={};function Uo(e){var t=jo[e]={binary:Ka(Mo[e]+" "+Do),nonBinary:{General_Category:Ka(Do),Script:Ka(zo[e])}};t.nonBinary.Script_Extensions=t.nonBinary.Script,t.nonBinary.gc=t.nonBinary.General_Category,t.nonBinary.sc=t.nonBinary.Script,t.nonBinary.scx=t.nonBinary.Script_Extensions}for(var Go=0,Ho=[9,10,11,12,13];Go<Ho.length;Go+=1)Uo(Ho[Go]);var Wo=ro.prototype,qo=function(e){this.parser=e,this.validFlags="gim"+(e.options.ecmaVersion>=6?"uy":"")+(e.options.ecmaVersion>=9?"s":"")+(e.options.ecmaVersion>=13?"d":""),this.unicodeProperties=jo[e.options.ecmaVersion>=13?13:e.options.ecmaVersion],this.source="",this.flags="",this.start=0,this.switchU=!1,this.switchN=!1,this.pos=0,this.lastIntValue=0,this.lastStringValue="",this.lastAssertionIsQuantifiable=!1,this.numCapturingParens=0,this.maxBackReference=0,this.groupNames=[],this.backReferenceNames=[]};function Ko(e){return 36===e||e>=40&&e<=43||46===e||63===e||e>=91&&e<=94||e>=123&&e<=125}function Xo(e){return e>=65&&e<=90||e>=97&&e<=122}function Yo(e){return Xo(e)||95===e}function Qo(e){return Yo(e)||Jo(e)}function Jo(e){return e>=48&&e<=57}function Zo(e){return e>=48&&e<=57||e>=65&&e<=70||e>=97&&e<=102}function el(e){return e>=65&&e<=70?e-65+10:e>=97&&e<=102?e-97+10:e-48}function tl(e){return e>=48&&e<=55}qo.prototype.reset=function(e,t,i){var s=-1!==i.indexOf("u");this.start=0|e,this.source=t+"",this.flags=i,this.switchU=s&&this.parser.options.ecmaVersion>=6,this.switchN=s&&this.parser.options.ecmaVersion>=9},qo.prototype.raise=function(e){this.parser.raiseRecoverable(this.start,"Invalid regular expression: /"+this.source+"/: "+e)},qo.prototype.at=function(e,t){void 0===t&&(t=!1);var i=this.source,s=i.length;if(e>=s)return-1;var n=i.charCodeAt(e);if(!t&&!this.switchU||n<=55295||n>=57344||e+1>=s)return n;var r=i.charCodeAt(e+1);return r>=56320&&r<=57343?(n<<10)+r-56613888:n},qo.prototype.nextIndex=function(e,t){void 0===t&&(t=!1);var i=this.source,s=i.length;if(e>=s)return s;var n,r=i.charCodeAt(e);return!t&&!this.switchU||r<=55295||r>=57344||e+1>=s||(n=i.charCodeAt(e+1))<56320||n>57343?e+1:e+2},qo.prototype.current=function(e){return void 0===e&&(e=!1),this.at(this.pos,e)},qo.prototype.lookahead=function(e){return void 0===e&&(e=!1),this.at(this.nextIndex(this.pos,e),e)},qo.prototype.advance=function(e){void 0===e&&(e=!1),this.pos=this.nextIndex(this.pos,e)},qo.prototype.eat=function(e,t){return void 0===t&&(t=!1),this.current(t)===e&&(this.advance(t),!0)},Wo.validateRegExpFlags=function(e){for(var t=e.validFlags,i=e.flags,s=0;s<i.length;s++){var n=i.charAt(s);-1===t.indexOf(n)&&this.raise(e.start,"Invalid regular expression flag"),i.indexOf(n,s+1)>-1&&this.raise(e.start,"Duplicate regular expression flag")}},Wo.validateRegExpPattern=function(e){this.regexp_pattern(e),!e.switchN&&this.options.ecmaVersion>=9&&e.groupNames.length>0&&(e.switchN=!0,this.regexp_pattern(e))},Wo.regexp_pattern=function(e){e.pos=0,e.lastIntValue=0,e.lastStringValue="",e.lastAssertionIsQuantifiable=!1,e.numCapturingParens=0,e.maxBackReference=0,e.groupNames.length=0,e.backReferenceNames.length=0,this.regexp_disjunction(e),e.pos!==e.source.length&&(e.eat(41)&&e.raise("Unmatched ')'"),(e.eat(93)||e.eat(125))&&e.raise("Lone quantifier brackets")),e.maxBackReference>e.numCapturingParens&&e.raise("Invalid escape");for(var t=0,i=e.backReferenceNames;t<i.length;t+=1){var s=i[t];-1===e.groupNames.indexOf(s)&&e.raise("Invalid named capture referenced")}},Wo.regexp_disjunction=function(e){for(this.regexp_alternative(e);e.eat(124);)this.regexp_alternative(e);this.regexp_eatQuantifier(e,!0)&&e.raise("Nothing to repeat"),e.eat(123)&&e.raise("Lone quantifier brackets")},Wo.regexp_alternative=function(e){for(;e.pos<e.source.length&&this.regexp_eatTerm(e););},Wo.regexp_eatTerm=function(e){return this.regexp_eatAssertion(e)?(e.lastAssertionIsQuantifiable&&this.regexp_eatQuantifier(e)&&e.switchU&&e.raise("Invalid quantifier"),!0):!!(e.switchU?this.regexp_eatAtom(e):this.regexp_eatExtendedAtom(e))&&(this.regexp_eatQuantifier(e),!0)},Wo.regexp_eatAssertion=function(e){var t=e.pos;if(e.lastAssertionIsQuantifiable=!1,e.eat(94)||e.eat(36))return!0;if(e.eat(92)){if(e.eat(66)||e.eat(98))return!0;e.pos=t}if(e.eat(40)&&e.eat(63)){var i=!1;if(this.options.ecmaVersion>=9&&(i=e.eat(60)),e.eat(61)||e.eat(33))return this.regexp_disjunction(e),e.eat(41)||e.raise("Unterminated group"),e.lastAssertionIsQuantifiable=!i,!0}return e.pos=t,!1},Wo.regexp_eatQuantifier=function(e,t){return void 0===t&&(t=!1),!!this.regexp_eatQuantifierPrefix(e,t)&&(e.eat(63),!0)},Wo.regexp_eatQuantifierPrefix=function(e,t){return e.eat(42)||e.eat(43)||e.eat(63)||this.regexp_eatBracedQuantifier(e,t)},Wo.regexp_eatBracedQuantifier=function(e,t){var i=e.pos;if(e.eat(123)){var s=0,n=-1;if(this.regexp_eatDecimalDigits(e)&&(s=e.lastIntValue,e.eat(44)&&this.regexp_eatDecimalDigits(e)&&(n=e.lastIntValue),e.eat(125)))return-1!==n&&n<s&&!t&&e.raise("numbers out of order in {} quantifier"),!0;e.switchU&&!t&&e.raise("Incomplete quantifier"),e.pos=i}return!1},Wo.regexp_eatAtom=function(e){return this.regexp_eatPatternCharacters(e)||e.eat(46)||this.regexp_eatReverseSolidusAtomEscape(e)||this.regexp_eatCharacterClass(e)||this.regexp_eatUncapturingGroup(e)||this.regexp_eatCapturingGroup(e)},Wo.regexp_eatReverseSolidusAtomEscape=function(e){var t=e.pos;if(e.eat(92)){if(this.regexp_eatAtomEscape(e))return!0;e.pos=t}return!1},Wo.regexp_eatUncapturingGroup=function(e){var t=e.pos;if(e.eat(40)){if(e.eat(63)&&e.eat(58)){if(this.regexp_disjunction(e),e.eat(41))return!0;e.raise("Unterminated group")}e.pos=t}return!1},Wo.regexp_eatCapturingGroup=function(e){if(e.eat(40)){if(this.options.ecmaVersion>=9?this.regexp_groupSpecifier(e):63===e.current()&&e.raise("Invalid group"),this.regexp_disjunction(e),e.eat(41))return e.numCapturingParens+=1,!0;e.raise("Unterminated group")}return!1},Wo.regexp_eatExtendedAtom=function(e){return e.eat(46)||this.regexp_eatReverseSolidusAtomEscape(e)||this.regexp_eatCharacterClass(e)||this.regexp_eatUncapturingGroup(e)||this.regexp_eatCapturingGroup(e)||this.regexp_eatInvalidBracedQuantifier(e)||this.regexp_eatExtendedPatternCharacter(e)},Wo.regexp_eatInvalidBracedQuantifier=function(e){return this.regexp_eatBracedQuantifier(e,!0)&&e.raise("Nothing to repeat"),!1},Wo.regexp_eatSyntaxCharacter=function(e){var t=e.current();return!!Ko(t)&&(e.lastIntValue=t,e.advance(),!0)},Wo.regexp_eatPatternCharacters=function(e){for(var t=e.pos,i=0;-1!==(i=e.current())&&!Ko(i);)e.advance();return e.pos!==t},Wo.regexp_eatExtendedPatternCharacter=function(e){var t=e.current();return!(-1===t||36===t||t>=40&&t<=43||46===t||63===t||91===t||94===t||124===t||(e.advance(),0))},Wo.regexp_groupSpecifier=function(e){if(e.eat(63)){if(this.regexp_eatGroupName(e))return-1!==e.groupNames.indexOf(e.lastStringValue)&&e.raise("Duplicate capture group name"),void e.groupNames.push(e.lastStringValue);e.raise("Invalid group")}},Wo.regexp_eatGroupName=function(e){if(e.lastStringValue="",e.eat(60)){if(this.regexp_eatRegExpIdentifierName(e)&&e.eat(62))return!0;e.raise("Invalid capture group name")}return!1},Wo.regexp_eatRegExpIdentifierName=function(e){if(e.lastStringValue="",this.regexp_eatRegExpIdentifierStart(e)){for(e.lastStringValue+=Xa(e.lastIntValue);this.regexp_eatRegExpIdentifierPart(e);)e.lastStringValue+=Xa(e.lastIntValue);return!0}return!1},Wo.regexp_eatRegExpIdentifierStart=function(e){var t=e.pos,i=this.options.ecmaVersion>=11,s=e.current(i);return e.advance(i),92===s&&this.regexp_eatRegExpUnicodeEscapeSequence(e,i)&&(s=e.lastIntValue),function(e){return Ca(e,!0)||36===e||95===e}(s)?(e.lastIntValue=s,!0):(e.pos=t,!1)},Wo.regexp_eatRegExpIdentifierPart=function(e){var t=e.pos,i=this.options.ecmaVersion>=11,s=e.current(i);return e.advance(i),92===s&&this.regexp_eatRegExpUnicodeEscapeSequence(e,i)&&(s=e.lastIntValue),function(e){return Na(e,!0)||36===e||95===e||8204===e||8205===e}(s)?(e.lastIntValue=s,!0):(e.pos=t,!1)},Wo.regexp_eatAtomEscape=function(e){return!!(this.regexp_eatBackReference(e)||this.regexp_eatCharacterClassEscape(e)||this.regexp_eatCharacterEscape(e)||e.switchN&&this.regexp_eatKGroupName(e))||(e.switchU&&(99===e.current()&&e.raise("Invalid unicode escape"),e.raise("Invalid escape")),!1)},Wo.regexp_eatBackReference=function(e){var t=e.pos;if(this.regexp_eatDecimalEscape(e)){var i=e.lastIntValue;if(e.switchU)return i>e.maxBackReference&&(e.maxBackReference=i),!0;if(i<=e.numCapturingParens)return!0;e.pos=t}return!1},Wo.regexp_eatKGroupName=function(e){if(e.eat(107)){if(this.regexp_eatGroupName(e))return e.backReferenceNames.push(e.lastStringValue),!0;e.raise("Invalid named reference")}return!1},Wo.regexp_eatCharacterEscape=function(e){return this.regexp_eatControlEscape(e)||this.regexp_eatCControlLetter(e)||this.regexp_eatZero(e)||this.regexp_eatHexEscapeSequence(e)||this.regexp_eatRegExpUnicodeEscapeSequence(e,!1)||!e.switchU&&this.regexp_eatLegacyOctalEscapeSequence(e)||this.regexp_eatIdentityEscape(e)},Wo.regexp_eatCControlLetter=function(e){var t=e.pos;if(e.eat(99)){if(this.regexp_eatControlLetter(e))return!0;e.pos=t}return!1},Wo.regexp_eatZero=function(e){return 48===e.current()&&!Jo(e.lookahead())&&(e.lastIntValue=0,e.advance(),!0)},Wo.regexp_eatControlEscape=function(e){var t=e.current();return 116===t?(e.lastIntValue=9,e.advance(),!0):110===t?(e.lastIntValue=10,e.advance(),!0):118===t?(e.lastIntValue=11,e.advance(),!0):102===t?(e.lastIntValue=12,e.advance(),!0):114===t&&(e.lastIntValue=13,e.advance(),!0)},Wo.regexp_eatControlLetter=function(e){var t=e.current();return!!Xo(t)&&(e.lastIntValue=t%32,e.advance(),!0)},Wo.regexp_eatRegExpUnicodeEscapeSequence=function(e,t){void 0===t&&(t=!1);var i,s=e.pos,n=t||e.switchU;if(e.eat(117)){if(this.regexp_eatFixedHexDigits(e,4)){var r=e.lastIntValue;if(n&&r>=55296&&r<=56319){var a=e.pos;if(e.eat(92)&&e.eat(117)&&this.regexp_eatFixedHexDigits(e,4)){var o=e.lastIntValue;if(o>=56320&&o<=57343)return e.lastIntValue=1024*(r-55296)+(o-56320)+65536,!0}e.pos=a,e.lastIntValue=r}return!0}if(n&&e.eat(123)&&this.regexp_eatHexDigits(e)&&e.eat(125)&&(i=e.lastIntValue)>=0&&i<=1114111)return!0;n&&e.raise("Invalid unicode escape"),e.pos=s}return!1},Wo.regexp_eatIdentityEscape=function(e){if(e.switchU)return!!this.regexp_eatSyntaxCharacter(e)||!!e.eat(47)&&(e.lastIntValue=47,!0);var t=e.current();return!(99===t||e.switchN&&107===t||(e.lastIntValue=t,e.advance(),0))},Wo.regexp_eatDecimalEscape=function(e){e.lastIntValue=0;var t=e.current();if(t>=49&&t<=57){do{e.lastIntValue=10*e.lastIntValue+(t-48),e.advance()}while((t=e.current())>=48&&t<=57);return!0}return!1},Wo.regexp_eatCharacterClassEscape=function(e){var t=e.current();if(function(e){return 100===e||68===e||115===e||83===e||119===e||87===e}(t))return e.lastIntValue=-1,e.advance(),!0;if(e.switchU&&this.options.ecmaVersion>=9&&(80===t||112===t)){if(e.lastIntValue=-1,e.advance(),e.eat(123)&&this.regexp_eatUnicodePropertyValueExpression(e)&&e.eat(125))return!0;e.raise("Invalid property name")}return!1},Wo.regexp_eatUnicodePropertyValueExpression=function(e){var t=e.pos;if(this.regexp_eatUnicodePropertyName(e)&&e.eat(61)){var i=e.lastStringValue;if(this.regexp_eatUnicodePropertyValue(e)){var s=e.lastStringValue;return this.regexp_validateUnicodePropertyNameAndValue(e,i,s),!0}}if(e.pos=t,this.regexp_eatLoneUnicodePropertyNameOrValue(e)){var n=e.lastStringValue;return this.regexp_validateUnicodePropertyNameOrValue(e,n),!0}return!1},Wo.regexp_validateUnicodePropertyNameAndValue=function(e,t,i){Wa(e.unicodeProperties.nonBinary,t)||e.raise("Invalid property name"),e.unicodeProperties.nonBinary[t].test(i)||e.raise("Invalid property value")},Wo.regexp_validateUnicodePropertyNameOrValue=function(e,t){e.unicodeProperties.binary.test(t)||e.raise("Invalid property name")},Wo.regexp_eatUnicodePropertyName=function(e){var t=0;for(e.lastStringValue="";Yo(t=e.current());)e.lastStringValue+=Xa(t),e.advance();return""!==e.lastStringValue},Wo.regexp_eatUnicodePropertyValue=function(e){var t=0;for(e.lastStringValue="";Qo(t=e.current());)e.lastStringValue+=Xa(t),e.advance();return""!==e.lastStringValue},Wo.regexp_eatLoneUnicodePropertyNameOrValue=function(e){return this.regexp_eatUnicodePropertyValue(e)},Wo.regexp_eatCharacterClass=function(e){if(e.eat(91)){if(e.eat(94),this.regexp_classRanges(e),e.eat(93))return!0;e.raise("Unterminated character class")}return!1},Wo.regexp_classRanges=function(e){for(;this.regexp_eatClassAtom(e);){var t=e.lastIntValue;if(e.eat(45)&&this.regexp_eatClassAtom(e)){var i=e.lastIntValue;!e.switchU||-1!==t&&-1!==i||e.raise("Invalid character class"),-1!==t&&-1!==i&&t>i&&e.raise("Range out of order in character class")}}},Wo.regexp_eatClassAtom=function(e){var t=e.pos;if(e.eat(92)){if(this.regexp_eatClassEscape(e))return!0;if(e.switchU){var i=e.current();(99===i||tl(i))&&e.raise("Invalid class escape"),e.raise("Invalid escape")}e.pos=t}var s=e.current();return 93!==s&&(e.lastIntValue=s,e.advance(),!0)},Wo.regexp_eatClassEscape=function(e){var t=e.pos;if(e.eat(98))return e.lastIntValue=8,!0;if(e.switchU&&e.eat(45))return e.lastIntValue=45,!0;if(!e.switchU&&e.eat(99)){if(this.regexp_eatClassControlLetter(e))return!0;e.pos=t}return this.regexp_eatCharacterClassEscape(e)||this.regexp_eatCharacterEscape(e)},Wo.regexp_eatClassControlLetter=function(e){var t=e.current();return!(!Jo(t)&&95!==t||(e.lastIntValue=t%32,e.advance(),0))},Wo.regexp_eatHexEscapeSequence=function(e){var t=e.pos;if(e.eat(120)){if(this.regexp_eatFixedHexDigits(e,2))return!0;e.switchU&&e.raise("Invalid escape"),e.pos=t}return!1},Wo.regexp_eatDecimalDigits=function(e){var t=e.pos,i=0;for(e.lastIntValue=0;Jo(i=e.current());)e.lastIntValue=10*e.lastIntValue+(i-48),e.advance();return e.pos!==t},Wo.regexp_eatHexDigits=function(e){var t=e.pos,i=0;for(e.lastIntValue=0;Zo(i=e.current());)e.lastIntValue=16*e.lastIntValue+el(i),e.advance();return e.pos!==t},Wo.regexp_eatLegacyOctalEscapeSequence=function(e){if(this.regexp_eatOctalDigit(e)){var t=e.lastIntValue;if(this.regexp_eatOctalDigit(e)){var i=e.lastIntValue;t<=3&&this.regexp_eatOctalDigit(e)?e.lastIntValue=64*t+8*i+e.lastIntValue:e.lastIntValue=8*t+i}else e.lastIntValue=t;return!0}return!1},Wo.regexp_eatOctalDigit=function(e){var t=e.current();return tl(t)?(e.lastIntValue=t-48,e.advance(),!0):(e.lastIntValue=0,!1)},Wo.regexp_eatFixedHexDigits=function(e,t){var i=e.pos;e.lastIntValue=0;for(var s=0;s<t;++s){var n=e.current();if(!Zo(n))return e.pos=i,!1;e.lastIntValue=16*e.lastIntValue+el(n),e.advance()}return!0};var il=function(e){this.type=e.type,this.value=e.value,this.start=e.start,this.end=e.end,e.options.locations&&(this.loc=new Ja(e,e.startLoc,e.endLoc)),e.options.ranges&&(this.range=[e.start,e.end])},sl=ro.prototype;function nl(e){return"function"!=typeof BigInt?null:BigInt(e.replace(/_/g,""))}sl.next=function(e){!e&&this.type.keyword&&this.containsEsc&&this.raiseRecoverable(this.start,"Escape sequence in keyword "+this.type.keyword),this.options.onToken&&this.options.onToken(new il(this)),this.lastTokEnd=this.end,this.lastTokStart=this.start,this.lastTokEndLoc=this.endLoc,this.lastTokStartLoc=this.startLoc,this.nextToken()},sl.getToken=function(){return this.next(),new il(this)},"undefined"!=typeof Symbol&&(sl[Symbol.iterator]=function(){var e=this;return{next:function(){var t=e.getToken();return{done:t.type===Da.eof,value:t}}}}),sl.nextToken=function(){var e=this.curContext();return e&&e.preserveSpace||this.skipSpace(),this.start=this.pos,this.options.locations&&(this.startLoc=this.curPosition()),this.pos>=this.input.length?this.finishToken(Da.eof):e.override?e.override(this):void this.readToken(this.fullCharCodeAtPos())},sl.readToken=function(e){return Ca(e,this.options.ecmaVersion>=6)||92===e?this.readWord():this.getTokenFromCode(e)},sl.fullCharCodeAtPos=function(){var e=this.input.charCodeAt(this.pos);if(e<=55295||e>=56320)return e;var t=this.input.charCodeAt(this.pos+1);return t<=56319||t>=57344?e:(e<<10)+t-56613888},sl.skipBlockComment=function(){var e=this.options.onComment&&this.curPosition(),t=this.pos,i=this.input.indexOf("*/",this.pos+=2);if(-1===i&&this.raise(this.pos-2,"Unterminated comment"),this.pos=i+2,this.options.locations)for(var s=void 0,n=t;(s=Fa(this.input,n,this.pos))>-1;)++this.curLine,n=this.lineStart=s;this.options.onComment&&this.options.onComment(!0,this.input.slice(t+2,i),t,this.pos,e,this.curPosition())},sl.skipLineComment=function(e){for(var t=this.pos,i=this.options.onComment&&this.curPosition(),s=this.input.charCodeAt(this.pos+=e);this.pos<this.input.length&&!Ba(s);)s=this.input.charCodeAt(++this.pos);this.options.onComment&&this.options.onComment(!1,this.input.slice(t+e,this.pos),t,this.pos,i,this.curPosition())},sl.skipSpace=function(){e:for(;this.pos<this.input.length;){var e=this.input.charCodeAt(this.pos);switch(e){case 32:case 160:++this.pos;break;case 13:10===this.input.charCodeAt(this.pos+1)&&++this.pos;case 10:case 8232:case 8233:++this.pos,this.options.locations&&(++this.curLine,this.lineStart=this.pos);break;case 47:switch(this.input.charCodeAt(this.pos+1)){case 42:this.skipBlockComment();break;case 47:this.skipLineComment(2);break;default:break e}break;default:if(!(e>8&&e<14||e>=5760&&za.test(String.fromCharCode(e))))break e;++this.pos}}},sl.finishToken=function(e,t){this.end=this.pos,this.options.locations&&(this.endLoc=this.curPosition());var i=this.type;this.type=e,this.value=t,this.updateContext(i)},sl.readToken_dot=function(){var e=this.input.charCodeAt(this.pos+1);if(e>=48&&e<=57)return this.readNumber(!0);var t=this.input.charCodeAt(this.pos+2);return this.options.ecmaVersion>=6&&46===e&&46===t?(this.pos+=3,this.finishToken(Da.ellipsis)):(++this.pos,this.finishToken(Da.dot))},sl.readToken_slash=function(){var e=this.input.charCodeAt(this.pos+1);return this.exprAllowed?(++this.pos,this.readRegexp()):61===e?this.finishOp(Da.assign,2):this.finishOp(Da.slash,1)},sl.readToken_mult_modulo_exp=function(e){var t=this.input.charCodeAt(this.pos+1),i=1,s=42===e?Da.star:Da.modulo;return this.options.ecmaVersion>=7&&42===e&&42===t&&(++i,s=Da.starstar,t=this.input.charCodeAt(this.pos+2)),61===t?this.finishOp(Da.assign,i+1):this.finishOp(s,i)},sl.readToken_pipe_amp=function(e){var t=this.input.charCodeAt(this.pos+1);return t===e?this.options.ecmaVersion>=12&&61===this.input.charCodeAt(this.pos+2)?this.finishOp(Da.assign,3):this.finishOp(124===e?Da.logicalOR:Da.logicalAND,2):61===t?this.finishOp(Da.assign,2):this.finishOp(124===e?Da.bitwiseOR:Da.bitwiseAND,1)},sl.readToken_caret=function(){return 61===this.input.charCodeAt(this.pos+1)?this.finishOp(Da.assign,2):this.finishOp(Da.bitwiseXOR,1)},sl.readToken_plus_min=function(e){var t=this.input.charCodeAt(this.pos+1);return t===e?45!==t||this.inModule||62!==this.input.charCodeAt(this.pos+2)||0!==this.lastTokEnd&&!La.test(this.input.slice(this.lastTokEnd,this.pos))?this.finishOp(Da.incDec,2):(this.skipLineComment(3),this.skipSpace(),this.nextToken()):61===t?this.finishOp(Da.assign,2):this.finishOp(Da.plusMin,1)},sl.readToken_lt_gt=function(e){var t=this.input.charCodeAt(this.pos+1),i=1;return t===e?(i=62===e&&62===this.input.charCodeAt(this.pos+2)?3:2,61===this.input.charCodeAt(this.pos+i)?this.finishOp(Da.assign,i+1):this.finishOp(Da.bitShift,i)):33!==t||60!==e||this.inModule||45!==this.input.charCodeAt(this.pos+2)||45!==this.input.charCodeAt(this.pos+3)?(61===t&&(i=2),this.finishOp(Da.relational,i)):(this.skipLineComment(4),this.skipSpace(),this.nextToken())},sl.readToken_eq_excl=function(e){var t=this.input.charCodeAt(this.pos+1);return 61===t?this.finishOp(Da.equality,61===this.input.charCodeAt(this.pos+2)?3:2):61===e&&62===t&&this.options.ecmaVersion>=6?(this.pos+=2,this.finishToken(Da.arrow)):this.finishOp(61===e?Da.eq:Da.prefix,1)},sl.readToken_question=function(){var e=this.options.ecmaVersion;if(e>=11){var t=this.input.charCodeAt(this.pos+1);if(46===t){var i=this.input.charCodeAt(this.pos+2);if(i<48||i>57)return this.finishOp(Da.questionDot,2)}if(63===t)return e>=12&&61===this.input.charCodeAt(this.pos+2)?this.finishOp(Da.assign,3):this.finishOp(Da.coalesce,2)}return this.finishOp(Da.question,1)},sl.readToken_numberSign=function(){var e=35;if(this.options.ecmaVersion>=13&&(++this.pos,Ca(e=this.fullCharCodeAtPos(),!0)||92===e))return this.finishToken(Da.privateId,this.readWord1());this.raise(this.pos,"Unexpected character '"+Xa(e)+"'")},sl.getTokenFromCode=function(e){switch(e){case 46:return this.readToken_dot();case 40:return++this.pos,this.finishToken(Da.parenL);case 41:return++this.pos,this.finishToken(Da.parenR);case 59:return++this.pos,this.finishToken(Da.semi);case 44:return++this.pos,this.finishToken(Da.comma);case 91:return++this.pos,this.finishToken(Da.bracketL);case 93:return++this.pos,this.finishToken(Da.bracketR);case 123:return++this.pos,this.finishToken(Da.braceL);case 125:return++this.pos,this.finishToken(Da.braceR);case 58:return++this.pos,this.finishToken(Da.colon);case 96:if(this.options.ecmaVersion<6)break;return++this.pos,this.finishToken(Da.backQuote);case 48:var t=this.input.charCodeAt(this.pos+1);if(120===t||88===t)return this.readRadixNumber(16);if(this.options.ecmaVersion>=6){if(111===t||79===t)return this.readRadixNumber(8);if(98===t||66===t)return this.readRadixNumber(2)}case 49:case 50:case 51:case 52:case 53:case 54:case 55:case 56:case 57:return this.readNumber(!1);case 34:case 39:return this.readString(e);case 47:return this.readToken_slash();case 37:case 42:return this.readToken_mult_modulo_exp(e);case 124:case 38:return this.readToken_pipe_amp(e);case 94:return this.readToken_caret();case 43:case 45:return this.readToken_plus_min(e);case 60:case 62:return this.readToken_lt_gt(e);case 61:case 33:return this.readToken_eq_excl(e);case 63:return this.readToken_question();case 126:return this.finishOp(Da.prefix,1);case 35:return this.readToken_numberSign()}this.raise(this.pos,"Unexpected character '"+Xa(e)+"'")},sl.finishOp=function(e,t){var i=this.input.slice(this.pos,this.pos+t);return this.pos+=t,this.finishToken(e,i)},sl.readRegexp=function(){for(var e,t,i=this.pos;;){this.pos>=this.input.length&&this.raise(i,"Unterminated regular expression");var s=this.input.charAt(this.pos);if(La.test(s)&&this.raise(i,"Unterminated regular expression"),e)e=!1;else{if("["===s)t=!0;else if("]"===s&&t)t=!1;else if("/"===s&&!t)break;e="\\"===s}++this.pos}var n=this.input.slice(i,this.pos);++this.pos;var r=this.pos,a=this.readWord1();this.containsEsc&&this.unexpected(r);var o=this.regexpState||(this.regexpState=new qo(this));o.reset(i,n,a),this.validateRegExpFlags(o),this.validateRegExpPattern(o);var l=null;try{l=new RegExp(n,a)}catch(e){}return this.finishToken(Da.regexp,{pattern:n,flags:a,value:l})},sl.readInt=function(e,t,i){for(var s=this.options.ecmaVersion>=12&&void 0===t,n=i&&48===this.input.charCodeAt(this.pos),r=this.pos,a=0,o=0,l=0,h=null==t?1/0:t;l<h;++l,++this.pos){var c=this.input.charCodeAt(this.pos),u=void 0;if(s&&95===c)n&&this.raiseRecoverable(this.pos,"Numeric separator is not allowed in legacy octal numeric literals"),95===o&&this.raiseRecoverable(this.pos,"Numeric separator must be exactly one underscore"),0===l&&this.raiseRecoverable(this.pos,"Numeric separator is not allowed at the first of digits"),o=c;else{if((u=c>=97?c-97+10:c>=65?c-65+10:c>=48&&c<=57?c-48:1/0)>=e)break;o=c,a=a*e+u}}return s&&95===o&&this.raiseRecoverable(this.pos-1,"Numeric separator is not allowed at the last of digits"),this.pos===r||null!=t&&this.pos-r!==t?null:a},sl.readRadixNumber=function(e){var t=this.pos;this.pos+=2;var i=this.readInt(e);return null==i&&this.raise(this.start+2,"Expected number in radix "+e),this.options.ecmaVersion>=11&&110===this.input.charCodeAt(this.pos)?(i=nl(this.input.slice(t,this.pos)),++this.pos):Ca(this.fullCharCodeAtPos())&&this.raise(this.pos,"Identifier directly after number"),this.finishToken(Da.num,i)},sl.readNumber=function(e){var t=this.pos;e||null!==this.readInt(10,void 0,!0)||this.raise(t,"Invalid number");var i=this.pos-t>=2&&48===this.input.charCodeAt(t);i&&this.strict&&this.raise(t,"Invalid number");var s=this.input.charCodeAt(this.pos);if(!i&&!e&&this.options.ecmaVersion>=11&&110===s){var n=nl(this.input.slice(t,this.pos));return++this.pos,Ca(this.fullCharCodeAtPos())&&this.raise(this.pos,"Identifier directly after number"),this.finishToken(Da.num,n)}i&&/[89]/.test(this.input.slice(t,this.pos))&&(i=!1),46!==s||i||(++this.pos,this.readInt(10),s=this.input.charCodeAt(this.pos)),69!==s&&101!==s||i||(43!==(s=this.input.charCodeAt(++this.pos))&&45!==s||++this.pos,null===this.readInt(10)&&this.raise(t,"Invalid number")),Ca(this.fullCharCodeAtPos())&&this.raise(this.pos,"Identifier directly after number");var r,a=(r=this.input.slice(t,this.pos),i?parseInt(r,8):parseFloat(r.replace(/_/g,"")));return this.finishToken(Da.num,a)},sl.readCodePoint=function(){var e;if(123===this.input.charCodeAt(this.pos)){this.options.ecmaVersion<6&&this.unexpected();var t=++this.pos;e=this.readHexChar(this.input.indexOf("}",this.pos)-this.pos),++this.pos,e>1114111&&this.invalidStringToken(t,"Code point out of bounds")}else e=this.readHexChar(4);return e},sl.readString=function(e){for(var t="",i=++this.pos;;){this.pos>=this.input.length&&this.raise(this.start,"Unterminated string constant");var s=this.input.charCodeAt(this.pos);if(s===e)break;92===s?(t+=this.input.slice(i,this.pos),t+=this.readEscapedChar(!1),i=this.pos):8232===s||8233===s?(this.options.ecmaVersion<10&&this.raise(this.start,"Unterminated string constant"),++this.pos,this.options.locations&&(this.curLine++,this.lineStart=this.pos)):(Ba(s)&&this.raise(this.start,"Unterminated string constant"),++this.pos)}return t+=this.input.slice(i,this.pos++),this.finishToken(Da.string,t)};var rl={};sl.tryReadTemplateToken=function(){this.inTemplateElement=!0;try{this.readTmplToken()}catch(e){if(e!==rl)throw e;this.readInvalidTemplateToken()}this.inTemplateElement=!1},sl.invalidStringToken=function(e,t){if(this.inTemplateElement&&this.options.ecmaVersion>=9)throw rl;this.raise(e,t)},sl.readTmplToken=function(){for(var e="",t=this.pos;;){this.pos>=this.input.length&&this.raise(this.start,"Unterminated template");var i=this.input.charCodeAt(this.pos);if(96===i||36===i&&123===this.input.charCodeAt(this.pos+1))return this.pos!==this.start||this.type!==Da.template&&this.type!==Da.invalidTemplate?(e+=this.input.slice(t,this.pos),this.finishToken(Da.template,e)):36===i?(this.pos+=2,this.finishToken(Da.dollarBraceL)):(++this.pos,this.finishToken(Da.backQuote));if(92===i)e+=this.input.slice(t,this.pos),e+=this.readEscapedChar(!0),t=this.pos;else if(Ba(i)){switch(e+=this.input.slice(t,this.pos),++this.pos,i){case 13:10===this.input.charCodeAt(this.pos)&&++this.pos;case 10:e+="\n";break;default:e+=String.fromCharCode(i)}this.options.locations&&(++this.curLine,this.lineStart=this.pos),t=this.pos}else++this.pos}},sl.readInvalidTemplateToken=function(){for(;this.pos<this.input.length;this.pos++)switch(this.input[this.pos]){case"\\":++this.pos;break;case"$":if("{"!==this.input[this.pos+1])break;case"`":return this.finishToken(Da.invalidTemplate,this.input.slice(this.start,this.pos))}this.raise(this.start,"Unterminated template")},sl.readEscapedChar=function(e){var t=this.input.charCodeAt(++this.pos);switch(++this.pos,t){case 110:return"\n";case 114:return"\r";case 120:return String.fromCharCode(this.readHexChar(2));case 117:return Xa(this.readCodePoint());case 116:return"\t";case 98:return"\b";case 118:return"\v";case 102:return"\f";case 13:10===this.input.charCodeAt(this.pos)&&++this.pos;case 10:return this.options.locations&&(this.lineStart=this.pos,++this.curLine),"";case 56:case 57:if(this.strict&&this.invalidStringToken(this.pos-1,"Invalid escape sequence"),e){var i=this.pos-1;return this.invalidStringToken(i,"Invalid escape sequence in template string"),null}default:if(t>=48&&t<=55){var s=this.input.substr(this.pos-1,3).match(/^[0-7]+/)[0],n=parseInt(s,8);return n>255&&(s=s.slice(0,-1),n=parseInt(s,8)),this.pos+=s.length-1,t=this.input.charCodeAt(this.pos),"0"===s&&56!==t&&57!==t||!this.strict&&!e||this.invalidStringToken(this.pos-1-s.length,e?"Octal literal in template string":"Octal literal in strict mode"),String.fromCharCode(n)}return Ba(t)?"":String.fromCharCode(t)}},sl.readHexChar=function(e){var t=this.pos,i=this.readInt(16,e);return null===i&&this.invalidStringToken(t,"Bad character escape sequence"),i},sl.readWord1=function(){this.containsEsc=!1;for(var e="",t=!0,i=this.pos,s=this.options.ecmaVersion>=6;this.pos<this.input.length;){var n=this.fullCharCodeAtPos();if(Na(n,s))this.pos+=n<=65535?1:2;else{if(92!==n)break;this.containsEsc=!0,e+=this.input.slice(i,this.pos);var r=this.pos;117!==this.input.charCodeAt(++this.pos)&&this.invalidStringToken(this.pos,"Expecting Unicode escape sequence \\uXXXX"),++this.pos;var a=this.readCodePoint();(t?Ca:Na)(a,s)||this.invalidStringToken(r,"Invalid Unicode escape"),e+=Xa(a),i=this.pos}t=!1}return e+this.input.slice(i,this.pos)},sl.readWord=function(){var e=this.readWord1(),t=Da.name;return this.keywords.test(e)&&(t=Ra[e]),this.finishToken(t,e)},ro.acorn={Parser:ro,version:"8.7.1",defaultOptions:eo,Position:Qa,SourceLocation:Ja,getLineInfo:Za,Node:No,TokenType:_a,tokTypes:Da,keywordTypes:Ra,TokContext:bo,tokContexts:vo,isIdentifierChar:Na,isIdentifierStart:Ca,Token:il,isNewLine:Ba,lineBreak:La,lineBreakG:Va,nonASCIIwhitespace:za};const al=e=>()=>{fe({code:"NO_FS_IN_BROWSER",message:`Cannot access the file system (via "${e}") when using the browser build of Rollup. Make sure you supply a plugin with custom resolveId and load hooks to Rollup.`,url:"https://rollupjs.org/guide/en/#a-simple-example"})},ol={mkdir:al("fs.mkdir"),readFile:al("fs.readFile"),writeFile:al("fs.writeFile")};async function ll(e,t,i,s,n,r,a,o){const l=await function(e,t,i,s,n,r,a){let o=null,l=null;if(n){o=new Set;for(const i of n)e===i.source&&t===i.importer&&o.add(i.plugin);l=(e,t)=>({...e,resolve:(e,i,{custom:r,isEntry:a,skipSelf:o}=se)=>s(e,i,r,a,o?[...n,{importer:i,plugin:t,source:e}]:n)})}return i.hookFirst("resolveId",[e,t,{custom:r,isEntry:a}],l,o)}(e,t,s,n,r,a,o);return null==l&&al("path.resolve"),l}const hl="at position ",cl="at output position ";function ul(e,t,{hook:i,id:s}={}){return"string"==typeof e&&(e={message:e}),e.code&&e.code!==ge.PLUGIN_ERROR&&(e.pluginCode=e.code),e.code=ge.PLUGIN_ERROR,e.plugin=t,i&&(e.hook=i),s&&(e.id=s),fe(e)}const dl=[{active:!0,deprecated:"resolveAssetUrl",replacement:"resolveFileUrl"}],pl={delete:()=>!1,get(){},has:()=>!1,set(){}};function fl(e){return e.startsWith(hl)||e.startsWith(cl)?fe({code:"ANONYMOUS_PLUGIN_CACHE",message:"A plugin is trying to use the Rollup cache but is not declaring a plugin name or cacheKey."}):fe({code:"DUPLICATE_PLUGIN_NAME",message:`The plugin name ${e} is being used twice in the same build. Plugin names must be distinct or provide a cacheKey (please post an issue to the plugin if you are a plugin user).`})}async function ml(e,t,i,s){const n=t.id,r=[];let a=null===e.map?null:Qr(e.map);const o=e.code;let l=e.ast;const h=[],u=[];let d=!1;const p=()=>d=!0;let f="";const m=e.code;let g;try{g=await i.hookReduceArg0("transform",[m,n],(function(e,i,n){let a,o;if("string"==typeof i)a=i;else{if(!i||"object"!=typeof i)return e;if(t.updateOptions(i),null==i.code)return(i.map||i.ast)&&s(function(e){return{code:ge.NO_TRANSFORM_MAP_OR_AST_WITHOUT_CODE,message:`The plugin "${e}" returned a "map" or "ast" without returning a "code". This will be ignored.`}}(n.name)),e;({code:a,map:o,ast:l}=i)}return null!==o&&r.push(Qr("string"==typeof o?JSON.parse(o):o)||{missing:!0,plugin:n.name}),a}),((e,t)=>{return f=t.name,{...e,addWatchFile(t){h.push(t),e.addWatchFile(t)},cache:d?e.cache:(l=e.cache,g=p,{delete:e=>(g(),l.delete(e)),get:e=>(g(),l.get(e)),has:e=>(g(),l.has(e)),set:(e,t)=>(g(),l.set(e,t))}),emitAsset:(t,i)=>(u.push({name:t,source:i,type:"asset"}),e.emitAsset(t,i)),emitChunk:(t,i)=>(u.push({id:t,name:i&&i.name,type:"chunk"}),e.emitChunk(t,i)),emitFile:e=>(u.push(e),i.emitFile(e)),error:(t,i)=>("string"==typeof t&&(t={message:t}),i&&me(t,i,m,n),t.id=n,t.hook="transform",e.error(t)),getCombinedSourcemap(){const e=function(e,t,i,s,n){return s.length?{version:3,...tr(e,t,i,s,er(n)).traceMappings()}:i}(n,o,a,r,s);return e?(a!==e&&(a=e,r.length=0),new c({...e,file:null,sourcesContent:e.sourcesContent})):new E(o).generateMap({hires:!0,includeContent:!0,source:n})},setAssetSource(){return this.error({code:"INVALID_SETASSETSOURCE",message:"setAssetSource cannot be called in transform for caching reasons. Use emitFile with a source, or call setAssetSource in another hook."})},warn(t,i){"string"==typeof t&&(t={message:t}),i&&me(t,i,m,n),t.id=n,t.hook="transform",e.warn(t)}};var l,g}))}catch(e){ul(e,f,{hook:"transform",id:n})}return d||u.length&&(t.transformFiles=u),{ast:l,code:g,customTransformCache:d,originalCode:o,originalSourcemap:a,sourcemapChain:r,transformDependencies:h}}const gl="resolveDependencies";class yl{constructor(e,t,i,s){this.graph=e,this.modulesById=t,this.options=i,this.pluginDriver=s,this.implicitEntryModules=new Set,this.indexedEntryModules=[],this.latestLoadModulesPromise=Promise.resolve(),this.moduleLoadPromises=new Map,this.modulesWithLoadedDependencies=new Set,this.nextChunkNamePriority=0,this.nextEntryModuleIndex=0,this.resolveId=async(e,t,i,s,n=null)=>this.getResolvedIdWithDefaults(this.getNormalizedResolvedIdWithoutDefaults(!this.options.external(e,t,!1)&&await ll(e,t,this.options.preserveSymlinks,this.pluginDriver,this.resolveId,n,i,"boolean"==typeof s?s:!t),t,e)),this.hasModuleSideEffects=i.treeshake?i.treeshake.moduleSideEffects:()=>!0}async addAdditionalModules(e){const t=this.extendLoadModulesPromise(Promise.all(e.map((e=>this.loadEntryModule(e,!1,void 0,null)))));return await this.awaitLoadModulesPromise(),t}async addEntryModules(e,t){const i=this.nextEntryModuleIndex;this.nextEntryModuleIndex+=e.length;const s=this.nextChunkNamePriority;this.nextChunkNamePriority+=e.length;const n=await this.extendLoadModulesPromise(Promise.all(e.map((({id:e,importer:t})=>this.loadEntryModule(e,!0,t,null)))).then((n=>{for(let r=0;r<n.length;r++){const a=n[r];a.isUserDefinedEntryPoint=a.isUserDefinedEntryPoint||t,El(a,e[r],t,s+r);const o=this.indexedEntryModules.find((e=>e.module===a));o?o.index=Math.min(o.index,i+r):this.indexedEntryModules.push({index:i+r,module:a})}return this.indexedEntryModules.sort((({index:e},{index:t})=>e>t?1:-1)),n})));return await this.awaitLoadModulesPromise(),{entryModules:this.indexedEntryModules.map((({module:e})=>e)),implicitEntryModules:[...this.implicitEntryModules],newEntryModules:n}}async emitChunk({fileName:e,id:t,importer:i,name:s,implicitlyLoadedAfterOneOf:n,preserveSignature:r}){const a={fileName:e||null,id:t,importer:i,name:s||null},o=n?await this.addEntryWithImplicitDependants(a,n):(await this.addEntryModules([a],!1)).newEntryModules[0];return null!=r&&(o.preserveSignature=r),o}async preloadModule(e){return(await this.fetchModule(this.getResolvedIdWithDefaults(e),void 0,!1,!e.resolveDependencies||gl)).info}addEntryWithImplicitDependants(e,t){const i=this.nextChunkNamePriority++;return this.extendLoadModulesPromise(this.loadEntryModule(e.id,!1,e.importer,null).then((async s=>{if(El(s,e,!1,i),!s.info.isEntry){this.implicitEntryModules.add(s);const i=await Promise.all(t.map((t=>this.loadEntryModule(t,!1,e.importer,s.id))));for(const e of i)s.implicitlyLoadedAfter.add(e);for(const e of s.implicitlyLoadedAfter)e.implicitlyLoadedBefore.add(s)}return s})))}async addModuleSource(e,t,i){let s;En("load modules",3);try{s=await this.graph.fileOperationQueue.run((async()=>{var t;return null!==(t=await this.pluginDriver.hookFirst("load",[e]))&&void 0!==t?t:await ol.readFile(e,"utf8")}))}catch(i){bn("load modules",3);let s=`Could not load ${e}`;throw t&&(s+=` (imported by ${ce(t)})`),s+=`: ${i.message}`,i.message=s,i}bn("load modules",3);const n="string"==typeof s?{code:s}:null!=s&&"object"==typeof s&&"string"==typeof s.code?s:fe(function(e){return{code:ge.BAD_LOADER,message:`Error loading ${ce(e)}: plugin load hook should return a string, a { code, map } object, or nothing/null`}}(e)),r=this.graph.cachedModules.get(e);if(!r||r.customTransformCache||r.originalCode!==n.code||await this.pluginDriver.hookFirst("shouldTransformCachedModule",[{ast:r.ast,code:r.code,id:r.id,meta:r.meta,moduleSideEffects:r.moduleSideEffects,resolvedSources:r.resolvedIds,syntheticNamedExports:r.syntheticNamedExports}]))i.updateOptions(n),i.setSource(await ml(n,i,this.pluginDriver,this.options.onwarn));else{if(r.transformFiles)for(const e of r.transformFiles)this.pluginDriver.emitFile(e);i.setSource(r)}}async awaitLoadModulesPromise(){let e;do{e=this.latestLoadModulesPromise,await e}while(e!==this.latestLoadModulesPromise)}extendLoadModulesPromise(e){return this.latestLoadModulesPromise=Promise.all([e,this.latestLoadModulesPromise]),this.latestLoadModulesPromise.catch((()=>{})),e}async fetchDynamicDependencies(e,t){const i=await Promise.all(t.map((t=>t.then((async([t,i])=>null===i?null:"string"==typeof i?(t.resolution=i,null):t.resolution=await this.fetchResolvedDependency(ce(i.id),e.id,i))))));for(const t of i)t&&(e.dynamicDependencies.add(t),t.dynamicImporters.push(e.id))}async fetchModule({id:e,meta:t,moduleSideEffects:i,syntheticNamedExports:s},n,r,a){const o=this.modulesById.get(e);if(o instanceof kn)return await this.handleExistingModule(o,r,a),o;const l=new kn(this.graph,e,this.options,r,i,s,t);this.modulesById.set(e,l),this.graph.watchFiles[e]=!0;const h=this.addModuleSource(e,n,l).then((()=>[this.getResolveStaticDependencyPromises(l),this.getResolveDynamicImportPromises(l),c])),c=vl(h).then((()=>this.pluginDriver.hookParallel("moduleParsed",[l.info])));c.catch((()=>{})),this.moduleLoadPromises.set(l,h);const u=await h;return a?a===gl&&await c:await this.fetchModuleDependencies(l,...u),l}async fetchModuleDependencies(e,t,i,s){this.modulesWithLoadedDependencies.has(e)||(this.modulesWithLoadedDependencies.add(e),await Promise.all([this.fetchStaticDependencies(e,t),this.fetchDynamicDependencies(e,i)]),e.linkImports(),await s)}fetchResolvedDependency(e,t,i){if(i.external){const{external:s,id:n,moduleSideEffects:r,meta:a}=i;this.modulesById.has(n)||this.modulesById.set(n,new Te(this.options,n,r,a,"absolute"!==s&&w(n)));const o=this.modulesById.get(n);return o instanceof Te?Promise.resolve(o):fe(function(e,t){return{code:ge.INVALID_EXTERNAL_ID,message:`'${e}' is imported as an external by ${ce(t)}, but is already an existing non-external module id.`}}(e,t))}return this.fetchModule(i,t,!1,!1)}async fetchStaticDependencies(e,t){for(const i of await Promise.all(t.map((t=>t.then((([t,i])=>this.fetchResolvedDependency(t,e.id,i)))))))e.dependencies.add(i),i.importers.push(e.id);if(!this.options.treeshake||"no-treeshake"===e.info.moduleSideEffects)for(const t of e.dependencies)t instanceof kn&&(t.importedFromNotTreeshaken=!0)}getNormalizedResolvedIdWithoutDefaults(e,t,i){const{makeAbsoluteExternalsRelative:s}=this.options;if(e){if("object"==typeof e){const n=e.external||this.options.external(e.id,t,!0);return{...e,external:n&&("relative"===n||!w(e.id)||!0===n&&bl(e.id,i,s)||"absolute")}}const n=this.options.external(e,t,!0);return{external:n&&(bl(e,i,s)||"absolute"),id:n&&s?xl(e,t):e}}const n=s?xl(i,t):i;return!1===e||this.options.external(n,t,!0)?{external:bl(n,i,s)||"absolute",id:n}:null}getResolveDynamicImportPromises(e){return e.dynamicImports.map((async t=>{const i=await this.resolveDynamicImport(e,"string"==typeof t.argument?t.argument:t.argument.esTreeNode,e.id);return i&&"object"==typeof i&&(t.id=i.id),[t,i]}))}getResolveStaticDependencyPromises(e){return Array.from(e.sources,(async t=>[t,e.resolvedIds[t]=e.resolvedIds[t]||this.handleResolveId(await this.resolveId(t,e.id,ne,!1),t,e.id)]))}getResolvedIdWithDefaults(e){var t,i;if(!e)return null;const s=e.external||!1;return{external:s,id:e.id,meta:e.meta||{},moduleSideEffects:null!==(t=e.moduleSideEffects)&&void 0!==t?t:this.hasModuleSideEffects(e.id,!!s),syntheticNamedExports:null!==(i=e.syntheticNamedExports)&&void 0!==i&&i}}async handleExistingModule(e,t,i){const s=this.moduleLoadPromises.get(e);if(i)return i===gl?vl(s):s;if(t){e.info.isEntry=!0,this.implicitEntryModules.delete(e);for(const t of e.implicitlyLoadedAfter)t.implicitlyLoadedBefore.delete(e);e.implicitlyLoadedAfter.clear()}return this.fetchModuleDependencies(e,...await s)}handleResolveId(e,t,i){return null===e?C(t)?fe(function(e,t){return{code:ge.UNRESOLVED_IMPORT,message:`Could not resolve '${e}' from ${ce(t)}`}}(t,i)):(this.options.onwarn(function(e,t){return{code:ge.UNRESOLVED_IMPORT,importer:ce(t),message:`'${e}' is imported by ${ce(t)}, but could not be resolved – treating it as an external dependency`,source:e,url:"https://rollupjs.org/guide/en/#warning-treating-module-as-external-dependency"}}(t,i)),{external:!0,id:t,meta:{},moduleSideEffects:this.hasModuleSideEffects(t,!0),syntheticNamedExports:!1}):(e.external&&e.syntheticNamedExports&&this.options.onwarn(function(e,t){return{code:ge.EXTERNAL_SYNTHETIC_EXPORTS,importer:ce(t),message:`External '${e}' can not have 'syntheticNamedExports' enabled.`,source:e}}(t,i)),e)}async loadEntryModule(e,t,i,s){const n=await ll(e,i,this.options.preserveSymlinks,this.pluginDriver,this.resolveId,null,ne,!0);return null==n?fe(null===s?function(e){return{code:ge.UNRESOLVED_ENTRY,message:`Could not resolve entry module (${ce(e)}).`}}(e):function(e,t){return{code:ge.MISSING_IMPLICIT_DEPENDANT,message:`Module "${ce(e)}" that should be implicitly loaded before "${ce(t)}" could not be resolved.`}}(e,s)):!1===n||"object"==typeof n&&n.external?fe(null===s?function(e){return{code:ge.UNRESOLVED_ENTRY,message:`Entry module cannot be external (${ce(e)}).`}}(e):function(e,t){return{code:ge.MISSING_IMPLICIT_DEPENDANT,message:`Module "${ce(e)}" that should be implicitly loaded before "${ce(t)}" cannot be external.`}}(e,s)):this.fetchModule(this.getResolvedIdWithDefaults("object"==typeof n?n:{id:n}),void 0,t,!1)}async resolveDynamicImport(e,t,i){var s,n;const r=await this.pluginDriver.hookFirst("resolveDynamicImport",[t,i]);return"string"!=typeof t?"string"==typeof r?r:r?{external:!1,moduleSideEffects:!0,...r}:null:null==r?null!==(s=(n=e.resolvedIds)[t])&&void 0!==s?s:n[t]=this.handleResolveId(await this.resolveId(t,e.id,ne,!1),t,e.id):this.handleResolveId(this.getResolvedIdWithDefaults(this.getNormalizedResolvedIdWithoutDefaults(r,i,t)),t,i)}}function xl(e,t){return C(e)?t?R(t,"..",e):R(e):e}function El(e,{fileName:t,name:i},s,n){var r;if(null!==t)e.chunkFileNames.add(t);else if(null!==i){let t=0;for(;(null===(r=e.chunkNames[t])||void 0===r?void 0:r.priority)<n;)t++;e.chunkNames.splice(t,0,{isUserDefined:s,name:i,priority:n})}}function bl(e,t,i){return!0===i||"ifRelativeSource"===i&&C(t)||!w(e)}async function vl(e){const[t,i]=await e;return Promise.all([...t,...i])}class Sl extends Xt{constructor(){super(),this.parent=null,this.variables.set("undefined",new Qs)}findVariable(e){let t=this.variables.get(e);return t||(t=new di(e),this.variables.set(e,t)),t}}function Al(e,t,i){const s=e.toLowerCase();t[Jr].has(s)?i(function(e){return{code:ge.FILE_NAME_CONFLICT,message:`The emitted file "${e}" overwrites a previously emitted file of the same name.`}}(e)):t[e]=Zr}function Il(e,t,i){if(!("string"==typeof e||e instanceof Uint8Array)){const e=t.fileName||t.name||i;return fe(Ie(`Could not set source for ${"string"==typeof e?`asset "${e}"`:"unnamed asset"}, asset source needs to be a string, Uint8Array or Buffer.`))}return e}function Pl(e,t){return"string"!=typeof e.fileName?fe((i=e.name||t,{code:ge.ASSET_NOT_FINALISED,message:`Plugin error - Unable to get file name for asset "${i}". Ensure that the source is set and that generate is called first.`})):e.fileName;var i}function kl(e,t){var i;const s=e.fileName||e.module&&(null===(i=null==t?void 0:t.get(e.module))||void 0===i?void 0:i.id);return s||fe((n=e.fileName||e.name,{code:ge.CHUNK_NOT_GENERATED,message:`Plugin error - Unable to get file name for chunk "${n}". Ensure that generate is called first.`}));var n}class wl{constructor(e,t,i){this.graph=e,this.options=t,this.bundle=null,this.facadeChunkByModule=null,this.outputOptions=null,this.assertAssetsFinalized=()=>{for(const[t,i]of this.filesByReferenceId)if("asset"===i.type&&"string"!=typeof i.fileName)return fe((e=i.name||t,{code:ge.ASSET_SOURCE_MISSING,message:`Plugin error creating asset "${e}" - no asset source set.`}));var e},this.emitFile=e=>function(e){return Boolean(e&&("asset"===e.type||"chunk"===e.type))}(e)?function(e){const t=e.fileName||e.name;return!t||"string"==typeof t&&!ue(t)}(e)?"chunk"===e.type?this.emitChunk(e):this.emitAsset(e):fe(Ie(`The "fileName" or "name" properties of emitted files must be strings that are neither absolute nor relative paths, received "${e.fileName||e.name}".`)):fe(Ie(`Emitted files must be of type "asset" or "chunk", received "${e&&e.type}".`)),this.getFileName=e=>{const t=this.filesByReferenceId.get(e);return t?"chunk"===t.type?kl(t,this.facadeChunkByModule):Pl(t,e):fe((i=e,{code:ge.FILE_NOT_FOUND,message:`Plugin error - Unable to get file name for unknown file "${i}".`}));var i},this.setAssetSource=(e,t)=>{const i=this.filesByReferenceId.get(e);if(!i)return fe((s=e,{code:ge.ASSET_NOT_FOUND,message:`Plugin error - Unable to set the source for unknown asset "${s}".`}));var s,n;if("asset"!==i.type)return fe(Ie(`Asset sources can only be set for emitted assets but "${e}" is an emitted chunk.`));if(void 0!==i.source)return fe((n=i.name||e,{code:ge.ASSET_SOURCE_ALREADY_SET,message:`Unable to set the source for asset "${n}", source already set.`}));const r=Il(t,i,e);this.bundle?this.finalizeAsset(i,r,e,this.bundle):i.source=r},this.setOutputBundle=(e,t,i)=>{this.outputOptions=t,this.bundle=e,this.facadeChunkByModule=i;for(const{fileName:t}of this.filesByReferenceId.values())t&&Al(t,e,this.options.onwarn);for(const[t,i]of this.filesByReferenceId)"asset"===i.type&&void 0!==i.source&&this.finalizeAsset(i,i.source,t,e)},this.filesByReferenceId=i?new Map(i.filesByReferenceId):new Map}assignReferenceId(e,t){let i;do{i=Br().update(i||t).digest("hex").substring(0,8)}while(this.filesByReferenceId.has(i));return this.filesByReferenceId.set(i,e),i}emitAsset(e){const t=void 0!==e.source?Il(e.source,e,null):void 0,i={fileName:e.fileName,name:e.name,source:t,type:"asset"},s=this.assignReferenceId(i,e.fileName||e.name||e.type);return this.bundle&&(e.fileName&&Al(e.fileName,this.bundle,this.options.onwarn),void 0!==t&&this.finalizeAsset(i,t,s,this.bundle)),s}emitChunk(e){if(this.graph.phase>hn.LOAD_AND_PARSE)return fe({code:ge.INVALID_ROLLUP_PHASE,message:"Cannot emit chunks after module loading has finished."});if("string"!=typeof e.id)return fe(Ie(`Emitted chunks need to have a valid string id, received "${e.id}"`));const t={fileName:e.fileName,module:null,name:e.name||e.id,type:"chunk"};return this.graph.moduleLoader.emitChunk(e).then((e=>t.module=e)).catch((()=>{})),this.assignReferenceId(t,e.id)}finalizeAsset(e,t,i,s){const n=e.fileName||function(e,t){for(const[i,s]of Object.entries(e))if("asset"===s.type&&Cl(t,s.source))return i;return null}(s,t)||function(e,t,i,s){const n=i.sanitizeFileName(e||"asset");return ta(ea("function"==typeof i.assetFileNames?i.assetFileNames({name:e,source:t,type:"asset"}):i.assetFileNames,"output.assetFileNames",{ext:()=>T(n).substring(1),extname:()=>T(n),hash:()=>Br().update(n).update(":").update(t).digest("hex").substring(0,8),name:()=>n.substring(0,n.length-T(n).length)}),s)}(e.name,t,this.outputOptions,s),r={...e,fileName:n,source:t};this.filesByReferenceId.set(i,r);const{options:a}=this;s[n]={fileName:n,get isAsset(){return ke('Accessing "isAsset" on files in the bundle is deprecated, please use "type === \'asset\'" instead',!0,a),!0},name:e.name,source:t,type:"asset"}}}function Cl(e,t){if("string"==typeof e)return e===t;if("string"==typeof t)return!1;if("equals"in e)return e.equals(t);if(e.length!==t.length)return!1;for(let i=0;i<e.length;i++)if(e[i]!==t[i])return!1;return!0}function Nl(e,t,i,s,n,r){let a=!1;return(...o)=>(a||(a=!0,ke({message:`The "this.${t}" plugin context function used by plugin ${s} is deprecated. The "this.${i}" plugin context function should be used instead.`,plugin:s},n,r)),e(...o))}function _l(e,i,s,n,r,a){let o,l=!0;if("string"!=typeof e.cacheKey&&(e.name.startsWith(hl)||e.name.startsWith(cl)||a.has(e.name)?l=!1:a.add(e.name)),i)if(l){const t=e.cacheKey||e.name;c=i[t]||(i[t]=Object.create(null)),o={delete:e=>delete c[e],get(e){const t=c[e];if(t)return t[0]=0,t[1]},has(e){const t=c[e];return!!t&&(t[0]=0,!0)},set(e,t){c[e]=[0,t]}}}else h=e.name,o={delete:()=>fl(h),get:()=>fl(h),has:()=>fl(h),set:()=>fl(h)};else o=pl;var h,c;return{addWatchFile(e){if(s.phase>=hn.GENERATE)return this.error({code:ge.INVALID_ROLLUP_PHASE,message:"Cannot call addWatchFile after the build has finished."});s.watchFiles[e]=!0},cache:o,emitAsset:Nl(((e,t)=>r.emitFile({name:e,source:t,type:"asset"})),"emitAsset","emitFile",e.name,!0,n),emitChunk:Nl(((e,t)=>r.emitFile({id:e,name:t&&t.name,type:"chunk"})),"emitChunk","emitFile",e.name,!0,n),emitFile:r.emitFile.bind(r),error:t=>ul(t,e.name),getAssetFileName:Nl(r.getFileName,"getAssetFileName","getFileName",e.name,!0,n),getChunkFileName:Nl(r.getFileName,"getChunkFileName","getFileName",e.name,!0,n),getFileName:r.getFileName,getModuleIds:()=>s.modulesById.keys(),getModuleInfo:s.getModuleInfo,getWatchFiles:()=>Object.keys(s.watchFiles),isExternal:Nl(((e,t,i=!1)=>n.external(e,t,i)),"isExternal","resolve",e.name,!0,n),load:e=>s.moduleLoader.preloadModule(e),meta:{rollupVersion:t,watchMode:s.watchMode},get moduleIds(){const t=s.modulesById.keys();return function*(){ke({message:`Accessing "this.moduleIds" on the plugin context by plugin ${e.name} is deprecated. The "this.getModuleIds" plugin context function should be used instead.`,plugin:e.name},!1,n),yield*t}()},parse:s.contextParse.bind(s),resolve:(t,i,{custom:n,isEntry:r,skipSelf:a}=se)=>s.moduleLoader.resolveId(t,i,n,r,a?[{importer:i,plugin:e,source:t}]:null),resolveId:Nl(((e,t)=>s.moduleLoader.resolveId(e,t,se,void 0).then((e=>e&&e.id))),"resolveId","resolve",e.name,!0,n),setAssetSource:r.setAssetSource,warn(t){"string"==typeof t&&(t={message:t}),t.code&&(t.pluginCode=t.code),t.code="PLUGIN_WARNING",t.plugin=e.name,n.onwarn(t)}}}const $l=Object.keys({buildEnd:1,buildStart:1,closeBundle:1,closeWatcher:1,load:1,moduleParsed:1,options:1,resolveDynamicImport:1,resolveId:1,shouldTransformCachedModule:1,transform:1,watchChange:1});class Tl{constructor(e,t,i,s,n){this.graph=e,this.options=t,this.pluginCache=s,this.sortedPlugins=new Map,this.unfulfilledActions=new Set,function(e,t){for(const{active:i,deprecated:s,replacement:n}of dl)for(const r of e)s in r&&ke({message:`The "${s}" hook used by plugin ${r.name} is deprecated. The "${n}" hook should be used instead.`,plugin:r.name},i,t)}(i,t),this.fileEmitter=new wl(e,t,n&&n.fileEmitter),this.emitFile=this.fileEmitter.emitFile.bind(this.fileEmitter),this.getFileName=this.fileEmitter.getFileName.bind(this.fileEmitter),this.finaliseAssets=this.fileEmitter.assertAssetsFinalized.bind(this.fileEmitter),this.setOutputBundle=this.fileEmitter.setOutputBundle.bind(this.fileEmitter),this.plugins=i.concat(n?n.plugins:[]);const r=new Set;if(this.pluginContexts=new Map(this.plugins.map((i=>[i,_l(i,s,e,t,this.fileEmitter,r)]))),n)for(const e of i)for(const i of $l)i in e&&t.onwarn((a=e.name,o=i,{code:ge.INPUT_HOOK_IN_OUTPUT_PLUGIN,message:`The "${o}" hook used by the output plugin ${a} is a build time hook and will not be run for that plugin. Either this plugin cannot be used as an output plugin, or it should have an option to configure it as an output plugin.`}));var a,o}createOutputPluginDriver(e){return new Tl(this.graph,this.options,e,this.pluginCache,this)}getUnfulfilledHookActions(){return this.unfulfilledActions}hookFirst(e,t,i,s){let n=Promise.resolve(null);for(const r of this.getSortedPlugins(e))s&&s.has(r)||(n=n.then((s=>null!=s?s:this.runHook(e,t,r,i))));return n}hookFirstSync(e,t,i){for(const s of this.getSortedPlugins(e)){const n=this.runHookSync(e,t,s,i);if(null!=n)return n}return null}async hookParallel(e,t,i){const s=[];for(const n of this.getSortedPlugins(e))n[e].sequential?(await Promise.all(s),s.length=0,await this.runHook(e,t,n,i)):s.push(this.runHook(e,t,n,i));await Promise.all(s)}hookReduceArg0(e,[t,...i],s,n){let r=Promise.resolve(t);for(const t of this.getSortedPlugins(e))r=r.then((r=>this.runHook(e,[r,...i],t,n).then((e=>s.call(this.pluginContexts.get(t),r,e,t)))));return r}hookReduceArg0Sync(e,[t,...i],s,n){for(const r of this.getSortedPlugins(e)){const a=[t,...i],o=this.runHookSync(e,a,r,n);t=s.call(this.pluginContexts.get(r),t,o,r)}return t}async hookReduceValue(e,t,i,s){const n=[],r=[];for(const t of this.getSortedPlugins(e,Ml))t[e].sequential?(n.push(...await Promise.all(r)),r.length=0,n.push(await this.runHook(e,i,t))):r.push(this.runHook(e,i,t));return n.push(...await Promise.all(r)),n.reduce(s,await t)}hookReduceValueSync(e,t,i,s,n){let r=t;for(const t of this.getSortedPlugins(e)){const a=this.runHookSync(e,i,t,n);r=s.call(this.pluginContexts.get(t),r,a,t)}return r}hookSeq(e,t,i){let s=Promise.resolve();for(const n of this.getSortedPlugins(e))s=s.then((()=>this.runHook(e,t,n,i)));return s.then(Dl)}getSortedPlugins(e,t){return M(this.sortedPlugins,e,(()=>Ol(e,this.plugins,t)))}runHook(e,t,i,s){const n=i[e],r="object"==typeof n?n.handler:n;let a=this.pluginContexts.get(i);s&&(a=s(a,i));let o=null;return Promise.resolve().then((()=>{if("function"!=typeof r)return r;const s=r.apply(a,t);return(null==s?void 0:s.then)?(o=[i.name,e,t],this.unfulfilledActions.add(o),Promise.resolve(s).then((e=>(this.unfulfilledActions.delete(o),e)))):s})).catch((t=>(null!==o&&this.unfulfilledActions.delete(o),ul(t,i.name,{hook:e}))))}runHookSync(e,t,i,s){const n=i[e],r="object"==typeof n?n.handler:n;let a=this.pluginContexts.get(i);s&&(a=s(a,i));try{return r.apply(a,t)}catch(t){return ul(t,i.name,{hook:e})}}}function Ol(e,t,i=Rl){const s=[],n=[],r=[];for(const a of t){const t=a[e];if(t){if("object"==typeof t){if(i(t.handler,e,a),"pre"===t.order){s.push(a);continue}if("post"===t.order){r.push(a);continue}}else i(t,e,a);n.push(a)}}return[...s,...n,...r]}function Rl(e,t,i){"function"!=typeof e&&fe(function(e,t){return{code:ge.INVALID_PLUGIN_HOOK,hook:e,message:`Error running plugin hook ${e} for plugin ${t}, expected a function hook or an object with a "handler" function.`,plugin:t}}(t,i.name))}function Ml(e,t,i){if("string"!=typeof e&&"function"!=typeof e)return fe(function(e,t){return{code:ge.INVALID_PLUGIN_HOOK,hook:e,message:`Error running plugin hook ${e} for plugin ${t}, expected a string, a function hook or an object with a "handler" string or function.`,plugin:t}}(t,i.name))}function Dl(){}class Ll{constructor(e){this.maxParallel=e,this.queue=[],this.workerCount=0}run(e){return new Promise(((t,i)=>{this.queue.push({reject:i,resolve:t,task:e}),this.work()}))}async work(){if(this.workerCount>=this.maxParallel)return;let e;for(this.workerCount++;e=this.queue.shift();){const{reject:t,resolve:i,task:s}=e;try{i(await s())}catch(e){t(e)}}this.workerCount--}}class Vl{constructor(e,t){var i,s;if(this.options=e,this.cachedModules=new Map,this.deoptimizationTracker=new G,this.entryModules=[],this.modulesById=new Map,this.needsTreeshakingPass=!1,this.phase=hn.LOAD_AND_PARSE,this.scope=new Sl,this.watchFiles=Object.create(null),this.watchMode=!1,this.externalModules=[],this.implicitEntryModules=[],this.modules=[],this.getModuleInfo=e=>{const t=this.modulesById.get(e);return t?t.info:null},!1!==e.cache){if(null===(i=e.cache)||void 0===i?void 0:i.modules)for(const t of e.cache.modules)this.cachedModules.set(t.id,t);this.pluginCache=(null===(s=e.cache)||void 0===s?void 0:s.plugins)||Object.create(null);for(const e in this.pluginCache){const t=this.pluginCache[e];for(const e of Object.values(t))e[0]++}}if(t){this.watchMode=!0;const e=(...e)=>this.pluginDriver.hookParallel("watchChange",e),i=()=>this.pluginDriver.hookParallel("closeWatcher",[]);t.onCurrentAwaited("change",e),t.onCurrentAwaited("close",i)}this.pluginDriver=new Tl(this,e,e.plugins,this.pluginCache),this.acornParser=ro.extend(...e.acornInjectPlugins),this.moduleLoader=new yl(this,this.modulesById,this.options,this.pluginDriver),this.fileOperationQueue=new Ll(e.maxParallelFileOps)}async build(){En("generate module graph",2),await this.generateModuleGraph(),bn("generate module graph",2),En("sort modules",2),this.phase=hn.ANALYSE,this.sortModules(),bn("sort modules",2),En("mark included statements",2),this.includeStatements(),bn("mark included statements",2),this.phase=hn.GENERATE}contextParse(e,t={}){const i=t.onComment,s=[];t.onComment=i&&"function"==typeof i?(e,n,r,a,...o)=>(s.push({end:a,start:r,type:e?"Block":"Line",value:n}),i.call(t,e,n,r,a,...o)):s;const n=this.acornParser.parse(e,{...this.options.acorn,...t});return"object"==typeof i&&i.push(...s),t.onComment=i,function(e,t,i){const s=[],n=[];for(const t of e)yt.test(t.value)?s.push(t):ht.test(t.value)&&n.push(t);for(const e of n)xt(t,e,!1);dt(t,{annotationIndex:0,annotations:s,code:i})}(s,n,e),n}getCache(){for(const e in this.pluginCache){const t=this.pluginCache[e];let i=!0;for(const[e,s]of Object.entries(t))s[0]>=this.options.experimentalCacheExpiry?delete t[e]:i=!1;i&&delete this.pluginCache[e]}return{modules:this.modules.map((e=>e.toJSON())),plugins:this.pluginCache}}async generateModuleGraph(){var e;if(({entryModules:this.entryModules,implicitEntryModules:this.implicitEntryModules}=await this.moduleLoader.addEntryModules((e=this.options.input,Array.isArray(e)?e.map((e=>({fileName:null,id:e,implicitlyLoadedAfter:[],importer:void 0,name:null}))):Object.entries(e).map((([e,t])=>({fileName:null,id:t,implicitlyLoadedAfter:[],importer:void 0,name:e})))),!0)),0===this.entryModules.length)throw new Error("You must supply options.input to rollup");for(const e of this.modulesById.values())e instanceof kn?this.modules.push(e):this.externalModules.push(e)}includeStatements(){for(const e of[...this.entryModules,...this.implicitEntryModules])An(e);if(this.options.treeshake){let e=1;do{En(`treeshaking pass ${e}`,3),this.needsTreeshakingPass=!1;for(const e of this.modules)e.isExecuted&&("no-treeshake"===e.info.moduleSideEffects?e.includeAllInBundle():e.include());if(1===e)for(const e of[...this.entryModules,...this.implicitEntryModules])!1!==e.preserveSignature&&(e.includeAllExports(!1),this.needsTreeshakingPass=!0);bn("treeshaking pass "+e++,3)}while(this.needsTreeshakingPass)}else for(const e of this.modules)e.includeAllInBundle();for(const e of this.externalModules)e.warnUnusedImports();for(const e of this.implicitEntryModules)for(const t of e.implicitlyLoadedAfter)t.info.isEntry||t.isIncluded()||fe(ve(t))}sortModules(){const{orderedModules:e,cyclePaths:t}=function(e){let t=0;const i=[],s=new Set,n=new Set,r=new Map,a=[],o=e=>{if(e instanceof kn){for(const t of e.dependencies)r.has(t)?s.has(t)||i.push(da(t,e,r)):(r.set(t,e),o(t));for(const t of e.implicitlyLoadedBefore)n.add(t);for(const{resolution:t}of e.dynamicImports)t instanceof kn&&n.add(t);a.push(e)}e.execIndex=t++,s.add(e)};for(const t of e)r.has(t)||(r.set(t,null),o(t));for(const e of n)r.has(e)||(r.set(e,null),o(e));return{cyclePaths:i,orderedModules:a}}(this.entryModules);for(const e of t)this.options.onwarn({code:"CIRCULAR_DEPENDENCY",cycle:e,importer:e[0],message:`Circular dependency: ${e.join(" -> ")}`});this.modules=e;for(const e of this.modules)e.bindReferences();this.warnForMissingExports()}warnForMissingExports(){for(const e of this.modules)for(const t of e.importDescriptions.values())"*"===t.name||t.module.getVariableForExportName(t.name)[0]||e.warn({code:"NON_EXISTENT_EXPORT",message:`Non-existent export '${t.name}' is imported from ${ce(t.module.id)}`,name:t.name,source:t.module.id},t.start)}}function Bl(e){return Array.isArray(e)?e.filter(Boolean):e?[e]:[]}function Fl(e,t){return t()}const zl=e=>console.warn(e.message||e);function jl(e,t,i,s,n=/$./){const r=new Set(t),a=Object.keys(e).filter((e=>!(r.has(e)||n.test(e))));a.length>0&&s({code:"UNKNOWN_OPTION",message:`Unknown ${i}: ${a.join(", ")}. Allowed options: ${[...r].sort().join(", ")}`})}const Ul={recommended:{annotations:!0,correctVarValueBeforeDeclaration:!1,moduleSideEffects:()=>!0,propertyReadSideEffects:!0,tryCatchDeoptimization:!0,unknownGlobalSideEffects:!1},safest:{annotations:!0,correctVarValueBeforeDeclaration:!0,moduleSideEffects:()=>!0,propertyReadSideEffects:!0,tryCatchDeoptimization:!0,unknownGlobalSideEffects:!0},smallest:{annotations:!0,correctVarValueBeforeDeclaration:!1,moduleSideEffects:()=>!1,propertyReadSideEffects:!1,tryCatchDeoptimization:!1,unknownGlobalSideEffects:!1}},Gl={es2015:{arrowFunctions:!0,constBindings:!0,objectShorthand:!0,reservedNamesAsProps:!0,symbols:!0},es5:{arrowFunctions:!1,constBindings:!1,objectShorthand:!1,reservedNamesAsProps:!0,symbols:!1}},Hl=(e,t,i,s)=>{const n=null==e?void 0:e.preset;if(n){const s=t[n];if(s)return{...s,...e};fe(Ee(`${i}.preset`,Wl(i),`valid values are ${le(Object.keys(t))}`,n))}return((e,t,i)=>s=>{if("string"==typeof s){const n=e[s];if(n)return n;fe(Ee(t,Wl(t),`valid values are ${i}${le(Object.keys(e))}. You can also supply an object for more fine-grained control`,s))}return(e=>e&&"object"==typeof e?e:{})(s)})(t,i,s)(e)},Wl=e=>e.split(".").join("").toLowerCase(),ql=e=>{const{onwarn:t}=e;return t?e=>{e.toString=()=>{let t="";return e.plugin&&(t+=`(${e.plugin} plugin) `),e.loc&&(t+=`${ce(e.loc.file)} (${e.loc.line}:${e.loc.column}) `),t+=e.message,t},t(e,zl)}:zl},Kl=e=>({allowAwaitOutsideFunction:!0,ecmaVersion:"latest",preserveParens:!1,sourceType:"module",...e.acorn}),Xl=e=>Bl(e.acornInjectPlugins),Yl=e=>{var t;return(null===(t=e.cache)||void 0===t?void 0:t.cache)||e.cache},Ql=e=>{if(!0===e)return()=>!0;if("function"==typeof e)return(t,...i)=>!t.startsWith("\0")&&e(t,...i)||!1;if(e){const t=new Set,i=[];for(const s of Bl(e))s instanceof RegExp?i.push(s):t.add(s);return(e,...s)=>t.has(e)||i.some((t=>t.test(e)))}return()=>!1},Jl=(e,t,i)=>{const s=e.inlineDynamicImports;return s&&we('The "inlineDynamicImports" option is deprecated. Use the "output.inlineDynamicImports" option instead.',!1,t,i),s},Zl=e=>{const t=e.input;return null==t?[]:"string"==typeof t?[t]:t},eh=(e,t,i)=>{const s=e.manualChunks;return s&&we('The "manualChunks" option is deprecated. Use the "output.manualChunks" option instead.',!1,t,i),s},th=(e,t,i)=>{var s;const n=e.maxParallelFileReads;"number"==typeof n&&we('The "maxParallelFileReads" option is deprecated. Use the "maxParallelFileOps" option instead.',!1,t,i);const r=null!==(s=e.maxParallelFileOps)&&void 0!==s?s:n;return"number"==typeof r?r<=0?1/0:r:20},ih=(e,t)=>{const i=e.moduleContext;if("function"==typeof i)return e=>{var s;return null!==(s=i(e))&&void 0!==s?s:t};if(i){const e=Object.create(null);for(const[t,s]of Object.entries(i))e[R(t)]=s;return i=>e[i]||t}return()=>t},sh=(e,t)=>{const i=e.preserveEntrySignatures;return null==i&&t.add("preserveEntrySignatures"),null!=i?i:"strict"},nh=(e,t,i)=>{const s=e.preserveModules;return s&&we('The "preserveModules" option is deprecated. Use the "output.preserveModules" option instead.',!1,t,i),s},rh=(e,t,i)=>{const s=e.treeshake;if(!1===s)return!1;const n=Hl(e.treeshake,Ul,"treeshake","false, true, ");return void 0!==n.pureExternalModules&&we('The "treeshake.pureExternalModules" option is deprecated. The "treeshake.moduleSideEffects" option should be used instead. "treeshake.pureExternalModules: true" is equivalent to "treeshake.moduleSideEffects: \'no-external\'"',!0,t,i),{annotations:!1!==n.annotations,correctVarValueBeforeDeclaration:!0===n.correctVarValueBeforeDeclaration,moduleSideEffects:"object"==typeof s&&s.pureExternalModules?ah(s.moduleSideEffects,s.pureExternalModules):ah(n.moduleSideEffects,void 0),propertyReadSideEffects:"always"===n.propertyReadSideEffects?"always":!1!==n.propertyReadSideEffects,tryCatchDeoptimization:!1!==n.tryCatchDeoptimization,unknownGlobalSideEffects:!1!==n.unknownGlobalSideEffects}},ah=(e,t)=>{if("boolean"==typeof e)return()=>e;if("no-external"===e)return(e,t)=>!t;if("function"==typeof e)return(t,i)=>!!t.startsWith("\0")||!1!==e(t,i);if(Array.isArray(e)){const t=new Set(e);return e=>t.has(e)}e&&fe(Ee("treeshake.moduleSideEffects","treeshake",'please use one of false, "no-external", a function or an array'));const i=Ql(t);return(e,t)=>!(t&&i(e))},oh=/[\x00-\x1F\x7F<>*#"{}|^[\]`;?:&=+$,]/g,lh=/^[a-z]:/i;function hh(e){const t=lh.exec(e),i=t?t[0]:"";return i+e.substr(i.length).replace(oh,"_")}const ch=(e,t,i)=>{const{file:s}=e;if("string"==typeof s){if(t)return fe(Ee("output.file","outputdir",'you must set "output.dir" instead of "output.file" when using the "output.preserveModules" option'));if(!Array.isArray(i.input))return fe(Ee("output.file","outputdir",'you must set "output.dir" instead of "output.file" when providing named inputs'))}return s},uh=e=>{const t=e.format;switch(t){case void 0:case"es":case"esm":case"module":return"es";case"cjs":case"commonjs":return"cjs";case"system":case"systemjs":return"system";case"amd":case"iife":case"umd":return t;default:return fe({message:'You must specify "output.format", which can be one of "amd", "cjs", "system", "es", "iife" or "umd".',url:"https://rollupjs.org/guide/en/#outputformat"})}},dh=(e,t)=>{var i;const s=(null!==(i=e.inlineDynamicImports)&&void 0!==i?i:t.inlineDynamicImports)||!1,{input:n}=t;return s&&(Array.isArray(n)?n:Object.keys(n)).length>1?fe(Ee("output.inlineDynamicImports","outputinlinedynamicimports",'multiple inputs are not supported when "output.inlineDynamicImports" is true')):s},ph=(e,t,i)=>{var s;const n=(null!==(s=e.preserveModules)&&void 0!==s?s:i.preserveModules)||!1;if(n){if(t)return fe(Ee("output.inlineDynamicImports","outputinlinedynamicimports",'this option is not supported for "output.preserveModules"'));if(!1===i.preserveEntrySignatures)return fe(Ee("preserveEntrySignatures","preserveentrysignatures",'setting this option to false is not supported for "output.preserveModules"'))}return n},fh=(e,t)=>{const i=e.preferConst;return null!=i&&ke('The "output.preferConst" option is deprecated. Use the "output.generatedCode.constBindings" option instead.',!1,t),!!i},mh=e=>{const{preserveModulesRoot:t}=e;if(null!=t)return R(t)},gh=e=>{const t={autoId:!1,basePath:"",define:"define",forceJsExtensionForImports:!1,...e.amd};if((t.autoId||t.basePath)&&t.id)return fe(Ee("output.amd.id","outputamd",'this option cannot be used together with "output.amd.autoId"/"output.amd.basePath"'));if(t.basePath&&!t.autoId)return fe(Ee("output.amd.basePath","outputamd",'this option only works with "output.amd.autoId"'));let i;return i=t.autoId?{autoId:!0,basePath:t.basePath,define:t.define,forceJsExtensionForImports:t.forceJsExtensionForImports}:{autoId:!1,define:t.define,forceJsExtensionForImports:t.forceJsExtensionForImports,id:t.id},i},yh=(e,t)=>{const i=e[t];return"function"==typeof i?i:()=>i||""},xh=(e,t)=>{const{dir:i}=e;return"string"==typeof i&&"string"==typeof t?fe(Ee("output.dir","outputdir",'you must set either "output.file" for a single-file build or "output.dir" when generating multiple chunks')):i},Eh=(e,t)=>{const i=e.dynamicImportFunction;return i&&ke('The "output.dynamicImportFunction" option is deprecated. Use the "renderDynamicImport" plugin hook instead.',!1,t),i},bh=(e,t)=>{const i=e.entryFileNames;return null==i&&t.add("entryFileNames"),null!=i?i:"[name].js"};function vh(e,t){const i=e.exports;if(null==i)t.add("exports");else if(!["default","named","none","auto"].includes(i))return fe((s=i,{code:ge.INVALID_EXPORT_OPTION,message:`"output.exports" must be "default", "named", "none", "auto", or left unspecified (defaults to "auto"), received "${s}"`,url:"https://rollupjs.org/guide/en/#outputexports"}));var s;return i||"auto"}const Sh=(e,t)=>{const i=Hl(e.generatedCode,Gl,"output.generatedCode","");return{arrowFunctions:!0===i.arrowFunctions,constBindings:!0===i.constBindings||t,objectShorthand:!0===i.objectShorthand,reservedNamesAsProps:!0===i.reservedNamesAsProps,symbols:!0===i.symbols}},Ah=(e,t)=>{if(t)return"";const i=e.indent;return!1===i?"":null==i||i},Ih=new Set(["auto","esModule","default","defaultOnly",!0,!1]),Ph=(e,t)=>{const i=e.interop,s=new Set,n=e=>{if(!s.has(e)){if(s.add(e),!Ih.has(e))return fe(Ee("output.interop","outputinterop",`use one of ${Array.from(Ih,(e=>JSON.stringify(e))).join(", ")}`,e));"boolean"==typeof e&&ke({message:`The boolean value "${e}" for the "output.interop" option is deprecated. Use ${e?'"auto"':'"esModule", "default" or "defaultOnly"'} instead.`,url:"https://rollupjs.org/guide/en/#outputinterop"},!1,t)}return e};if("function"==typeof i){const e=Object.create(null);let t=null;return s=>null===s?t||n(t=i(s)):s in e?e[s]:n(e[s]=i(s))}return void 0===i?()=>!0:()=>n(i)},kh=(e,t,i,s)=>{const n=e.manualChunks||s.manualChunks;if(n){if(t)return fe(Ee("output.manualChunks","outputmanualchunks",'this option is not supported for "output.inlineDynamicImports"'));if(i)return fe(Ee("output.manualChunks","outputmanualchunks",'this option is not supported for "output.preserveModules"'))}return n||{}},wh=(e,t,i)=>{var s;return null!==(s=e.minifyInternalExports)&&void 0!==s?s:i||"es"===t||"system"===t},Ch=(e,t,i)=>{const s=e.namespaceToStringTag;return null!=s?(ke('The "output.namespaceToStringTag" option is deprecated. Use the "output.generatedCode.symbols" option instead.',!1,i),s):t.symbols||!1},Nh=e=>{const{sourcemapBaseUrl:t}=e;if(t)return function(e){try{new URL(e)}catch(e){return!1}return!0}(t)?t:fe(Ee("output.sourcemapBaseUrl","outputsourcemapbaseurl",`must be a valid URL, received ${JSON.stringify(t)}`))};function _h(e,t){e.forEach(((e,i)=>{e.name||(e.name=`${t}${i+1}`)}))}function $h(e,t,i,s,n){const{options:r,outputPluginDriver:a,unsetOptions:o}=function(e,t,i,s){if(!e)throw new Error("You must supply an options object");const n=Bl(e.plugins);_h(n,cl);const r=t.createOutputPluginDriver(n);return{...Th(i,s,e,r),outputPluginDriver:r}}(s,n.pluginDriver,t,i);return Fl(0,(async()=>{const i=new ma(r,o,t,a,n),s=await i.generate(e);if(e){if(!r.dir&&!r.file)return fe({code:"MISSING_OPTION",message:'You must specify "output.file" or "output.dir" for the build.'});await Promise.all(Object.values(s).map((e=>n.fileOperationQueue.run((()=>async function(e,t){const i=R(t.dir||$(t.file),e.fileName);let s,n;if(await ol.mkdir($(i),{recursive:!0}),"asset"===e.type)n=e.source;else if(n=e.code,t.sourcemap&&e.map){let r;if("inline"===t.sourcemap)r=e.map.toUrl();else{const{sourcemapBaseUrl:n}=t,a=`${_(e.fileName)}.map`;r=n?new URL(a,n).toString():a,s=ol.writeFile(`${i}.map`,e.map.toString())}"hidden"!==t.sourcemap&&(n+=`//# sourceMappingURL=${r}\n`)}return Promise.all([ol.writeFile(i,n),s])}(e,r)))))),await a.hookParallel("writeBundle",[r,s])}return l=s,{output:Object.values(l).filter((e=>Object.keys(e).length>0)).sort(((e,t)=>Rh(e)-Rh(t)))};var l}))}function Th(e,t,i,s){return function(e,t,i){var s,n,r,a,o,l,h;const c=new Set(i),u=e.compact||!1,d=uh(e),p=dh(e,t),f=ph(e,p,t),m=ch(e,f,t),g=fh(e,t),y=Sh(e,g),x={amd:gh(e),assetFileNames:null!==(s=e.assetFileNames)&&void 0!==s?s:"assets/[name]-[hash][extname]",banner:yh(e,"banner"),chunkFileNames:null!==(n=e.chunkFileNames)&&void 0!==n?n:"[name]-[hash].js",compact:u,dir:xh(e,m),dynamicImportFunction:Eh(e,t),entryFileNames:bh(e,c),esModule:null===(r=e.esModule)||void 0===r||r,exports:vh(e,c),extend:e.extend||!1,externalLiveBindings:null===(a=e.externalLiveBindings)||void 0===a||a,file:m,footer:yh(e,"footer"),format:d,freeze:null===(o=e.freeze)||void 0===o||o,generatedCode:y,globals:e.globals||{},hoistTransitiveImports:null===(l=e.hoistTransitiveImports)||void 0===l||l,indent:Ah(e,u),inlineDynamicImports:p,interop:Ph(e,t),intro:yh(e,"intro"),manualChunks:kh(e,p,f,t),minifyInternalExports:wh(e,d,u),name:e.name,namespaceToStringTag:Ch(e,y,t),noConflict:e.noConflict||!1,outro:yh(e,"outro"),paths:e.paths||{},plugins:Bl(e.plugins),preferConst:g,preserveModules:f,preserveModulesRoot:mh(e),sanitizeFileName:"function"==typeof e.sanitizeFileName?e.sanitizeFileName:!1===e.sanitizeFileName?e=>e:hh,sourcemap:e.sourcemap||!1,sourcemapBaseUrl:Nh(e),sourcemapExcludeSources:e.sourcemapExcludeSources||!1,sourcemapFile:e.sourcemapFile,sourcemapPathTransform:e.sourcemapPathTransform,strict:null===(h=e.strict)||void 0===h||h,systemNullSetters:e.systemNullSetters||!1,validate:e.validate||!1};return jl(e,Object.keys(x),"output options",t.onwarn),{options:x,unsetOptions:c}}(s.hookReduceArg0Sync("outputOptions",[i.output||i],((e,t)=>t||e),(e=>{const t=()=>e.error({code:ge.CANNOT_EMIT_FROM_OPTIONS_HOOK,message:'Cannot emit files or set asset sources in the "outputOptions" hook, use the "renderStart" hook instead.'});return{...e,emitFile:t,setAssetSource:t}})),e,t)}var Oh;function Rh(e){return"asset"===e.type?Oh.ASSET:e.isEntry?Oh.ENTRY_CHUNK:Oh.SECONDARY_CHUNK}!function(e){e[e.ENTRY_CHUNK=0]="ENTRY_CHUNK",e[e.SECONDARY_CHUNK=1]="SECONDARY_CHUNK",e[e.ASSET=2]="ASSET"}(Oh||(Oh={})),e.VERSION=t,e.defineConfig=function(e){return e},e.rollup=function(e){return async function(e,i){const{options:s,unsetOptions:n}=await async function(e,i){if(!e)throw new Error("You must supply an options object to rollup");const s=Ol("options",Bl(e.plugins)),{options:n,unsetOptions:r}=function(e){var t,i,s;const n=new Set,r=null!==(t=e.context)&&void 0!==t?t:"undefined",a=ql(e),o=e.strictDeprecations||!1,l=th(e,a,o),h={acorn:Kl(e),acornInjectPlugins:Xl(e),cache:Yl(e),context:r,experimentalCacheExpiry:null!==(i=e.experimentalCacheExpiry)&&void 0!==i?i:10,external:Ql(e.external),inlineDynamicImports:Jl(e,a,o),input:Zl(e),makeAbsoluteExternalsRelative:null===(s=e.makeAbsoluteExternalsRelative)||void 0===s||s,manualChunks:eh(e,a,o),maxParallelFileOps:l,maxParallelFileReads:l,moduleContext:ih(e,r),onwarn:a,perf:e.perf||!1,plugins:Bl(e.plugins),preserveEntrySignatures:sh(e,n),preserveModules:nh(e,a,o),preserveSymlinks:e.preserveSymlinks||!1,shimMissingExports:e.shimMissingExports||!1,strictDeprecations:o,treeshake:rh(e,a,o)};return jl(e,[...Object.keys(h),"watch"],"input options",h.onwarn,/^(output)$/),{options:h,unsetOptions:n}}(await s.reduce(function(e){return async(i,s)=>{const n="handler"in s.options?s.options.handler:s.options;return await n.call({meta:{rollupVersion:t,watchMode:e}},await i)||i}}(i),Promise.resolve(e)));return _h(n.plugins,hl),{options:n,unsetOptions:r}}(e,null!==i);!function(e){e.perf?(fn=new Map,En=gn,bn=yn,e.plugins=e.plugins.map(Sn)):(En=pn,bn=pn)}(s);const r=new Vl(s,i),a=!1!==e.cache;delete s.cache,delete e.cache,En("BUILD",1),await Fl(r.pluginDriver,(async()=>{try{await r.pluginDriver.hookParallel("buildStart",[s]),await r.build()}catch(e){const t=Object.keys(r.watchFiles);throw t.length>0&&(e.watchFiles=t),await r.pluginDriver.hookParallel("buildEnd",[e]),await r.pluginDriver.hookParallel("closeBundle",[]),e}await r.pluginDriver.hookParallel("buildEnd",[])})),bn("BUILD",1);const o={cache:a?r.getCache():void 0,async close(){o.closed||(o.closed=!0,await r.pluginDriver.hookParallel("closeBundle",[]))},closed:!1,generate:async e=>o.closed?fe(Pe()):$h(!1,s,n,e,r),watchFiles:Object.keys(r.watchFiles),write:async e=>o.closed?fe(Pe()):$h(!0,s,n,e,r)};return s.perf&&(o.getTimings=xn),o}(e,null)},Object.defineProperty(e,"__esModule",{value:!0})},"object"==typeof exports&&"undefined"!=typeof module?t(exports):"function"==typeof define&&define.amd?define(["exports"],t):t((e="undefined"!=typeof globalThis?globalThis:e||self).rollup={});
//# sourceMappingURL=rollup.browser.js.map
