{"ast": null, "code": "export default shuffler(Math.random);\nexport function shuffler(random) {\n  return function shuffle(array, i0 = 0, i1 = array.length) {\n    let m = i1 - (i0 = +i0);\n    while (m) {\n      const i = random() * m-- | 0,\n        t = array[m + i0];\n      array[m + i0] = array[i + i0];\n      array[i + i0] = t;\n    }\n    return array;\n  };\n}", "map": {"version": 3, "names": ["shuffler", "Math", "random", "shuffle", "array", "i0", "i1", "length", "m", "i", "t"], "sources": ["D:/ecommerce/node_modules/d3-array/src/shuffle.js"], "sourcesContent": ["export default shuffler(Math.random);\n\nexport function shuffler(random) {\n  return function shuffle(array, i0 = 0, i1 = array.length) {\n    let m = i1 - (i0 = +i0);\n    while (m) {\n      const i = random() * m-- | 0, t = array[m + i0];\n      array[m + i0] = array[i + i0];\n      array[i + i0] = t;\n    }\n    return array;\n  };\n}\n"], "mappings": "AAAA,eAAeA,QAAQ,CAACC,IAAI,CAACC,MAAM,CAAC;AAEpC,OAAO,SAASF,QAAQA,CAACE,MAAM,EAAE;EAC/B,OAAO,SAASC,OAAOA,CAACC,KAAK,EAAEC,EAAE,GAAG,CAAC,EAAEC,EAAE,GAAGF,KAAK,CAACG,MAAM,EAAE;IACxD,IAAIC,CAAC,GAAGF,EAAE,IAAID,EAAE,GAAG,CAACA,EAAE,CAAC;IACvB,OAAOG,CAAC,EAAE;MACR,MAAMC,CAAC,GAAGP,MAAM,CAAC,CAAC,GAAGM,CAAC,EAAE,GAAG,CAAC;QAAEE,CAAC,GAAGN,KAAK,CAACI,CAAC,GAAGH,EAAE,CAAC;MAC/CD,KAAK,CAACI,CAAC,GAAGH,EAAE,CAAC,GAAGD,KAAK,CAACK,CAAC,GAAGJ,EAAE,CAAC;MAC7BD,KAAK,CAACK,CAAC,GAAGJ,EAAE,CAAC,GAAGK,CAAC;IACnB;IACA,OAAON,KAAK;EACd,CAAC;AACH", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}