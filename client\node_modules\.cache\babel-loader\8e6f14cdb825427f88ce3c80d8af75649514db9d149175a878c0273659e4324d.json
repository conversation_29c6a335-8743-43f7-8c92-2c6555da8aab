{"ast": null, "code": "var _jsxFileName = \"D:\\\\ecommerce\\\\client\\\\src\\\\components\\\\admin\\\\AdminDashboard.js\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect } from 'react';\nimport { Box, Grid, Paper, Typography, Card, CardContent, Table, TableBody, TableCell, TableContainer, TableHead, TableRow, Button, Chip, Avatar, LinearProgress, IconButton, Menu, MenuItem, CircularProgress, Alert } from '@mui/material';\nimport { TrendingUp, TrendingDown, People, ShoppingCart, Visibility, AttachMoney, MoreVert, Edit, Delete, Refresh } from '@mui/icons-material';\nimport { analyticsAPI, handleAPIError } from '../../services/api';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst AdminDashboard = () => {\n  _s();\n  var _analytics$stats, _analytics$stats$tota, _analytics$stats2, _analytics$stats3, _analytics$stats3$pag, _analytics$stats4;\n  const [anchorEl, setAnchorEl] = useState(null);\n  const [selectedProduct, setSelectedProduct] = useState(null);\n  const [analytics, setAnalytics] = useState(null);\n  const [loading, setLoading] = useState(true);\n  const [error, setError] = useState(null);\n\n  // Fetch analytics data\n  const fetchAnalytics = async () => {\n    try {\n      setLoading(true);\n      setError(null);\n      const response = await analyticsAPI.getAdminAnalytics();\n      setAnalytics(response.data);\n    } catch (err) {\n      setError(handleAPIError(err));\n      console.error('Failed to fetch analytics:', err);\n      // Fallback to mock data\n      setAnalytics(getMockAnalytics());\n    } finally {\n      setLoading(false);\n    }\n  };\n  useEffect(() => {\n    fetchAnalytics();\n  }, []);\n\n  // Mock data fallback\n  const getMockAnalytics = () => ({\n    stats: {\n      totalVisits: 914001,\n      bounceRate: 46.41,\n      pageViews: 4054876,\n      growthRate: 46.43\n    },\n    totals: {\n      users: 1250,\n      products: 45,\n      orders: 324,\n      revenue: 72000\n    },\n    monthlyData: [{\n      month: 'Jan',\n      users: 324,\n      sales: 45000\n    }, {\n      month: 'Feb',\n      users: 456,\n      sales: 52000\n    }, {\n      month: 'Mar',\n      users: 378,\n      sales: 48000\n    }, {\n      month: 'Apr',\n      users: 520,\n      sales: 61000\n    }, {\n      month: 'May',\n      users: 489,\n      sales: 58000\n    }, {\n      month: 'Jun',\n      users: 612,\n      sales: 72000\n    }],\n    recentOrders: [{\n      id: 'ORD001',\n      customer: 'John Doe',\n      total: 24.99,\n      status: 'Completed',\n      date: '2024-01-15',\n      items: 2\n    }, {\n      id: 'ORD002',\n      customer: 'Jane Smith',\n      total: 18.50,\n      status: 'Processing',\n      date: '2024-01-14',\n      items: 1\n    }, {\n      id: 'ORD003',\n      customer: 'Mike Johnson',\n      total: 32.75,\n      status: 'Shipped',\n      date: '2024-01-13',\n      items: 3\n    }],\n    topProducts: [{\n      id: 1,\n      name: 'Cinnamon Sticks',\n      sales: 245,\n      revenue: 6125\n    }, {\n      id: 2,\n      name: 'Black Pepper',\n      sales: 189,\n      revenue: 3402\n    }, {\n      id: 3,\n      name: 'Turmeric Powder',\n      sales: 156,\n      revenue: 4992\n    }],\n    trafficSources: [{\n      name: 'Organic',\n      value: 44.46,\n      color: '#2196f3'\n    }, {\n      name: 'Referral',\n      value: 5.54,\n      color: '#4caf50'\n    }, {\n      name: 'Other',\n      value: 50,\n      color: '#ff9800'\n    }],\n    browserStats: [{\n      name: 'Google Chrome',\n      percentage: 60,\n      color: '#4285f4'\n    }, {\n      name: 'Mozilla Firefox',\n      percentage: 18,\n      color: '#ff7139'\n    }, {\n      name: 'Internet Explorer',\n      percentage: 12,\n      color: '#00bcf2'\n    }, {\n      name: 'Safari',\n      percentage: 10,\n      color: '#000000'\n    }]\n  });\n  if (loading) {\n    return /*#__PURE__*/_jsxDEV(Box, {\n      sx: {\n        display: 'flex',\n        justifyContent: 'center',\n        alignItems: 'center',\n        height: '100vh'\n      },\n      children: /*#__PURE__*/_jsxDEV(CircularProgress, {\n        size: 60\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 115,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 114,\n      columnNumber: 7\n    }, this);\n  }\n  const stats = [{\n    title: 'Total Visits',\n    value: (analytics === null || analytics === void 0 ? void 0 : (_analytics$stats = analytics.stats) === null || _analytics$stats === void 0 ? void 0 : (_analytics$stats$tota = _analytics$stats.totalVisits) === null || _analytics$stats$tota === void 0 ? void 0 : _analytics$stats$tota.toLocaleString()) || '914,001',\n    icon: People,\n    color: '#f44336',\n    trend: '+12.5%'\n  }, {\n    title: 'Bounce Rate',\n    value: `${(analytics === null || analytics === void 0 ? void 0 : (_analytics$stats2 = analytics.stats) === null || _analytics$stats2 === void 0 ? void 0 : _analytics$stats2.bounceRate) || 46.41}%`,\n    icon: TrendingDown,\n    color: '#ff9800',\n    trend: '-2.3%'\n  }, {\n    title: 'Page Views',\n    value: (analytics === null || analytics === void 0 ? void 0 : (_analytics$stats3 = analytics.stats) === null || _analytics$stats3 === void 0 ? void 0 : (_analytics$stats3$pag = _analytics$stats3.pageViews) === null || _analytics$stats3$pag === void 0 ? void 0 : _analytics$stats3$pag.toLocaleString()) || '4,054,876',\n    icon: Visibility,\n    color: '#4caf50',\n    trend: '+8.7%'\n  }, {\n    title: 'Growth Rate',\n    value: `${(analytics === null || analytics === void 0 ? void 0 : (_analytics$stats4 = analytics.stats) === null || _analytics$stats4 === void 0 ? void 0 : _analytics$stats4.growthRate) || 46.43}%`,\n    icon: TrendingUp,\n    color: '#2196f3',\n    trend: '+15.2%'\n  }];\n  const trafficData = (analytics === null || analytics === void 0 ? void 0 : analytics.trafficSources) || [{\n    name: 'Organic',\n    value: 44.46,\n    color: '#2196f3'\n  }, {\n    name: 'Referral',\n    value: 5.54,\n    color: '#4caf50'\n  }, {\n    name: 'Other',\n    value: 50,\n    color: '#ff9800'\n  }];\n  const browserStats = (analytics === null || analytics === void 0 ? void 0 : analytics.browserStats) || [{\n    name: 'Google Chrome',\n    percentage: 60,\n    color: '#4285f4'\n  }, {\n    name: 'Mozilla Firefox',\n    percentage: 18,\n    color: '#ff7139'\n  }, {\n    name: 'Internet Explorer',\n    percentage: 12,\n    color: '#00bcf2'\n  }, {\n    name: 'Safari',\n    percentage: 10,\n    color: '#000000'\n  }];\n  const monthlyData = (analytics === null || analytics === void 0 ? void 0 : analytics.monthlyData) || [{\n    month: 'Jan',\n    users: 324,\n    sales: 45000\n  }, {\n    month: 'Feb',\n    users: 456,\n    sales: 52000\n  }, {\n    month: 'Mar',\n    users: 378,\n    sales: 48000\n  }, {\n    month: 'Apr',\n    users: 520,\n    sales: 61000\n  }, {\n    month: 'May',\n    users: 489,\n    sales: 58000\n  }, {\n    month: 'Jun',\n    users: 612,\n    sales: 72000\n  }];\n  const recentOrders = (analytics === null || analytics === void 0 ? void 0 : analytics.recentOrders) || [{\n    id: 'ORD001',\n    customer: 'John Doe',\n    total: 24.99,\n    status: 'Completed',\n    date: '2024-01-15',\n    items: 2\n  }, {\n    id: 'ORD002',\n    customer: 'Jane Smith',\n    total: 18.50,\n    status: 'Processing',\n    date: '2024-01-14',\n    items: 1\n  }, {\n    id: 'ORD003',\n    customer: 'Mike Johnson',\n    total: 32.75,\n    status: 'Shipped',\n    date: '2024-01-13',\n    items: 3\n  }];\n  const topProducts = (analytics === null || analytics === void 0 ? void 0 : analytics.topProducts) || [{\n    id: 1,\n    name: 'Cinnamon Sticks',\n    sales: 245,\n    revenue: 6125,\n    image: '🍯'\n  }, {\n    id: 2,\n    name: 'Black Pepper',\n    sales: 189,\n    revenue: 3402,\n    image: '🌶️'\n  }, {\n    id: 3,\n    name: 'Turmeric Powder',\n    sales: 156,\n    revenue: 4992,\n    image: '🟡'\n  }];\n  const handleMenuClick = (event, product) => {\n    setAnchorEl(event.currentTarget);\n    setSelectedProduct(product);\n  };\n  const handleMenuClose = () => {\n    setAnchorEl(null);\n    setSelectedProduct(null);\n  };\n  const getStatusColor = status => {\n    switch (status) {\n      case 'Completed':\n        return 'success';\n      case 'Processing':\n        return 'warning';\n      case 'Shipped':\n        return 'info';\n      case 'Pending':\n        return 'default';\n      default:\n        return 'default';\n    }\n  };\n  return /*#__PURE__*/_jsxDEV(Box, {\n    sx: {\n      p: 3,\n      backgroundColor: '#f5f5f5',\n      minHeight: '100vh'\n    },\n    children: [/*#__PURE__*/_jsxDEV(Box, {\n      sx: {\n        display: 'flex',\n        justifyContent: 'space-between',\n        alignItems: 'center',\n        mb: 3\n      },\n      children: [/*#__PURE__*/_jsxDEV(Typography, {\n        variant: \"h4\",\n        sx: {\n          fontWeight: 'bold'\n        },\n        children: \"Admin Dashboard\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 208,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Button, {\n        variant: \"outlined\",\n        startIcon: /*#__PURE__*/_jsxDEV(Refresh, {}, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 213,\n          columnNumber: 22\n        }, this),\n        onClick: fetchAnalytics,\n        disabled: loading,\n        children: \"Refresh Data\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 211,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 207,\n      columnNumber: 7\n    }, this), error && /*#__PURE__*/_jsxDEV(Alert, {\n      severity: \"warning\",\n      sx: {\n        mb: 3\n      },\n      children: [error, \" - Showing fallback data\"]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 222,\n      columnNumber: 9\n    }, this), /*#__PURE__*/_jsxDEV(Grid, {\n      container: true,\n      spacing: 3,\n      sx: {\n        mb: 4\n      },\n      children: stats.map((stat, index) => /*#__PURE__*/_jsxDEV(Grid, {\n        item: true,\n        xs: 12,\n        sm: 6,\n        md: 3,\n        children: /*#__PURE__*/_jsxDEV(Card, {\n          sx: {\n            background: stat.color,\n            color: 'white',\n            height: '120px'\n          },\n          children: /*#__PURE__*/_jsxDEV(CardContent, {\n            children: [/*#__PURE__*/_jsxDEV(Box, {\n              sx: {\n                display: 'flex',\n                justifyContent: 'space-between',\n                alignItems: 'center'\n              },\n              children: [/*#__PURE__*/_jsxDEV(Box, {\n                children: [/*#__PURE__*/_jsxDEV(Typography, {\n                  variant: \"h4\",\n                  sx: {\n                    fontWeight: 'bold',\n                    mb: 1\n                  },\n                  children: stat.value\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 235,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                  variant: \"body2\",\n                  sx: {\n                    opacity: 0.9\n                  },\n                  children: stat.title\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 238,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 234,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(stat.icon, {\n                sx: {\n                  fontSize: 40,\n                  opacity: 0.8\n                }\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 242,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 233,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(Typography, {\n              variant: \"caption\",\n              sx: {\n                mt: 1,\n                display: 'block'\n              },\n              children: stat.trend\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 244,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 232,\n            columnNumber: 15\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 231,\n          columnNumber: 13\n        }, this)\n      }, index, false, {\n        fileName: _jsxFileName,\n        lineNumber: 230,\n        columnNumber: 11\n      }, this))\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 228,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Grid, {\n      container: true,\n      spacing: 3,\n      children: [/*#__PURE__*/_jsxDEV(Grid, {\n        item: true,\n        xs: 12,\n        md: 8,\n        children: /*#__PURE__*/_jsxDEV(Paper, {\n          sx: {\n            p: 3,\n            height: '400px'\n          },\n          children: [/*#__PURE__*/_jsxDEV(Typography, {\n            variant: \"h6\",\n            sx: {\n              mb: 2\n            },\n            children: \"Monthly Sales & Users\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 257,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Box, {\n            sx: {\n              height: '90%',\n              display: 'flex',\n              flexDirection: 'column',\n              justifyContent: 'center'\n            },\n            children: [/*#__PURE__*/_jsxDEV(Grid, {\n              container: true,\n              spacing: 2,\n              children: monthlyData.map((data, index) => /*#__PURE__*/_jsxDEV(Grid, {\n                item: true,\n                xs: 2,\n                children: /*#__PURE__*/_jsxDEV(Box, {\n                  sx: {\n                    textAlign: 'center'\n                  },\n                  children: [/*#__PURE__*/_jsxDEV(Typography, {\n                    variant: \"caption\",\n                    color: \"textSecondary\",\n                    children: data.month\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 263,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(Box, {\n                    sx: {\n                      mt: 1,\n                      mb: 1\n                    },\n                    children: [/*#__PURE__*/_jsxDEV(Box, {\n                      sx: {\n                        height: `${data.users / 600 * 100}px`,\n                        backgroundColor: '#2196f3',\n                        borderRadius: 1,\n                        mb: 0.5,\n                        minHeight: '20px'\n                      }\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 267,\n                      columnNumber: 25\n                    }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                      variant: \"caption\",\n                      sx: {\n                        fontSize: '0.7rem'\n                      },\n                      children: [data.users, \" users\"]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 276,\n                      columnNumber: 25\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 266,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(Box, {\n                    children: [/*#__PURE__*/_jsxDEV(Box, {\n                      sx: {\n                        height: `${data.sales / 80000 * 100}px`,\n                        backgroundColor: '#4caf50',\n                        borderRadius: 1,\n                        mb: 0.5,\n                        minHeight: '20px'\n                      }\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 281,\n                      columnNumber: 25\n                    }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                      variant: \"caption\",\n                      sx: {\n                        fontSize: '0.7rem'\n                      },\n                      children: [\"$\", data.sales]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 290,\n                      columnNumber: 25\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 280,\n                    columnNumber: 23\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 262,\n                  columnNumber: 21\n                }, this)\n              }, data.month, false, {\n                fileName: _jsxFileName,\n                lineNumber: 261,\n                columnNumber: 19\n              }, this))\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 259,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Box, {\n              sx: {\n                display: 'flex',\n                justifyContent: 'center',\n                mt: 2,\n                gap: 2\n              },\n              children: [/*#__PURE__*/_jsxDEV(Box, {\n                sx: {\n                  display: 'flex',\n                  alignItems: 'center'\n                },\n                children: [/*#__PURE__*/_jsxDEV(Box, {\n                  sx: {\n                    width: 12,\n                    height: 12,\n                    backgroundColor: '#2196f3',\n                    mr: 1\n                  }\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 300,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                  variant: \"caption\",\n                  children: \"Users\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 301,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 299,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(Box, {\n                sx: {\n                  display: 'flex',\n                  alignItems: 'center'\n                },\n                children: [/*#__PURE__*/_jsxDEV(Box, {\n                  sx: {\n                    width: 12,\n                    height: 12,\n                    backgroundColor: '#4caf50',\n                    mr: 1\n                  }\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 304,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                  variant: \"caption\",\n                  children: \"Sales ($)\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 305,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 303,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 298,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 258,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 256,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 255,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Grid, {\n        item: true,\n        xs: 12,\n        md: 4,\n        children: /*#__PURE__*/_jsxDEV(Paper, {\n          sx: {\n            p: 3,\n            height: '400px'\n          },\n          children: [/*#__PURE__*/_jsxDEV(Typography, {\n            variant: \"h6\",\n            sx: {\n              mb: 2\n            },\n            children: \"Customer Satisfaction\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 315,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Box, {\n            sx: {\n              textAlign: 'center',\n              mb: 3\n            },\n            children: [/*#__PURE__*/_jsxDEV(Typography, {\n              variant: \"h2\",\n              sx: {\n                color: '#4caf50',\n                fontWeight: 'bold'\n              },\n              children: \"93.13%\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 317,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Typography, {\n              variant: \"body2\",\n              color: \"textSecondary\",\n              children: \"Previous: 79.82 | Change: +14.29\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 320,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 316,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Typography, {\n            variant: \"subtitle1\",\n            sx: {\n              mb: 2\n            },\n            children: \"Browser Stats\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 325,\n            columnNumber: 13\n          }, this), browserStats.map((browser, index) => /*#__PURE__*/_jsxDEV(Box, {\n            sx: {\n              mb: 2\n            },\n            children: [/*#__PURE__*/_jsxDEV(Box, {\n              sx: {\n                display: 'flex',\n                justifyContent: 'space-between',\n                mb: 1\n              },\n              children: [/*#__PURE__*/_jsxDEV(Typography, {\n                variant: \"body2\",\n                children: browser.name\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 329,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                variant: \"body2\",\n                children: [browser.percentage, \"%\"]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 330,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 328,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(LinearProgress, {\n              variant: \"determinate\",\n              value: browser.percentage,\n              sx: {\n                height: 8,\n                borderRadius: 4,\n                backgroundColor: '#e0e0e0',\n                '& .MuiLinearProgress-bar': {\n                  backgroundColor: browser.color\n                }\n              }\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 332,\n              columnNumber: 17\n            }, this)]\n          }, index, true, {\n            fileName: _jsxFileName,\n            lineNumber: 327,\n            columnNumber: 15\n          }, this))]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 314,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 313,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Grid, {\n        item: true,\n        xs: 12,\n        md: 6,\n        children: /*#__PURE__*/_jsxDEV(Paper, {\n          sx: {\n            p: 3,\n            height: '350px'\n          },\n          children: [/*#__PURE__*/_jsxDEV(Typography, {\n            variant: \"h6\",\n            sx: {\n              mb: 2\n            },\n            children: \"Visit By Traffic Types\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 352,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Box, {\n            sx: {\n              display: 'flex',\n              flexDirection: 'column',\n              height: '80%',\n              justifyContent: 'center'\n            },\n            children: [trafficData.map((item, index) => /*#__PURE__*/_jsxDEV(Box, {\n              sx: {\n                mb: 3\n              },\n              children: [/*#__PURE__*/_jsxDEV(Box, {\n                sx: {\n                  display: 'flex',\n                  justifyContent: 'space-between',\n                  mb: 1\n                },\n                children: [/*#__PURE__*/_jsxDEV(Typography, {\n                  variant: \"body2\",\n                  children: item.name\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 357,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                  variant: \"body2\",\n                  sx: {\n                    fontWeight: 'bold'\n                  },\n                  children: [item.value, \"%\"]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 358,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 356,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(LinearProgress, {\n                variant: \"determinate\",\n                value: item.value,\n                sx: {\n                  height: 12,\n                  borderRadius: 6,\n                  backgroundColor: '#e0e0e0',\n                  '& .MuiLinearProgress-bar': {\n                    backgroundColor: item.color,\n                    borderRadius: 6\n                  }\n                }\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 362,\n                columnNumber: 19\n              }, this)]\n            }, index, true, {\n              fileName: _jsxFileName,\n              lineNumber: 355,\n              columnNumber: 17\n            }, this)), /*#__PURE__*/_jsxDEV(Box, {\n              sx: {\n                mt: 2,\n                textAlign: 'center'\n              },\n              children: /*#__PURE__*/_jsxDEV(Typography, {\n                variant: \"caption\",\n                color: \"textSecondary\",\n                children: \"Total traffic distribution across different sources\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 378,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 377,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 353,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 351,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 350,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Grid, {\n        item: true,\n        xs: 12,\n        md: 6,\n        children: /*#__PURE__*/_jsxDEV(Paper, {\n          sx: {\n            p: 3,\n            height: '350px'\n          },\n          children: [/*#__PURE__*/_jsxDEV(Typography, {\n            variant: \"h6\",\n            sx: {\n              mb: 2\n            },\n            children: \"Top Selling Products\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 389,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(TableContainer, {\n            children: /*#__PURE__*/_jsxDEV(Table, {\n              size: \"small\",\n              children: [/*#__PURE__*/_jsxDEV(TableHead, {\n                children: /*#__PURE__*/_jsxDEV(TableRow, {\n                  children: [/*#__PURE__*/_jsxDEV(TableCell, {\n                    children: \"Product\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 394,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n                    align: \"right\",\n                    children: \"Sales\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 395,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n                    align: \"right\",\n                    children: \"Revenue\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 396,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n                    align: \"right\",\n                    children: \"Actions\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 397,\n                    columnNumber: 21\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 393,\n                  columnNumber: 19\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 392,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(TableBody, {\n                children: topProducts.map(product => /*#__PURE__*/_jsxDEV(TableRow, {\n                  children: [/*#__PURE__*/_jsxDEV(TableCell, {\n                    children: /*#__PURE__*/_jsxDEV(Box, {\n                      sx: {\n                        display: 'flex',\n                        alignItems: 'center'\n                      },\n                      children: [/*#__PURE__*/_jsxDEV(Typography, {\n                        sx: {\n                          mr: 1,\n                          fontSize: '1.2em'\n                        },\n                        children: product.image\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 405,\n                        columnNumber: 27\n                      }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                        variant: \"body2\",\n                        children: product.name\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 406,\n                        columnNumber: 27\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 404,\n                      columnNumber: 25\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 403,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n                    align: \"right\",\n                    children: product.sales\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 409,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n                    align: \"right\",\n                    children: [\"$\", product.revenue]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 410,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n                    align: \"right\",\n                    children: /*#__PURE__*/_jsxDEV(IconButton, {\n                      size: \"small\",\n                      onClick: e => handleMenuClick(e, product),\n                      children: /*#__PURE__*/_jsxDEV(MoreVert, {}, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 413,\n                        columnNumber: 27\n                      }, this)\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 412,\n                      columnNumber: 25\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 411,\n                    columnNumber: 23\n                  }, this)]\n                }, product.id, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 402,\n                  columnNumber: 21\n                }, this))\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 400,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 391,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 390,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 388,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 387,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 253,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Paper, {\n      sx: {\n        p: 3,\n        mt: 3\n      },\n      children: [/*#__PURE__*/_jsxDEV(Typography, {\n        variant: \"h6\",\n        sx: {\n          mb: 2\n        },\n        children: \"Recent Orders\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 427,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(TableContainer, {\n        children: /*#__PURE__*/_jsxDEV(Table, {\n          children: [/*#__PURE__*/_jsxDEV(TableHead, {\n            children: /*#__PURE__*/_jsxDEV(TableRow, {\n              children: [/*#__PURE__*/_jsxDEV(TableCell, {\n                children: \"Order ID\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 432,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n                children: \"Customer\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 433,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n                children: \"Product\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 434,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n                align: \"right\",\n                children: \"Amount\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 435,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n                children: \"Status\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 436,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n                children: \"Date\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 437,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n                align: \"right\",\n                children: \"Actions\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 438,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 431,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 430,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(TableBody, {\n            children: recentOrders.map(order => /*#__PURE__*/_jsxDEV(TableRow, {\n              children: [/*#__PURE__*/_jsxDEV(TableCell, {\n                children: order.id\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 444,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n                children: order.customer\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 445,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n                children: order.product\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 446,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n                align: \"right\",\n                children: [\"$\", order.amount]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 447,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n                children: /*#__PURE__*/_jsxDEV(Chip, {\n                  label: order.status,\n                  color: getStatusColor(order.status),\n                  size: \"small\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 449,\n                  columnNumber: 21\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 448,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n                children: order.date\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 455,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n                align: \"right\",\n                children: /*#__PURE__*/_jsxDEV(Button, {\n                  size: \"small\",\n                  variant: \"outlined\",\n                  children: \"View\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 457,\n                  columnNumber: 21\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 456,\n                columnNumber: 19\n              }, this)]\n            }, order.id, true, {\n              fileName: _jsxFileName,\n              lineNumber: 443,\n              columnNumber: 17\n            }, this))\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 441,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 429,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 428,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 426,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Menu, {\n      anchorEl: anchorEl,\n      open: Boolean(anchorEl),\n      onClose: handleMenuClose,\n      children: [/*#__PURE__*/_jsxDEV(MenuItem, {\n        onClick: handleMenuClose,\n        children: [/*#__PURE__*/_jsxDEV(Edit, {\n          sx: {\n            mr: 1\n          }\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 475,\n          columnNumber: 11\n        }, this), \" Edit\"]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 474,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(MenuItem, {\n        onClick: handleMenuClose,\n        children: [/*#__PURE__*/_jsxDEV(Delete, {\n          sx: {\n            mr: 1\n          }\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 478,\n          columnNumber: 11\n        }, this), \" Delete\"]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 477,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 469,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 206,\n    columnNumber: 5\n  }, this);\n};\n_s(AdminDashboard, \"d4Qz07Eg2M/Y61mDRYW1VuwcSn8=\");\n_c = AdminDashboard;\nexport default AdminDashboard;\nvar _c;\n$RefreshReg$(_c, \"AdminDashboard\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "Box", "Grid", "Paper", "Typography", "Card", "<PERSON><PERSON><PERSON><PERSON>", "Table", "TableBody", "TableCell", "TableContainer", "TableHead", "TableRow", "<PERSON><PERSON>", "Chip", "Avatar", "LinearProgress", "IconButton", "<PERSON><PERSON>", "MenuItem", "CircularProgress", "<PERSON><PERSON>", "TrendingUp", "TrendingDown", "People", "ShoppingCart", "Visibility", "AttachMoney", "<PERSON><PERSON><PERSON>", "Edit", "Delete", "Refresh", "analyticsAPI", "handleAPIError", "jsxDEV", "_jsxDEV", "AdminDashboard", "_s", "_analytics$stats", "_analytics$stats$tota", "_analytics$stats2", "_analytics$stats3", "_analytics$stats3$pag", "_analytics$stats4", "anchorEl", "setAnchorEl", "selectedProduct", "setSelectedProduct", "analytics", "setAnalytics", "loading", "setLoading", "error", "setError", "fetchAnalytics", "response", "getAdminAnalytics", "data", "err", "console", "getMockAnalytics", "stats", "totalVisits", "bounceRate", "pageViews", "growthRate", "totals", "users", "products", "orders", "revenue", "monthlyData", "month", "sales", "recentOrders", "id", "customer", "total", "status", "date", "items", "topProducts", "name", "trafficSources", "value", "color", "browserStats", "percentage", "sx", "display", "justifyContent", "alignItems", "height", "children", "size", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "title", "toLocaleString", "icon", "trend", "trafficData", "image", "handleMenuClick", "event", "product", "currentTarget", "handleMenuClose", "getStatusColor", "p", "backgroundColor", "minHeight", "mb", "variant", "fontWeight", "startIcon", "onClick", "disabled", "severity", "container", "spacing", "map", "stat", "index", "item", "xs", "sm", "md", "background", "opacity", "fontSize", "mt", "flexDirection", "textAlign", "borderRadius", "gap", "width", "mr", "browser", "align", "e", "order", "amount", "label", "open", "Boolean", "onClose", "_c", "$RefreshReg$"], "sources": ["D:/ecommerce/client/src/components/admin/AdminDashboard.js"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\nimport {\n  Box,\n  Grid,\n  Paper,\n  Typography,\n  Card,\n  CardContent,\n  Table,\n  TableBody,\n  TableCell,\n  TableContainer,\n  TableHead,\n  TableRow,\n  Button,\n  Chip,\n  Avatar,\n  LinearProgress,\n  IconButton,\n  Menu,\n  MenuItem,\n  CircularProgress,\n  Alert,\n} from '@mui/material';\nimport {\n  TrendingUp,\n  TrendingDown,\n  People,\n  ShoppingCart,\n  Visibility,\n  AttachMoney,\n  MoreVert,\n  Edit,\n  Delete,\n  Refresh,\n} from '@mui/icons-material';\nimport { analyticsAPI, handleAPIError } from '../../services/api';\n\nconst AdminDashboard = () => {\n  const [anchorEl, setAnchorEl] = useState(null);\n  const [selectedProduct, setSelectedProduct] = useState(null);\n  const [analytics, setAnalytics] = useState(null);\n  const [loading, setLoading] = useState(true);\n  const [error, setError] = useState(null);\n\n  // Fetch analytics data\n  const fetchAnalytics = async () => {\n    try {\n      setLoading(true);\n      setError(null);\n      const response = await analyticsAPI.getAdminAnalytics();\n      setAnalytics(response.data);\n    } catch (err) {\n      setError(handleAPIError(err));\n      console.error('Failed to fetch analytics:', err);\n      // Fallback to mock data\n      setAnalytics(getMockAnalytics());\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  useEffect(() => {\n    fetchAnalytics();\n  }, []);\n\n  // Mock data fallback\n  const getMockAnalytics = () => ({\n    stats: {\n      totalVisits: 914001,\n      bounceRate: 46.41,\n      pageViews: 4054876,\n      growthRate: 46.43\n    },\n    totals: {\n      users: 1250,\n      products: 45,\n      orders: 324,\n      revenue: 72000\n    },\n    monthlyData: [\n      { month: 'Jan', users: 324, sales: 45000 },\n      { month: 'Feb', users: 456, sales: 52000 },\n      { month: 'Mar', users: 378, sales: 48000 },\n      { month: 'Apr', users: 520, sales: 61000 },\n      { month: 'May', users: 489, sales: 58000 },\n      { month: 'Jun', users: 612, sales: 72000 },\n    ],\n    recentOrders: [\n      { id: 'ORD001', customer: 'John Doe', total: 24.99, status: 'Completed', date: '2024-01-15', items: 2 },\n      { id: 'ORD002', customer: 'Jane Smith', total: 18.50, status: 'Processing', date: '2024-01-14', items: 1 },\n      { id: 'ORD003', customer: 'Mike Johnson', total: 32.75, status: 'Shipped', date: '2024-01-13', items: 3 },\n    ],\n    topProducts: [\n      { id: 1, name: 'Cinnamon Sticks', sales: 245, revenue: 6125 },\n      { id: 2, name: 'Black Pepper', sales: 189, revenue: 3402 },\n      { id: 3, name: 'Turmeric Powder', sales: 156, revenue: 4992 },\n    ],\n    trafficSources: [\n      { name: 'Organic', value: 44.46, color: '#2196f3' },\n      { name: 'Referral', value: 5.54, color: '#4caf50' },\n      { name: 'Other', value: 50, color: '#ff9800' }\n    ],\n    browserStats: [\n      { name: 'Google Chrome', percentage: 60, color: '#4285f4' },\n      { name: 'Mozilla Firefox', percentage: 18, color: '#ff7139' },\n      { name: 'Internet Explorer', percentage: 12, color: '#00bcf2' },\n      { name: 'Safari', percentage: 10, color: '#000000' }\n    ]\n  });\n\n  if (loading) {\n    return (\n      <Box sx={{ display: 'flex', justifyContent: 'center', alignItems: 'center', height: '100vh' }}>\n        <CircularProgress size={60} />\n      </Box>\n    );\n  }\n\n  const stats = [\n    {\n      title: 'Total Visits',\n      value: analytics?.stats?.totalVisits?.toLocaleString() || '914,001',\n      icon: People,\n      color: '#f44336',\n      trend: '+12.5%'\n    },\n    {\n      title: 'Bounce Rate',\n      value: `${analytics?.stats?.bounceRate || 46.41}%`,\n      icon: TrendingDown,\n      color: '#ff9800',\n      trend: '-2.3%'\n    },\n    {\n      title: 'Page Views',\n      value: analytics?.stats?.pageViews?.toLocaleString() || '4,054,876',\n      icon: Visibility,\n      color: '#4caf50',\n      trend: '+8.7%'\n    },\n    {\n      title: 'Growth Rate',\n      value: `${analytics?.stats?.growthRate || 46.43}%`,\n      icon: TrendingUp,\n      color: '#2196f3',\n      trend: '+15.2%'\n    },\n  ];\n\n  const trafficData = analytics?.trafficSources || [\n    { name: 'Organic', value: 44.46, color: '#2196f3' },\n    { name: 'Referral', value: 5.54, color: '#4caf50' },\n    { name: 'Other', value: 50, color: '#ff9800' },\n  ];\n\n  const browserStats = analytics?.browserStats || [\n    { name: 'Google Chrome', percentage: 60, color: '#4285f4' },\n    { name: 'Mozilla Firefox', percentage: 18, color: '#ff7139' },\n    { name: 'Internet Explorer', percentage: 12, color: '#00bcf2' },\n    { name: 'Safari', percentage: 10, color: '#000000' },\n  ];\n\n  const monthlyData = analytics?.monthlyData || [\n    { month: 'Jan', users: 324, sales: 45000 },\n    { month: 'Feb', users: 456, sales: 52000 },\n    { month: 'Mar', users: 378, sales: 48000 },\n    { month: 'Apr', users: 520, sales: 61000 },\n    { month: 'May', users: 489, sales: 58000 },\n    { month: 'Jun', users: 612, sales: 72000 },\n  ];\n\n  const recentOrders = analytics?.recentOrders || [\n    { id: 'ORD001', customer: 'John Doe', total: 24.99, status: 'Completed', date: '2024-01-15', items: 2 },\n    { id: 'ORD002', customer: 'Jane Smith', total: 18.50, status: 'Processing', date: '2024-01-14', items: 1 },\n    { id: 'ORD003', customer: 'Mike Johnson', total: 32.75, status: 'Shipped', date: '2024-01-13', items: 3 },\n  ];\n\n  const topProducts = analytics?.topProducts || [\n    { id: 1, name: 'Cinnamon Sticks', sales: 245, revenue: 6125, image: '🍯' },\n    { id: 2, name: 'Black Pepper', sales: 189, revenue: 3402, image: '🌶️' },\n    { id: 3, name: 'Turmeric Powder', sales: 156, revenue: 4992, image: '🟡' },\n  ];\n\n  const handleMenuClick = (event, product) => {\n    setAnchorEl(event.currentTarget);\n    setSelectedProduct(product);\n  };\n\n  const handleMenuClose = () => {\n    setAnchorEl(null);\n    setSelectedProduct(null);\n  };\n\n  const getStatusColor = (status) => {\n    switch (status) {\n      case 'Completed': return 'success';\n      case 'Processing': return 'warning';\n      case 'Shipped': return 'info';\n      case 'Pending': return 'default';\n      default: return 'default';\n    }\n  };\n\n  return (\n    <Box sx={{ p: 3, backgroundColor: '#f5f5f5', minHeight: '100vh' }}>\n      <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 3 }}>\n        <Typography variant=\"h4\" sx={{ fontWeight: 'bold' }}>\n          Admin Dashboard\n        </Typography>\n        <Button\n          variant=\"outlined\"\n          startIcon={<Refresh />}\n          onClick={fetchAnalytics}\n          disabled={loading}\n        >\n          Refresh Data\n        </Button>\n      </Box>\n\n      {error && (\n        <Alert severity=\"warning\" sx={{ mb: 3 }}>\n          {error} - Showing fallback data\n        </Alert>\n      )}\n\n      {/* Stats Cards */}\n      <Grid container spacing={3} sx={{ mb: 4 }}>\n        {stats.map((stat, index) => (\n          <Grid item xs={12} sm={6} md={3} key={index}>\n            <Card sx={{ background: stat.color, color: 'white', height: '120px' }}>\n              <CardContent>\n                <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>\n                  <Box>\n                    <Typography variant=\"h4\" sx={{ fontWeight: 'bold', mb: 1 }}>\n                      {stat.value}\n                    </Typography>\n                    <Typography variant=\"body2\" sx={{ opacity: 0.9 }}>\n                      {stat.title}\n                    </Typography>\n                  </Box>\n                  <stat.icon sx={{ fontSize: 40, opacity: 0.8 }} />\n                </Box>\n                <Typography variant=\"caption\" sx={{ mt: 1, display: 'block' }}>\n                  {stat.trend}\n                </Typography>\n              </CardContent>\n            </Card>\n          </Grid>\n        ))}\n      </Grid>\n\n      <Grid container spacing={3}>\n        {/* User Statistics Chart */}\n        <Grid item xs={12} md={8}>\n          <Paper sx={{ p: 3, height: '400px' }}>\n            <Typography variant=\"h6\" sx={{ mb: 2 }}>Monthly Sales & Users</Typography>\n            <Box sx={{ height: '90%', display: 'flex', flexDirection: 'column', justifyContent: 'center' }}>\n              <Grid container spacing={2}>\n                {monthlyData.map((data, index) => (\n                  <Grid item xs={2} key={data.month}>\n                    <Box sx={{ textAlign: 'center' }}>\n                      <Typography variant=\"caption\" color=\"textSecondary\">\n                        {data.month}\n                      </Typography>\n                      <Box sx={{ mt: 1, mb: 1 }}>\n                        <Box\n                          sx={{\n                            height: `${(data.users / 600) * 100}px`,\n                            backgroundColor: '#2196f3',\n                            borderRadius: 1,\n                            mb: 0.5,\n                            minHeight: '20px',\n                          }}\n                        />\n                        <Typography variant=\"caption\" sx={{ fontSize: '0.7rem' }}>\n                          {data.users} users\n                        </Typography>\n                      </Box>\n                      <Box>\n                        <Box\n                          sx={{\n                            height: `${(data.sales / 80000) * 100}px`,\n                            backgroundColor: '#4caf50',\n                            borderRadius: 1,\n                            mb: 0.5,\n                            minHeight: '20px',\n                          }}\n                        />\n                        <Typography variant=\"caption\" sx={{ fontSize: '0.7rem' }}>\n                          ${data.sales}\n                        </Typography>\n                      </Box>\n                    </Box>\n                  </Grid>\n                ))}\n              </Grid>\n              <Box sx={{ display: 'flex', justifyContent: 'center', mt: 2, gap: 2 }}>\n                <Box sx={{ display: 'flex', alignItems: 'center' }}>\n                  <Box sx={{ width: 12, height: 12, backgroundColor: '#2196f3', mr: 1 }} />\n                  <Typography variant=\"caption\">Users</Typography>\n                </Box>\n                <Box sx={{ display: 'flex', alignItems: 'center' }}>\n                  <Box sx={{ width: 12, height: 12, backgroundColor: '#4caf50', mr: 1 }} />\n                  <Typography variant=\"caption\">Sales ($)</Typography>\n                </Box>\n              </Box>\n            </Box>\n          </Paper>\n        </Grid>\n\n        {/* Customer Satisfaction */}\n        <Grid item xs={12} md={4}>\n          <Paper sx={{ p: 3, height: '400px' }}>\n            <Typography variant=\"h6\" sx={{ mb: 2 }}>Customer Satisfaction</Typography>\n            <Box sx={{ textAlign: 'center', mb: 3 }}>\n              <Typography variant=\"h2\" sx={{ color: '#4caf50', fontWeight: 'bold' }}>\n                93.13%\n              </Typography>\n              <Typography variant=\"body2\" color=\"textSecondary\">\n                Previous: 79.82 | Change: +14.29\n              </Typography>\n            </Box>\n\n            <Typography variant=\"subtitle1\" sx={{ mb: 2 }}>Browser Stats</Typography>\n            {browserStats.map((browser, index) => (\n              <Box key={index} sx={{ mb: 2 }}>\n                <Box sx={{ display: 'flex', justifyContent: 'space-between', mb: 1 }}>\n                  <Typography variant=\"body2\">{browser.name}</Typography>\n                  <Typography variant=\"body2\">{browser.percentage}%</Typography>\n                </Box>\n                <LinearProgress\n                  variant=\"determinate\"\n                  value={browser.percentage}\n                  sx={{\n                    height: 8,\n                    borderRadius: 4,\n                    backgroundColor: '#e0e0e0',\n                    '& .MuiLinearProgress-bar': {\n                      backgroundColor: browser.color\n                    }\n                  }}\n                />\n              </Box>\n            ))}\n          </Paper>\n        </Grid>\n\n        {/* Traffic Sources */}\n        <Grid item xs={12} md={6}>\n          <Paper sx={{ p: 3, height: '350px' }}>\n            <Typography variant=\"h6\" sx={{ mb: 2 }}>Visit By Traffic Types</Typography>\n            <Box sx={{ display: 'flex', flexDirection: 'column', height: '80%', justifyContent: 'center' }}>\n              {trafficData.map((item, index) => (\n                <Box key={index} sx={{ mb: 3 }}>\n                  <Box sx={{ display: 'flex', justifyContent: 'space-between', mb: 1 }}>\n                    <Typography variant=\"body2\">{item.name}</Typography>\n                    <Typography variant=\"body2\" sx={{ fontWeight: 'bold' }}>\n                      {item.value}%\n                    </Typography>\n                  </Box>\n                  <LinearProgress\n                    variant=\"determinate\"\n                    value={item.value}\n                    sx={{\n                      height: 12,\n                      borderRadius: 6,\n                      backgroundColor: '#e0e0e0',\n                      '& .MuiLinearProgress-bar': {\n                        backgroundColor: item.color,\n                        borderRadius: 6,\n                      },\n                    }}\n                  />\n                </Box>\n              ))}\n              <Box sx={{ mt: 2, textAlign: 'center' }}>\n                <Typography variant=\"caption\" color=\"textSecondary\">\n                  Total traffic distribution across different sources\n                </Typography>\n              </Box>\n            </Box>\n          </Paper>\n        </Grid>\n\n        {/* Top Products */}\n        <Grid item xs={12} md={6}>\n          <Paper sx={{ p: 3, height: '350px' }}>\n            <Typography variant=\"h6\" sx={{ mb: 2 }}>Top Selling Products</Typography>\n            <TableContainer>\n              <Table size=\"small\">\n                <TableHead>\n                  <TableRow>\n                    <TableCell>Product</TableCell>\n                    <TableCell align=\"right\">Sales</TableCell>\n                    <TableCell align=\"right\">Revenue</TableCell>\n                    <TableCell align=\"right\">Actions</TableCell>\n                  </TableRow>\n                </TableHead>\n                <TableBody>\n                  {topProducts.map((product) => (\n                    <TableRow key={product.id}>\n                      <TableCell>\n                        <Box sx={{ display: 'flex', alignItems: 'center' }}>\n                          <Typography sx={{ mr: 1, fontSize: '1.2em' }}>{product.image}</Typography>\n                          <Typography variant=\"body2\">{product.name}</Typography>\n                        </Box>\n                      </TableCell>\n                      <TableCell align=\"right\">{product.sales}</TableCell>\n                      <TableCell align=\"right\">${product.revenue}</TableCell>\n                      <TableCell align=\"right\">\n                        <IconButton size=\"small\" onClick={(e) => handleMenuClick(e, product)}>\n                          <MoreVert />\n                        </IconButton>\n                      </TableCell>\n                    </TableRow>\n                  ))}\n                </TableBody>\n              </Table>\n            </TableContainer>\n          </Paper>\n        </Grid>\n      </Grid>\n\n      {/* Recent Orders */}\n      <Paper sx={{ p: 3, mt: 3 }}>\n        <Typography variant=\"h6\" sx={{ mb: 2 }}>Recent Orders</Typography>\n        <TableContainer>\n          <Table>\n            <TableHead>\n              <TableRow>\n                <TableCell>Order ID</TableCell>\n                <TableCell>Customer</TableCell>\n                <TableCell>Product</TableCell>\n                <TableCell align=\"right\">Amount</TableCell>\n                <TableCell>Status</TableCell>\n                <TableCell>Date</TableCell>\n                <TableCell align=\"right\">Actions</TableCell>\n              </TableRow>\n            </TableHead>\n            <TableBody>\n              {recentOrders.map((order) => (\n                <TableRow key={order.id}>\n                  <TableCell>{order.id}</TableCell>\n                  <TableCell>{order.customer}</TableCell>\n                  <TableCell>{order.product}</TableCell>\n                  <TableCell align=\"right\">${order.amount}</TableCell>\n                  <TableCell>\n                    <Chip\n                      label={order.status}\n                      color={getStatusColor(order.status)}\n                      size=\"small\"\n                    />\n                  </TableCell>\n                  <TableCell>{order.date}</TableCell>\n                  <TableCell align=\"right\">\n                    <Button size=\"small\" variant=\"outlined\">\n                      View\n                    </Button>\n                  </TableCell>\n                </TableRow>\n              ))}\n            </TableBody>\n          </Table>\n        </TableContainer>\n      </Paper>\n\n      {/* Menu for product actions */}\n      <Menu\n        anchorEl={anchorEl}\n        open={Boolean(anchorEl)}\n        onClose={handleMenuClose}\n      >\n        <MenuItem onClick={handleMenuClose}>\n          <Edit sx={{ mr: 1 }} /> Edit\n        </MenuItem>\n        <MenuItem onClick={handleMenuClose}>\n          <Delete sx={{ mr: 1 }} /> Delete\n        </MenuItem>\n      </Menu>\n    </Box>\n  );\n};\n\nexport default AdminDashboard;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAClD,SACEC,GAAG,EACHC,IAAI,EACJC,KAAK,EACLC,UAAU,EACVC,IAAI,EACJC,WAAW,EACXC,KAAK,EACLC,SAAS,EACTC,SAAS,EACTC,cAAc,EACdC,SAAS,EACTC,QAAQ,EACRC,MAAM,EACNC,IAAI,EACJC,MAAM,EACNC,cAAc,EACdC,UAAU,EACVC,IAAI,EACJC,QAAQ,EACRC,gBAAgB,EAChBC,KAAK,QACA,eAAe;AACtB,SACEC,UAAU,EACVC,YAAY,EACZC,MAAM,EACNC,YAAY,EACZC,UAAU,EACVC,WAAW,EACXC,QAAQ,EACRC,IAAI,EACJC,MAAM,EACNC,OAAO,QACF,qBAAqB;AAC5B,SAASC,YAAY,EAAEC,cAAc,QAAQ,oBAAoB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAElE,MAAMC,cAAc,GAAGA,CAAA,KAAM;EAAAC,EAAA;EAAA,IAAAC,gBAAA,EAAAC,qBAAA,EAAAC,iBAAA,EAAAC,iBAAA,EAAAC,qBAAA,EAAAC,iBAAA;EAC3B,MAAM,CAACC,QAAQ,EAAEC,WAAW,CAAC,GAAG9C,QAAQ,CAAC,IAAI,CAAC;EAC9C,MAAM,CAAC+C,eAAe,EAAEC,kBAAkB,CAAC,GAAGhD,QAAQ,CAAC,IAAI,CAAC;EAC5D,MAAM,CAACiD,SAAS,EAAEC,YAAY,CAAC,GAAGlD,QAAQ,CAAC,IAAI,CAAC;EAChD,MAAM,CAACmD,OAAO,EAAEC,UAAU,CAAC,GAAGpD,QAAQ,CAAC,IAAI,CAAC;EAC5C,MAAM,CAACqD,KAAK,EAAEC,QAAQ,CAAC,GAAGtD,QAAQ,CAAC,IAAI,CAAC;;EAExC;EACA,MAAMuD,cAAc,GAAG,MAAAA,CAAA,KAAY;IACjC,IAAI;MACFH,UAAU,CAAC,IAAI,CAAC;MAChBE,QAAQ,CAAC,IAAI,CAAC;MACd,MAAME,QAAQ,GAAG,MAAMvB,YAAY,CAACwB,iBAAiB,CAAC,CAAC;MACvDP,YAAY,CAACM,QAAQ,CAACE,IAAI,CAAC;IAC7B,CAAC,CAAC,OAAOC,GAAG,EAAE;MACZL,QAAQ,CAACpB,cAAc,CAACyB,GAAG,CAAC,CAAC;MAC7BC,OAAO,CAACP,KAAK,CAAC,4BAA4B,EAAEM,GAAG,CAAC;MAChD;MACAT,YAAY,CAACW,gBAAgB,CAAC,CAAC,CAAC;IAClC,CAAC,SAAS;MACRT,UAAU,CAAC,KAAK,CAAC;IACnB;EACF,CAAC;EAEDnD,SAAS,CAAC,MAAM;IACdsD,cAAc,CAAC,CAAC;EAClB,CAAC,EAAE,EAAE,CAAC;;EAEN;EACA,MAAMM,gBAAgB,GAAGA,CAAA,MAAO;IAC9BC,KAAK,EAAE;MACLC,WAAW,EAAE,MAAM;MACnBC,UAAU,EAAE,KAAK;MACjBC,SAAS,EAAE,OAAO;MAClBC,UAAU,EAAE;IACd,CAAC;IACDC,MAAM,EAAE;MACNC,KAAK,EAAE,IAAI;MACXC,QAAQ,EAAE,EAAE;MACZC,MAAM,EAAE,GAAG;MACXC,OAAO,EAAE;IACX,CAAC;IACDC,WAAW,EAAE,CACX;MAAEC,KAAK,EAAE,KAAK;MAAEL,KAAK,EAAE,GAAG;MAAEM,KAAK,EAAE;IAAM,CAAC,EAC1C;MAAED,KAAK,EAAE,KAAK;MAAEL,KAAK,EAAE,GAAG;MAAEM,KAAK,EAAE;IAAM,CAAC,EAC1C;MAAED,KAAK,EAAE,KAAK;MAAEL,KAAK,EAAE,GAAG;MAAEM,KAAK,EAAE;IAAM,CAAC,EAC1C;MAAED,KAAK,EAAE,KAAK;MAAEL,KAAK,EAAE,GAAG;MAAEM,KAAK,EAAE;IAAM,CAAC,EAC1C;MAAED,KAAK,EAAE,KAAK;MAAEL,KAAK,EAAE,GAAG;MAAEM,KAAK,EAAE;IAAM,CAAC,EAC1C;MAAED,KAAK,EAAE,KAAK;MAAEL,KAAK,EAAE,GAAG;MAAEM,KAAK,EAAE;IAAM,CAAC,CAC3C;IACDC,YAAY,EAAE,CACZ;MAAEC,EAAE,EAAE,QAAQ;MAAEC,QAAQ,EAAE,UAAU;MAAEC,KAAK,EAAE,KAAK;MAAEC,MAAM,EAAE,WAAW;MAAEC,IAAI,EAAE,YAAY;MAAEC,KAAK,EAAE;IAAE,CAAC,EACvG;MAAEL,EAAE,EAAE,QAAQ;MAAEC,QAAQ,EAAE,YAAY;MAAEC,KAAK,EAAE,KAAK;MAAEC,MAAM,EAAE,YAAY;MAAEC,IAAI,EAAE,YAAY;MAAEC,KAAK,EAAE;IAAE,CAAC,EAC1G;MAAEL,EAAE,EAAE,QAAQ;MAAEC,QAAQ,EAAE,cAAc;MAAEC,KAAK,EAAE,KAAK;MAAEC,MAAM,EAAE,SAAS;MAAEC,IAAI,EAAE,YAAY;MAAEC,KAAK,EAAE;IAAE,CAAC,CAC1G;IACDC,WAAW,EAAE,CACX;MAAEN,EAAE,EAAE,CAAC;MAAEO,IAAI,EAAE,iBAAiB;MAAET,KAAK,EAAE,GAAG;MAAEH,OAAO,EAAE;IAAK,CAAC,EAC7D;MAAEK,EAAE,EAAE,CAAC;MAAEO,IAAI,EAAE,cAAc;MAAET,KAAK,EAAE,GAAG;MAAEH,OAAO,EAAE;IAAK,CAAC,EAC1D;MAAEK,EAAE,EAAE,CAAC;MAAEO,IAAI,EAAE,iBAAiB;MAAET,KAAK,EAAE,GAAG;MAAEH,OAAO,EAAE;IAAK,CAAC,CAC9D;IACDa,cAAc,EAAE,CACd;MAAED,IAAI,EAAE,SAAS;MAAEE,KAAK,EAAE,KAAK;MAAEC,KAAK,EAAE;IAAU,CAAC,EACnD;MAAEH,IAAI,EAAE,UAAU;MAAEE,KAAK,EAAE,IAAI;MAAEC,KAAK,EAAE;IAAU,CAAC,EACnD;MAAEH,IAAI,EAAE,OAAO;MAAEE,KAAK,EAAE,EAAE;MAAEC,KAAK,EAAE;IAAU,CAAC,CAC/C;IACDC,YAAY,EAAE,CACZ;MAAEJ,IAAI,EAAE,eAAe;MAAEK,UAAU,EAAE,EAAE;MAAEF,KAAK,EAAE;IAAU,CAAC,EAC3D;MAAEH,IAAI,EAAE,iBAAiB;MAAEK,UAAU,EAAE,EAAE;MAAEF,KAAK,EAAE;IAAU,CAAC,EAC7D;MAAEH,IAAI,EAAE,mBAAmB;MAAEK,UAAU,EAAE,EAAE;MAAEF,KAAK,EAAE;IAAU,CAAC,EAC/D;MAAEH,IAAI,EAAE,QAAQ;MAAEK,UAAU,EAAE,EAAE;MAAEF,KAAK,EAAE;IAAU,CAAC;EAExD,CAAC,CAAC;EAEF,IAAInC,OAAO,EAAE;IACX,oBACEf,OAAA,CAAClC,GAAG;MAACuF,EAAE,EAAE;QAAEC,OAAO,EAAE,MAAM;QAAEC,cAAc,EAAE,QAAQ;QAAEC,UAAU,EAAE,QAAQ;QAAEC,MAAM,EAAE;MAAQ,CAAE;MAAAC,QAAA,eAC5F1D,OAAA,CAACf,gBAAgB;QAAC0E,IAAI,EAAE;MAAG;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAC3B,CAAC;EAEV;EAEA,MAAMrC,KAAK,GAAG,CACZ;IACEsC,KAAK,EAAE,cAAc;IACrBf,KAAK,EAAE,CAAApC,SAAS,aAATA,SAAS,wBAAAV,gBAAA,GAATU,SAAS,CAAEa,KAAK,cAAAvB,gBAAA,wBAAAC,qBAAA,GAAhBD,gBAAA,CAAkBwB,WAAW,cAAAvB,qBAAA,uBAA7BA,qBAAA,CAA+B6D,cAAc,CAAC,CAAC,KAAI,SAAS;IACnEC,IAAI,EAAE7E,MAAM;IACZ6D,KAAK,EAAE,SAAS;IAChBiB,KAAK,EAAE;EACT,CAAC,EACD;IACEH,KAAK,EAAE,aAAa;IACpBf,KAAK,EAAE,GAAG,CAAApC,SAAS,aAATA,SAAS,wBAAAR,iBAAA,GAATQ,SAAS,CAAEa,KAAK,cAAArB,iBAAA,uBAAhBA,iBAAA,CAAkBuB,UAAU,KAAI,KAAK,GAAG;IAClDsC,IAAI,EAAE9E,YAAY;IAClB8D,KAAK,EAAE,SAAS;IAChBiB,KAAK,EAAE;EACT,CAAC,EACD;IACEH,KAAK,EAAE,YAAY;IACnBf,KAAK,EAAE,CAAApC,SAAS,aAATA,SAAS,wBAAAP,iBAAA,GAATO,SAAS,CAAEa,KAAK,cAAApB,iBAAA,wBAAAC,qBAAA,GAAhBD,iBAAA,CAAkBuB,SAAS,cAAAtB,qBAAA,uBAA3BA,qBAAA,CAA6B0D,cAAc,CAAC,CAAC,KAAI,WAAW;IACnEC,IAAI,EAAE3E,UAAU;IAChB2D,KAAK,EAAE,SAAS;IAChBiB,KAAK,EAAE;EACT,CAAC,EACD;IACEH,KAAK,EAAE,aAAa;IACpBf,KAAK,EAAE,GAAG,CAAApC,SAAS,aAATA,SAAS,wBAAAL,iBAAA,GAATK,SAAS,CAAEa,KAAK,cAAAlB,iBAAA,uBAAhBA,iBAAA,CAAkBsB,UAAU,KAAI,KAAK,GAAG;IAClDoC,IAAI,EAAE/E,UAAU;IAChB+D,KAAK,EAAE,SAAS;IAChBiB,KAAK,EAAE;EACT,CAAC,CACF;EAED,MAAMC,WAAW,GAAG,CAAAvD,SAAS,aAATA,SAAS,uBAATA,SAAS,CAAEmC,cAAc,KAAI,CAC/C;IAAED,IAAI,EAAE,SAAS;IAAEE,KAAK,EAAE,KAAK;IAAEC,KAAK,EAAE;EAAU,CAAC,EACnD;IAAEH,IAAI,EAAE,UAAU;IAAEE,KAAK,EAAE,IAAI;IAAEC,KAAK,EAAE;EAAU,CAAC,EACnD;IAAEH,IAAI,EAAE,OAAO;IAAEE,KAAK,EAAE,EAAE;IAAEC,KAAK,EAAE;EAAU,CAAC,CAC/C;EAED,MAAMC,YAAY,GAAG,CAAAtC,SAAS,aAATA,SAAS,uBAATA,SAAS,CAAEsC,YAAY,KAAI,CAC9C;IAAEJ,IAAI,EAAE,eAAe;IAAEK,UAAU,EAAE,EAAE;IAAEF,KAAK,EAAE;EAAU,CAAC,EAC3D;IAAEH,IAAI,EAAE,iBAAiB;IAAEK,UAAU,EAAE,EAAE;IAAEF,KAAK,EAAE;EAAU,CAAC,EAC7D;IAAEH,IAAI,EAAE,mBAAmB;IAAEK,UAAU,EAAE,EAAE;IAAEF,KAAK,EAAE;EAAU,CAAC,EAC/D;IAAEH,IAAI,EAAE,QAAQ;IAAEK,UAAU,EAAE,EAAE;IAAEF,KAAK,EAAE;EAAU,CAAC,CACrD;EAED,MAAMd,WAAW,GAAG,CAAAvB,SAAS,aAATA,SAAS,uBAATA,SAAS,CAAEuB,WAAW,KAAI,CAC5C;IAAEC,KAAK,EAAE,KAAK;IAAEL,KAAK,EAAE,GAAG;IAAEM,KAAK,EAAE;EAAM,CAAC,EAC1C;IAAED,KAAK,EAAE,KAAK;IAAEL,KAAK,EAAE,GAAG;IAAEM,KAAK,EAAE;EAAM,CAAC,EAC1C;IAAED,KAAK,EAAE,KAAK;IAAEL,KAAK,EAAE,GAAG;IAAEM,KAAK,EAAE;EAAM,CAAC,EAC1C;IAAED,KAAK,EAAE,KAAK;IAAEL,KAAK,EAAE,GAAG;IAAEM,KAAK,EAAE;EAAM,CAAC,EAC1C;IAAED,KAAK,EAAE,KAAK;IAAEL,KAAK,EAAE,GAAG;IAAEM,KAAK,EAAE;EAAM,CAAC,EAC1C;IAAED,KAAK,EAAE,KAAK;IAAEL,KAAK,EAAE,GAAG;IAAEM,KAAK,EAAE;EAAM,CAAC,CAC3C;EAED,MAAMC,YAAY,GAAG,CAAA1B,SAAS,aAATA,SAAS,uBAATA,SAAS,CAAE0B,YAAY,KAAI,CAC9C;IAAEC,EAAE,EAAE,QAAQ;IAAEC,QAAQ,EAAE,UAAU;IAAEC,KAAK,EAAE,KAAK;IAAEC,MAAM,EAAE,WAAW;IAAEC,IAAI,EAAE,YAAY;IAAEC,KAAK,EAAE;EAAE,CAAC,EACvG;IAAEL,EAAE,EAAE,QAAQ;IAAEC,QAAQ,EAAE,YAAY;IAAEC,KAAK,EAAE,KAAK;IAAEC,MAAM,EAAE,YAAY;IAAEC,IAAI,EAAE,YAAY;IAAEC,KAAK,EAAE;EAAE,CAAC,EAC1G;IAAEL,EAAE,EAAE,QAAQ;IAAEC,QAAQ,EAAE,cAAc;IAAEC,KAAK,EAAE,KAAK;IAAEC,MAAM,EAAE,SAAS;IAAEC,IAAI,EAAE,YAAY;IAAEC,KAAK,EAAE;EAAE,CAAC,CAC1G;EAED,MAAMC,WAAW,GAAG,CAAAjC,SAAS,aAATA,SAAS,uBAATA,SAAS,CAAEiC,WAAW,KAAI,CAC5C;IAAEN,EAAE,EAAE,CAAC;IAAEO,IAAI,EAAE,iBAAiB;IAAET,KAAK,EAAE,GAAG;IAAEH,OAAO,EAAE,IAAI;IAAEkC,KAAK,EAAE;EAAK,CAAC,EAC1E;IAAE7B,EAAE,EAAE,CAAC;IAAEO,IAAI,EAAE,cAAc;IAAET,KAAK,EAAE,GAAG;IAAEH,OAAO,EAAE,IAAI;IAAEkC,KAAK,EAAE;EAAM,CAAC,EACxE;IAAE7B,EAAE,EAAE,CAAC;IAAEO,IAAI,EAAE,iBAAiB;IAAET,KAAK,EAAE,GAAG;IAAEH,OAAO,EAAE,IAAI;IAAEkC,KAAK,EAAE;EAAK,CAAC,CAC3E;EAED,MAAMC,eAAe,GAAGA,CAACC,KAAK,EAAEC,OAAO,KAAK;IAC1C9D,WAAW,CAAC6D,KAAK,CAACE,aAAa,CAAC;IAChC7D,kBAAkB,CAAC4D,OAAO,CAAC;EAC7B,CAAC;EAED,MAAME,eAAe,GAAGA,CAAA,KAAM;IAC5BhE,WAAW,CAAC,IAAI,CAAC;IACjBE,kBAAkB,CAAC,IAAI,CAAC;EAC1B,CAAC;EAED,MAAM+D,cAAc,GAAIhC,MAAM,IAAK;IACjC,QAAQA,MAAM;MACZ,KAAK,WAAW;QAAE,OAAO,SAAS;MAClC,KAAK,YAAY;QAAE,OAAO,SAAS;MACnC,KAAK,SAAS;QAAE,OAAO,MAAM;MAC7B,KAAK,SAAS;QAAE,OAAO,SAAS;MAChC;QAAS,OAAO,SAAS;IAC3B;EACF,CAAC;EAED,oBACE3C,OAAA,CAAClC,GAAG;IAACuF,EAAE,EAAE;MAAEuB,CAAC,EAAE,CAAC;MAAEC,eAAe,EAAE,SAAS;MAAEC,SAAS,EAAE;IAAQ,CAAE;IAAApB,QAAA,gBAChE1D,OAAA,CAAClC,GAAG;MAACuF,EAAE,EAAE;QAAEC,OAAO,EAAE,MAAM;QAAEC,cAAc,EAAE,eAAe;QAAEC,UAAU,EAAE,QAAQ;QAAEuB,EAAE,EAAE;MAAE,CAAE;MAAArB,QAAA,gBACzF1D,OAAA,CAAC/B,UAAU;QAAC+G,OAAO,EAAC,IAAI;QAAC3B,EAAE,EAAE;UAAE4B,UAAU,EAAE;QAAO,CAAE;QAAAvB,QAAA,EAAC;MAErD;QAAAE,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAY,CAAC,eACb/D,OAAA,CAACtB,MAAM;QACLsG,OAAO,EAAC,UAAU;QAClBE,SAAS,eAAElF,OAAA,CAACJ,OAAO;UAAAgE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAE;QACvBoB,OAAO,EAAEhE,cAAe;QACxBiE,QAAQ,EAAErE,OAAQ;QAAA2C,QAAA,EACnB;MAED;QAAAE,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAQ,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACN,CAAC,EAEL9C,KAAK,iBACJjB,OAAA,CAACd,KAAK;MAACmG,QAAQ,EAAC,SAAS;MAAChC,EAAE,EAAE;QAAE0B,EAAE,EAAE;MAAE,CAAE;MAAArB,QAAA,GACrCzC,KAAK,EAAC,0BACT;IAAA;MAAA2C,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAO,CACR,eAGD/D,OAAA,CAACjC,IAAI;MAACuH,SAAS;MAACC,OAAO,EAAE,CAAE;MAAClC,EAAE,EAAE;QAAE0B,EAAE,EAAE;MAAE,CAAE;MAAArB,QAAA,EACvChC,KAAK,CAAC8D,GAAG,CAAC,CAACC,IAAI,EAAEC,KAAK,kBACrB1F,OAAA,CAACjC,IAAI;QAAC4H,IAAI;QAACC,EAAE,EAAE,EAAG;QAACC,EAAE,EAAE,CAAE;QAACC,EAAE,EAAE,CAAE;QAAApC,QAAA,eAC9B1D,OAAA,CAAC9B,IAAI;UAACmF,EAAE,EAAE;YAAE0C,UAAU,EAAEN,IAAI,CAACvC,KAAK;YAAEA,KAAK,EAAE,OAAO;YAAEO,MAAM,EAAE;UAAQ,CAAE;UAAAC,QAAA,eACpE1D,OAAA,CAAC7B,WAAW;YAAAuF,QAAA,gBACV1D,OAAA,CAAClC,GAAG;cAACuF,EAAE,EAAE;gBAAEC,OAAO,EAAE,MAAM;gBAAEC,cAAc,EAAE,eAAe;gBAAEC,UAAU,EAAE;cAAS,CAAE;cAAAE,QAAA,gBAClF1D,OAAA,CAAClC,GAAG;gBAAA4F,QAAA,gBACF1D,OAAA,CAAC/B,UAAU;kBAAC+G,OAAO,EAAC,IAAI;kBAAC3B,EAAE,EAAE;oBAAE4B,UAAU,EAAE,MAAM;oBAAEF,EAAE,EAAE;kBAAE,CAAE;kBAAArB,QAAA,EACxD+B,IAAI,CAACxC;gBAAK;kBAAAW,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACD,CAAC,eACb/D,OAAA,CAAC/B,UAAU;kBAAC+G,OAAO,EAAC,OAAO;kBAAC3B,EAAE,EAAE;oBAAE2C,OAAO,EAAE;kBAAI,CAAE;kBAAAtC,QAAA,EAC9C+B,IAAI,CAACzB;gBAAK;kBAAAJ,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACD,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACV,CAAC,eACN/D,OAAA,CAACyF,IAAI,CAACvB,IAAI;gBAACb,EAAE,EAAE;kBAAE4C,QAAQ,EAAE,EAAE;kBAAED,OAAO,EAAE;gBAAI;cAAE;gBAAApC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC9C,CAAC,eACN/D,OAAA,CAAC/B,UAAU;cAAC+G,OAAO,EAAC,SAAS;cAAC3B,EAAE,EAAE;gBAAE6C,EAAE,EAAE,CAAC;gBAAE5C,OAAO,EAAE;cAAQ,CAAE;cAAAI,QAAA,EAC3D+B,IAAI,CAACtB;YAAK;cAAAP,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACD,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACF;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACV;MAAC,GAlB6B2B,KAAK;QAAA9B,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAmBrC,CACP;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACE,CAAC,eAEP/D,OAAA,CAACjC,IAAI;MAACuH,SAAS;MAACC,OAAO,EAAE,CAAE;MAAA7B,QAAA,gBAEzB1D,OAAA,CAACjC,IAAI;QAAC4H,IAAI;QAACC,EAAE,EAAE,EAAG;QAACE,EAAE,EAAE,CAAE;QAAApC,QAAA,eACvB1D,OAAA,CAAChC,KAAK;UAACqF,EAAE,EAAE;YAAEuB,CAAC,EAAE,CAAC;YAAEnB,MAAM,EAAE;UAAQ,CAAE;UAAAC,QAAA,gBACnC1D,OAAA,CAAC/B,UAAU;YAAC+G,OAAO,EAAC,IAAI;YAAC3B,EAAE,EAAE;cAAE0B,EAAE,EAAE;YAAE,CAAE;YAAArB,QAAA,EAAC;UAAqB;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAY,CAAC,eAC1E/D,OAAA,CAAClC,GAAG;YAACuF,EAAE,EAAE;cAAEI,MAAM,EAAE,KAAK;cAAEH,OAAO,EAAE,MAAM;cAAE6C,aAAa,EAAE,QAAQ;cAAE5C,cAAc,EAAE;YAAS,CAAE;YAAAG,QAAA,gBAC7F1D,OAAA,CAACjC,IAAI;cAACuH,SAAS;cAACC,OAAO,EAAE,CAAE;cAAA7B,QAAA,EACxBtB,WAAW,CAACoD,GAAG,CAAC,CAAClE,IAAI,EAAEoE,KAAK,kBAC3B1F,OAAA,CAACjC,IAAI;gBAAC4H,IAAI;gBAACC,EAAE,EAAE,CAAE;gBAAAlC,QAAA,eACf1D,OAAA,CAAClC,GAAG;kBAACuF,EAAE,EAAE;oBAAE+C,SAAS,EAAE;kBAAS,CAAE;kBAAA1C,QAAA,gBAC/B1D,OAAA,CAAC/B,UAAU;oBAAC+G,OAAO,EAAC,SAAS;oBAAC9B,KAAK,EAAC,eAAe;oBAAAQ,QAAA,EAChDpC,IAAI,CAACe;kBAAK;oBAAAuB,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACD,CAAC,eACb/D,OAAA,CAAClC,GAAG;oBAACuF,EAAE,EAAE;sBAAE6C,EAAE,EAAE,CAAC;sBAAEnB,EAAE,EAAE;oBAAE,CAAE;oBAAArB,QAAA,gBACxB1D,OAAA,CAAClC,GAAG;sBACFuF,EAAE,EAAE;wBACFI,MAAM,EAAE,GAAInC,IAAI,CAACU,KAAK,GAAG,GAAG,GAAI,GAAG,IAAI;wBACvC6C,eAAe,EAAE,SAAS;wBAC1BwB,YAAY,EAAE,CAAC;wBACftB,EAAE,EAAE,GAAG;wBACPD,SAAS,EAAE;sBACb;oBAAE;sBAAAlB,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACH,CAAC,eACF/D,OAAA,CAAC/B,UAAU;sBAAC+G,OAAO,EAAC,SAAS;sBAAC3B,EAAE,EAAE;wBAAE4C,QAAQ,EAAE;sBAAS,CAAE;sBAAAvC,QAAA,GACtDpC,IAAI,CAACU,KAAK,EAAC,QACd;oBAAA;sBAAA4B,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAY,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACV,CAAC,eACN/D,OAAA,CAAClC,GAAG;oBAAA4F,QAAA,gBACF1D,OAAA,CAAClC,GAAG;sBACFuF,EAAE,EAAE;wBACFI,MAAM,EAAE,GAAInC,IAAI,CAACgB,KAAK,GAAG,KAAK,GAAI,GAAG,IAAI;wBACzCuC,eAAe,EAAE,SAAS;wBAC1BwB,YAAY,EAAE,CAAC;wBACftB,EAAE,EAAE,GAAG;wBACPD,SAAS,EAAE;sBACb;oBAAE;sBAAAlB,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACH,CAAC,eACF/D,OAAA,CAAC/B,UAAU;sBAAC+G,OAAO,EAAC,SAAS;sBAAC3B,EAAE,EAAE;wBAAE4C,QAAQ,EAAE;sBAAS,CAAE;sBAAAvC,QAAA,GAAC,GACvD,EAACpC,IAAI,CAACgB,KAAK;oBAAA;sBAAAsB,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACF,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACV,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH;cAAC,GAjCezC,IAAI,CAACe,KAAK;gBAAAuB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAkC3B,CACP;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACE,CAAC,eACP/D,OAAA,CAAClC,GAAG;cAACuF,EAAE,EAAE;gBAAEC,OAAO,EAAE,MAAM;gBAAEC,cAAc,EAAE,QAAQ;gBAAE2C,EAAE,EAAE,CAAC;gBAAEI,GAAG,EAAE;cAAE,CAAE;cAAA5C,QAAA,gBACpE1D,OAAA,CAAClC,GAAG;gBAACuF,EAAE,EAAE;kBAAEC,OAAO,EAAE,MAAM;kBAAEE,UAAU,EAAE;gBAAS,CAAE;gBAAAE,QAAA,gBACjD1D,OAAA,CAAClC,GAAG;kBAACuF,EAAE,EAAE;oBAAEkD,KAAK,EAAE,EAAE;oBAAE9C,MAAM,EAAE,EAAE;oBAAEoB,eAAe,EAAE,SAAS;oBAAE2B,EAAE,EAAE;kBAAE;gBAAE;kBAAA5C,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAC,eACzE/D,OAAA,CAAC/B,UAAU;kBAAC+G,OAAO,EAAC,SAAS;kBAAAtB,QAAA,EAAC;gBAAK;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAY,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC7C,CAAC,eACN/D,OAAA,CAAClC,GAAG;gBAACuF,EAAE,EAAE;kBAAEC,OAAO,EAAE,MAAM;kBAAEE,UAAU,EAAE;gBAAS,CAAE;gBAAAE,QAAA,gBACjD1D,OAAA,CAAClC,GAAG;kBAACuF,EAAE,EAAE;oBAAEkD,KAAK,EAAE,EAAE;oBAAE9C,MAAM,EAAE,EAAE;oBAAEoB,eAAe,EAAE,SAAS;oBAAE2B,EAAE,EAAE;kBAAE;gBAAE;kBAAA5C,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAC,eACzE/D,OAAA,CAAC/B,UAAU;kBAAC+G,OAAO,EAAC,SAAS;kBAAAtB,QAAA,EAAC;gBAAS;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAY,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACjD,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACD;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACJ,CAAC,eAGP/D,OAAA,CAACjC,IAAI;QAAC4H,IAAI;QAACC,EAAE,EAAE,EAAG;QAACE,EAAE,EAAE,CAAE;QAAApC,QAAA,eACvB1D,OAAA,CAAChC,KAAK;UAACqF,EAAE,EAAE;YAAEuB,CAAC,EAAE,CAAC;YAAEnB,MAAM,EAAE;UAAQ,CAAE;UAAAC,QAAA,gBACnC1D,OAAA,CAAC/B,UAAU;YAAC+G,OAAO,EAAC,IAAI;YAAC3B,EAAE,EAAE;cAAE0B,EAAE,EAAE;YAAE,CAAE;YAAArB,QAAA,EAAC;UAAqB;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAY,CAAC,eAC1E/D,OAAA,CAAClC,GAAG;YAACuF,EAAE,EAAE;cAAE+C,SAAS,EAAE,QAAQ;cAAErB,EAAE,EAAE;YAAE,CAAE;YAAArB,QAAA,gBACtC1D,OAAA,CAAC/B,UAAU;cAAC+G,OAAO,EAAC,IAAI;cAAC3B,EAAE,EAAE;gBAAEH,KAAK,EAAE,SAAS;gBAAE+B,UAAU,EAAE;cAAO,CAAE;cAAAvB,QAAA,EAAC;YAEvE;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAY,CAAC,eACb/D,OAAA,CAAC/B,UAAU;cAAC+G,OAAO,EAAC,OAAO;cAAC9B,KAAK,EAAC,eAAe;cAAAQ,QAAA,EAAC;YAElD;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAY,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACV,CAAC,eAEN/D,OAAA,CAAC/B,UAAU;YAAC+G,OAAO,EAAC,WAAW;YAAC3B,EAAE,EAAE;cAAE0B,EAAE,EAAE;YAAE,CAAE;YAAArB,QAAA,EAAC;UAAa;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAY,CAAC,EACxEZ,YAAY,CAACqC,GAAG,CAAC,CAACiB,OAAO,EAAEf,KAAK,kBAC/B1F,OAAA,CAAClC,GAAG;YAAauF,EAAE,EAAE;cAAE0B,EAAE,EAAE;YAAE,CAAE;YAAArB,QAAA,gBAC7B1D,OAAA,CAAClC,GAAG;cAACuF,EAAE,EAAE;gBAAEC,OAAO,EAAE,MAAM;gBAAEC,cAAc,EAAE,eAAe;gBAAEwB,EAAE,EAAE;cAAE,CAAE;cAAArB,QAAA,gBACnE1D,OAAA,CAAC/B,UAAU;gBAAC+G,OAAO,EAAC,OAAO;gBAAAtB,QAAA,EAAE+C,OAAO,CAAC1D;cAAI;gBAAAa,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAa,CAAC,eACvD/D,OAAA,CAAC/B,UAAU;gBAAC+G,OAAO,EAAC,OAAO;gBAAAtB,QAAA,GAAE+C,OAAO,CAACrD,UAAU,EAAC,GAAC;cAAA;gBAAAQ,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAY,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC3D,CAAC,eACN/D,OAAA,CAACnB,cAAc;cACbmG,OAAO,EAAC,aAAa;cACrB/B,KAAK,EAAEwD,OAAO,CAACrD,UAAW;cAC1BC,EAAE,EAAE;gBACFI,MAAM,EAAE,CAAC;gBACT4C,YAAY,EAAE,CAAC;gBACfxB,eAAe,EAAE,SAAS;gBAC1B,0BAA0B,EAAE;kBAC1BA,eAAe,EAAE4B,OAAO,CAACvD;gBAC3B;cACF;YAAE;cAAAU,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC;UAAA,GAhBM2B,KAAK;YAAA9B,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAiBV,CACN,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACG;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACJ,CAAC,eAGP/D,OAAA,CAACjC,IAAI;QAAC4H,IAAI;QAACC,EAAE,EAAE,EAAG;QAACE,EAAE,EAAE,CAAE;QAAApC,QAAA,eACvB1D,OAAA,CAAChC,KAAK;UAACqF,EAAE,EAAE;YAAEuB,CAAC,EAAE,CAAC;YAAEnB,MAAM,EAAE;UAAQ,CAAE;UAAAC,QAAA,gBACnC1D,OAAA,CAAC/B,UAAU;YAAC+G,OAAO,EAAC,IAAI;YAAC3B,EAAE,EAAE;cAAE0B,EAAE,EAAE;YAAE,CAAE;YAAArB,QAAA,EAAC;UAAsB;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAY,CAAC,eAC3E/D,OAAA,CAAClC,GAAG;YAACuF,EAAE,EAAE;cAAEC,OAAO,EAAE,MAAM;cAAE6C,aAAa,EAAE,QAAQ;cAAE1C,MAAM,EAAE,KAAK;cAAEF,cAAc,EAAE;YAAS,CAAE;YAAAG,QAAA,GAC5FU,WAAW,CAACoB,GAAG,CAAC,CAACG,IAAI,EAAED,KAAK,kBAC3B1F,OAAA,CAAClC,GAAG;cAAauF,EAAE,EAAE;gBAAE0B,EAAE,EAAE;cAAE,CAAE;cAAArB,QAAA,gBAC7B1D,OAAA,CAAClC,GAAG;gBAACuF,EAAE,EAAE;kBAAEC,OAAO,EAAE,MAAM;kBAAEC,cAAc,EAAE,eAAe;kBAAEwB,EAAE,EAAE;gBAAE,CAAE;gBAAArB,QAAA,gBACnE1D,OAAA,CAAC/B,UAAU;kBAAC+G,OAAO,EAAC,OAAO;kBAAAtB,QAAA,EAAEiC,IAAI,CAAC5C;gBAAI;kBAAAa,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAa,CAAC,eACpD/D,OAAA,CAAC/B,UAAU;kBAAC+G,OAAO,EAAC,OAAO;kBAAC3B,EAAE,EAAE;oBAAE4B,UAAU,EAAE;kBAAO,CAAE;kBAAAvB,QAAA,GACpDiC,IAAI,CAAC1C,KAAK,EAAC,GACd;gBAAA;kBAAAW,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAY,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACV,CAAC,eACN/D,OAAA,CAACnB,cAAc;gBACbmG,OAAO,EAAC,aAAa;gBACrB/B,KAAK,EAAE0C,IAAI,CAAC1C,KAAM;gBAClBI,EAAE,EAAE;kBACFI,MAAM,EAAE,EAAE;kBACV4C,YAAY,EAAE,CAAC;kBACfxB,eAAe,EAAE,SAAS;kBAC1B,0BAA0B,EAAE;oBAC1BA,eAAe,EAAEc,IAAI,CAACzC,KAAK;oBAC3BmD,YAAY,EAAE;kBAChB;gBACF;cAAE;gBAAAzC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC;YAAA,GAnBM2B,KAAK;cAAA9B,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAoBV,CACN,CAAC,eACF/D,OAAA,CAAClC,GAAG;cAACuF,EAAE,EAAE;gBAAE6C,EAAE,EAAE,CAAC;gBAAEE,SAAS,EAAE;cAAS,CAAE;cAAA1C,QAAA,eACtC1D,OAAA,CAAC/B,UAAU;gBAAC+G,OAAO,EAAC,SAAS;gBAAC9B,KAAK,EAAC,eAAe;gBAAAQ,QAAA,EAAC;cAEpD;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAY;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACV,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACD;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACJ,CAAC,eAGP/D,OAAA,CAACjC,IAAI;QAAC4H,IAAI;QAACC,EAAE,EAAE,EAAG;QAACE,EAAE,EAAE,CAAE;QAAApC,QAAA,eACvB1D,OAAA,CAAChC,KAAK;UAACqF,EAAE,EAAE;YAAEuB,CAAC,EAAE,CAAC;YAAEnB,MAAM,EAAE;UAAQ,CAAE;UAAAC,QAAA,gBACnC1D,OAAA,CAAC/B,UAAU;YAAC+G,OAAO,EAAC,IAAI;YAAC3B,EAAE,EAAE;cAAE0B,EAAE,EAAE;YAAE,CAAE;YAAArB,QAAA,EAAC;UAAoB;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAY,CAAC,eACzE/D,OAAA,CAACzB,cAAc;YAAAmF,QAAA,eACb1D,OAAA,CAAC5B,KAAK;cAACuF,IAAI,EAAC,OAAO;cAAAD,QAAA,gBACjB1D,OAAA,CAACxB,SAAS;gBAAAkF,QAAA,eACR1D,OAAA,CAACvB,QAAQ;kBAAAiF,QAAA,gBACP1D,OAAA,CAAC1B,SAAS;oBAAAoF,QAAA,EAAC;kBAAO;oBAAAE,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAW,CAAC,eAC9B/D,OAAA,CAAC1B,SAAS;oBAACoI,KAAK,EAAC,OAAO;oBAAAhD,QAAA,EAAC;kBAAK;oBAAAE,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAW,CAAC,eAC1C/D,OAAA,CAAC1B,SAAS;oBAACoI,KAAK,EAAC,OAAO;oBAAAhD,QAAA,EAAC;kBAAO;oBAAAE,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAW,CAAC,eAC5C/D,OAAA,CAAC1B,SAAS;oBAACoI,KAAK,EAAC,OAAO;oBAAAhD,QAAA,EAAC;kBAAO;oBAAAE,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAW,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACpC;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACF,CAAC,eACZ/D,OAAA,CAAC3B,SAAS;gBAAAqF,QAAA,EACPZ,WAAW,CAAC0C,GAAG,CAAEhB,OAAO,iBACvBxE,OAAA,CAACvB,QAAQ;kBAAAiF,QAAA,gBACP1D,OAAA,CAAC1B,SAAS;oBAAAoF,QAAA,eACR1D,OAAA,CAAClC,GAAG;sBAACuF,EAAE,EAAE;wBAAEC,OAAO,EAAE,MAAM;wBAAEE,UAAU,EAAE;sBAAS,CAAE;sBAAAE,QAAA,gBACjD1D,OAAA,CAAC/B,UAAU;wBAACoF,EAAE,EAAE;0BAAEmD,EAAE,EAAE,CAAC;0BAAEP,QAAQ,EAAE;wBAAQ,CAAE;wBAAAvC,QAAA,EAAEc,OAAO,CAACH;sBAAK;wBAAAT,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAa,CAAC,eAC1E/D,OAAA,CAAC/B,UAAU;wBAAC+G,OAAO,EAAC,OAAO;wBAAAtB,QAAA,EAAEc,OAAO,CAACzB;sBAAI;wBAAAa,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAa,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACpD;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACG,CAAC,eACZ/D,OAAA,CAAC1B,SAAS;oBAACoI,KAAK,EAAC,OAAO;oBAAAhD,QAAA,EAAEc,OAAO,CAAClC;kBAAK;oBAAAsB,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAY,CAAC,eACpD/D,OAAA,CAAC1B,SAAS;oBAACoI,KAAK,EAAC,OAAO;oBAAAhD,QAAA,GAAC,GAAC,EAACc,OAAO,CAACrC,OAAO;kBAAA;oBAAAyB,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAY,CAAC,eACvD/D,OAAA,CAAC1B,SAAS;oBAACoI,KAAK,EAAC,OAAO;oBAAAhD,QAAA,eACtB1D,OAAA,CAAClB,UAAU;sBAAC6E,IAAI,EAAC,OAAO;sBAACwB,OAAO,EAAGwB,CAAC,IAAKrC,eAAe,CAACqC,CAAC,EAAEnC,OAAO,CAAE;sBAAAd,QAAA,eACnE1D,OAAA,CAACP,QAAQ;wBAAAmE,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAE;oBAAC;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACF;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACJ,CAAC;gBAAA,GAbCS,OAAO,CAAChC,EAAE;kBAAAoB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAcf,CACX;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACO,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACP;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACM,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACZ;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACJ,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,eAGP/D,OAAA,CAAChC,KAAK;MAACqF,EAAE,EAAE;QAAEuB,CAAC,EAAE,CAAC;QAAEsB,EAAE,EAAE;MAAE,CAAE;MAAAxC,QAAA,gBACzB1D,OAAA,CAAC/B,UAAU;QAAC+G,OAAO,EAAC,IAAI;QAAC3B,EAAE,EAAE;UAAE0B,EAAE,EAAE;QAAE,CAAE;QAAArB,QAAA,EAAC;MAAa;QAAAE,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAY,CAAC,eAClE/D,OAAA,CAACzB,cAAc;QAAAmF,QAAA,eACb1D,OAAA,CAAC5B,KAAK;UAAAsF,QAAA,gBACJ1D,OAAA,CAACxB,SAAS;YAAAkF,QAAA,eACR1D,OAAA,CAACvB,QAAQ;cAAAiF,QAAA,gBACP1D,OAAA,CAAC1B,SAAS;gBAAAoF,QAAA,EAAC;cAAQ;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAW,CAAC,eAC/B/D,OAAA,CAAC1B,SAAS;gBAAAoF,QAAA,EAAC;cAAQ;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAW,CAAC,eAC/B/D,OAAA,CAAC1B,SAAS;gBAAAoF,QAAA,EAAC;cAAO;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAW,CAAC,eAC9B/D,OAAA,CAAC1B,SAAS;gBAACoI,KAAK,EAAC,OAAO;gBAAAhD,QAAA,EAAC;cAAM;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAW,CAAC,eAC3C/D,OAAA,CAAC1B,SAAS;gBAAAoF,QAAA,EAAC;cAAM;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAW,CAAC,eAC7B/D,OAAA,CAAC1B,SAAS;gBAAAoF,QAAA,EAAC;cAAI;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAW,CAAC,eAC3B/D,OAAA,CAAC1B,SAAS;gBAACoI,KAAK,EAAC,OAAO;gBAAAhD,QAAA,EAAC;cAAO;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAW,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACpC;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACF,CAAC,eACZ/D,OAAA,CAAC3B,SAAS;YAAAqF,QAAA,EACPnB,YAAY,CAACiD,GAAG,CAAEoB,KAAK,iBACtB5G,OAAA,CAACvB,QAAQ;cAAAiF,QAAA,gBACP1D,OAAA,CAAC1B,SAAS;gBAAAoF,QAAA,EAAEkD,KAAK,CAACpE;cAAE;gBAAAoB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAY,CAAC,eACjC/D,OAAA,CAAC1B,SAAS;gBAAAoF,QAAA,EAAEkD,KAAK,CAACnE;cAAQ;gBAAAmB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAY,CAAC,eACvC/D,OAAA,CAAC1B,SAAS;gBAAAoF,QAAA,EAAEkD,KAAK,CAACpC;cAAO;gBAAAZ,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAY,CAAC,eACtC/D,OAAA,CAAC1B,SAAS;gBAACoI,KAAK,EAAC,OAAO;gBAAAhD,QAAA,GAAC,GAAC,EAACkD,KAAK,CAACC,MAAM;cAAA;gBAAAjD,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAY,CAAC,eACpD/D,OAAA,CAAC1B,SAAS;gBAAAoF,QAAA,eACR1D,OAAA,CAACrB,IAAI;kBACHmI,KAAK,EAAEF,KAAK,CAACjE,MAAO;kBACpBO,KAAK,EAAEyB,cAAc,CAACiC,KAAK,CAACjE,MAAM,CAAE;kBACpCgB,IAAI,EAAC;gBAAO;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACb;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACO,CAAC,eACZ/D,OAAA,CAAC1B,SAAS;gBAAAoF,QAAA,EAAEkD,KAAK,CAAChE;cAAI;gBAAAgB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAY,CAAC,eACnC/D,OAAA,CAAC1B,SAAS;gBAACoI,KAAK,EAAC,OAAO;gBAAAhD,QAAA,eACtB1D,OAAA,CAACtB,MAAM;kBAACiF,IAAI,EAAC,OAAO;kBAACqB,OAAO,EAAC,UAAU;kBAAAtB,QAAA,EAAC;gBAExC;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACA,CAAC;YAAA,GAjBC6C,KAAK,CAACpE,EAAE;cAAAoB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAkBb,CACX;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACO,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACP;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACM,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACZ,CAAC,eAGR/D,OAAA,CAACjB,IAAI;MACH0B,QAAQ,EAAEA,QAAS;MACnBsG,IAAI,EAAEC,OAAO,CAACvG,QAAQ,CAAE;MACxBwG,OAAO,EAAEvC,eAAgB;MAAAhB,QAAA,gBAEzB1D,OAAA,CAAChB,QAAQ;QAACmG,OAAO,EAAET,eAAgB;QAAAhB,QAAA,gBACjC1D,OAAA,CAACN,IAAI;UAAC2D,EAAE,EAAE;YAAEmD,EAAE,EAAE;UAAE;QAAE;UAAA5C,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,SACzB;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAU,CAAC,eACX/D,OAAA,CAAChB,QAAQ;QAACmG,OAAO,EAAET,eAAgB;QAAAhB,QAAA,gBACjC1D,OAAA,CAACL,MAAM;UAAC0D,EAAE,EAAE;YAAEmD,EAAE,EAAE;UAAE;QAAE;UAAA5C,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,WAC3B;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAU,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACP,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACJ,CAAC;AAEV,CAAC;AAAC7D,EAAA,CA5bID,cAAc;AAAAiH,EAAA,GAAdjH,cAAc;AA8bpB,eAAeA,cAAc;AAAC,IAAAiH,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}