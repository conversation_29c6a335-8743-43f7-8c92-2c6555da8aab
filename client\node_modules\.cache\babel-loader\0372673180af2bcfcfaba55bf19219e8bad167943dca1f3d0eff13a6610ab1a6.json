{"ast": null, "code": "var arrayLikeKeys = require('./_arrayLikeKeys'),\n  baseKeysIn = require('./_baseKeysIn'),\n  isArrayLike = require('./isArrayLike');\n\n/**\n * Creates an array of the own and inherited enumerable property names of `object`.\n *\n * **Note:** Non-object values are coerced to objects.\n *\n * @static\n * @memberOf _\n * @since 3.0.0\n * @category Object\n * @param {Object} object The object to query.\n * @returns {Array} Returns the array of property names.\n * @example\n *\n * function Foo() {\n *   this.a = 1;\n *   this.b = 2;\n * }\n *\n * Foo.prototype.c = 3;\n *\n * _.keysIn(new Foo);\n * // => ['a', 'b', 'c'] (iteration order is not guaranteed)\n */\nfunction keysIn(object) {\n  return isArrayLike(object) ? arrayLikeKeys(object, true) : baseKeysIn(object);\n}\nmodule.exports = keysIn;", "map": {"version": 3, "names": ["arrayLikeKeys", "require", "baseKeysIn", "isArrayLike", "keysIn", "object", "module", "exports"], "sources": ["D:/ecommerce/node_modules/lodash/keysIn.js"], "sourcesContent": ["var arrayLikeKeys = require('./_arrayLikeKeys'),\n    baseKeysIn = require('./_baseKeysIn'),\n    isArrayLike = require('./isArrayLike');\n\n/**\n * Creates an array of the own and inherited enumerable property names of `object`.\n *\n * **Note:** Non-object values are coerced to objects.\n *\n * @static\n * @memberOf _\n * @since 3.0.0\n * @category Object\n * @param {Object} object The object to query.\n * @returns {Array} Returns the array of property names.\n * @example\n *\n * function Foo() {\n *   this.a = 1;\n *   this.b = 2;\n * }\n *\n * Foo.prototype.c = 3;\n *\n * _.keysIn(new Foo);\n * // => ['a', 'b', 'c'] (iteration order is not guaranteed)\n */\nfunction keysIn(object) {\n  return isArrayLike(object) ? arrayLikeKeys(object, true) : baseKeysIn(object);\n}\n\nmodule.exports = keysIn;\n"], "mappings": "AAAA,IAAIA,aAAa,GAAGC,OAAO,CAAC,kBAAkB,CAAC;EAC3CC,UAAU,GAAGD,OAAO,CAAC,eAAe,CAAC;EACrCE,WAAW,GAAGF,OAAO,CAAC,eAAe,CAAC;;AAE1C;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASG,MAAMA,CAACC,MAAM,EAAE;EACtB,OAAOF,WAAW,CAACE,MAAM,CAAC,GAAGL,aAAa,CAACK,MAAM,EAAE,IAAI,CAAC,GAAGH,UAAU,CAACG,MAAM,CAAC;AAC/E;AAEAC,MAAM,CAACC,OAAO,GAAGH,MAAM", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}