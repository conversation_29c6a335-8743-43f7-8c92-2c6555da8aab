import React, { useState, useEffect } from 'react';
import { useSelector, useDispatch } from 'react-redux';
import {
  Container,
  Typography,
  Grid,
  Card,
  CardContent,
  Button,
  IconButton,
  Box,
  Divider,
  Paper,
  CardMedia,
  Chip,
  TextField,
  Alert,
  List,
  ListItem,
  ListItemText,
  ListItemIcon,
  Breadcrumbs,
  Link
} from '@mui/material';
import {
  Add,
  Remove,
  Delete,
  ShoppingCart,
  LocalShipping,
  Security,
  LocalOffer,
  CreditCard,
  Home as HomeIcon,
  CheckCircle,
  NavigateNext,
  Favorite,
  Share
} from '@mui/icons-material';
import { cartAPI } from '../services/api';
import { styled } from '@mui/material/styles';
import { useAuth } from '../context/AuthContext';
import { useNavigate } from 'react-router-dom';

// Flipkart-style styled components
const FlipkartHeader = styled(Paper)(({ theme }) => ({
  backgroundColor: '#2874f0',
  color: 'white',
  padding: theme.spacing(1, 0),
  boxShadow: '0 2px 4px rgba(0,0,0,0.1)',
}));

const CartCard = styled(Card)(({ theme }) => ({
  marginBottom: theme.spacing(2),
  boxShadow: '0 2px 4px rgba(0,0,0,0.1)',
  '&:hover': {
    boxShadow: '0 4px 8px rgba(0,0,0,0.15)',
  },
}));

const Cart = () => {
  const dispatch = useDispatch();
  const navigate = useNavigate();
  const { user, isAuthenticated } = useAuth();
  const [cartItems, setCartItems] = useState([]);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    // Load cart items from localStorage
    const cart = cartAPI.getCart();
    setCartItems(cart.items);
    setLoading(false);
  }, []);

  const handleQuantityChange = (itemId, newQuantity) => {
    const cart = cartAPI.updateQuantity(itemId, newQuantity);
    setCartItems(cart.items);
    dispatch({ type: 'UPDATE_CART', payload: cart });
  };

  const handleRemoveItem = (itemId) => {
    const cart = cartAPI.removeFromCart(itemId);
    setCartItems(cart.items);
    dispatch({ type: 'REMOVE_FROM_CART', payload: itemId });
  };

  const calculateTotal = () => {
    return cartItems.reduce((sum, item) => sum + (item.price * item.quantity), 0);
  };

  const calculateSavings = () => {
    return cartItems.reduce((sum, item) => sum + (item.price * 0.25 * item.quantity), 0);
  };

  if (loading) {
    return (
      <Box sx={{ display: 'flex', justifyContent: 'center', alignItems: 'center', height: '50vh' }}>
        <Typography>Loading cart...</Typography>
      </Box>
    );
  }

  if (cartItems.length === 0) {
    return (
      <Box sx={{ backgroundColor: '#f1f3f6', minHeight: '100vh' }}>
        {/* Header */}
        <FlipkartHeader>
          <Container maxWidth="xl">
            <Typography variant="h6" sx={{ py: 1 }}>
              🛒 SpiceMart - Your Cart
            </Typography>
          </Container>
        </FlipkartHeader>

        <Container maxWidth="lg" sx={{ py: 8 }}>
          <Paper sx={{ p: 8, textAlign: 'center' }}>
            <ShoppingCart sx={{ fontSize: 80, color: '#878787', mb: 2 }} />
            <Typography variant="h4" gutterBottom color="textSecondary">
              Your cart is empty!
            </Typography>
            <Typography variant="body1" sx={{ mb: 4, color: '#878787' }}>
              Add items to it now.
            </Typography>
            <Button
              variant="contained"
              size="large"
              href="/"
              sx={{
                backgroundColor: '#2874f0',
                '&:hover': { backgroundColor: '#1e5bb8' },
                px: 4,
                py: 1.5
              }}
            >
              Shop Now
            </Button>
          </Paper>
        </Container>
      </Box>
    );
  }

  const total = calculateTotal();
  const savings = calculateSavings();
  const deliveryCharge = total > 500 ? 0 : 40;
  const finalTotal = total + deliveryCharge;

  return (
    <Box sx={{ backgroundColor: '#f1f3f6', minHeight: '100vh' }}>
      {/* Header */}
      <FlipkartHeader>
        <Container maxWidth="xl">
          <Grid container alignItems="center" spacing={2}>
            <Grid item xs={12} md={6}>
              <Typography variant="h6" sx={{ py: 1 }}>
                🛒 SpiceMart
              </Typography>
            </Grid>
            <Grid item xs={12} md={6}>
              <Box sx={{ display: 'flex', justifyContent: 'flex-end' }}>
                <Chip
                  icon={<Security />}
                  label="100% Secure"
                  sx={{ color: 'white', borderColor: 'white' }}
                  variant="outlined"
                />
              </Box>
            </Grid>
          </Grid>
        </Container>
      </FlipkartHeader>

      {/* Breadcrumbs */}
      <Container maxWidth="xl" sx={{ mt: 2 }}>
        <Breadcrumbs separator={<NavigateNext fontSize="small" />}>
          <Link color="inherit" href="/" sx={{ display: 'flex', alignItems: 'center' }}>
            <HomeIcon sx={{ mr: 0.5 }} fontSize="inherit" />
            Home
          </Link>
          <Typography color="text.primary">My Cart</Typography>
        </Breadcrumbs>
      </Container>

      <Container maxWidth="xl" sx={{ mt: 3, pb: 4 }}>
        <Grid container spacing={3}>
          {/* Cart Items */}
          <Grid item xs={12} lg={8}>
            <Paper sx={{ mb: 2 }}>
              <Box sx={{ p: 2, backgroundColor: '#fff', borderBottom: '1px solid #e0e0e0' }}>
                <Typography variant="h6" sx={{ fontWeight: 'bold' }}>
                  My Cart ({cartItems.length} item{cartItems.length > 1 ? 's' : ''})
                </Typography>
              </Box>

              {cartItems.map((item) => (
                <CartCard key={item.id} elevation={0}>
                  <CardContent sx={{ p: 3 }}>
                    <Grid container spacing={3} alignItems="center">
                      <Grid item xs={12} sm={2}>
                        <CardMedia
                          component="img"
                          height="120"
                          image={item.image || 'https://via.placeholder.com/120x120?text=Spice'}
                          alt={item.name}
                          sx={{ objectFit: 'cover', borderRadius: 1 }}
                        />
                      </Grid>

                      <Grid item xs={12} sm={4}>
                        <Typography variant="h6" sx={{ fontWeight: 'bold', mb: 1 }}>
                          {item.name}
                        </Typography>
                        <Typography variant="body2" color="textSecondary" sx={{ mb: 1 }}>
                          Premium Quality Spice
                        </Typography>
                        <Box sx={{ display: 'flex', gap: 1, mb: 2 }}>
                          <Chip
                            icon={<LocalShipping />}
                            label="Free Delivery"
                            size="small"
                            color="success"
                            variant="outlined"
                          />
                          <Chip
                            icon={<Security />}
                            label="Assured"
                            size="small"
                            color="primary"
                            variant="outlined"
                          />
                        </Box>
                        <Box sx={{ display: 'flex', gap: 2 }}>
                          <Button size="small" startIcon={<Favorite />}>
                            SAVE FOR LATER
                          </Button>
                          <Button
                            size="small"
                            startIcon={<Delete />}
                            onClick={() => handleRemoveItem(item.id)}
                          >
                            REMOVE
                          </Button>
                        </Box>
                      </Grid>

                      <Grid item xs={12} sm={3}>
                        <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
                          <IconButton
                            onClick={() => handleQuantityChange(item.id, item.quantity - 1)}
                            disabled={item.quantity <= 1}
                            sx={{ border: '1px solid #e0e0e0' }}
                          >
                            <Remove />
                          </IconButton>
                          <TextField
                            value={item.quantity}
                            size="small"
                            sx={{
                              mx: 1,
                              width: 60,
                              '& input': { textAlign: 'center' }
                            }}
                            inputProps={{ min: 1, max: 10 }}
                          />
                          <IconButton
                            onClick={() => handleQuantityChange(item.id, item.quantity + 1)}
                            sx={{ border: '1px solid #e0e0e0' }}
                          >
                            <Add />
                          </IconButton>
                        </Box>
                      </Grid>

                      <Grid item xs={12} sm={3}>
                        <Box sx={{ textAlign: 'right' }}>
                          <Typography variant="h6" sx={{ fontWeight: 'bold', color: '#212121' }}>
                            ₹{(item.price * 75 * item.quantity).toFixed(0)}
                          </Typography>
                          <Typography
                            variant="body2"
                            sx={{
                              textDecoration: 'line-through',
                              color: '#878787',
                              mb: 1
                            }}
                          >
                            ₹{(item.price * 100 * item.quantity).toFixed(0)}
                          </Typography>
                          <Typography variant="body2" sx={{ color: '#388e3c', fontWeight: 'bold' }}>
                            25% Off
                          </Typography>
                        </Box>
                      </Grid>
                    </Grid>
                  </CardContent>
                  <Divider />
                </CartCard>
              ))}

              <Box sx={{ p: 2, backgroundColor: '#fafafa', textAlign: 'right' }}>
                <Button
                  variant="contained"
                  size="large"
                  onClick={() => {
                    if (isAuthenticated) {
                      navigate('/checkout');
                    } else {
                      navigate('/login');
                    }
                  }}
                  sx={{
                    backgroundColor: '#fb641b',
                    '&:hover': { backgroundColor: '#e55a16' },
                    px: 4,
                    py: 1.5
                  }}
                >
                  {isAuthenticated ? 'PLACE ORDER' : 'LOGIN TO PLACE ORDER'}
                </Button>
              </Box>
            </Paper>
          </Grid>

          {/* Price Details */}
          <Grid item xs={12} lg={4}>
            <Paper sx={{ position: 'sticky', top: 20 }}>
              <Box sx={{ p: 2, borderBottom: '1px solid #e0e0e0' }}>
                <Typography variant="h6" sx={{ fontWeight: 'bold', color: '#878787' }}>
                  PRICE DETAILS
                </Typography>
              </Box>

              <Box sx={{ p: 2 }}>
                <Box sx={{ display: 'flex', justifyContent: 'space-between', mb: 2 }}>
                  <Typography>Price ({cartItems.length} item{cartItems.length > 1 ? 's' : ''})</Typography>
                  <Typography>₹{(total * 100).toFixed(0)}</Typography>
                </Box>

                <Box sx={{ display: 'flex', justifyContent: 'space-between', mb: 2 }}>
                  <Typography>Discount</Typography>
                  <Typography sx={{ color: '#388e3c' }}>
                    −₹{(savings * 100).toFixed(0)}
                  </Typography>
                </Box>

                <Box sx={{ display: 'flex', justifyContent: 'space-between', mb: 2 }}>
                  <Typography>Delivery Charges</Typography>
                  <Typography sx={{ color: deliveryCharge === 0 ? '#388e3c' : 'inherit' }}>
                    {deliveryCharge === 0 ? 'FREE' : `₹${deliveryCharge}`}
                  </Typography>
                </Box>

                <Divider sx={{ my: 2 }} />

                <Box sx={{ display: 'flex', justifyContent: 'space-between', mb: 2 }}>
                  <Typography variant="h6" sx={{ fontWeight: 'bold' }}>
                    Total Amount
                  </Typography>
                  <Typography variant="h6" sx={{ fontWeight: 'bold' }}>
                    ₹{(finalTotal * 75).toFixed(0)}
                  </Typography>
                </Box>

                <Typography variant="body2" sx={{ color: '#388e3c', fontWeight: 'bold' }}>
                  You will save ₹{(savings * 100).toFixed(0)} on this order
                </Typography>
              </Box>
            </Paper>

            {/* Offers */}
            <Paper sx={{ mt: 2 }}>
              <Box sx={{ p: 2, borderBottom: '1px solid #e0e0e0' }}>
                <Typography variant="h6" sx={{ fontWeight: 'bold', color: '#878787' }}>
                  OFFERS
                </Typography>
              </Box>

              <List>
                <ListItem>
                  <ListItemIcon>
                    <LocalOffer sx={{ color: '#2874f0' }} />
                  </ListItemIcon>
                  <ListItemText
                    primary="Bank Offer"
                    secondary="10% off on HDFC Bank Credit Cards"
                  />
                </ListItem>

                <ListItem>
                  <ListItemIcon>
                    <CreditCard sx={{ color: '#2874f0' }} />
                  </ListItemIcon>
                  <ListItemText
                    primary="No Cost EMI"
                    secondary="Available on orders above ₹3000"
                  />
                </ListItem>
              </List>
            </Paper>
          </Grid>
        </Grid>
      </Container>
    </Box>
  );
};

export default Cart;