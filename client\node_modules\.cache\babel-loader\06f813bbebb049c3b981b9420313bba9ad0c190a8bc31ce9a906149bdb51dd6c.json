{"ast": null, "code": "var _jsxFileName = \"D:\\\\ecommerce\\\\client\\\\src\\\\components\\\\auth\\\\Register.js\",\n  _s = $RefreshSig$();\nimport React, { useState } from 'react';\nimport { Box, Paper, TextField, Button, Typography, Container, Alert, CircularProgress, Link, Divider, InputAdornment, IconButton, Checkbox, FormControlLabel } from '@mui/material';\nimport { Visibility, VisibilityOff, Email, Lock, Person, Google, Facebook } from '@mui/icons-material';\nimport { styled } from '@mui/material/styles';\nimport { useNavigate } from 'react-router-dom';\nimport { usersAPI, authAPI, handleAPIError } from '../../services/api';\n\n// Flipkart-style styled components\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst FlipkartHeader = styled(Paper)(({\n  theme\n}) => ({\n  backgroundColor: '#2874f0',\n  color: 'white',\n  padding: theme.spacing(2),\n  textAlign: 'center',\n  marginBottom: theme.spacing(3)\n}));\n_c = FlipkartHeader;\nconst RegisterContainer = styled(Container)(({\n  theme\n}) => ({\n  minHeight: '100vh',\n  display: 'flex',\n  alignItems: 'center',\n  backgroundColor: '#f1f3f6',\n  padding: theme.spacing(2)\n}));\n_c2 = RegisterContainer;\nconst RegisterCard = styled(Paper)(({\n  theme\n}) => ({\n  padding: theme.spacing(4),\n  maxWidth: 450,\n  width: '100%',\n  margin: '0 auto',\n  boxShadow: '0 4px 12px rgba(0,0,0,0.15)'\n}));\n_c3 = RegisterCard;\nconst Register = () => {\n  _s();\n  const navigate = useNavigate();\n  const [formData, setFormData] = useState({\n    name: '',\n    email: '',\n    password: '',\n    confirmPassword: ''\n  });\n  const [showPassword, setShowPassword] = useState(false);\n  const [showConfirmPassword, setShowConfirmPassword] = useState(false);\n  const [agreeTerms, setAgreeTerms] = useState(false);\n  const [loading, setLoading] = useState(false);\n  const [error, setError] = useState('');\n  const [success, setSuccess] = useState('');\n  const {\n    name,\n    email,\n    password,\n    confirmPassword\n  } = formData;\n  const handleChange = e => {\n    setFormData({\n      ...formData,\n      [e.target.name]: e.target.value\n    });\n    setError('');\n  };\n  const validateForm = () => {\n    if (!name || !email || !password || !confirmPassword) {\n      setError('Please fill in all fields');\n      return false;\n    }\n    if (name.length < 2) {\n      setError('Name must be at least 2 characters');\n      return false;\n    }\n    if (password.length < 6) {\n      setError('Password must be at least 6 characters');\n      return false;\n    }\n    if (password !== confirmPassword) {\n      setError('Passwords do not match');\n      return false;\n    }\n    if (!agreeTerms) {\n      setError('Please agree to the terms and conditions');\n      return false;\n    }\n    return true;\n  };\n  const handleSubmit = async e => {\n    e.preventDefault();\n    if (!validateForm()) {\n      return;\n    }\n    try {\n      setLoading(true);\n      setError('');\n      const response = await usersAPI.register({\n        name,\n        email,\n        password\n      });\n\n      // Store token and user data\n      authAPI.setToken(response.data.token);\n      localStorage.setItem('user', JSON.stringify(response.data));\n      setSuccess('Registration successful! Redirecting...');\n\n      // Redirect to dashboard\n      setTimeout(() => {\n        navigate('/dashboard');\n      }, 1500);\n    } catch (err) {\n      setError(handleAPIError(err));\n    } finally {\n      setLoading(false);\n    }\n  };\n  return /*#__PURE__*/_jsxDEV(Box, {\n    sx: {\n      backgroundColor: '#f1f3f6',\n      minHeight: '100vh'\n    },\n    children: [/*#__PURE__*/_jsxDEV(FlipkartHeader, {\n      elevation: 0,\n      children: [/*#__PURE__*/_jsxDEV(Typography, {\n        variant: \"h4\",\n        sx: {\n          fontWeight: 'bold'\n        },\n        children: \"\\uD83D\\uDED2 SpiceMart\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 145,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Typography, {\n        variant: \"subtitle1\",\n        children: \"Create your account to get started\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 148,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 144,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(RegisterContainer, {\n      maxWidth: \"sm\",\n      children: /*#__PURE__*/_jsxDEV(RegisterCard, {\n        children: [/*#__PURE__*/_jsxDEV(Box, {\n          sx: {\n            textAlign: 'center',\n            mb: 3\n          },\n          children: [/*#__PURE__*/_jsxDEV(Typography, {\n            variant: \"h5\",\n            sx: {\n              fontWeight: 'bold',\n              mb: 1\n            },\n            children: \"Join SpiceMart\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 156,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Typography, {\n            variant: \"body2\",\n            color: \"textSecondary\",\n            children: \"Create an account to start shopping\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 159,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 155,\n          columnNumber: 11\n        }, this), error && /*#__PURE__*/_jsxDEV(Alert, {\n          severity: \"error\",\n          sx: {\n            mb: 2\n          },\n          children: error\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 165,\n          columnNumber: 13\n        }, this), success && /*#__PURE__*/_jsxDEV(Alert, {\n          severity: \"success\",\n          sx: {\n            mb: 2\n          },\n          children: success\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 171,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"form\", {\n          onSubmit: handleSubmit,\n          children: [/*#__PURE__*/_jsxDEV(TextField, {\n            fullWidth: true,\n            label: \"Full Name\",\n            name: \"name\",\n            value: name,\n            onChange: handleChange,\n            margin: \"normal\",\n            required: true,\n            InputProps: {\n              startAdornment: /*#__PURE__*/_jsxDEV(InputAdornment, {\n                position: \"start\",\n                children: /*#__PURE__*/_jsxDEV(Person, {\n                  color: \"action\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 188,\n                  columnNumber: 21\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 187,\n                columnNumber: 19\n              }, this)\n            }\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 177,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(TextField, {\n            fullWidth: true,\n            label: \"Email Address\",\n            name: \"email\",\n            type: \"email\",\n            value: email,\n            onChange: handleChange,\n            margin: \"normal\",\n            required: true,\n            InputProps: {\n              startAdornment: /*#__PURE__*/_jsxDEV(InputAdornment, {\n                position: \"start\",\n                children: /*#__PURE__*/_jsxDEV(Email, {\n                  color: \"action\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 206,\n                  columnNumber: 21\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 205,\n                columnNumber: 19\n              }, this)\n            }\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 194,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(TextField, {\n            fullWidth: true,\n            label: \"Password\",\n            name: \"password\",\n            type: showPassword ? 'text' : 'password',\n            value: password,\n            onChange: handleChange,\n            margin: \"normal\",\n            required: true,\n            InputProps: {\n              startAdornment: /*#__PURE__*/_jsxDEV(InputAdornment, {\n                position: \"start\",\n                children: /*#__PURE__*/_jsxDEV(Lock, {\n                  color: \"action\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 224,\n                  columnNumber: 21\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 223,\n                columnNumber: 19\n              }, this),\n              endAdornment: /*#__PURE__*/_jsxDEV(InputAdornment, {\n                position: \"end\",\n                children: /*#__PURE__*/_jsxDEV(IconButton, {\n                  onClick: () => setShowPassword(!showPassword),\n                  edge: \"end\",\n                  children: showPassword ? /*#__PURE__*/_jsxDEV(VisibilityOff, {}, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 233,\n                    columnNumber: 39\n                  }, this) : /*#__PURE__*/_jsxDEV(Visibility, {}, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 233,\n                    columnNumber: 59\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 229,\n                  columnNumber: 21\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 228,\n                columnNumber: 19\n              }, this)\n            }\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 212,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(TextField, {\n            fullWidth: true,\n            label: \"Confirm Password\",\n            name: \"confirmPassword\",\n            type: showConfirmPassword ? 'text' : 'password',\n            value: confirmPassword,\n            onChange: handleChange,\n            margin: \"normal\",\n            required: true,\n            InputProps: {\n              startAdornment: /*#__PURE__*/_jsxDEV(InputAdornment, {\n                position: \"start\",\n                children: /*#__PURE__*/_jsxDEV(Lock, {\n                  color: \"action\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 252,\n                  columnNumber: 21\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 251,\n                columnNumber: 19\n              }, this),\n              endAdornment: /*#__PURE__*/_jsxDEV(InputAdornment, {\n                position: \"end\",\n                children: /*#__PURE__*/_jsxDEV(IconButton, {\n                  onClick: () => setShowConfirmPassword(!showConfirmPassword),\n                  edge: \"end\",\n                  children: showConfirmPassword ? /*#__PURE__*/_jsxDEV(VisibilityOff, {}, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 261,\n                    columnNumber: 46\n                  }, this) : /*#__PURE__*/_jsxDEV(Visibility, {}, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 261,\n                    columnNumber: 66\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 257,\n                  columnNumber: 21\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 256,\n                columnNumber: 19\n              }, this)\n            }\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 240,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(FormControlLabel, {\n            control: /*#__PURE__*/_jsxDEV(Checkbox, {\n              checked: agreeTerms,\n              onChange: e => setAgreeTerms(e.target.checked),\n              sx: {\n                color: '#2874f0'\n              }\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 270,\n              columnNumber: 17\n            }, this),\n            label: /*#__PURE__*/_jsxDEV(Typography, {\n              variant: \"body2\",\n              children: [\"I agree to the\", ' ', /*#__PURE__*/_jsxDEV(Link, {\n                href: \"#\",\n                sx: {\n                  color: '#2874f0'\n                },\n                children: \"Terms & Conditions\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 279,\n                columnNumber: 19\n              }, this), ' ', \"and\", ' ', /*#__PURE__*/_jsxDEV(Link, {\n                href: \"#\",\n                sx: {\n                  color: '#2874f0'\n                },\n                children: \"Privacy Policy\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 283,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 277,\n              columnNumber: 17\n            }, this),\n            sx: {\n              mt: 2,\n              mb: 1\n            }\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 268,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Button, {\n            type: \"submit\",\n            fullWidth: true,\n            variant: \"contained\",\n            size: \"large\",\n            disabled: loading,\n            sx: {\n              mt: 2,\n              mb: 2,\n              backgroundColor: '#fb641b',\n              '&:hover': {\n                backgroundColor: '#e55a16'\n              },\n              py: 1.5,\n              fontSize: '1.1rem',\n              fontWeight: 'bold'\n            },\n            children: loading ? /*#__PURE__*/_jsxDEV(CircularProgress, {\n              size: 24,\n              color: \"inherit\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 307,\n              columnNumber: 26\n            }, this) : 'CREATE ACCOUNT'\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 291,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Divider, {\n            sx: {\n              my: 2\n            },\n            children: /*#__PURE__*/_jsxDEV(Typography, {\n              variant: \"caption\",\n              color: \"textSecondary\",\n              children: \"OR\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 311,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 310,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Box, {\n            sx: {\n              display: 'flex',\n              gap: 1,\n              mb: 3\n            },\n            children: [/*#__PURE__*/_jsxDEV(Button, {\n              fullWidth: true,\n              variant: \"outlined\",\n              startIcon: /*#__PURE__*/_jsxDEV(Google, {}, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 320,\n                columnNumber: 28\n              }, this),\n              sx: {\n                borderColor: '#db4437',\n                color: '#db4437'\n              },\n              children: \"Google\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 317,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Button, {\n              fullWidth: true,\n              variant: \"outlined\",\n              startIcon: /*#__PURE__*/_jsxDEV(Facebook, {}, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 328,\n                columnNumber: 28\n              }, this),\n              sx: {\n                borderColor: '#4267b2',\n                color: '#4267b2'\n              },\n              children: \"Facebook\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 325,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 316,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Box, {\n            sx: {\n              textAlign: 'center'\n            },\n            children: /*#__PURE__*/_jsxDEV(Typography, {\n              variant: \"body2\",\n              color: \"textSecondary\",\n              children: [\"Already have an account?\", ' ', /*#__PURE__*/_jsxDEV(Link, {\n                component: \"button\",\n                variant: \"body2\",\n                onClick: () => navigate('/login'),\n                sx: {\n                  color: '#2874f0',\n                  textDecoration: 'none'\n                },\n                children: \"Sign in\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 338,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 336,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 335,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 176,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Box, {\n          sx: {\n            mt: 3,\n            p: 2,\n            backgroundColor: '#f8f9fa',\n            borderRadius: 1\n          },\n          children: [/*#__PURE__*/_jsxDEV(Typography, {\n            variant: \"caption\",\n            sx: {\n              fontWeight: 'bold',\n              display: 'block',\n              mb: 1\n            },\n            children: \"Why join SpiceMart?\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 352,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Typography, {\n            variant: \"caption\",\n            sx: {\n              display: 'block'\n            },\n            children: \"\\u2022 Free delivery on orders above \\u20B9500\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 355,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Typography, {\n            variant: \"caption\",\n            sx: {\n              display: 'block'\n            },\n            children: \"\\u2022 Exclusive member discounts\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 358,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Typography, {\n            variant: \"caption\",\n            sx: {\n              display: 'block'\n            },\n            children: \"\\u2022 Loyalty points on every purchase\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 361,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 351,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 154,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 153,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 142,\n    columnNumber: 5\n  }, this);\n};\n_s(Register, \"aOdnoZn5JwT5q15VU9gx9Tc7VXQ=\", false, function () {\n  return [useNavigate];\n});\n_c4 = Register;\nexport default Register;\nvar _c, _c2, _c3, _c4;\n$RefreshReg$(_c, \"FlipkartHeader\");\n$RefreshReg$(_c2, \"RegisterContainer\");\n$RefreshReg$(_c3, \"RegisterCard\");\n$RefreshReg$(_c4, \"Register\");", "map": {"version": 3, "names": ["React", "useState", "Box", "Paper", "TextField", "<PERSON><PERSON>", "Typography", "Container", "<PERSON><PERSON>", "CircularProgress", "Link", "Divider", "InputAdornment", "IconButton", "Checkbox", "FormControlLabel", "Visibility", "VisibilityOff", "Email", "Lock", "Person", "Google", "Facebook", "styled", "useNavigate", "usersAPI", "authAPI", "handleAPIError", "jsxDEV", "_jsxDEV", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "theme", "backgroundColor", "color", "padding", "spacing", "textAlign", "marginBottom", "_c", "RegisterContainer", "minHeight", "display", "alignItems", "_c2", "RegisterCard", "max<PERSON><PERSON><PERSON>", "width", "margin", "boxShadow", "_c3", "Register", "_s", "navigate", "formData", "setFormData", "name", "email", "password", "confirmPassword", "showPassword", "setShowPassword", "showConfirmPassword", "setShowConfirmPassword", "agreeTerms", "setAgreeTerms", "loading", "setLoading", "error", "setError", "success", "setSuccess", "handleChange", "e", "target", "value", "validateForm", "length", "handleSubmit", "preventDefault", "response", "register", "setToken", "data", "token", "localStorage", "setItem", "JSON", "stringify", "setTimeout", "err", "sx", "children", "elevation", "variant", "fontWeight", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "mb", "severity", "onSubmit", "fullWidth", "label", "onChange", "required", "InputProps", "startAdornment", "position", "type", "endAdornment", "onClick", "edge", "control", "checked", "href", "mt", "size", "disabled", "py", "fontSize", "my", "gap", "startIcon", "borderColor", "component", "textDecoration", "p", "borderRadius", "_c4", "$RefreshReg$"], "sources": ["D:/ecommerce/client/src/components/auth/Register.js"], "sourcesContent": ["import React, { useState } from 'react';\nimport {\n  Box,\n  Paper,\n  TextField,\n  Button,\n  Typography,\n  Container,\n  Alert,\n  CircularProgress,\n  Link,\n  Divider,\n  InputAdornment,\n  IconButton,\n  Checkbox,\n  FormControlLabel,\n} from '@mui/material';\nimport {\n  Visibility,\n  VisibilityOff,\n  Email,\n  Lock,\n  Person,\n  Google,\n  Facebook,\n} from '@mui/icons-material';\nimport { styled } from '@mui/material/styles';\nimport { useNavigate } from 'react-router-dom';\nimport { usersAPI, authAPI, handleAPIError } from '../../services/api';\n\n// Flipkart-style styled components\nconst FlipkartHeader = styled(Paper)(({ theme }) => ({\n  backgroundColor: '#2874f0',\n  color: 'white',\n  padding: theme.spacing(2),\n  textAlign: 'center',\n  marginBottom: theme.spacing(3),\n}));\n\nconst RegisterContainer = styled(Container)(({ theme }) => ({\n  minHeight: '100vh',\n  display: 'flex',\n  alignItems: 'center',\n  backgroundColor: '#f1f3f6',\n  padding: theme.spacing(2),\n}));\n\nconst RegisterCard = styled(Paper)(({ theme }) => ({\n  padding: theme.spacing(4),\n  maxWidth: 450,\n  width: '100%',\n  margin: '0 auto',\n  boxShadow: '0 4px 12px rgba(0,0,0,0.15)',\n}));\n\nconst Register = () => {\n  const navigate = useNavigate();\n  const [formData, setFormData] = useState({\n    name: '',\n    email: '',\n    password: '',\n    confirmPassword: '',\n  });\n  const [showPassword, setShowPassword] = useState(false);\n  const [showConfirmPassword, setShowConfirmPassword] = useState(false);\n  const [agreeTerms, setAgreeTerms] = useState(false);\n  const [loading, setLoading] = useState(false);\n  const [error, setError] = useState('');\n  const [success, setSuccess] = useState('');\n\n  const { name, email, password, confirmPassword } = formData;\n\n  const handleChange = (e) => {\n    setFormData({\n      ...formData,\n      [e.target.name]: e.target.value,\n    });\n    setError('');\n  };\n\n  const validateForm = () => {\n    if (!name || !email || !password || !confirmPassword) {\n      setError('Please fill in all fields');\n      return false;\n    }\n\n    if (name.length < 2) {\n      setError('Name must be at least 2 characters');\n      return false;\n    }\n\n    if (password.length < 6) {\n      setError('Password must be at least 6 characters');\n      return false;\n    }\n\n    if (password !== confirmPassword) {\n      setError('Passwords do not match');\n      return false;\n    }\n\n    if (!agreeTerms) {\n      setError('Please agree to the terms and conditions');\n      return false;\n    }\n\n    return true;\n  };\n\n  const handleSubmit = async (e) => {\n    e.preventDefault();\n    \n    if (!validateForm()) {\n      return;\n    }\n\n    try {\n      setLoading(true);\n      setError('');\n      \n      const response = await usersAPI.register({ name, email, password });\n      \n      // Store token and user data\n      authAPI.setToken(response.data.token);\n      localStorage.setItem('user', JSON.stringify(response.data));\n      \n      setSuccess('Registration successful! Redirecting...');\n      \n      // Redirect to dashboard\n      setTimeout(() => {\n        navigate('/dashboard');\n      }, 1500);\n      \n    } catch (err) {\n      setError(handleAPIError(err));\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  return (\n    <Box sx={{ backgroundColor: '#f1f3f6', minHeight: '100vh' }}>\n      {/* Header */}\n      <FlipkartHeader elevation={0}>\n        <Typography variant=\"h4\" sx={{ fontWeight: 'bold' }}>\n          🛒 SpiceMart\n        </Typography>\n        <Typography variant=\"subtitle1\">\n          Create your account to get started\n        </Typography>\n      </FlipkartHeader>\n\n      <RegisterContainer maxWidth=\"sm\">\n        <RegisterCard>\n          <Box sx={{ textAlign: 'center', mb: 3 }}>\n            <Typography variant=\"h5\" sx={{ fontWeight: 'bold', mb: 1 }}>\n              Join SpiceMart\n            </Typography>\n            <Typography variant=\"body2\" color=\"textSecondary\">\n              Create an account to start shopping\n            </Typography>\n          </Box>\n\n          {error && (\n            <Alert severity=\"error\" sx={{ mb: 2 }}>\n              {error}\n            </Alert>\n          )}\n\n          {success && (\n            <Alert severity=\"success\" sx={{ mb: 2 }}>\n              {success}\n            </Alert>\n          )}\n\n          <form onSubmit={handleSubmit}>\n            <TextField\n              fullWidth\n              label=\"Full Name\"\n              name=\"name\"\n              value={name}\n              onChange={handleChange}\n              margin=\"normal\"\n              required\n              InputProps={{\n                startAdornment: (\n                  <InputAdornment position=\"start\">\n                    <Person color=\"action\" />\n                  </InputAdornment>\n                ),\n              }}\n            />\n\n            <TextField\n              fullWidth\n              label=\"Email Address\"\n              name=\"email\"\n              type=\"email\"\n              value={email}\n              onChange={handleChange}\n              margin=\"normal\"\n              required\n              InputProps={{\n                startAdornment: (\n                  <InputAdornment position=\"start\">\n                    <Email color=\"action\" />\n                  </InputAdornment>\n                ),\n              }}\n            />\n\n            <TextField\n              fullWidth\n              label=\"Password\"\n              name=\"password\"\n              type={showPassword ? 'text' : 'password'}\n              value={password}\n              onChange={handleChange}\n              margin=\"normal\"\n              required\n              InputProps={{\n                startAdornment: (\n                  <InputAdornment position=\"start\">\n                    <Lock color=\"action\" />\n                  </InputAdornment>\n                ),\n                endAdornment: (\n                  <InputAdornment position=\"end\">\n                    <IconButton\n                      onClick={() => setShowPassword(!showPassword)}\n                      edge=\"end\"\n                    >\n                      {showPassword ? <VisibilityOff /> : <Visibility />}\n                    </IconButton>\n                  </InputAdornment>\n                ),\n              }}\n            />\n\n            <TextField\n              fullWidth\n              label=\"Confirm Password\"\n              name=\"confirmPassword\"\n              type={showConfirmPassword ? 'text' : 'password'}\n              value={confirmPassword}\n              onChange={handleChange}\n              margin=\"normal\"\n              required\n              InputProps={{\n                startAdornment: (\n                  <InputAdornment position=\"start\">\n                    <Lock color=\"action\" />\n                  </InputAdornment>\n                ),\n                endAdornment: (\n                  <InputAdornment position=\"end\">\n                    <IconButton\n                      onClick={() => setShowConfirmPassword(!showConfirmPassword)}\n                      edge=\"end\"\n                    >\n                      {showConfirmPassword ? <VisibilityOff /> : <Visibility />}\n                    </IconButton>\n                  </InputAdornment>\n                ),\n              }}\n            />\n\n            <FormControlLabel\n              control={\n                <Checkbox\n                  checked={agreeTerms}\n                  onChange={(e) => setAgreeTerms(e.target.checked)}\n                  sx={{ color: '#2874f0' }}\n                />\n              }\n              label={\n                <Typography variant=\"body2\">\n                  I agree to the{' '}\n                  <Link href=\"#\" sx={{ color: '#2874f0' }}>\n                    Terms & Conditions\n                  </Link>{' '}\n                  and{' '}\n                  <Link href=\"#\" sx={{ color: '#2874f0' }}>\n                    Privacy Policy\n                  </Link>\n                </Typography>\n              }\n              sx={{ mt: 2, mb: 1 }}\n            />\n\n            <Button\n              type=\"submit\"\n              fullWidth\n              variant=\"contained\"\n              size=\"large\"\n              disabled={loading}\n              sx={{\n                mt: 2,\n                mb: 2,\n                backgroundColor: '#fb641b',\n                '&:hover': { backgroundColor: '#e55a16' },\n                py: 1.5,\n                fontSize: '1.1rem',\n                fontWeight: 'bold',\n              }}\n            >\n              {loading ? <CircularProgress size={24} color=\"inherit\" /> : 'CREATE ACCOUNT'}\n            </Button>\n\n            <Divider sx={{ my: 2 }}>\n              <Typography variant=\"caption\" color=\"textSecondary\">\n                OR\n              </Typography>\n            </Divider>\n\n            <Box sx={{ display: 'flex', gap: 1, mb: 3 }}>\n              <Button\n                fullWidth\n                variant=\"outlined\"\n                startIcon={<Google />}\n                sx={{ borderColor: '#db4437', color: '#db4437' }}\n              >\n                Google\n              </Button>\n              <Button\n                fullWidth\n                variant=\"outlined\"\n                startIcon={<Facebook />}\n                sx={{ borderColor: '#4267b2', color: '#4267b2' }}\n              >\n                Facebook\n              </Button>\n            </Box>\n\n            <Box sx={{ textAlign: 'center' }}>\n              <Typography variant=\"body2\" color=\"textSecondary\">\n                Already have an account?{' '}\n                <Link\n                  component=\"button\"\n                  variant=\"body2\"\n                  onClick={() => navigate('/login')}\n                  sx={{ color: '#2874f0', textDecoration: 'none' }}\n                >\n                  Sign in\n                </Link>\n              </Typography>\n            </Box>\n          </form>\n\n          {/* Benefits */}\n          <Box sx={{ mt: 3, p: 2, backgroundColor: '#f8f9fa', borderRadius: 1 }}>\n            <Typography variant=\"caption\" sx={{ fontWeight: 'bold', display: 'block', mb: 1 }}>\n              Why join SpiceMart?\n            </Typography>\n            <Typography variant=\"caption\" sx={{ display: 'block' }}>\n              • Free delivery on orders above ₹500\n            </Typography>\n            <Typography variant=\"caption\" sx={{ display: 'block' }}>\n              • Exclusive member discounts\n            </Typography>\n            <Typography variant=\"caption\" sx={{ display: 'block' }}>\n              • Loyalty points on every purchase\n            </Typography>\n          </Box>\n        </RegisterCard>\n      </RegisterContainer>\n    </Box>\n  );\n};\n\nexport default Register;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,QAAQ,OAAO;AACvC,SACEC,GAAG,EACHC,KAAK,EACLC,SAAS,EACTC,MAAM,EACNC,UAAU,EACVC,SAAS,EACTC,KAAK,EACLC,gBAAgB,EAChBC,IAAI,EACJC,OAAO,EACPC,cAAc,EACdC,UAAU,EACVC,QAAQ,EACRC,gBAAgB,QACX,eAAe;AACtB,SACEC,UAAU,EACVC,aAAa,EACbC,KAAK,EACLC,IAAI,EACJC,MAAM,EACNC,MAAM,EACNC,QAAQ,QACH,qBAAqB;AAC5B,SAASC,MAAM,QAAQ,sBAAsB;AAC7C,SAASC,WAAW,QAAQ,kBAAkB;AAC9C,SAASC,QAAQ,EAAEC,OAAO,EAAEC,cAAc,QAAQ,oBAAoB;;AAEtE;AAAA,SAAAC,MAAA,IAAAC,OAAA;AACA,MAAMC,cAAc,GAAGP,MAAM,CAACpB,KAAK,CAAC,CAAC,CAAC;EAAE4B;AAAM,CAAC,MAAM;EACnDC,eAAe,EAAE,SAAS;EAC1BC,KAAK,EAAE,OAAO;EACdC,OAAO,EAAEH,KAAK,CAACI,OAAO,CAAC,CAAC,CAAC;EACzBC,SAAS,EAAE,QAAQ;EACnBC,YAAY,EAAEN,KAAK,CAACI,OAAO,CAAC,CAAC;AAC/B,CAAC,CAAC,CAAC;AAACG,EAAA,GANER,cAAc;AAQpB,MAAMS,iBAAiB,GAAGhB,MAAM,CAAChB,SAAS,CAAC,CAAC,CAAC;EAAEwB;AAAM,CAAC,MAAM;EAC1DS,SAAS,EAAE,OAAO;EAClBC,OAAO,EAAE,MAAM;EACfC,UAAU,EAAE,QAAQ;EACpBV,eAAe,EAAE,SAAS;EAC1BE,OAAO,EAAEH,KAAK,CAACI,OAAO,CAAC,CAAC;AAC1B,CAAC,CAAC,CAAC;AAACQ,GAAA,GANEJ,iBAAiB;AAQvB,MAAMK,YAAY,GAAGrB,MAAM,CAACpB,KAAK,CAAC,CAAC,CAAC;EAAE4B;AAAM,CAAC,MAAM;EACjDG,OAAO,EAAEH,KAAK,CAACI,OAAO,CAAC,CAAC,CAAC;EACzBU,QAAQ,EAAE,GAAG;EACbC,KAAK,EAAE,MAAM;EACbC,MAAM,EAAE,QAAQ;EAChBC,SAAS,EAAE;AACb,CAAC,CAAC,CAAC;AAACC,GAAA,GANEL,YAAY;AAQlB,MAAMM,QAAQ,GAAGA,CAAA,KAAM;EAAAC,EAAA;EACrB,MAAMC,QAAQ,GAAG5B,WAAW,CAAC,CAAC;EAC9B,MAAM,CAAC6B,QAAQ,EAAEC,WAAW,CAAC,GAAGrD,QAAQ,CAAC;IACvCsD,IAAI,EAAE,EAAE;IACRC,KAAK,EAAE,EAAE;IACTC,QAAQ,EAAE,EAAE;IACZC,eAAe,EAAE;EACnB,CAAC,CAAC;EACF,MAAM,CAACC,YAAY,EAAEC,eAAe,CAAC,GAAG3D,QAAQ,CAAC,KAAK,CAAC;EACvD,MAAM,CAAC4D,mBAAmB,EAAEC,sBAAsB,CAAC,GAAG7D,QAAQ,CAAC,KAAK,CAAC;EACrE,MAAM,CAAC8D,UAAU,EAAEC,aAAa,CAAC,GAAG/D,QAAQ,CAAC,KAAK,CAAC;EACnD,MAAM,CAACgE,OAAO,EAAEC,UAAU,CAAC,GAAGjE,QAAQ,CAAC,KAAK,CAAC;EAC7C,MAAM,CAACkE,KAAK,EAAEC,QAAQ,CAAC,GAAGnE,QAAQ,CAAC,EAAE,CAAC;EACtC,MAAM,CAACoE,OAAO,EAAEC,UAAU,CAAC,GAAGrE,QAAQ,CAAC,EAAE,CAAC;EAE1C,MAAM;IAAEsD,IAAI;IAAEC,KAAK;IAAEC,QAAQ;IAAEC;EAAgB,CAAC,GAAGL,QAAQ;EAE3D,MAAMkB,YAAY,GAAIC,CAAC,IAAK;IAC1BlB,WAAW,CAAC;MACV,GAAGD,QAAQ;MACX,CAACmB,CAAC,CAACC,MAAM,CAAClB,IAAI,GAAGiB,CAAC,CAACC,MAAM,CAACC;IAC5B,CAAC,CAAC;IACFN,QAAQ,CAAC,EAAE,CAAC;EACd,CAAC;EAED,MAAMO,YAAY,GAAGA,CAAA,KAAM;IACzB,IAAI,CAACpB,IAAI,IAAI,CAACC,KAAK,IAAI,CAACC,QAAQ,IAAI,CAACC,eAAe,EAAE;MACpDU,QAAQ,CAAC,2BAA2B,CAAC;MACrC,OAAO,KAAK;IACd;IAEA,IAAIb,IAAI,CAACqB,MAAM,GAAG,CAAC,EAAE;MACnBR,QAAQ,CAAC,oCAAoC,CAAC;MAC9C,OAAO,KAAK;IACd;IAEA,IAAIX,QAAQ,CAACmB,MAAM,GAAG,CAAC,EAAE;MACvBR,QAAQ,CAAC,wCAAwC,CAAC;MAClD,OAAO,KAAK;IACd;IAEA,IAAIX,QAAQ,KAAKC,eAAe,EAAE;MAChCU,QAAQ,CAAC,wBAAwB,CAAC;MAClC,OAAO,KAAK;IACd;IAEA,IAAI,CAACL,UAAU,EAAE;MACfK,QAAQ,CAAC,0CAA0C,CAAC;MACpD,OAAO,KAAK;IACd;IAEA,OAAO,IAAI;EACb,CAAC;EAED,MAAMS,YAAY,GAAG,MAAOL,CAAC,IAAK;IAChCA,CAAC,CAACM,cAAc,CAAC,CAAC;IAElB,IAAI,CAACH,YAAY,CAAC,CAAC,EAAE;MACnB;IACF;IAEA,IAAI;MACFT,UAAU,CAAC,IAAI,CAAC;MAChBE,QAAQ,CAAC,EAAE,CAAC;MAEZ,MAAMW,QAAQ,GAAG,MAAMtD,QAAQ,CAACuD,QAAQ,CAAC;QAAEzB,IAAI;QAAEC,KAAK;QAAEC;MAAS,CAAC,CAAC;;MAEnE;MACA/B,OAAO,CAACuD,QAAQ,CAACF,QAAQ,CAACG,IAAI,CAACC,KAAK,CAAC;MACrCC,YAAY,CAACC,OAAO,CAAC,MAAM,EAAEC,IAAI,CAACC,SAAS,CAACR,QAAQ,CAACG,IAAI,CAAC,CAAC;MAE3DZ,UAAU,CAAC,yCAAyC,CAAC;;MAErD;MACAkB,UAAU,CAAC,MAAM;QACfpC,QAAQ,CAAC,YAAY,CAAC;MACxB,CAAC,EAAE,IAAI,CAAC;IAEV,CAAC,CAAC,OAAOqC,GAAG,EAAE;MACZrB,QAAQ,CAACzC,cAAc,CAAC8D,GAAG,CAAC,CAAC;IAC/B,CAAC,SAAS;MACRvB,UAAU,CAAC,KAAK,CAAC;IACnB;EACF,CAAC;EAED,oBACErC,OAAA,CAAC3B,GAAG;IAACwF,EAAE,EAAE;MAAE1D,eAAe,EAAE,SAAS;MAAEQ,SAAS,EAAE;IAAQ,CAAE;IAAAmD,QAAA,gBAE1D9D,OAAA,CAACC,cAAc;MAAC8D,SAAS,EAAE,CAAE;MAAAD,QAAA,gBAC3B9D,OAAA,CAACvB,UAAU;QAACuF,OAAO,EAAC,IAAI;QAACH,EAAE,EAAE;UAAEI,UAAU,EAAE;QAAO,CAAE;QAAAH,QAAA,EAAC;MAErD;QAAAI,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAY,CAAC,eACbrE,OAAA,CAACvB,UAAU;QAACuF,OAAO,EAAC,WAAW;QAAAF,QAAA,EAAC;MAEhC;QAAAI,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAY,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACC,CAAC,eAEjBrE,OAAA,CAACU,iBAAiB;MAACM,QAAQ,EAAC,IAAI;MAAA8C,QAAA,eAC9B9D,OAAA,CAACe,YAAY;QAAA+C,QAAA,gBACX9D,OAAA,CAAC3B,GAAG;UAACwF,EAAE,EAAE;YAAEtD,SAAS,EAAE,QAAQ;YAAE+D,EAAE,EAAE;UAAE,CAAE;UAAAR,QAAA,gBACtC9D,OAAA,CAACvB,UAAU;YAACuF,OAAO,EAAC,IAAI;YAACH,EAAE,EAAE;cAAEI,UAAU,EAAE,MAAM;cAAEK,EAAE,EAAE;YAAE,CAAE;YAAAR,QAAA,EAAC;UAE5D;YAAAI,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAY,CAAC,eACbrE,OAAA,CAACvB,UAAU;YAACuF,OAAO,EAAC,OAAO;YAAC5D,KAAK,EAAC,eAAe;YAAA0D,QAAA,EAAC;UAElD;YAAAI,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAY,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACV,CAAC,EAEL/B,KAAK,iBACJtC,OAAA,CAACrB,KAAK;UAAC4F,QAAQ,EAAC,OAAO;UAACV,EAAE,EAAE;YAAES,EAAE,EAAE;UAAE,CAAE;UAAAR,QAAA,EACnCxB;QAAK;UAAA4B,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACD,CACR,EAEA7B,OAAO,iBACNxC,OAAA,CAACrB,KAAK;UAAC4F,QAAQ,EAAC,SAAS;UAACV,EAAE,EAAE;YAAES,EAAE,EAAE;UAAE,CAAE;UAAAR,QAAA,EACrCtB;QAAO;UAAA0B,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CACR,eAEDrE,OAAA;UAAMwE,QAAQ,EAAExB,YAAa;UAAAc,QAAA,gBAC3B9D,OAAA,CAACzB,SAAS;YACRkG,SAAS;YACTC,KAAK,EAAC,WAAW;YACjBhD,IAAI,EAAC,MAAM;YACXmB,KAAK,EAAEnB,IAAK;YACZiD,QAAQ,EAAEjC,YAAa;YACvBxB,MAAM,EAAC,QAAQ;YACf0D,QAAQ;YACRC,UAAU,EAAE;cACVC,cAAc,eACZ9E,OAAA,CAACjB,cAAc;gBAACgG,QAAQ,EAAC,OAAO;gBAAAjB,QAAA,eAC9B9D,OAAA,CAACT,MAAM;kBAACa,KAAK,EAAC;gBAAQ;kBAAA8D,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACX;YAEpB;UAAE;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,eAEFrE,OAAA,CAACzB,SAAS;YACRkG,SAAS;YACTC,KAAK,EAAC,eAAe;YACrBhD,IAAI,EAAC,OAAO;YACZsD,IAAI,EAAC,OAAO;YACZnC,KAAK,EAAElB,KAAM;YACbgD,QAAQ,EAAEjC,YAAa;YACvBxB,MAAM,EAAC,QAAQ;YACf0D,QAAQ;YACRC,UAAU,EAAE;cACVC,cAAc,eACZ9E,OAAA,CAACjB,cAAc;gBAACgG,QAAQ,EAAC,OAAO;gBAAAjB,QAAA,eAC9B9D,OAAA,CAACX,KAAK;kBAACe,KAAK,EAAC;gBAAQ;kBAAA8D,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACV;YAEpB;UAAE;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,eAEFrE,OAAA,CAACzB,SAAS;YACRkG,SAAS;YACTC,KAAK,EAAC,UAAU;YAChBhD,IAAI,EAAC,UAAU;YACfsD,IAAI,EAAElD,YAAY,GAAG,MAAM,GAAG,UAAW;YACzCe,KAAK,EAAEjB,QAAS;YAChB+C,QAAQ,EAAEjC,YAAa;YACvBxB,MAAM,EAAC,QAAQ;YACf0D,QAAQ;YACRC,UAAU,EAAE;cACVC,cAAc,eACZ9E,OAAA,CAACjB,cAAc;gBAACgG,QAAQ,EAAC,OAAO;gBAAAjB,QAAA,eAC9B9D,OAAA,CAACV,IAAI;kBAACc,KAAK,EAAC;gBAAQ;kBAAA8D,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACT,CACjB;cACDY,YAAY,eACVjF,OAAA,CAACjB,cAAc;gBAACgG,QAAQ,EAAC,KAAK;gBAAAjB,QAAA,eAC5B9D,OAAA,CAAChB,UAAU;kBACTkG,OAAO,EAAEA,CAAA,KAAMnD,eAAe,CAAC,CAACD,YAAY,CAAE;kBAC9CqD,IAAI,EAAC,KAAK;kBAAArB,QAAA,EAEThC,YAAY,gBAAG9B,OAAA,CAACZ,aAAa;oBAAA8E,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE,CAAC,gBAAGrE,OAAA,CAACb,UAAU;oBAAA+E,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACxC;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACC;YAEpB;UAAE;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,eAEFrE,OAAA,CAACzB,SAAS;YACRkG,SAAS;YACTC,KAAK,EAAC,kBAAkB;YACxBhD,IAAI,EAAC,iBAAiB;YACtBsD,IAAI,EAAEhD,mBAAmB,GAAG,MAAM,GAAG,UAAW;YAChDa,KAAK,EAAEhB,eAAgB;YACvB8C,QAAQ,EAAEjC,YAAa;YACvBxB,MAAM,EAAC,QAAQ;YACf0D,QAAQ;YACRC,UAAU,EAAE;cACVC,cAAc,eACZ9E,OAAA,CAACjB,cAAc;gBAACgG,QAAQ,EAAC,OAAO;gBAAAjB,QAAA,eAC9B9D,OAAA,CAACV,IAAI;kBAACc,KAAK,EAAC;gBAAQ;kBAAA8D,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACT,CACjB;cACDY,YAAY,eACVjF,OAAA,CAACjB,cAAc;gBAACgG,QAAQ,EAAC,KAAK;gBAAAjB,QAAA,eAC5B9D,OAAA,CAAChB,UAAU;kBACTkG,OAAO,EAAEA,CAAA,KAAMjD,sBAAsB,CAAC,CAACD,mBAAmB,CAAE;kBAC5DmD,IAAI,EAAC,KAAK;kBAAArB,QAAA,EAET9B,mBAAmB,gBAAGhC,OAAA,CAACZ,aAAa;oBAAA8E,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE,CAAC,gBAAGrE,OAAA,CAACb,UAAU;oBAAA+E,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC/C;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACC;YAEpB;UAAE;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,eAEFrE,OAAA,CAACd,gBAAgB;YACfkG,OAAO,eACLpF,OAAA,CAACf,QAAQ;cACPoG,OAAO,EAAEnD,UAAW;cACpByC,QAAQ,EAAGhC,CAAC,IAAKR,aAAa,CAACQ,CAAC,CAACC,MAAM,CAACyC,OAAO,CAAE;cACjDxB,EAAE,EAAE;gBAAEzD,KAAK,EAAE;cAAU;YAAE;cAAA8D,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC1B,CACF;YACDK,KAAK,eACH1E,OAAA,CAACvB,UAAU;cAACuF,OAAO,EAAC,OAAO;cAAAF,QAAA,GAAC,gBACZ,EAAC,GAAG,eAClB9D,OAAA,CAACnB,IAAI;gBAACyG,IAAI,EAAC,GAAG;gBAACzB,EAAE,EAAE;kBAAEzD,KAAK,EAAE;gBAAU,CAAE;gBAAA0D,QAAA,EAAC;cAEzC;gBAAAI,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC,EAAC,GAAG,EAAC,KACT,EAAC,GAAG,eACPrE,OAAA,CAACnB,IAAI;gBAACyG,IAAI,EAAC,GAAG;gBAACzB,EAAE,EAAE;kBAAEzD,KAAK,EAAE;gBAAU,CAAE;gBAAA0D,QAAA,EAAC;cAEzC;gBAAAI,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACG,CACb;YACDR,EAAE,EAAE;cAAE0B,EAAE,EAAE,CAAC;cAAEjB,EAAE,EAAE;YAAE;UAAE;YAAAJ,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACtB,CAAC,eAEFrE,OAAA,CAACxB,MAAM;YACLwG,IAAI,EAAC,QAAQ;YACbP,SAAS;YACTT,OAAO,EAAC,WAAW;YACnBwB,IAAI,EAAC,OAAO;YACZC,QAAQ,EAAErD,OAAQ;YAClByB,EAAE,EAAE;cACF0B,EAAE,EAAE,CAAC;cACLjB,EAAE,EAAE,CAAC;cACLnE,eAAe,EAAE,SAAS;cAC1B,SAAS,EAAE;gBAAEA,eAAe,EAAE;cAAU,CAAC;cACzCuF,EAAE,EAAE,GAAG;cACPC,QAAQ,EAAE,QAAQ;cAClB1B,UAAU,EAAE;YACd,CAAE;YAAAH,QAAA,EAED1B,OAAO,gBAAGpC,OAAA,CAACpB,gBAAgB;cAAC4G,IAAI,EAAE,EAAG;cAACpF,KAAK,EAAC;YAAS;cAAA8D,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,GAAG;UAAgB;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACtE,CAAC,eAETrE,OAAA,CAAClB,OAAO;YAAC+E,EAAE,EAAE;cAAE+B,EAAE,EAAE;YAAE,CAAE;YAAA9B,QAAA,eACrB9D,OAAA,CAACvB,UAAU;cAACuF,OAAO,EAAC,SAAS;cAAC5D,KAAK,EAAC,eAAe;cAAA0D,QAAA,EAAC;YAEpD;cAAAI,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAY;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACN,CAAC,eAEVrE,OAAA,CAAC3B,GAAG;YAACwF,EAAE,EAAE;cAAEjD,OAAO,EAAE,MAAM;cAAEiF,GAAG,EAAE,CAAC;cAAEvB,EAAE,EAAE;YAAE,CAAE;YAAAR,QAAA,gBAC1C9D,OAAA,CAACxB,MAAM;cACLiG,SAAS;cACTT,OAAO,EAAC,UAAU;cAClB8B,SAAS,eAAE9F,OAAA,CAACR,MAAM;gBAAA0E,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAE;cACtBR,EAAE,EAAE;gBAAEkC,WAAW,EAAE,SAAS;gBAAE3F,KAAK,EAAE;cAAU,CAAE;cAAA0D,QAAA,EAClD;YAED;cAAAI,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC,eACTrE,OAAA,CAACxB,MAAM;cACLiG,SAAS;cACTT,OAAO,EAAC,UAAU;cAClB8B,SAAS,eAAE9F,OAAA,CAACP,QAAQ;gBAAAyE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAE;cACxBR,EAAE,EAAE;gBAAEkC,WAAW,EAAE,SAAS;gBAAE3F,KAAK,EAAE;cAAU,CAAE;cAAA0D,QAAA,EAClD;YAED;cAAAI,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACN,CAAC,eAENrE,OAAA,CAAC3B,GAAG;YAACwF,EAAE,EAAE;cAAEtD,SAAS,EAAE;YAAS,CAAE;YAAAuD,QAAA,eAC/B9D,OAAA,CAACvB,UAAU;cAACuF,OAAO,EAAC,OAAO;cAAC5D,KAAK,EAAC,eAAe;cAAA0D,QAAA,GAAC,0BACxB,EAAC,GAAG,eAC5B9D,OAAA,CAACnB,IAAI;gBACHmH,SAAS,EAAC,QAAQ;gBAClBhC,OAAO,EAAC,OAAO;gBACfkB,OAAO,EAAEA,CAAA,KAAM3D,QAAQ,CAAC,QAAQ,CAAE;gBAClCsC,EAAE,EAAE;kBAAEzD,KAAK,EAAE,SAAS;kBAAE6F,cAAc,EAAE;gBAAO,CAAE;gBAAAnC,QAAA,EAClD;cAED;gBAAAI,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACG;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACV,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACF,CAAC,eAGPrE,OAAA,CAAC3B,GAAG;UAACwF,EAAE,EAAE;YAAE0B,EAAE,EAAE,CAAC;YAAEW,CAAC,EAAE,CAAC;YAAE/F,eAAe,EAAE,SAAS;YAAEgG,YAAY,EAAE;UAAE,CAAE;UAAArC,QAAA,gBACpE9D,OAAA,CAACvB,UAAU;YAACuF,OAAO,EAAC,SAAS;YAACH,EAAE,EAAE;cAAEI,UAAU,EAAE,MAAM;cAAErD,OAAO,EAAE,OAAO;cAAE0D,EAAE,EAAE;YAAE,CAAE;YAAAR,QAAA,EAAC;UAEnF;YAAAI,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAY,CAAC,eACbrE,OAAA,CAACvB,UAAU;YAACuF,OAAO,EAAC,SAAS;YAACH,EAAE,EAAE;cAAEjD,OAAO,EAAE;YAAQ,CAAE;YAAAkD,QAAA,EAAC;UAExD;YAAAI,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAY,CAAC,eACbrE,OAAA,CAACvB,UAAU;YAACuF,OAAO,EAAC,SAAS;YAACH,EAAE,EAAE;cAAEjD,OAAO,EAAE;YAAQ,CAAE;YAAAkD,QAAA,EAAC;UAExD;YAAAI,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAY,CAAC,eACbrE,OAAA,CAACvB,UAAU;YAACuF,OAAO,EAAC,SAAS;YAACH,EAAE,EAAE;cAAEjD,OAAO,EAAE;YAAQ,CAAE;YAAAkD,QAAA,EAAC;UAExD;YAAAI,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAY,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACV,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACM;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACE,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACjB,CAAC;AAEV,CAAC;AAAC/C,EAAA,CAzTID,QAAQ;EAAA,QACK1B,WAAW;AAAA;AAAAyG,GAAA,GADxB/E,QAAQ;AA2Td,eAAeA,QAAQ;AAAC,IAAAZ,EAAA,EAAAK,GAAA,EAAAM,GAAA,EAAAgF,GAAA;AAAAC,YAAA,CAAA5F,EAAA;AAAA4F,YAAA,CAAAvF,GAAA;AAAAuF,YAAA,CAAAjF,GAAA;AAAAiF,YAAA,CAAAD,GAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}